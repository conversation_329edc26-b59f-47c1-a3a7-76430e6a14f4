import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Box, Flex, IconButton, useToast, Tooltip, HStack, <PERSON><PERSON>, Spinner, useBreakpointValue, Text, VStack, Menu, <PERSON>uButton, <PERSON>uList, MenuItem } from '@chakra-ui/react';
import { Download, Share, Delete, Favorite, FavoriteBorder, Save } from '@styled-icons/material';
import { useTranslation } from 'next-i18next';
import ArtifactDisplay from './ArtifactDisplay';
import { useShareModal } from '../../contexts/ShareModalContext';
import { handleDownload, handleShare } from '../../utils/imageUtils';
import { apiUrl } from '../../utils/apiUtils';
import { useAuth } from '../../contexts/AuthContext';
import FlowEditor from '../Flow/FlowEditor';
import { APP_TYPE } from '../../utils/constants';
import { PlayBtn } from '@styled-icons/bootstrap';
import { Edit } from "@styled-icons/fluentui-system-regular/Edit";
// import { Clipboard, ClipboardCheck } from '@styled-icons/bootstrap';
import { ClipboardCheckmark, Clipboard } from '@styled-icons/fluentui-system-regular'
import ShareModal from './ShareModal';
import { preprocessSvg } from '../../utils/svgUtils';
import ReactMarkdown from 'react-markdown';
import { Magic } from '@styled-icons/bootstrap/Magic';
import { Lightbulb } from '@styled-icons/material/Lightbulb';
import UserInputModal from './UserInputModal';

const ArtifactCard = ({ data, setData, onDelete, showDeleteButton = false, app, mode, onAIAction }) => {
    const toast = useToast();
    const { openShareModal } = useShareModal();
    const svgRef = useRef(null);
    const { t } = useTranslation('common');
    const [isLiked, setIsLiked] = useState(data?.isLiked || false);
    const { user } = useAuth();
    const [doc, setDoc] = useState();
    const [isLoading, setIsLoading] = useState(false); // 添加加载状态
    const [copied, setCopied] = useState(false);
    const isMobile = useBreakpointValue({ base: true, md: false })
    const [imgGenerated, setImgGenerated] = useState();
    const [userInputModalVisible, setUserInputModalVisible] = useState(false);
    const [aiActionLoading, setAiActionLoading] = useState(false); // AI action 加载状态

    const handleDelete = async () => {
        try {
            const response = await fetch(`${apiUrl}/ai/updateArtifact`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    _id: data._id,
                    app,
                    mode,
                    data: {
                        folder: 1
                    }
                })
            });

            if (response.ok) {
                onDelete(data._id);
                toast({
                    title: "data deleted",
                    status: "success",
                    duration: 3000,
                    isClosable: true,
                });
            } else {
                throw new Error('Failed to delete card');
            }
        } catch (error) {
            console.error('Error deleting card:', error);
            toast({
                title: "Error deleting card",
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        }
    };

    const handleLike = async () => {
        try {
            const response = await fetch(`${apiUrl}/ai/likeArtifact`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    _id: data._id,
                    isLiked: !isLiked
                })
            });

            if (response.ok) {
                setIsLiked(!isLiked);
                toast({
                    title: isLiked ? "Removed from favorites" : "Added to favorites",
                    status: "success",
                    duration: 2000,
                    isClosable: true,
                });
            } else {
                throw new Error('Failed to update like status');
            }
        } catch (error) {
            console.error('Error updating like status:', error);
            toast({
                title: "Error updating like status",
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        }
    };

    // AI 菜单项点击处理方法
    const handleAIAction = async (action, userInput = '') => {
        if (action === 'improve_codes' && !userInput) {
            // 如果是优化操作且没有用户输入，显示输入弹窗
            setUserInputModalVisible(true);
            return;
        }

        if (onAIAction) {
            setAiActionLoading(true); // 开始加载
            try {
                await onAIAction(action, data, userInput);
            } catch (error) {
                console.error('AI action error:', error);
            } finally {
                setAiActionLoading(false); // 结束加载
            }
        }
    };

    const handleSaveSVG = useCallback(() => {
        if (data?.type != 'svg') return;

        const svgContent = preprocessSvg(data.content);
        const blob = new Blob([svgContent], { type: 'image/svg+xml' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.href = url;
        link.download = 'artifact.svg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, [data]);

    useEffect(() => {
        setIsLiked(data?.isLiked);
    }, [data]);

    useEffect(() => {
        if (data.type === 'flow') {
            setDoc(data);
        }
    }, [data])

    if (data?.err) {
        return <VStack spacing={3}>
            <Text>{data.err === 'exceed_msg_limit' && t('upgrade_to_vip_msg') || data.err}</Text>
            {
                data.err === 'exceed_msg_limit' &&
                <Button
                    aria-label={t('upgrade_to_vip')}
                    colorScheme={'blue'}
                    size={'sm'}
                    onClick={() => {
                        window.open(process.env.PRICING_URL, '_blank')
                    }}
                >
                    {t('upgrade_to_vip')}
                </Button>
            }
        </VStack>
    }

    return (
        <Box borderWidth={1} borderRadius="lg" overflow="hidden">
            {
                data && (mode === 'imageInsights' || [APP_TYPE.graphics, APP_TYPE.onePageSlides, APP_TYPE.infographic, APP_TYPE.mindsnap, APP_TYPE.insightcards].includes(app)) &&
                <ArtifactDisplay
                    // src={card.content}
                    // id={card._id}
                    app={app}
                    artifact={data}
                    svgRef={svgRef}
                />
            }

            {
                data && (data.type === 'flow' || [APP_TYPE.mindmap, APP_TYPE.reading, APP_TYPE.movie, APP_TYPE.feynman, APP_TYPE.bloom, APP_TYPE.solo, APP_TYPE.dok, APP_TYPE.marzano, APP_TYPE.layeredExplanation, APP_TYPE.criticalThinking, APP_TYPE.reflection, APP_TYPE.refineQuestion, APP_TYPE.bias, APP_TYPE.brainstorming, APP_TYPE.mindkit, APP_TYPE.businessmodel, APP_TYPE.startupmentor, APP_TYPE.okr, APP_TYPE.decision, APP_TYPE.planner, APP_TYPE.youtube, APP_TYPE.counselor, APP_TYPE.dreamlens, APP_TYPE.horoscope, APP_TYPE.art, APP_TYPE.photo, APP_TYPE.erase, APP_TYPE.avatar, APP_TYPE.imageEditor].includes(app)) &&
                <Box
                    style={{
                        width: '100%',
                        height: isMobile ? (imgGenerated ? undefined : 300) : 600,
                    }}
                    ref={svgRef}
                    align='left'
                >
                    <FlowEditor
                        app={app}
                        mode={mode}
                        doc={doc}
                        imgGenerated={imgGenerated}
                        setImgGenerated={setImgGenerated}
                        content={data.content}
                        mimeContents={data.mimeContents}
                        context={data.context}
                        ai_action={data.action}
                        onDocSaved={(doc) => {
                            setDoc(doc);
                            doc && setData(d => {
                                return {
                                    ...d,
                                    _id: doc._id
                                }
                            })
                        }}
                    />
                </Box>
            }
            {
                (app === APP_TYPE.slides || data.type === 'slides') && data?.hid &&
                <div
                    ref={svgRef}
                    style={{
                        width: data?.hid ? '100%' : 1,
                        height: data?.hid ? 540 : 1
                    }}
                >
                    <iframe
                        className='nodrag'
                        id='slides-frame'
                        style={{
                            width: '100%',
                            height: '100%'
                        }}
                        // ref={iframeRef}
                        src={`https://service.funblocks.net/present.html?theme=sky&hid=${data.hid || ''}`}
                        title="FunBlocks AI Slides"
                    />
                </div>
            }
            {
                data?.type === 'markdown' &&
                <Box p={4} textAlign="left" ref={svgRef}>
                    <ReactMarkdown>{data.content}</ReactMarkdown>
                </Box>
            }
            {
                data?.err && <div>{data.err}</div>
            }

            <Flex justifyContent="space-between" p={1} bg="gray.100" width={'100%'} alignItems="center">
                <HStack spacing={1} fontSize="sm" color="gray.500" marginLeft={1}>
                    <span style={{ maxWidth: '300px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {data?.promptLabel || data?.title || ''}
                    </span>
                    {
                        doc?.type === 'flow' &&
                        <Tooltip label={t('to_aiflow_tips')} aria-label="Share tooltip">
                            <Button
                                aria-label={t('to_aiflow_tips')}
                                color={'dodgerblue'}
                                size={'sm'}
                                onClick={() => window.open('https://app.funblocks.net/#/aiflow?hid=' + doc.hid, '_blank')}
                            >
                                {t('to_aiflow')}
                            </Button>
                        </Tooltip>
                    }
                    {
                        data?.type === 'slides' &&
                        <IconButton
                            icon={<PlayBtn size={24} />}
                            aria-label={'presentation'}
                            color={'gray.500'}
                            size={'sm'}
                            onClick={() => window.open(`https://service.funblocks.net/present.html?theme=sky&hid=${data.hid}`, '_blank')}
                        />
                    }
                    {
                        ['slides', 'markdown'].includes(data?.type) &&
                        <IconButton
                            icon={<Edit size={20} />}
                            aria-label={'edit'}
                            color={'gray.500'}
                            size={'sm'}
                            onClick={() => {
                                if (data?.type === 'slides') {
                                    window.open(`https://app.funblocks.net/#/slidesEditor?hid=${data.hid}`, '_blank');
                                } else if(data?.type === 'markdown') {
                                    window.open(`https://app.funblocks.net/#/editor?hid=${data.hid}`, '_blank');
                                } 
                            }}
                        />
                    }
                </HStack>
                <HStack spacing={1}>
                    {/* AI Actions 菜单 */}
                    {onAIAction && ['mermaid', 'svg'].includes(data.type) && (
                        <Tooltip label={t('ai_actions')} aria-label="AI Actions tooltip">
                            <Menu>
                                <MenuButton
                                    as={IconButton}
                                    icon={aiActionLoading ? <Spinner size="sm" /> : <Magic size={20} />}
                                    aria-label={t('ai_actions')}
                                    color={'dodgerblue'}
                                    size={'sm'}
                                    variant="ghost"
                                    isLoading={aiActionLoading}
                                    disabled={aiActionLoading}
                                />
                                <MenuList>
                                    <MenuItem
                                        icon={<Lightbulb size={16} color='dodgerblue' />}
                                        onClick={() => handleAIAction('fix_codes_bug')}
                                        isDisabled={aiActionLoading}
                                    >
                                        {t('fix_codes_bug')}
                                    </MenuItem>
                                    <MenuItem
                                        icon={<Magic size={16} color='dodgerblue' />}
                                        onClick={() => handleAIAction('improve_codes')}
                                        isDisabled={aiActionLoading}
                                    >
                                        {t('improve_codes')}
                                    </MenuItem>
                                </MenuList>
                            </Menu>
                        </Tooltip>
                    )}
                    {
                        ![APP_TYPE.mindmap].includes(app) &&
                        <Tooltip label={isLiked ? t('unlike') : t(user?._id === data?.userId ? 'share_to_showcase' : 'like')} aria-label="Like tooltip">
                            <IconButton
                                icon={isLiked ? <Favorite size={20} /> : <FavoriteBorder size={20} />}
                                aria-label={isLiked ? t('unlike') : t('like')}
                                color={isLiked ? 'red.500' : 'gray.500'}
                                size={'sm'}
                                onClick={handleLike}
                            />
                        </Tooltip>
                    }
                    {
                        data?.type === 'svg' && <Tooltip label={t('save_svg')} placement="bottom">
                            <IconButton
                                icon={<Save size={20} />}
                                aria-label={t('save_svg')}
                                color={'gray.500'}
                                size="sm"
                                onClick={() => handleSaveSVG()}
                            />
                        </Tooltip>
                    }
                    {
                        !['slides'].includes(data?.type) &&
                        <Tooltip label={t('download')} aria-label="Download tooltip">
                            <IconButton
                                icon={isLoading === 'download' ? <Spinner size="sm" /> : <Download size={20} />} // 显示加载指示器
                                aria-label={t('download')}
                                color={'gray.500'}
                                size={'sm'}
                                onClick={async () => {
                                    setIsLoading('download'); // 开始加载
                                    try {
                                        await handleDownload(svgRef, toast);
                                    } finally {
                                        setIsLoading(null); // 结束加载
                                    }
                                }}
                                isLoading={isLoading === 'download'}
                            />
                        </Tooltip>
                    }

                    {
                        data?.content &&
                        <Tooltip label={t(copied ? 'copied' : 'copy')} aria-label="Share tooltip">
                            <IconButton
                                icon={copied ? <ClipboardCheckmark color='green' size={20} /> : <Clipboard size={20} />}
                                aria-label={t('share')}
                                color={'gray.500'}
                                size={'sm'}
                                onClick={() => {
                                    navigator.clipboard.writeText(data.content);
                                    setCopied(true);
                                    setTimeout(() => setCopied(false), 2000);
                                }}
                            />
                        </Tooltip>
                    }
                    <Tooltip label={t('share')} aria-label="Share tooltip">
                        <IconButton
                            icon={isLoading === 'share' ? <Spinner size="sm" /> : <Share size={20} />}
                            aria-label={t('share')}
                            color={'gray.500'}
                            size={'sm'}
                            onClick={async () => {
                                setIsLoading('share'); // 开始加载
                                try {
                                    await handleShare(app, data?._id, data?.title, svgRef, openShareModal, toast);
                                } finally {
                                    setIsLoading(null); // 结束加载
                                }
                            }}
                            isLoading={isLoading === 'share'}
                        />
                    </Tooltip>
                    {showDeleteButton && (
                        <Tooltip label={t('delete')} aria-label="Delete tooltip">
                            <IconButton
                                icon={<Delete size={20} />}
                                aria-label={t('delete')}
                                color={'gray.500'}
                                size={'sm'}
                                onClick={handleDelete}
                            />
                        </Tooltip>
                    )}
                </HStack>
            </Flex>

            {/* UserInputModal */}
            <UserInputModal
                visible={userInputModalVisible}
                title={t('improve_codes')}
                placeholder={t('please_enter_your_requirements')}
                onConfirm={(userInput) => {
                    setUserInputModalVisible(false);
                    handleAIAction('improve_codes', userInput);
                }}
                onCancel={() => setUserInputModalVisible(false)}
            />
        </Box>
    );
};

export default ArtifactCard;
