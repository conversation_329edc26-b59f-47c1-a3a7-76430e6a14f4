import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  VStack,
  useColorModeValue,
  Image,
  Badge
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

const ArtStyleSelector = ({ onSelect, value }) => {
  const { t } = useTranslation('sketch');
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const selectedBorderColor = useColorModeValue('blue.500', 'blue.300');

  const artStyles = [
    {
      id: 'realistic',
      name: t('style_realistic'),
      description: t('style_realistic_desc'),
      preview: '/images/sketch-styles/realistic.jpg',
      popular: true
    },
    {
      id: 'anime',
      name: t('style_anime'),
      description: t('style_anime_desc'),
      preview: '/images/sketch-styles/anime.jpg',
      popular: true
    },
    {
      id: 'cartoon',
      name: t('style_cartoon'),
      description: t('style_cartoon_desc'),
      preview: '/images/sketch-styles/cartoon.jpg',
      popular: false
    },
    {
      id: 'watercolor',
      name: t('style_watercolor'),
      description: t('style_watercolor_desc'),
      preview: '/images/sketch-styles/watercolor.jpg',
      popular: false
    },
    {
      id: 'oil_painting',
      name: t('style_oil_painting'),
      description: t('style_oil_painting_desc'),
      preview: '/images/sketch-styles/oil-painting.jpg',
      popular: false
    },
    {
      id: 'sketch',
      name: t('style_sketch'),
      description: t('style_sketch_desc'),
      preview: '/images/sketch-styles/sketch.jpg',
      popular: false
    },
    {
      id: 'cyberpunk',
      name: t('style_cyberpunk'),
      description: t('style_cyberpunk_desc'),
      preview: '/images/sketch-styles/cyberpunk.jpg',
      popular: true
    },
    {
      id: 'fantasy',
      name: t('style_fantasy'),
      description: t('style_fantasy_desc'),
      preview: '/images/sketch-styles/fantasy.jpg',
      popular: false
    },
    {
      id: 'minimalist',
      name: t('style_minimalist'),
      description: t('style_minimalist_desc'),
      preview: '/images/sketch-styles/minimalist.jpg',
      popular: false
    },
    {
      id: 'abstract',
      name: t('style_abstract'),
      description: t('style_abstract_desc'),
      preview: '/images/sketch-styles/abstract.jpg',
      popular: false
    }
  ];

  const handleStyleSelect = (styleId) => {
    onSelect(styleId);
  };

  return (
    <VStack spacing={4} width="100%">
      <Text fontSize="lg" fontWeight="semibold" textAlign="center">
        {t('choose_art_style')}
      </Text>
      
      <SimpleGrid columns={{ base: 2, md: 3, lg: 5 }} spacing={3} width="100%">
        {artStyles.map((style) => (
          <Box
            key={style.id}
            position="relative"
            cursor="pointer"
            onClick={() => handleStyleSelect(style.id)}
            transition="all 0.2s"
            _hover={{ transform: 'scale(1.05)' }}
          >
            <Box
              border="2px solid"
              borderColor={value === style.id ? selectedBorderColor : borderColor}
              borderRadius="lg"
              overflow="hidden"
              bg={bgColor}
              position="relative"
            >
              {/* Style preview image placeholder */}
              <Box
                width="100%"
                height="80px"
                bg="gray.100"
                display="flex"
                alignItems="center"
                justifyContent="center"
                position="relative"
              >
                <Text fontSize="xs" color="gray.500" textAlign="center">
                  {style.name}
                </Text>
                
                {/* Popular badge */}
                {style.popular && (
                  <Badge
                    position="absolute"
                    top="2"
                    right="2"
                    colorScheme="orange"
                    size="sm"
                    fontSize="xs"
                  >
                    {t('popular')}
                  </Badge>
                )}
              </Box>
              
              {/* Style info */}
              <VStack spacing={1} p={2} align="stretch">
                <Text
                  fontSize="sm"
                  fontWeight="medium"
                  textAlign="center"
                  noOfLines={1}
                >
                  {style.name}
                </Text>
                <Text
                  fontSize="xs"
                  color="gray.500"
                  textAlign="center"
                  noOfLines={2}
                  height="32px"
                >
                  {style.description}
                </Text>
              </VStack>
            </Box>
          </Box>
        ))}
      </SimpleGrid>
      
      <Text fontSize="sm" color="gray.500" textAlign="center">
        {t('style_selection_tip')}
      </Text>
    </VStack>
  );
};

export default ArtStyleSelector;
