import { useRef, useEffect, useState, useMemo } from 'react'
import { <PERSON>ton, VStack, useToast, useDisclosure, Heading, Box, Tabs, TabList, TabPanels, TabPanel, Modal, ModalOverlay, ModalContent, ModalHeader, ModalBody, ModalCloseButton, Flex, HStack, Tab, Text, Menu, MenuButton, MenuList, MenuItem, Link, IconButton } from '@chakra-ui/react'
import { GoogleLogin } from './GoogleLogin'
import { useAuth } from '../../contexts/AuthContext'
import ShareModal from './ShareModal'
import { ShareModalProvider } from '../../contexts/ShareModalContext'
import CardGenerator from './CardGenerator'
import { QRCodeCanvas } from 'qrcode.react';
import styles from './Main.module.css';
import LoginModal from './LoginModal';
import { ChevronDownIcon, HamburgerIcon } from '@chakra-ui/icons';
import LocaleSelector from '../LocaleSelector'
import { useTranslation } from 'next-i18next'
import { useBreakpointValue } from '@chakra-ui/react'
import { AccountCircle } from '@styled-icons/material'
import getConfig from 'next/config';
import { APP_TYPE, getAppname } from '../../utils/constants'
import Artifacts from './Artifacts'
import MindmapIntro from './MindmapIntro'
import BrainstormingIntro from './BrainstormingIntro'
import DecisionIntro from './DecisionIntro'
import PlannerIntro from './PlannerIntro'
import SlidesIntro from './SlidesIntro'
import TeachingSlidesIntro from './TeachingSlidesIntro'
import SummarizerIntro from './SummarizerIntro'
import DreamLensIntro from './DreamLensIntro'
import ArtInsightIntro from './ArtInsightIntro'
import PhotoIntro from './PhotoIntro'
import ReadingMapIntro from './ReadingMapIntro'
import StartupMentorIntro from './StartupMentorIntro'
import BusinessModelAnalyzerIntro from './BusinessModelAnalyzerIntro'
import OKRAssistantIntro from './OKRAssistantIntro'
import PoeticLensIntro from './PoeticLensIntro'
import OnePageSlidesIntro from './OnePageSlidesIntro'
import InfographicIntro from '../infographic/InfographicIntro'
import GraphicsIntro from '../graphics/GraphicsIntro'
import MindKitIntro from './MindKitIntro'
import HoroscopeIntro from './HoroscopeIntro'
import CounselorIntro from './CounselorIntro'
import CriticalThinkingIntro from './CriticalThinkingIntro'
import ReflectionIntro from './ReflectionIntro'
import CineMapIntro from './CineMapIntro'
import QuestionCraftIntro from './QuestionCraftIntro'
import LogicLensIntro from './LogicLensIntro'
import FeynmanIntro from './FeynmanIntro'
import BloomIntro from './BloomIntro'
import SOLOIntro from './SOLOIntro'
import DOKIntro from './DOKIntro'
import DOKAssessmentIntro from './DOKAssessmentIntro'
import MarzanoIntro from './MarzanoIntro'
import LayeredExplanationIntro from './LayeredExplanationIntro'
import EraseIntro from './EraseIntro'
import AvatarStudioIntro from './AvatarStudioIntro'
import ImageEditorIntro from './ImageEditorIntro'
import MindSnapIntro from './MindSnapIntro'
import InsightCardsIntro from './InsightCardsIntro'
import Footer from '../common/Footer'
import PromptOptimizerIntro from './PromptOptimizerIntro'
import LessonPlansIntro from './LessonPlansIntro'

const MainFrame = ({ app, initial_showcases, isCollection, mental_model }) => {
  const { t } = useTranslation('common')
  const toast = useToast()
  const { isLoggedIn, logon, logout, openLoginModal, user } = useAuth()
  const [isTabsSticky, setIsTabsSticky] = useState(false)
  const tabsRef = useRef(null)
  const [showMainHeader, setShowMainHeader] = useState(true);
  const mainHeaderRef = useRef(null);
  const { isOpen: isCardGeneratorOpen, onOpen: onCardGeneratorOpen, onClose: onCardGeneratorClose } = useDisclosure()
  const [activeTab, setActiveTab] = useState(0)
  const isMobile = useBreakpointValue({ base: true, md: false })
  const { basePathGraphics, basePathMindmap } = getConfig().publicRuntimeConfig;
  const basePath = app === APP_TYPE.mindmap && basePathMindmap || basePathGraphics;
  const appname = useMemo(() => getAppname(app), [app])

  useEffect(() => {
    const handleScroll = () => {
      if (tabsRef.current) {
        const tabsTop = tabsRef.current.getBoundingClientRect().top
        setIsTabsSticky(tabsTop <= 0)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      if (tabsRef.current && mainHeaderRef.current) {
        const cardsGalleryRect = tabsRef.current.getBoundingClientRect();
        const mainHeaderRect = mainHeaderRef.current.getBoundingClientRect();
        setShowMainHeader(cardsGalleryRect.top > mainHeaderRect.bottom);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = () => {
    logout()
    toast({
      title: "已登出",
      status: "info",
      duration: 3000,
      isClosable: true,
    })
  }

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const handleTabChange = (index) => {
    setActiveTab(index)
  }

  const tabClicked = () => {
    if (isTabsSticky && tabsRef.current) {
      const tabsRect = tabsRef.current.getBoundingClientRect();
      const scrollPosition = window.pageYOffset + tabsRect.top - 20;
      window.scrollTo({
        top: scrollPosition,
        behavior: 'smooth'
      });
    }
  }

  return (
    <ShareModalProvider>
      <Flex
        ref={mainHeaderRef}
        position="sticky"
        top="0"
        width="100%"
        zIndex="sticky"
        bg={'white'}
        boxShadow="md"
        transition="opacity 0.3s"
        opacity={showMainHeader ? (isMobile ? 1 : 0.92) : 0}
        pointerEvents={showMainHeader ? 'auto' : 'none'}
        justifyContent="space-between"
        alignItems="center"
        flexDirection={'row'}
        p={isMobile ? 1 : 2}
      >
        {isMobile && (
          <Menu>
            <MenuButton
              as={IconButton}
              icon={<HamburgerIcon />}
              variant="outline"
              aria-label="Options"
            />
            <MenuList>
              <MenuItem as={Link} href={getConfig().publicRuntimeConfig.basePath} isExternal>FunBlocks AI Tools</MenuItem>
              {/* {app === APP_TYPE.graphics && <MenuItem as={Link} href={`${basePath}/intro`} isExternal>{t('why_funblocks_ai', { app })}</MenuItem>} */}
              <MenuItem as={Link} href={process.env.PRICING_URL} isExternal>{t('pricing')}</MenuItem>
              {user ? (
                <MenuItem onClick={handleLogout}>{t('logout')}</MenuItem>
              ) : (
                <MenuItem onClick={openLoginModal}>{t('login')}</MenuItem>
              )}
            </MenuList>
          </Menu>
        )}
        <Flex alignItems="center" columnGap={2} style={{ fontSize: isMobile ? '18px' : '22px', fontWeight: 'bold' }}>
          <Link href={getConfig().publicRuntimeConfig.basePath} isExternal style={{ color: 'dodgerblue' }}>{isMobile ? 'AI' : 'FunBlocks AI Tools'}</Link>
          <span style={{ color: 'blueviolet' }}>{appname}</span>
        </Flex>


        <Flex justifyContent="flex-end" zIndex={1} alignItems="center" columnGap={isMobile ? 2 : 8}>
          {!isMobile && (
            <>
              {/* {app === APP_TYPE.graphics && <Link href={`${basePath}/intro`} isExternal>{t('why_funblocks_ai', { app })}</Link>} */}
              <Link href={process.env.PRICING_URL} isExternal>{t('pricing')}</Link>
            </>
          )}
          <LocaleSelector />
          {!isMobile && (
            <>
              {!user && <GoogleLogin />}
              {user ? (
                <Menu>
                  <MenuButton
                    as={IconButton}
                    icon={<AccountCircle size={30} />}
                    variant="ghost"
                    aria-label="Options"
                  />
                  <MenuList>
                    {
                      app != APP_TYPE.graphics &&
                      <MenuItem onClick={() => { window.open(`${getConfig().publicRuntimeConfig.basePath}/my`, '_blank') }}>{t('my_generations')}</MenuItem>
                    }
                    <MenuItem onClick={handleLogout}>{t('logout')}</MenuItem>
                  </MenuList>
                </Menu>
              ) : (
                <Button colorScheme='blue' onClick={openLoginModal} ml={2}>{t('login')}</Button>
              )}
            </>
          )}
        </Flex>
      </Flex>

      <VStack spacing={10} width="100%" minHeight="100vh" alignItems="center">
        <div style={{ display: 'none' }}>
          <QRCodeCanvas
            value={`https://www.funblocks.net${basePath}`}
            size={100}

            id="qrcode-canvas"
          />
        </div>

        <ShareModal app={app} />

        <LoginModal />

        <Flex width="100%" justifyContent="center" flexDirection="column">
          <Flex width="100%" justifyContent="center" flexDirection="column" alignItems={"center"} padding={isMobile ? 2 : 0} pt={isMobile ? 6 : 20} pb={isMobile ? 6 : 16}>
            <Heading
              pb={isMobile ? 0 : 6}
              pl={0}
              pr={0}
              bgGradient="linear(to-r, dodgerblue, fuchsia)"
              bgClip="text"
              fontSize={isMobile ? "xl" : "4xl"}
              fontWeight="extrabold"
            >
              {t('app_slogan_' + app?.replaceAll('-', '').toLowerCase())}
            </Heading>
            <Text fontSize={isMobile ? "unset" : "lg"} pt={3} pb={isMobile ? 4 : 12}>{t('app_one_sentence_' + app?.replaceAll('-', '').toLowerCase())}</Text>

            <CardGenerator showExample={true} onLoginRequired={openLoginModal} app={app} mental_model={mental_model} />
          </Flex>

          {
            isCollection &&
            <Box mt={isMobile ? 12 : 20} ref={tabsRef} position="relative" width="100%" maxWidth="900px" alignSelf="center" alignItems="center">
              {/* {
                [APP_TYPE.graphics].includes(app) &&
                <Tabs width="100%" index={activeTab} onChange={handleTabChange}>
                  <TabList
                    position={isTabsSticky ? "fixed" : "static"}
                    top="0"
                    left="0"
                    zIndex="1000"
                    width="100%"
                    bg="white"
                    opacity={isTabsSticky ? 0.8 : 1}
                    boxShadow={isTabsSticky ? "0 2px 4px rgba(0,0,0,0.1)" : "none"}
                    transition="all 0.3s ease"
                    alignItems="center"
                    justifyContent="space-between"
                    paddingX={4}
                  >
                    <HStack>
                      <Tab
                        fontWeight={"semibold"}
                        onClick={tabClicked}
                      >
                        {t(app?.toLowerCase())}
                      </Tab>
                      <Tab
                        fontWeight={"semibold"}
                        onClick={tabClicked}
                      >
                        {t('mine')}
                      </Tab>
                    </HStack>
                    {isTabsSticky && (
                      <Button
                        onClick={() => {
                          if (app === APP_TYPE.graphics) {
                            onCardGeneratorOpen()
                          } else {
                            scrollToTop();
                          }
                        }}
                        colorScheme='purple'
                        size="sm"
                      >
                        {t('generate_card')}
                      </Button>
                    )}
                  </TabList>
                  <TabPanels>
                    <TabPanel className={styles.tabPanel}>
                      <Artifacts collection={isCollection ? 'collection' : 'showcase'} app={app} initial_items={initial_showcases} />
                    </TabPanel>
                    <TabPanel className={styles.tabPanel}>
                      {!isLoggedIn && <Text>{t('please_login_to_view_mine', { app: app?.toLowerCase() })}</Text>}
                      {isLoggedIn && <Artifacts collection={'my'} app={app} />}
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              } */}
              {
                isCollection &&
                <Tabs width="100%" index={activeTab} onChange={handleTabChange}>
                  <TabList
                    position={isTabsSticky ? "fixed" : "static"}
                    top="0"
                    left="0"
                    zIndex="1000"
                    width="100%"
                    bg="white"
                    opacity={isTabsSticky ? 0.8 : 1}
                    boxShadow={isTabsSticky ? "0 2px 4px rgba(0,0,0,0.1)" : "none"}
                    transition="all 0.3s ease"
                    alignItems="center"
                    justifyContent="space-between"
                    paddingX={4}
                  >
                    <HStack>
                      <Tab
                        fontWeight={"semibold"}
                        onClick={tabClicked}
                      >
                        Classic Books
                      </Tab>
                    </HStack>
                    {isTabsSticky && (
                      <Button
                        onClick={() => {
                          if (app === APP_TYPE.graphics) {
                            onCardGeneratorOpen()
                          } else {
                            scrollToTop();
                          }
                        }}
                        colorScheme='purple'
                        size="sm"
                      >
                        {t('generate_card')}
                      </Button>
                    )}
                  </TabList>
                  <TabPanels>
                    <TabPanel className={styles.tabPanel}>
                      <Artifacts collection={'collection'} app={APP_TYPE.reading} mode={APP_TYPE.movie === app && 'movie' || 'book'} initial_items={initial_showcases} />
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              }
            </Box>
          }
          {
            !isCollection &&
            [APP_TYPE.mindmap, APP_TYPE.reading, APP_TYPE.movie].includes(app) &&
            <Box mt={isMobile ? 12 : 16} ref={tabsRef} position="relative" width="100%" maxWidth={isMobile ? undefined : "900px"} alignSelf="center" alignItems="center">
              <Artifacts collection={'showcase'} app={app} mode={APP_TYPE.movie === app && 'movie' || 'book'} initial_items={initial_showcases} />
            </Box>
          }
          {
            !isCollection &&
            <>
              {
                APP_TYPE.mindmap === app && (
                  <MindmapIntro />
                )
              }
              {
                APP_TYPE.brainstorming === app && (
                  <BrainstormingIntro />
                )
              }
              {
                APP_TYPE.decision === app && (
                  <DecisionIntro />
                )
              }
              {
                APP_TYPE.planner === app && (
                  <PlannerIntro />
                )
              }
              {
                APP_TYPE.slides === app && (
                  <SlidesIntro />
                )
              }
              {
                APP_TYPE.onePageSlides === app && (
                  <OnePageSlidesIntro />
                )
              }
              {
                APP_TYPE.youtube === app && (
                  <SummarizerIntro />
                )
              }
              {
                APP_TYPE.dreamlens === app && (
                  <DreamLensIntro />
                )
              }
              {
                APP_TYPE.art === app && (
                  <ArtInsightIntro />
                )
              }
              {
                APP_TYPE.photo === app && (
                  <PhotoIntro />
                )
              }
              {
                APP_TYPE.reading === app && (
                  <ReadingMapIntro />
                )
              }
              {
                APP_TYPE.startupmentor === app && (
                  <StartupMentorIntro />
                )
              }
              {
                APP_TYPE.businessmodel === app && (
                  <BusinessModelAnalyzerIntro />
                )
              }
              {
                APP_TYPE.okr === app && (
                  <OKRAssistantIntro />
                )
              }
              {
                APP_TYPE.poetic === app && (
                  <PoeticLensIntro />
                )
              }
              {
                APP_TYPE.infographic === app && (
                  <InfographicIntro />
                )
              }
              {
                APP_TYPE.graphics === app && (
                  <GraphicsIntro />
                )
              }
              {
                APP_TYPE.mindkit === app && (
                  <MindKitIntro />
                )
              }
              {
                APP_TYPE.horoscope === app && (
                  <HoroscopeIntro />
                )
              }
              {
                APP_TYPE.counselor === app && (
                  <CounselorIntro />
                )
              }
              {
                APP_TYPE.criticalThinking === app && (
                  <CriticalThinkingIntro />
                )
              }
              {
                APP_TYPE.reflection === app && (
                  <ReflectionIntro />
                )
              }
              {
                APP_TYPE.movie === app && (
                  <CineMapIntro />
                )
              }
              {
                APP_TYPE.refineQuestion === app && (
                  <QuestionCraftIntro />
                )
              }
              {
                APP_TYPE.bias === app && (
                  <LogicLensIntro />
                )
              }
              {
                APP_TYPE.feynman === app && (
                  <FeynmanIntro />
                )
              }
              {
                APP_TYPE.bloom === app && (
                  <BloomIntro />
                )
              }
              {
                APP_TYPE.solo === app && (
                  <SOLOIntro />
                )
              }
              {
                APP_TYPE.dok === app && (
                  <DOKIntro />
                )
              }
              {
                APP_TYPE.dokAssessment === app && (
                  <DOKAssessmentIntro />
                )
              }
              {
                APP_TYPE.marzano === app && (
                  <MarzanoIntro />
                )
              }
              {
                APP_TYPE.layeredExplanation === app && (
                  <LayeredExplanationIntro />
                )
              }
              {
                APP_TYPE.erase === app && (
                  <EraseIntro />
                )
              }
              {
                APP_TYPE.avatar === app && (
                  <AvatarStudioIntro />
                )
              }
              {
                APP_TYPE.imageEditor === app && (
                  <ImageEditorIntro />
                )
              }
              {
                APP_TYPE.mindsnap === app && (
                  <MindSnapIntro />
                )
              }
              {
                APP_TYPE.insightcards === app && (
                  <InsightCardsIntro />
                )
              }
              {
                APP_TYPE.promptOptimizer === app && (
                  <PromptOptimizerIntro />
                )
              }
              {
                APP_TYPE.lessonPlanner === app && (
                  <LessonPlansIntro />
                )
              }
              {
                APP_TYPE.teachingSlides === app && (
                  <TeachingSlidesIntro />
                )
              }
            </>
          }
          <Footer />
        </Flex>

        <Modal isOpen={isCardGeneratorOpen} onClose={onCardGeneratorClose} size="xl" isCentered>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{t('generate_new_card')}</ModalHeader>
            <ModalCloseButton />
            {/* <ModalBody> */}
            <CardGenerator
              onLoginRequired={() => {
                onCardGeneratorClose()
                openLoginModal()
              }}
              app={app}
              mental_model={mental_model}
            />
            {/* </ModalBody> */}
          </ModalContent>
        </Modal>

      </VStack>
    </ShareModalProvider>
  )
}

export default MainFrame
