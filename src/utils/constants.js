export const APP_TYPE = {
    mindmap: 'Mindmap',
    graphics: 'Graphics',
    infographic: "Infographic",
    insightcards: "InsightCards",
    brainstorming: 'Brainstorming',
    mindkit: 'MindKit',
    mindsnap: 'MindSnap',  //TODO
    businessmodel: 'Businessmodel',
    startupmentor: 'Startupmentor',
    okr: 'Okr',
    decision: 'Decision',
    planner: 'Planner',
    slides: 'Slides',
    onePageSlides: 'one-page-slide',
    erase: 'Erase',
    avatar: 'Avatar',
    imageEditor: 'ImageEditor',
    youtube: 'Youtube',
    counselor: 'Counselor',
    dreamlens: 'DreamLens',
    horoscope: 'Horoscope',
    art: 'Art',
    photo: 'Photo',
    reading: 'Reading',
    movie: 'Movie',
    criticalThinking: 'critical-thinking',
    reflection: 'reflection',
    refineQuestion: 'refine-question',
    bias: 'Bias',
    poetic: 'Poetic',
    feynman: 'Feynman',
    bloom: '<PERSON>',
    solo: 'solo',
    dok: 'dok',
    marzano: 'marzano',
    layeredExplanation: 'layered-explanation',
    promptOptimizer: 'prompt-optimizer',
    lessonPlanner: 'lesson-plans',
    teachingSlides: 'teaching-slides',
    dokAssessment: 'dok-assessment'
}

export const getAppname = (app) => {
    return app === APP_TYPE.art && 'Art Insight'
    || app === APP_TYPE.onePageSlides && 'SlideGenius'
    || app === APP_TYPE.poetic && 'Poetic Lens'
    || app === APP_TYPE.bloom && `Bloom's Taxonomy`
    || app === APP_TYPE.solo && 'SOLO Taxonomy'
    || app === APP_TYPE.dok && `Webb's DOK`
    || app === APP_TYPE.marzano && `Marzano's Taxonomy`
    || app === APP_TYPE.feynman && 'Feynman Tutor'
    || app === APP_TYPE.reading && 'Reading Map'
    || app === APP_TYPE.movie && 'CineMap'
    || app === APP_TYPE.photo && 'Photo Coach'
    || app === APP_TYPE.decision && 'Decision Analyst'
    || app === APP_TYPE.criticalThinking && 'Critical Analysis'
    || app === APP_TYPE.refineQuestion && 'QuestionCraft AI'
    || app === APP_TYPE.bias && 'LogicLens'
    || app === APP_TYPE.youtube && 'Youtube Summarizer'
    || app === APP_TYPE.planner && 'Task Planner'
    || app === APP_TYPE.startupmentor && 'Startup Mentor'
    || app === APP_TYPE.businessmodel && 'Business Model Consultant'
    || app === APP_TYPE.okr && 'OKR Assistant'
    || app === APP_TYPE.erase && 'Watermarks Eraser'
    || app === APP_TYPE.avatar && 'Avatar Studio'
    || app === APP_TYPE.imageEditor && 'AI Image Editor'
    || app === APP_TYPE.insightcards && 'InsightCards'
    || app === APP_TYPE.layeredExplanation && 'MindLadder'
    || app === APP_TYPE.lessonPlanner && 'Lesson Plans'
    || app === APP_TYPE.teachingSlides && 'Teaching Slides'
    || app === APP_TYPE.dokAssessment && 'DOK Assessment'
    || app.charAt(0).toUpperCase() + app.slice(1)
}

export const getAppValue = (app) => {
    const lowerApp = app?.toLowerCase();
    return Object.values(APP_TYPE).find(value => value.toLowerCase() === lowerApp) || null;
}