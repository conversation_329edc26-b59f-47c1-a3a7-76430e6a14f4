import React, { useEffect, useMemo, useRef, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { Box, Heading, Text, Button, Image, Modal, ModalOverlay, ModalContent, ModalBody, useDisclosure, IconButton, VStack, HStack, Container, List, ListItem, ListIcon, useToast, Spinner, useBreakpointValue, SimpleGrid, Link, useMediaQuery } from '@chakra-ui/react';
import { CheckCircleIcon, CheckIcon, CloseIcon } from '@chakra-ui/icons';
import { apiUrl } from '../../utils/apiUtils';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import getConfig from 'next/config';
import { preprocessSvg, getSvgDimensions } from '../../utils/svgUtils';
import ArtifactDisplay from '../../components/CardGenerator/ArtifactDisplay';
import { QRCodeCanvas } from 'qrcode.react';
import { ShareModalProvider, useShareModal } from '../../contexts/ShareModalContext';
import ShareModal from '../../components/CardGenerator/ShareModal';
import FlowEditor from '../../components/Flow/FlowEditor';
import { APP_TYPE, getAppname, getAppValue } from '../../utils/constants';
import { handleDownload, handleShare } from '../../utils/imageUtils';
import { aiTools } from '../../components/Home/Main';

const Tools = ({ app, artifact, svgRef }) => {
    const { t } = useTranslation('common');
    const toast = useToast();
    const { openShareModal } = useShareModal();
    const [isLoading, setIsLoading] = useState();

    return <HStack spacing={4}>
        <Button size={'sm'} colorScheme='whiteAlpha' variant='link'
            // onClick={() => handleDownload(svgRef, toast)}
            onClick={async () => {
                setIsLoading('download'); // 开始加载
                try {
                    await handleDownload(svgRef, toast);
                } finally {
                    setIsLoading(null); // 结束加载
                }
            }}
        >
            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: 6 }}>
                {t('download')} {isLoading === 'download' && <Spinner size="sm" />}
            </div>
        </Button>
        <Button size={'sm'} colorScheme="blue"
            onClick={async () => {
                setIsLoading('share'); // 开始加载
                try {
                    await handleShare(app, artifact?._id, artifact?.title, svgRef, openShareModal, toast);
                } finally {
                    setIsLoading(null); // 结束加载
                }
            }}
        >
            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: 6 }}>
                {t('share')} {isLoading === 'share' && <Spinner size="sm" />}
            </div>
        </Button>
    </HStack>
}

const Features = ({ app, appTranslationFile }) => {
    const { t } = useTranslation(appTranslationFile);

    return <List spacing={3} textAlign="left">
        {
            ([APP_TYPE.mindmap, APP_TYPE.brainstorming, APP_TYPE.decision, APP_TYPE.graphics, APP_TYPE.infographic, APP_TYPE.dreamlens, APP_TYPE.art, APP_TYPE.photo, APP_TYPE.reading, APP_TYPE.startupmentor, APP_TYPE.businessmodel, APP_TYPE.okr, APP_TYPE.poetic, APP_TYPE.horoscope, APP_TYPE.movie, APP_TYPE.feynman, APP_TYPE.bloom, APP_TYPE.solo, APP_TYPE.dok, APP_TYPE.marzano, APP_TYPE.erase, APP_TYPE.avatar, APP_TYPE.imageEditor, APP_TYPE.layeredExplanation, APP_TYPE.promptOptimizer, APP_TYPE.lessonPlanner, APP_TYPE.dokAssessment].includes(app) ? [1, 2, 3] : [1, 2, 3, 4]).map(i => {
                return <ListItem key={i + ''}>
                    <ListIcon as={CheckIcon} color="green.300" />
                    {
                        t(
                            [APP_TYPE.graphics, APP_TYPE.infographic].includes(app) && ('graphics_feature_' + i + '_text')
                            ||app === APP_TYPE.onePageSlides && ('one_slides_pro_benefit_' + i)
                            ||app === APP_TYPE.promptOptimizer && ('prompt_feature_' + i + '_text')
                            ||app === APP_TYPE.lessonPlanner && ('lesson_plans_feature_' + i + '_text')
                            ||app === APP_TYPE.teachingSlides && ('teaching_slides_feature_' + i + '_text')
                            ||app === APP_TYPE.dokAssessment && ('dok_assessment_feature_' + i + '_text')
                            ||(appTranslationFile + '_feature_' + i + '_text')
                        )

                    }
                </ListItem>
            })
        }
    </List>
}

const SharePage = ({ cardData, artifact, app, appTranslationFile }) => {
    const router = useRouter();
    const { t } = useTranslation('common');

    const { basePath } = getConfig().publicRuntimeConfig;
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [imageUrl, setImageUrl] = useState('');
    const svgRef = useRef();

    const [isMobile] = useMediaQuery("(max-width: 768px)")
    const shareUrl = typeof window !== 'undefined' ? window.location.href : '';

    const tools = useMemo(() => [].concat(...Object.values(aiTools)), [])

    useEffect(() => {
        if (artifact?.type != 'svg') return;

        const blob = new Blob([preprocessSvg(artifact.content)], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        setImageUrl(url);
    }, [artifact])

    console.log('artifact..............', artifact)

    return (
        <ShareModalProvider>
            <Head>
                <title key="title">{cardData.title}</title>
                <meta name="keywords" content={cardData.keywords || t('meta.keywords')} />
                <meta name="description" content={cardData.description} key="description" />
                <meta property="og:title" content={cardData.title} key="og:title" />
                <meta property="og:description" content={cardData.description} key="og:description" />
                <meta property="og:image" content={cardData.imageUrl} key="og:image" />
                <meta property="og:url" content={cardData.shareUrl} key="og:url" />

                {/* Twitter 卡片标签 */}
                <meta name="twitter:card" content="summary_large_image" key="twitter:card" />
                <meta name="twitter:title" content={cardData.title} key="twitter:title" />
                <meta name="twitter:description" content={cardData.description} key="twitter:description" />
                <meta name="twitter:image" content={cardData.imageUrl} key="twitter:image" />
            </Head>

            <Box
                minHeight="100vh"
                bgGradient="linear(to-b, purple.500, blue.500)"
                color="white"
                py={6}
            >
                <Container maxW="container.xl" pt={{ base: 4, md: 10 }}>
                    <VStack spacing={4} align="center">
                        <Heading as="h1" size={{ base: "lg", md: "2xl" }} textAlign="center" mb={2}>
                            {t('app_slogan_' + app.replaceAll('-', '').toLowerCase())}
                        </Heading>
                        <Text fontSize={{ base: "lg", md: "xl" }} fontWeight={'bold'} textAlign="center" mb={2}>
                            {t('app_one_sentence_' + app.replaceAll('-', '').toLowerCase())}
                        </Text>
                        {
                            artifact?.type === 'flow' &&
                            <Box
                                style={{
                                    width: '100%',
                                    maxWidth: 1040,
                                    height: isMobile ? 300 : 680,
                                    color: 'black',
                                }}
                                ref={svgRef}
                                align='left'
                            >
                                <FlowEditor
                                    app={app}
                                    mode={artifact.mode}
                                    doc={artifact}
                                />
                            </Box>
                        }
                        {
                            artifact?.type === 'slides' && artifact.hid &&
                            <div
                                ref={svgRef}
                                style={{
                                    width: isMobile ? '100%' : 960,
                                    height: isMobile ? 300 : 540
                                }}
                            >
                                <iframe
                                    className='nodrag'
                                    id='slides-frame'
                                    style={{
                                        width: '100%',
                                        height: '100%'
                                    }}
                                    // ref={iframeRef}
                                    src={`https://service.funblocks.net/view.html?theme=sky&hid=${artifact.hid}`}
                                    title="FunBlocks AI Slides"
                                />
                            </div>
                        }
                        {
                            artifact && (['markdown', 'svg', 'mermaid'].includes(artifact.type) || [APP_TYPE.poetic, APP_TYPE.graphics, APP_TYPE.onePageSlides, APP_TYPE.infographic, APP_TYPE.mindsnap, APP_TYPE.insightcards, APP_TYPE.promptOptimizer].includes(app)) &&
                            <ArtifactDisplay
                                app={app}
                                artifact={artifact}
                                svgRef={svgRef}
                                showDownload={true}
                                showShare={true}
                            />
                        }

                        {artifact && <Tools app={app} svgRef={svgRef} artifact={artifact} />}

                        <VStack spacing={2} mt={2}>
                            <Button borderRadius={'full'} colorScheme="teal" size="lg" onClick={() => window.open(basePath + '/' + (app?.toLowerCase() || ''), '_blank')} px={8}>
                                {t('find_more_cards_or_create_your_own')}
                            </Button>
                        </VStack>

                        <Box maxW="680px" textAlign="center" mt={4} mb={8}>
                            <Heading as="h2" size="lg" mb={4}>
                                {t('why_funblocks_ai', { app: getAppname(app) })}
                            </Heading>
                            <Features
                                app={app}
                                appTranslationFile={appTranslationFile}
                            />
                        </Box>

                        <Box maxW="800px" textAlign="center" mt={4} mb={8}>
                            <Heading as="h2" size="md" mb={4}>
                                FunBlocks AI Tools
                            </Heading>

                            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={2} w="full">
                                {
                                    tools.map((tool) => {
                                        return <Link href={basePath + tool.link} isExternal style={{ color: 'white' }}>{tool.title}</Link>
                                    })
                                }
                            </SimpleGrid>
                        </Box>
                    </VStack>
                </Container>

                <Modal isOpen={isOpen} onClose={onClose} size="full">
                    <ModalOverlay />
                    <ModalContent bg="rgba(0,0,0,0.8)">
                        <ModalBody
                            display="flex"
                            justifyContent="center"
                            alignItems="center"
                            height="100vh"
                            position="relative"
                        >
                            <IconButton
                                icon={<CloseIcon />}
                                position="absolute"
                                right="20px"
                                top="20px"
                                onClick={onClose}
                                aria-label={t('close_modal')}
                                bg="transparent"
                                color="white"
                                _hover={{ bg: 'rgba(255,255,255,0.2)' }}
                            />
                            <Image
                                src={imageUrl}
                                alt="Shared SVG Full Screen"
                                objectFit="contain"
                                maxHeight="90vh"
                                maxWidth="90vw"
                                onClick={(e) => e.stopPropagation()}
                                cursor="default"
                            />
                        </ModalBody>
                    </ModalContent>
                </Modal>
                <ShareModal app={app} />
                <div style={{ display: 'none' }}>
                    <QRCodeCanvas
                        value={`https://www.funblocks.net${basePath + '/' + (app?.toLowerCase() || '')}`}
                        size={100}

                        id="qrcode-canvas"
                    />
                </div>
            </Box>
        </ShareModalProvider>
    );
};

export async function getServerSideProps({ params, locale, resolvedUrl }) {
    const { slug } = params;
    const app = getAppValue(slug[0]);
    const id = slug[1];

    const shareUrl = resolvedUrl;
    const metaInfo = app === APP_TYPE.graphics ? {
        title: 'FunBlocks AI Graphics - Fun, Informative, Extraordinary graphics with AI',
        description: 'Generate insightful and fun infographic cards, easily gain new perspectives and stand out on social platforms.',
        imageUrl: 'https://www.funblocks.net/assets/img/portfolio/fullsize/ai_insights.png',
    } : {
        title: 'FunBlocks AI Mindmap - AI generating mind map for books, movies, videos, web pages and more',
        description: 'AI-powered whiteboard & mind mapping tool. Boost creativity by 10x with GPT-4 & Claude LLM. Explore ideas, solve problems & learn faster. Free trial available!',
        imageUrl: 'https://www.funblocks.net/assets/img/portfolio/fullsize/aiflow_benefits.png',
        keywords: 'LLM, ChatGPT, AI, Mind map, Brainstorming, AI Tools, FunBlocks AI, AI Graphics, AI Infographics, AI Mindmap, Mind Mapping, Visual Learning, Knowledge Management, AI-Guided Exploration, One-Click Generation, Study Research, Brainstorming, ChatGPT Alternative, Claude Alternative, Content Summarization, Video Summarization, Web Summarization, Document Summarization, AI Learning Tools',
    };

    metaInfo.shareUrl = `https://www.funblocks.net/aitools/${app?.toLowerCase() || 'mindmap'}`

    const appTranslationFile = (!app || app === 'undefined') && 'mindmap'
        || app === APP_TYPE.art && 'artinsight'
        || app === APP_TYPE.brainstorming && 'brainstorm'
        || app === APP_TYPE.businessmodel && 'bma'
        || app === APP_TYPE.movie && 'cinemap'
        || app === APP_TYPE.bias && 'logic'
        || app === APP_TYPE.onePageSlides && 'oneslide'
        || app === APP_TYPE.refineQuestion && 'question'
        || app === APP_TYPE.startupmentor && 'startup'
        || app === APP_TYPE.youtube && 'summarizer'
        || app === APP_TYPE.layeredExplanation && 'layered'
        || app === APP_TYPE.promptOptimizer && 'prompt-optimizer'
        || app === APP_TYPE.imageEditor && 'image-editor'
        || [APP_TYPE.graphics, APP_TYPE.infographic].includes(app) && 'common'
        || app.toLowerCase();

    try {
        const response = await fetch(`${apiUrl}/ai/artifact?_id=${id?.trim()}&app=${app}`);

        const data = await response.json();

        const cardData = data?.data ? {
            ...metaInfo,
            title: data.data.title || `${data.data.promptLabel} - ${data.data.userInput}`,
            description: data.data?.type === 'markdown' && data.data.content
                || data.data.description
                || data.data.type === 'flow' && `AI generated Mind map with FunBlocks AIFlow, topic: ${data.data.title}`
                || '',
            imageUrl: data.data?.type === 'svg' && `${apiUrl}/ai/svg2png/${id}` || data.data.imageUrl || '',
            shareUrl: shareUrl,
        } : metaInfo;

        return {
            props: {
                ...(await serverSideTranslations(locale, ['common', appTranslationFile])),
                cardData,
                artifact: data.data,
                // 添加这些 props 以在 _app.js 中使用
                metaTitle: cardData.title,
                metaDescription: cardData.description,
                metaImage: cardData.imageUrl,
                metaUrl: cardData.shareUrl,
                app,
                appTranslationFile
            },
        };
    } catch (error) {
        console.error('Error fetching card data:', error);
        return {
            props: {
                ...(await serverSideTranslations(locale, ['common'])),
                cardData: metaInfo,
                // 添加默认的 meta 数据
                metaTitle: metaInfo.title,
                metaDescription: metaInfo.description,
                metaImage: metaInfo.imageUrl,
                metaUrl: metaInfo.shareUrl,
                app,
                appTranslationFile
            },
        };
    }
}

export default SharePage;
