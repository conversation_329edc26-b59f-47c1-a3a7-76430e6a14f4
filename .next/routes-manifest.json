{"version": 3, "pages404": true, "basePath": "/aitools", "redirects": [{"source": "/aitools/", "destination": "/aitools", "basePath": false, "locale": false, "internal": true, "statusCode": 308, "regex": "^/aitools/$"}, {"source": "/:path+/", "destination": "/:path+", "locale": false, "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/collections/[...slug]", "regex": "^/collections/(.+?)(?:/)?$", "routeKeys": {"slug": "slug"}, "namedRegex": "^/collections/(?<slug>.+?)(?:/)?$"}, {"page": "/mindkit/[mental_model]", "regex": "^/mindkit/([^/]+?)(?:/)?$", "routeKeys": {"mental_model": "mental_model"}, "namedRegex": "^/mindkit/(?<mental_model>[^/]+?)(?:/)?$"}, {"page": "/mindsnap/[mental_model]", "regex": "^/mindsnap/([^/]+?)(?:/)?$", "routeKeys": {"mental_model": "mental_model"}, "namedRegex": "^/mindsnap/(?<mental_model>[^/]+?)(?:/)?$"}, {"page": "/share/[...slug]", "regex": "^/share/(.+?)(?:/)?$", "routeKeys": {"slug": "slug"}, "namedRegex": "^/share/(?<slug>.+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/art", "regex": "^/art(?:/)?$", "routeKeys": {}, "namedRegex": "^/art(?:/)?$"}, {"page": "/avatar", "regex": "^/avatar(?:/)?$", "routeKeys": {}, "namedRegex": "^/avatar(?:/)?$"}, {"page": "/bias", "regex": "^/bias(?:/)?$", "routeKeys": {}, "namedRegex": "^/bias(?:/)?$"}, {"page": "/bloom", "regex": "^/bloom(?:/)?$", "routeKeys": {}, "namedRegex": "^/bloom(?:/)?$"}, {"page": "/brainstorming", "regex": "^/brainstorming(?:/)?$", "routeKeys": {}, "namedRegex": "^/brainstorming(?:/)?$"}, {"page": "/businessmodel", "regex": "^/businessmodel(?:/)?$", "routeKeys": {}, "namedRegex": "^/businessmodel(?:/)?$"}, {"page": "/counselor", "regex": "^/counselor(?:/)?$", "routeKeys": {}, "namedRegex": "^/counselor(?:/)?$"}, {"page": "/critical-thinking", "regex": "^/critical\\-thinking(?:/)?$", "routeKeys": {}, "namedRegex": "^/critical\\-thinking(?:/)?$"}, {"page": "/decision", "regex": "^/decision(?:/)?$", "routeKeys": {}, "namedRegex": "^/decision(?:/)?$"}, {"page": "/dok", "regex": "^/dok(?:/)?$", "routeKeys": {}, "namedRegex": "^/dok(?:/)?$"}, {"page": "/dok-assessment", "regex": "^/dok\\-assessment(?:/)?$", "routeKeys": {}, "namedRegex": "^/dok\\-assessment(?:/)?$"}, {"page": "/dreamlens", "regex": "^/dreamlens(?:/)?$", "routeKeys": {}, "namedRegex": "^/dreamlens(?:/)?$"}, {"page": "/erase", "regex": "^/erase(?:/)?$", "routeKeys": {}, "namedRegex": "^/erase(?:/)?$"}, {"page": "/feynman", "regex": "^/feynman(?:/)?$", "routeKeys": {}, "namedRegex": "^/feynman(?:/)?$"}, {"page": "/graphics", "regex": "^/graphics(?:/)?$", "routeKeys": {}, "namedRegex": "^/graphics(?:/)?$"}, {"page": "/horoscope", "regex": "^/horoscope(?:/)?$", "routeKeys": {}, "namedRegex": "^/horoscope(?:/)?$"}, {"page": "/image-editor", "regex": "^/image\\-editor(?:/)?$", "routeKeys": {}, "namedRegex": "^/image\\-editor(?:/)?$"}, {"page": "/infographic", "regex": "^/infographic(?:/)?$", "routeKeys": {}, "namedRegex": "^/infographic(?:/)?$"}, {"page": "/insightcards", "regex": "^/insightcards(?:/)?$", "routeKeys": {}, "namedRegex": "^/insightcards(?:/)?$"}, {"page": "/layered-explanation", "regex": "^/layered\\-explanation(?:/)?$", "routeKeys": {}, "namedRegex": "^/layered\\-explanation(?:/)?$"}, {"page": "/lesson-plans", "regex": "^/lesson\\-plans(?:/)?$", "routeKeys": {}, "namedRegex": "^/lesson\\-plans(?:/)?$"}, {"page": "/marzano", "regex": "^/marzano(?:/)?$", "routeKeys": {}, "namedRegex": "^/marzano(?:/)?$"}, {"page": "/mindkit", "regex": "^/mindkit(?:/)?$", "routeKeys": {}, "namedRegex": "^/mindkit(?:/)?$"}, {"page": "/mindmap", "regex": "^/mindmap(?:/)?$", "routeKeys": {}, "namedRegex": "^/mindmap(?:/)?$"}, {"page": "/mindsnap", "regex": "^/mindsnap(?:/)?$", "routeKeys": {}, "namedRegex": "^/mindsnap(?:/)?$"}, {"page": "/movie", "regex": "^/movie(?:/)?$", "routeKeys": {}, "namedRegex": "^/movie(?:/)?$"}, {"page": "/my", "regex": "^/my(?:/)?$", "routeKeys": {}, "namedRegex": "^/my(?:/)?$"}, {"page": "/okr", "regex": "^/okr(?:/)?$", "routeKeys": {}, "namedRegex": "^/okr(?:/)?$"}, {"page": "/one-page-slide", "regex": "^/one\\-page\\-slide(?:/)?$", "routeKeys": {}, "namedRegex": "^/one\\-page\\-slide(?:/)?$"}, {"page": "/photo", "regex": "^/photo(?:/)?$", "routeKeys": {}, "namedRegex": "^/photo(?:/)?$"}, {"page": "/planner", "regex": "^/planner(?:/)?$", "routeKeys": {}, "namedRegex": "^/planner(?:/)?$"}, {"page": "/poetic", "regex": "^/poetic(?:/)?$", "routeKeys": {}, "namedRegex": "^/poetic(?:/)?$"}, {"page": "/prompt-optimizer", "regex": "^/prompt\\-optimizer(?:/)?$", "routeKeys": {}, "namedRegex": "^/prompt\\-optimizer(?:/)?$"}, {"page": "/reading", "regex": "^/reading(?:/)?$", "routeKeys": {}, "namedRegex": "^/reading(?:/)?$"}, {"page": "/refine-question", "regex": "^/refine\\-question(?:/)?$", "routeKeys": {}, "namedRegex": "^/refine\\-question(?:/)?$"}, {"page": "/reflection", "regex": "^/reflection(?:/)?$", "routeKeys": {}, "namedRegex": "^/reflection(?:/)?$"}, {"page": "/slides", "regex": "^/slides(?:/)?$", "routeKeys": {}, "namedRegex": "^/slides(?:/)?$"}, {"page": "/solo", "regex": "^/solo(?:/)?$", "routeKeys": {}, "namedRegex": "^/solo(?:/)?$"}, {"page": "/startupmentor", "regex": "^/startupmentor(?:/)?$", "routeKeys": {}, "namedRegex": "^/startupmentor(?:/)?$"}, {"page": "/teaching-slides", "regex": "^/teaching\\-slides(?:/)?$", "routeKeys": {}, "namedRegex": "^/teaching\\-slides(?:/)?$"}, {"page": "/test-mental-model", "regex": "^/test\\-mental\\-model(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-mental\\-model(?:/)?$"}, {"page": "/youtube", "regex": "^/youtube(?:/)?$", "routeKeys": {}, "namedRegex": "^/youtube(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/index.json$"}, {"page": "/art", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/art.json$"}, {"page": "/avatar", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/avatar.json$"}, {"page": "/bias", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/bias.json$"}, {"page": "/bloom", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/bloom.json$"}, {"page": "/brainstorming", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/brainstorming.json$"}, {"page": "/businessmodel", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/businessmodel.json$"}, {"page": "/collections/[...slug]", "routeKeys": {"slug": "slug"}, "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/collections/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/collections/(?<slug>.+?)\\.json$"}, {"page": "/counselor", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/counselor.json$"}, {"page": "/critical-thinking", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/critical-thinking.json$"}, {"page": "/decision", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/decision.json$"}, {"page": "/dok", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/dok.json$"}, {"page": "/dok-assessment", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/dok-assessment.json$"}, {"page": "/dreamlens", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/dreamlens.json$"}, {"page": "/erase", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/erase.json$"}, {"page": "/feynman", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/feynman.json$"}, {"page": "/graphics", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/graphics.json$"}, {"page": "/horoscope", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/horoscope.json$"}, {"page": "/image-editor", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/image-editor.json$"}, {"page": "/infographic", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/infographic.json$"}, {"page": "/insightcards", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/insightcards.json$"}, {"page": "/layered-explanation", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/layered-explanation.json$"}, {"page": "/lesson-plans", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/lesson-plans.json$"}, {"page": "/marzano", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/marzano.json$"}, {"page": "/mindkit", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/mindkit.json$"}, {"page": "/mindkit/[mental_model]", "routeKeys": {"mental_model": "mental_model"}, "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/mindkit/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/mindkit/(?<mental_model>[^/]+?)\\.json$"}, {"page": "/mindmap", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/mindmap.json$"}, {"page": "/mindsnap", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/mindsnap.json$"}, {"page": "/mindsnap/[mental_model]", "routeKeys": {"mental_model": "mental_model"}, "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/mindsnap/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/mindsnap/(?<mental_model>[^/]+?)\\.json$"}, {"page": "/movie", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/movie.json$"}, {"page": "/my", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/my.json$"}, {"page": "/okr", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/okr.json$"}, {"page": "/one-page-slide", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/one-page-slide.json$"}, {"page": "/photo", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/photo.json$"}, {"page": "/planner", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/planner.json$"}, {"page": "/poetic", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/poetic.json$"}, {"page": "/prompt-optimizer", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/prompt-optimizer.json$"}, {"page": "/reading", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/reading.json$"}, {"page": "/refine-question", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/refine-question.json$"}, {"page": "/reflection", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/reflection.json$"}, {"page": "/share/[...slug]", "routeKeys": {"slug": "slug"}, "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/share/(.+?)\\.json$", "namedDataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/share/(?<slug>.+?)\\.json$"}, {"page": "/slides", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/slides.json$"}, {"page": "/solo", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/solo.json$"}, {"page": "/startupmentor", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/startupmentor.json$"}, {"page": "/teaching-slides", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/teaching-slides.json$"}, {"page": "/test-mental-model", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/test-mental-model.json$"}, {"page": "/youtube", "dataRouteRegex": "^/_next/data/mSMT0TsTKqNsS70kpoSdH/youtube.json$"}], "i18n": {"defaultLocale": "en", "locales": ["en", "zh"]}, "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}