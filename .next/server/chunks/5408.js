exports.id = 5408;
exports.ids = [5408];
exports.modules = {

/***/ 3870:
/***/ ((module) => {

// Exports
module.exports = {
	"footer": "Footer_footer__GeF4n",
	"footerContainer": "Footer_footerContainer__SNoPe",
	"footerLogo": "Footer_footerLogo__1_fbO",
	"footerLinks": "Footer_footerLinks__ylCiD",
	"toolsGrid": "Footer_toolsGrid__YVg3f",
	"copyright": "Footer_copyright__fqH5S"
};


/***/ }),

/***/ 980:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2210);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1301);
/* harmony import */ var _Section__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1306);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_4__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);





const CaseStudyCard = ({ icon , title , industry , challenge , solution , results , imageSrc , onClick  })=>{
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
        bg: "white",
        rounded: "xl",
        shadow: "md",
        borderWidth: "1px",
        overflow: "hidden",
        h: "100%",
        transition: "transform 0.3s, box-shadow 0.3s",
        _hover: {
            transform: "translateY(-5px)",
            shadow: "lg"
        },
        display: "flex",
        flexDirection: "column",
        role: "group",
        touchAction: "manipulation",
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                p: {
                    base: 4,
                    md: 5
                },
                alignItems: "center",
                borderBottomWidth: "1px",
                borderBottomColor: "gray.100",
                bg: "blue.50",
                flexWrap: "wrap",
                gap: {
                    base: 2,
                    md: 0
                },
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                        as: icon,
                        w: {
                            base: 5,
                            md: 6
                        },
                        h: {
                            base: 5,
                            md: 6
                        },
                        color: "blue.500",
                        mr: 3,
                        flexShrink: 0,
                        _groupHover: {
                            transform: "scale(1.1)"
                        },
                        transition: "transform 0.2s"
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                size: {
                                    base: "sm",
                                    md: "md"
                                },
                                lineHeight: "shorter",
                                children: title
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {
                                colorScheme: "purple",
                                mt: 1,
                                fontSize: {
                                    base: "xs",
                                    md: "sm"
                                },
                                px: {
                                    base: 1.5,
                                    md: 2
                                },
                                py: {
                                    base: 0.5,
                                    md: 1
                                },
                                children: industry
                            })
                        ]
                    })
                ]
            }),
            imageSrc && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Image, {
                src: imageSrc,
                alt: title,
                height: {
                    base: "160px",
                    sm: "180px",
                    md: "240px",
                    lg: "360px"
                },
                width: "100%",
                objectFit: "contain",
                loading: "lazy"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                p: {
                    base: 4,
                    md: 5
                },
                flex: "1",
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                    align: "start",
                    spacing: {
                        base: 3,
                        md: 4
                    },
                    children: [
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                    fontWeight: "bold",
                                    color: "red.500",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    children: "Challenge:"
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                    color: "gray.600",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    lineHeight: "1.6",
                                    children: challenge
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                    fontWeight: "bold",
                                    color: "blue.500",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    children: "Solution:"
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                    color: "gray.600",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    lineHeight: "1.6",
                                    children: solution
                                })
                            ]
                        }),
                        results && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                    fontWeight: "bold",
                                    color: "green.500",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    children: "Results:"
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                    color: "gray.600",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    lineHeight: "1.6",
                                    children: results
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                p: {
                    base: 3,
                    md: 4
                },
                borderTopWidth: "1px",
                borderTopColor: "gray.100",
                bg: "gray.50",
                _groupHover: {
                    bg: "blue.50"
                },
                transition: "background 0.2s",
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                    justify: "flex-end",
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                        rightIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {}),
                        colorScheme: "blue",
                        variant: "ghost",
                        size: {
                            base: "sm",
                            md: "md"
                        },
                        onClick: onClick ? onClick : ()=>window.scrollTo({
                                top: 0,
                                behavior: "smooth"
                            }),
                        height: {
                            base: "36px",
                            md: "40px"
                        },
                        _groupHover: {
                            bg: "blue.100",
                            transform: "translateX(4px)"
                        },
                        transition: "all 0.2s",
                        children: "Try it yourself"
                    })
                })
            })
        ]
    });
};
const CaseStudies = ({ caseStudies , description , appname , onClick  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)("common");
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_Section__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
        bg: "gray.50",
        id: "case-studies",
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
            spacing: {
                base: 6,
                md: 8
            },
            width: "100%",
            px: {
                base: 3,
                md: 0
            },
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {
                    colorScheme: "green",
                    fontSize: {
                        base: "sm",
                        md: "md"
                    },
                    px: {
                        base: 2,
                        md: 3
                    },
                    py: {
                        base: 0.5,
                        md: 1
                    },
                    borderRadius: "full",
                    children: t("case_studies_badge", "Real-World Applications")
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                    size: {
                        base: "md",
                        md: "lg"
                    },
                    textAlign: "center",
                    bgGradient: "linear(to-r, blue.400, green.400)",
                    bgClip: "text",
                    px: {
                        base: 2,
                        md: 0
                    },
                    lineHeight: {
                        base: "1.3",
                        md: "1.2"
                    },
                    children: t("case_studies_title", "Success Stories: {appname} in Action", {
                        appname
                    })
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                    fontSize: {
                        base: "sm",
                        md: "lg"
                    },
                    color: "gray.600",
                    textAlign: "center",
                    maxW: "2xl",
                    mb: {
                        base: 2,
                        md: 4
                    },
                    px: {
                        base: 2,
                        md: 0
                    },
                    lineHeight: "1.6",
                    children: description
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                    columns: {
                        base: 1,
                        md: 2
                    },
                    spacing: {
                        base: 5,
                        md: 8
                    },
                    width: "100%",
                    children: caseStudies.map((study, index)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(CaseStudyCard, {
                            icon: study.icon,
                            title: study.title,
                            industry: study.industry,
                            challenge: study.challenge,
                            solution: study.solution,
                            results: study.results,
                            imageSrc: study.imageSrc,
                            onClick: onClick
                        }, index))
                }),
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                    textAlign: "center",
                    mt: {
                        base: 4,
                        md: 6
                    },
                    width: "100%",
                    px: {
                        base: 2,
                        md: 0
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                            fontSize: {
                                base: "md",
                                md: "lg"
                            },
                            fontWeight: "medium",
                            mb: {
                                base: 3,
                                md: 4
                            },
                            color: "gray.700",
                            children: t("case_studies_cta_text", "Ready to create your own success story?")
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                            colorScheme: "blue",
                            size: {
                                base: "md",
                                md: "lg"
                            },
                            rightIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {}),
                            onClick: onClick ? onClick : ()=>window.scrollTo({
                                    top: 0,
                                    behavior: "smooth"
                                }),
                            px: {
                                base: 5,
                                md: 8
                            },
                            py: {
                                base: 6,
                                md: 6
                            },
                            height: {
                                base: "48px",
                                md: "auto"
                            },
                            borderRadius: "full",
                            _hover: {
                                transform: "translateY(-2px)"
                            },
                            transition: "all 0.3s",
                            children: t("case_studies_cta_button", "Start Creating Now")
                        })
                    ]
                })
            ]
        })
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CaseStudies);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 7157:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1301);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _Section__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1306);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_5__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);






/**
 * Reusable ComparisonTable component
 * @param {Object} props - Component props
 * @param {string} props.namespace - Translation namespace (default: 'common')
 * @param {string} props.titleKey - Translation key for title (default: 'comparison_title')
 * @param {string} props.descriptionKey - Translation key for description (default: 'comparison_description')
 * @param {Array} props.features - Array of feature objects (optional, will use default if not provided)
 * @param {Array} props.columns - Array of column objects (optional, will use default if not provided)
 * @param {string} props.bg - Background color (default: 'white')
 * @param {string} props.id - Section ID for anchor links
 * @param {string} props.highlightColumn - Key of the column to highlight (default: first column)
 * @param {string} props.productName - Product name to use in default description (default: 'our product')
 */ const ComparisonTable = ({ namespace ="common" , title ="comparison_title" , description ="comparison_description" , features: customFeatures , columns: customColumns , bg ="white" , id , highlightColumn , productName ="our product"  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(namespace);
    const headerBg = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("purple.100", "purple.900");
    const highlightBg = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("purple.50", "purple.800");
    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.200", "gray.700");
    // Default columns if none provided
    const defaultColumns = [
        {
            key: "product",
            label: t("comparison_product", productName),
            highlight: true
        },
        {
            key: "competitor1",
            label: t("comparison_competitor1", "Competitor A")
        },
        {
            key: "competitor2",
            label: t("comparison_competitor2", "Competitor B")
        },
        {
            key: "competitor3",
            label: t("comparison_competitor3", "Competitor C")
        }
    ];
    // Default features if none provided
    const defaultFeatures = [
        {
            name: t("comparison_feature_1", "AI-Generated Content"),
            tooltip: t("comparison_feature_1_tooltip", "Content is automatically generated by artificial intelligence"),
            mindladder: true,
            traditional: false,
            mindmap: false,
            online: true
        },
        {
            name: t("comparison_feature_2", "Progressive Complexity Layers"),
            tooltip: t("comparison_feature_2_tooltip", "Content is organized in increasing levels of complexity"),
            mindladder: true,
            traditional: false,
            mindmap: false,
            online: false
        },
        {
            name: t("comparison_feature_3", "Interactive Exploration"),
            tooltip: t("comparison_feature_3_tooltip", "Users can click to explore concepts in more detail"),
            mindladder: true,
            traditional: false,
            mindmap: true,
            online: false
        },
        {
            name: t("comparison_feature_4", "Visual Knowledge Mapping"),
            tooltip: t("comparison_feature_4_tooltip", "Information is presented in visual, connected format"),
            mindladder: true,
            traditional: false,
            mindmap: true,
            online: false
        },
        {
            name: t("comparison_feature_5", "Personalized Learning Paths"),
            tooltip: t("comparison_feature_5_tooltip", "Content adapts based on user interests and interactions"),
            mindladder: true,
            traditional: false,
            mindmap: false,
            online: true
        },
        {
            name: t("comparison_feature_6", "Multiple Perspectives"),
            tooltip: t("comparison_feature_6_tooltip", "Presents different viewpoints on the same topic"),
            mindladder: true,
            traditional: false,
            mindmap: false,
            online: false
        },
        {
            name: t("comparison_feature_7", "Counterintuitive Insights"),
            tooltip: t("comparison_feature_7_tooltip", "Highlights surprising or paradoxical aspects of concepts"),
            mindladder: true,
            traditional: false,
            mindmap: false,
            online: false
        },
        {
            name: t("comparison_feature_8", "Expert-Level Content"),
            tooltip: t("comparison_feature_8_tooltip", "Provides advanced, cutting-edge information"),
            mindladder: true,
            traditional: true,
            mindmap: false,
            online: true
        },
        {
            name: t("comparison_feature_9", "Cross-Concept Connections"),
            tooltip: t("comparison_feature_9_tooltip", "Shows relationships between different concepts"),
            mindladder: true,
            traditional: false,
            mindmap: true,
            online: false
        },
        {
            name: t("comparison_feature_10", "One-Click Generation"),
            tooltip: t("comparison_feature_10_tooltip", "Creates complete learning resources with a single input"),
            mindladder: true,
            traditional: false,
            mindmap: false,
            online: false
        }
    ];
    // Use provided features/columns or fall back to defaults
    const featuresList = customFeatures || defaultFeatures;
    const columnsList = customColumns || defaultColumns;
    // Determine which column to highlight
    const highlightedColumn = highlightColumn || columnsList.find((col)=>col.highlight)?.key || columnsList[0].key;
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_Section__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
        bg: bg,
        id: id,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Container, {
            maxW: "container.xl",
            px: {
                base: 3,
                md: 6
            },
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
                    spacing: {
                        base: 6,
                        md: 8
                    },
                    mb: {
                        base: 8,
                        md: 10
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {
                            as: "h2",
                            size: {
                                base: "lg",
                                md: "xl"
                            },
                            textAlign: "center",
                            bgGradient: "linear(to-r, blue.400, purple.500)",
                            bgClip: "text",
                            px: {
                                base: 2,
                                md: 0
                            },
                            lineHeight: {
                                base: "1.3",
                                md: "1.2"
                            },
                            children: title
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                            fontSize: {
                                base: "sm",
                                md: "lg"
                            },
                            textAlign: "center",
                            maxW: "3xl",
                            px: {
                                base: 2,
                                md: 0
                            },
                            color: "gray.600",
                            lineHeight: "1.6",
                            children: description
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {
                            gap: {
                                base: 3,
                                md: 4
                            },
                            flexWrap: "wrap",
                            justify: "center",
                            width: "100%",
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {
                                    colorScheme: "purple",
                                    p: {
                                        base: 1.5,
                                        md: 2
                                    },
                                    borderRadius: "md",
                                    fontSize: {
                                        base: "xs",
                                        md: "md"
                                    },
                                    textAlign: "center",
                                    children: t("comparison_winner", "Winner in Key Categories")
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {
                                    colorScheme: "blue",
                                    p: {
                                        base: 1.5,
                                        md: 2
                                    },
                                    borderRadius: "md",
                                    fontSize: {
                                        base: "xs",
                                        md: "md"
                                    },
                                    textAlign: "center",
                                    children: t("comparison_value", "Best Value")
                                })
                            ]
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
                    overflowX: "auto",
                    width: "100%",
                    sx: {
                        WebkitOverflowScrolling: "touch",
                        scrollbarWidth: "thin",
                        "&::-webkit-scrollbar": {
                            width: "6px",
                            height: "6px"
                        },
                        "&::-webkit-scrollbar-thumb": {
                            backgroundColor: "rgba(0,0,0,0.2)",
                            borderRadius: "3px"
                        }
                    },
                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Table, {
                        variant: "simple",
                        size: {
                            base: "sm",
                            md: "md"
                        },
                        borderWidth: "1px",
                        borderColor: borderColor,
                        borderRadius: "lg",
                        style: {
                            minWidth: "650px"
                        },
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Thead, {
                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tr, {
                                    bg: headerBg,
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Th, {
                                            borderBottomWidth: "2px",
                                            fontSize: {
                                                base: "xs",
                                                md: "sm"
                                            },
                                            py: {
                                                base: 3,
                                                md: 4
                                            },
                                            px: {
                                                base: 2,
                                                md: 4
                                            },
                                            children: t("comparison_feature", "Feature")
                                        }),
                                        columnsList.map((column)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Th, {
                                                borderBottomWidth: "2px",
                                                bg: column.key === highlightedColumn ? "purple.100" : undefined,
                                                color: column.key === highlightedColumn ? "purple.800" : undefined,
                                                fontSize: {
                                                    base: "xs",
                                                    md: "sm"
                                                },
                                                py: {
                                                    base: 3,
                                                    md: 4
                                                },
                                                px: {
                                                    base: 2,
                                                    md: 4
                                                },
                                                textAlign: "center",
                                                children: column.label
                                            }, column.key))
                                    ]
                                })
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tbody, {
                                children: featuresList.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tr, {
                                        bg: idx % 2 === 0 ? "white" : "gray.50",
                                        _hover: {
                                            bg: "gray.100"
                                        },
                                        transition: "background 0.2s",
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Td, {
                                                fontWeight: "medium",
                                                py: {
                                                    base: 2,
                                                    md: 3
                                                },
                                                px: {
                                                    base: 2,
                                                    md: 4
                                                },
                                                fontSize: {
                                                    base: "xs",
                                                    md: "sm"
                                                },
                                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                                                    spacing: {
                                                        base: 1,
                                                        md: 2
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                                                            children: feature.name
                                                        }),
                                                        feature.tooltip && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                                            label: feature.tooltip,
                                                            placement: "top",
                                                            fontSize: {
                                                                base: "xs",
                                                                md: "sm"
                                                            },
                                                            hasArrow: true,
                                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                                                    as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaInfoCircle,
                                                                    color: "gray.400",
                                                                    boxSize: {
                                                                        base: 3,
                                                                        md: 4
                                                                    }
                                                                })
                                                            })
                                                        })
                                                    ]
                                                })
                                            }),
                                            columnsList.map((column)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Td, {
                                                    bg: column.key === highlightedColumn ? highlightBg : undefined,
                                                    color: column.key === highlightedColumn ? "purple.800" : undefined,
                                                    py: {
                                                        base: 2,
                                                        md: 3
                                                    },
                                                    px: {
                                                        base: 2,
                                                        md: 4
                                                    },
                                                    textAlign: "center",
                                                    children: feature[column.key] ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCheck,
                                                        color: "green.500",
                                                        boxSize: {
                                                            base: 4,
                                                            md: 5
                                                        }
                                                    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTimes,
                                                        color: "red.500",
                                                        boxSize: {
                                                            base: 4,
                                                            md: 5
                                                        }
                                                    })
                                                }, column.key))
                                        ]
                                    }, idx))
                            })
                        ]
                    })
                })
            ]
        })
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComparisonTable);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 9030:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3870);
/* harmony import */ var _index_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_index_module_css__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _Section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1306);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Section__WEBPACK_IMPORTED_MODULE_1__]);
_Section__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];



function Footer() {
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("footer", {
        className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footer),
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            className: "container",
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerContainer),
                    children: [
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerLinks),
                            style: {
                                marginRight: "20px"
                            },
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                    className: "footer-logo",
                                    children: "FunBlocks"
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                                    "data-i18n": "footer.description",
                                    style: {
                                        color: "#bbb"
                                    },
                                    children: "An AI-powered platform for visualization-enhanced thinking and productivity."
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerLinks),
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                                    "data-i18n": "footer.product",
                                    children: "FunBlocks AI Products"
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", {
                                    style: {
                                        marginLeft: 14
                                    },
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/aiflow",
                                                children: "FunBlocks AI Flow"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/aitools",
                                                children: "FunBlocks AI Tools"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/welcome_extension",
                                                children: "FunBlocks AI Extension"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/slides",
                                                children: "FunBlocks AI Slides"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/aidocs",
                                                children: "FunBlocks AI Docs"
                                            })
                                        })
                                    ]
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerLinks),
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                                    "data-i18n": "footer.resources",
                                    children: "Resources"
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul", {
                                    style: {
                                        marginLeft: 14
                                    },
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/docs",
                                                children: "FunBlocks AI Tutorials"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/blog",
                                                children: "FunBlocks AI Blog"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "https://app.funblocks.net/shares",
                                                children: "FunBlocks AI Generated Content"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "https://www.funblocks.net/aitools/collections/Reading",
                                                children: "Classic Book Mindmaps"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "https://www.funblocks.net/aitools/collections/Movie",
                                                children: "Classic Movie Mindmaps"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/thinking-matters/behind-aiflow",
                                                children: "Thinking Matters"
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                                href: "/thinking-matters/category/classic-mental-models",
                                                children: "Mental Models"
                                            })
                                        })
                                    ]
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerLinks),
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                                    "data-i18n": "footer.company",
                                    children: "Company"
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("ul", {
                                    style: {
                                        marginLeft: 14
                                    },
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                            href: "https://discord.gg/XtdZFBy4uR",
                                            target: "_blank",
                                            children: "Contact Us"
                                        })
                                    })
                                })
                            ]
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerContainer),
                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                        className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerLinks),
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("h4", {
                                "data-i18n": "footer.resources",
                                children: "FunBlocks AI Tools"
                            }),
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().toolsGrid),
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/mindmap",
                                        target: "_blank",
                                        children: "AI Mindmap"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/slides",
                                        target: "_blank",
                                        children: "AI Slides"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/graphics",
                                        target: "_blank",
                                        children: "AI Graphics"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/brainstorming",
                                        target: "_blank",
                                        children: "AI Brainstorming"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/mindkit",
                                        target: "_blank",
                                        children: "AI MindKit"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/youtube",
                                        target: "_blank",
                                        children: "AI Youtube Summarizer"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/critical-thinking",
                                        target: "_blank",
                                        children: "AI Critical Analysis"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/refine-question",
                                        target: "_blank",
                                        children: "AI Question Craft"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/bias",
                                        target: "_blank",
                                        children: "AI LogicLens"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/reflection",
                                        target: "_blank",
                                        children: "AI Reflection"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/decision",
                                        target: "_blank",
                                        children: "AI Decision Analyzer"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/okr",
                                        target: "_blank",
                                        children: "AI OKR Assistant"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/startupmentor",
                                        target: "_blank",
                                        children: "AI Startup Mentor"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/businessmodel",
                                        target: "_blank",
                                        children: "AI Business Model Analyzer"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/planner",
                                        target: "_blank",
                                        children: "AI Task Planner"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/counselor",
                                        target: "_blank",
                                        children: "AI Counselor"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/dreamlens",
                                        target: "_blank",
                                        children: "AI DreamLens"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/horoscope",
                                        target: "_blank",
                                        children: "AI Horoscope"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/art",
                                        target: "_blank",
                                        children: "AI Art Insight"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/photo",
                                        target: "_blank",
                                        children: "AI Photo Coach"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/poetic",
                                        target: "_blank",
                                        children: "AI Poetic Lens"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/avatar",
                                        target: "_blank",
                                        children: "AI Avatar Studio"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/erase",
                                        target: "_blank",
                                        children: "AI Watermarks Remover"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/reading",
                                        target: "_blank",
                                        children: "AI Reading Map"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/movie",
                                        target: "_blank",
                                        children: "AI CineMap"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/feynman",
                                        target: "_blank",
                                        children: "AI Feynman"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/marzano",
                                        target: "_blank",
                                        children: "AI Marzano Taxonomy"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/bloom",
                                        target: "_blank",
                                        children: "AI Bloom Taxonomy"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/solo",
                                        target: "_blank",
                                        children: "AI SOLO Taxonomy"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/dok",
                                        target: "_blank",
                                        children: "AI DOK Taxonomy"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/layered-explanation",
                                        target: "_blank",
                                        children: "AI MindLadder"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/infographic",
                                        target: "_blank",
                                        children: "AI Infographic"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/insightcards",
                                        target: "_blank",
                                        children: "AI InsightCards"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/mindsnap",
                                        target: "_blank",
                                        children: "AI MindSnap"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                        href: "https://www.funblocks.net/aitools/one-page-slide",
                                        target: "_blank",
                                        children: "AI SlideGenius"
                                    })
                                ]
                            })
                        ]
                    })
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: (_index_module_css__WEBPACK_IMPORTED_MODULE_2___default().copyright),
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                        "data-i18n": "footer.copyright",
                        children: "\xa9 2025 FunBlocks AI. All rights reserved."
                    })
                })
            ]
        })
    });
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 3706:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1301);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _Section__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1306);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_5__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);






const ResearchCard = ({ title , description , icon , citations , color  })=>{
    const cardBg = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("white", "gray.800");
    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.200", "gray.700");
    const textColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.600", "gray.300");
    const headingColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.800", "white");
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Card, {
        bg: cardBg,
        borderWidth: "1px",
        borderColor: borderColor,
        borderRadius: "lg",
        overflow: "hidden",
        boxShadow: "md",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        transition: "all 0.3s",
        _hover: {
            transform: "translateY(-4px)",
            boxShadow: "lg",
            borderColor: `${color}.200`
        },
        role: "group",
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {
                pb: 0,
                pt: {
                    base: 4,
                    md: 5
                },
                px: {
                    base: 4,
                    md: 5
                },
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {
                    align: "center",
                    mb: 3,
                    flexWrap: {
                        base: "wrap",
                        sm: "nowrap"
                    },
                    gap: {
                        base: 2,
                        sm: 0
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {
                            w: {
                                base: 10,
                                md: 12
                            },
                            h: {
                                base: 10,
                                md: 12
                            },
                            align: "center",
                            justify: "center",
                            borderRadius: "full",
                            bg: `${color}.100`,
                            color: `${color}.500`,
                            mr: {
                                base: 3,
                                md: 4
                            },
                            transition: "all 0.2s",
                            _groupHover: {
                                transform: "scale(1.05)",
                                bg: `${color}.200`
                            },
                            flexShrink: 0,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                as: icon,
                                boxSize: {
                                    base: 5,
                                    md: 6
                                }
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {
                            size: {
                                base: "sm",
                                md: "md"
                            },
                            fontWeight: "semibold",
                            color: headingColor,
                            lineHeight: "shorter",
                            children: title
                        })
                    ]
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {
                pt: {
                    base: 1,
                    md: 2
                },
                pb: {
                    base: 3,
                    md: 4
                },
                px: {
                    base: 4,
                    md: 5
                },
                flex: "1",
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                    fontSize: {
                        base: "xs",
                        md: "sm"
                    },
                    color: textColor,
                    lineHeight: "1.6",
                    children: description
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.CardFooter, {
                pt: 0,
                pb: {
                    base: 4,
                    md: 5
                },
                px: {
                    base: 4,
                    md: 5
                },
                bg: `${color}.50`,
                borderTop: "1px solid",
                borderColor: `${color}.100`,
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
                    align: "start",
                    spacing: {
                        base: 1,
                        md: 1.5
                    },
                    width: "100%",
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                            fontSize: {
                                base: "2xs",
                                md: "xs"
                            },
                            fontWeight: "bold",
                            color: `${color}.700`,
                            textTransform: "uppercase",
                            letterSpacing: "wider",
                            children: citations.length > 1 ? "Research Citations:" : "Research Citation:"
                        }),
                        citations.map((citation, idx)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                                spacing: {
                                    base: 1,
                                    md: 2
                                },
                                fontSize: {
                                    base: "2xs",
                                    md: "xs"
                                },
                                color: textColor,
                                align: "flex-start",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                                        noOfLines: 1,
                                        maxW: {
                                            base: "90%",
                                            md: "90%"
                                        },
                                        children: citation.text
                                    }),
                                    citation.url && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link, {
                                        href: citation.url,
                                        isExternal: true,
                                        color: `${color}.500`,
                                        _hover: {
                                            textDecoration: "none",
                                            color: `${color}.600`
                                        },
                                        "aria-label": "View research citation",
                                        flexShrink: 0,
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                            as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaExternalLinkAlt,
                                            boxSize: {
                                                base: 2.5,
                                                md: 3
                                            },
                                            transition: "transform 0.2s",
                                            _hover: {
                                                transform: "scale(1.2)"
                                            }
                                        })
                                    })
                                ]
                            }, idx))
                    ]
                })
            })
        ]
    });
};
/**
 * Reusable ResearchBacked component
 * @param {Object} props - Component props
 * @param {string} props.namespace - Translation namespace (default: 'common')
 * @param {string} props.titleKey - Translation key for title (default: 'research_title')
 * @param {string} props.descriptionKey - Translation key for description (default: 'research_description')
 * @param {Array} props.researchAreas - Array of research area objects (optional, will use default if not provided)
 * @param {string} props.bg - Background color (default: 'gray.50')
 * @param {string} props.id - Section ID for anchor links
 * @param {Array} props.buttons - Array of button objects (optional)
 * @param {string} props.productName - Product name to use in default description (default: 'our product')
 */ const ResearchBacked = ({ namespace ="common" , title ="research_title" , description ="research_description" , researchAreas: customResearchAreas , bg ="gray.50" , id , buttons: customButtons , productName ="our product"  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(namespace);
    // Default buttons if none provided
    const defaultButtons = [
        {
            label: t("research_button_science", "Science-Based"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain,
            colorScheme: "blue"
        },
        {
            label: t("research_button_data", "Data-Driven"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartLine,
            colorScheme: "green"
        }
    ];
    // Default research areas if none provided
    const defaultResearchAreas = [
        {
            title: t("research_area_1_title", "Research Area 1"),
            description: t("research_area_1_description", `${productName} is built on solid research foundations in this area, providing significant benefits to users.`),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain,
            color: "blue",
            citations: [
                {
                    text: "Sweller, J. (2011). Cognitive load theory. Psychology of Learning and Motivation, 55, 37-76.",
                    url: "https://doi.org/10.1016/B978-0-12-387691-1.00002-8"
                },
                {
                    text: "Paas, F., & Ayres, P. (2014). Cognitive load theory: A broader view on the role of memory in learning and education. Educational Psychology Review, 26(2), 191-195.",
                    url: "https://doi.org/10.1007/s10648-014-9263-5"
                }
            ]
        },
        {
            title: t("research_area_2_title", "Dual Coding Theory"),
            description: t("research_area_2_description", "By combining visual mind maps with textual explanations, MindLadder leverages dual coding theory to engage multiple cognitive channels, enhancing memory formation and recall."),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb,
            color: "purple",
            citations: [
                {
                    text: "Paivio, A. (2014). Mind and its evolution: A dual coding theoretical approach. Psychology Press.",
                    url: "https://doi.org/10.4324/9781315785233"
                },
                {
                    text: "Clark, J. M., & Paivio, A. (1991). Dual coding theory and education. Educational Psychology Review, 3(3), 149-210.",
                    url: "https://doi.org/10.1007/**********"
                }
            ]
        },
        {
            title: t("research_area_3_title", "Spaced Repetition"),
            description: t("research_area_3_description", "MindLadder's layered approach naturally implements spaced repetition principles, revisiting core concepts at increasing levels of complexity to optimize long-term retention."),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartLine,
            color: "green",
            citations: [
                {
                    text: "Kang, S. H. (2016). Spaced repetition promotes efficient and effective learning: Policy implications for instruction. Policy Insights from the Behavioral and Brain Sciences, 3(1), 12-19.",
                    url: "https://doi.org/10.1177/2372732215624708"
                },
                {
                    text: "Dunlosky, J., et al. (2013). Improving students' learning with effective learning techniques: Promising directions from cognitive and educational psychology. Psychological Science in the Public Interest, 14(1), 4-58.",
                    url: "https://doi.org/10.1177/1529100612453266"
                }
            ]
        },
        {
            title: t("research_area_4_title", "Desirable Difficulties"),
            description: t("research_area_4_description", 'The counterintuitive aspects in MindLadder\'s higher tiers create "desirable difficulties" that enhance learning by requiring deeper processing and more effortful retrieval.'),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBook,
            color: "orange",
            citations: [
                {
                    text: "Bjork, R. A., & Bjork, E. L. (2020). Desirable difficulties in theory and practice. Journal of Applied Research in Memory and Cognition, 9(4), 475-479.",
                    url: "https://doi.org/10.1016/j.jarmac.2020.09.003"
                },
                {
                    text: "Soderstrom, N. C., & Bjork, R. A. (2015). Learning versus performance: An integrative review. Perspectives on Psychological Science, 10(2), 176-199.",
                    url: "https://doi.org/10.1177/1745691615569000"
                }
            ]
        }
    ];
    // Use provided research areas/buttons or fall back to defaults
    const researchAreasList = customResearchAreas || defaultResearchAreas;
    const buttonsList = customButtons || defaultButtons;
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_Section__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
        bg: bg,
        id: id,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Container, {
            maxW: "container.xl",
            px: {
                base: 3,
                md: 6
            },
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
                    spacing: {
                        base: 6,
                        md: 8
                    },
                    mb: {
                        base: 8,
                        md: 12
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {
                            as: "h2",
                            size: {
                                base: "lg",
                                md: "xl"
                            },
                            textAlign: "center",
                            bgGradient: "linear(to-r, blue.400, purple.500)",
                            bgClip: "text",
                            px: {
                                base: 2,
                                md: 0
                            },
                            lineHeight: {
                                base: "1.3",
                                md: "1.2"
                            },
                            children: title
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                            fontSize: {
                                base: "sm",
                                md: "lg"
                            },
                            textAlign: "center",
                            maxW: "3xl",
                            px: {
                                base: 2,
                                md: 0
                            },
                            color: "gray.600",
                            lineHeight: "1.6",
                            children: description
                        }),
                        buttonsList.length > 0 && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {
                            gap: {
                                base: 2,
                                md: 4
                            },
                            flexWrap: "wrap",
                            justify: "center",
                            width: "100%",
                            px: {
                                base: 2,
                                md: 0
                            },
                            children: buttonsList.map((button, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {
                                    size: {
                                        base: "xs",
                                        md: "sm"
                                    },
                                    colorScheme: button.colorScheme || "blue",
                                    variant: "outline",
                                    leftIcon: button.icon && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                        as: button.icon,
                                        boxSize: {
                                            base: 3,
                                            md: 4
                                        }
                                    }),
                                    px: {
                                        base: 2,
                                        md: 3
                                    },
                                    py: {
                                        base: 1,
                                        md: 2
                                    },
                                    borderRadius: "full",
                                    fontWeight: "medium",
                                    _hover: {
                                        transform: "translateY(-2px)",
                                        bg: `${button.colorScheme || "blue"}.50`
                                    },
                                    transition: "all 0.2s",
                                    children: button.label
                                }, idx))
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.SimpleGrid, {
                    columns: {
                        base: 1,
                        sm: 2,
                        lg: researchAreasList.length
                    },
                    spacing: {
                        base: 4,
                        md: 6
                    },
                    width: "100%",
                    children: researchAreasList.map((area, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ResearchCard, {
                            ...area
                        }, idx))
                })
            ]
        })
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResearchBacked);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 1306:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2210);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__]);
_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];


{}const Section = ({ title , children , bg ="white" , gap , id , bgGradient  })=>{
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useBreakpointValue)({
        base: true,
        md: false
    });
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
        id: id,
        bg: bg,
        py: "4rem",
        alignItems: "center",
        width: "100%",
        bgGradient: bgGradient,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
            className: "container",
            gap: gap,
            alignItems: "center",
            children: [
                title && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                    fontSize: isMobile ? "2xl" : "3xl",
                    mb: isMobile ? 4 : 8,
                    textAlign: "center",
                    bgGradient: "linear(to-r, blue.400, purple.400)",
                    bgClip: "text",
                    children: title
                }),
                children
            ]
        })
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Section);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 966:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1301);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _Section__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1306);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_5__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, _Section__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);






const TestimonialCard = ({ content , author , role , organization , rating , image  })=>{
    const cardBg = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("white", "gray.800");
    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.200", "gray.700");
    const textColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.600", "gray.300");
    const authorColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.800", "white");
    const metaColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)("gray.500", "gray.400");
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
        bg: cardBg,
        p: {
            base: 4,
            sm: 5,
            md: 6
        },
        borderRadius: "lg",
        borderWidth: "1px",
        borderColor: borderColor,
        boxShadow: "md",
        position: "relative",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        transition: "all 0.3s",
        _hover: {
            transform: "translateY(-4px)",
            boxShadow: "lg"
        },
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaQuoteLeft,
                position: "absolute",
                top: {
                    base: 3,
                    md: 4
                },
                left: {
                    base: 3,
                    md: 4
                },
                color: "purple.100",
                boxSize: {
                    base: 6,
                    md: 8
                },
                opacity: 0.6
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
                mb: {
                    base: 4,
                    md: 6
                },
                pt: {
                    base: 5,
                    md: 6
                },
                pl: {
                    base: 5,
                    md: 6
                },
                flex: "1",
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                    fontSize: {
                        base: "sm",
                        md: "md"
                    },
                    fontStyle: "italic",
                    color: textColor,
                    lineHeight: "1.6",
                    children: [
                        '"',
                        content,
                        '"'
                    ]
                })
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {
                direction: {
                    base: "column",
                    sm: "row"
                },
                align: {
                    base: "flex-start",
                    sm: "center"
                },
                spacing: {
                    base: 2,
                    md: 4
                },
                wrap: "wrap",
                gap: {
                    base: 2,
                    md: 3
                },
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                        spacing: {
                            base: 3,
                            md: 4
                        },
                        align: "center",
                        flex: "1",
                        minW: "0",
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Avatar, {
                                size: {
                                    base: "sm",
                                    md: "md"
                                },
                                name: author,
                                src: image,
                                boxShadow: "sm"
                            }),
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
                                minW: "0",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                                        fontWeight: "bold",
                                        fontSize: {
                                            base: "sm",
                                            md: "md"
                                        },
                                        color: authorColor,
                                        noOfLines: 1,
                                        children: author
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                                        fontSize: {
                                            base: "xs",
                                            md: "sm"
                                        },
                                        color: metaColor,
                                        noOfLines: 1,
                                        children: role
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                                        fontSize: {
                                            base: "2xs",
                                            md: "xs"
                                        },
                                        color: metaColor,
                                        noOfLines: 1,
                                        children: organization
                                    })
                                ]
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                        spacing: 1,
                        justify: {
                            base: "flex-start",
                            sm: "flex-end"
                        },
                        alignSelf: {
                            base: "flex-start",
                            sm: "center"
                        },
                        mt: {
                            base: 1,
                            sm: 0
                        },
                        children: [
                            [
                                ...Array(Math.floor(rating))
                            ].map((_, i)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                    as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaStar,
                                    color: "yellow.400",
                                    boxSize: {
                                        base: 3,
                                        md: 4
                                    }
                                }, i)),
                            rating % 1 !== 0 && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaStarHalfAlt,
                                color: "yellow.400",
                                boxSize: {
                                    base: 3,
                                    md: 4
                                }
                            })
                        ]
                    })
                ]
            })
        ]
    });
};
/**
 * Reusable Testimonials component
 * @param {Object} props - Component props
 * @param {string} props.namespace - Translation namespace (default: 'common')
 * @param {string} props.titleKey - Translation key for title (default: 'testimonials_title')
 * @param {string} props.descriptionKey - Translation key for description (default: 'testimonials_description')
 * @param {string} props.ratingKey - Translation key for rating text (default: 'testimonials_rating')
 * @param {string} props.usersKey - Translation key for users count text (default: 'testimonials_users')
 * @param {Array} props.testimonials - Array of testimonial objects (optional, will use default if not provided)
 * @param {string} props.bg - Background color (default: 'white')
 * @param {string} props.id - Section ID for anchor links
 */ const Testimonials = ({ namespace ="common" , appname ="FunBlocks AI" , rating ="4.7" , users ="15,000" , testimonials: customTestimonials , bg ="white" , id  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(namespace);
    // Default testimonials if none provided
    const defaultTestimonials = [
        {
            content: t("testimonial_1_content", "MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I've cut my study time by 30% while improving my grades!"),
            author: "Emily Johnson",
            role: t("testimonial_1_role", "Medical Student"),
            organization: "Stanford University",
            rating: 5,
            image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
        },
        {
            content: t("testimonial_2_content", "As a high school physics teacher, I've been using MindLadder to create learning materials for my students. The 8-tier system is brilliant for gradually building understanding of difficult concepts like quantum mechanics. My students' test scores have improved by 27% since implementing these knowledge ladders."),
            author: "David Martinez",
            role: t("testimonial_2_role", "Physics Teacher"),
            organization: "Westlake High School",
            rating: 5,
            image: "https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
        },
        {
            content: t("testimonial_3_content", "Our engineering team uses MindLadder to onboard new hires to our complex software architecture. The visual nature combined with progressive complexity layers has reduced our onboarding time by 40%. It's now our standard tool for knowledge transfer across departments."),
            author: "Sarah Chen",
            role: t("testimonial_3_role", "Engineering Director"),
            organization: "Tesla",
            rating: 4.5,
            image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
        },
        {
            content: t("testimonial_4_content", "As someone with ADHD, traditional learning resources often overwhelm me. MindLadder's visual approach and ability to explore concepts at my own pace has been a game-changer. For the first time, I can see how different ideas connect and build on each other."),
            author: "Michael Rodriguez",
            role: t("testimonial_4_role", "Software Developer"),
            organization: "Freelance",
            rating: 5,
            image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
        },
        {
            content: t("testimonial_5_content", "I've been using MindLadder to prepare for my MBA courses. The way it connects business concepts through multiple layers of understanding has given me an edge in class discussions. I especially appreciate how it highlights counterintuitive aspects that challenge my assumptions."),
            author: "Jennifer Park",
            role: t("testimonial_5_role", "MBA Student"),
            organization: "Harvard Business School",
            rating: 4.5,
            image: "https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
        },
        {
            content: t("testimonial_6_content", "Our research team uses MindLadder to map interdisciplinary connections between AI ethics and policy. The ability to start with simple analogies and build to expert-level insights has helped us communicate complex ideas to stakeholders from diverse backgrounds."),
            author: "Dr. James Wilson",
            role: t("testimonial_6_role", "Research Director"),
            organization: "MIT Media Lab",
            rating: 5,
            image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
        }
    ];
    // Use provided testimonials or fall back to defaults
    const testimonialsList = customTestimonials || defaultTestimonials;
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_Section__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
        bg: bg,
        id: id,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Container, {
            maxW: "container.xl",
            px: {
                base: 3,
                md: 6
            },
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
                    spacing: {
                        base: 6,
                        md: 8
                    },
                    mb: {
                        base: 8,
                        md: 12
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {
                            as: "h2",
                            size: {
                                base: "lg",
                                md: "xl"
                            },
                            textAlign: "center",
                            bgGradient: "linear(to-r, blue.400, purple.500)",
                            bgClip: "text",
                            px: {
                                base: 2,
                                md: 0
                            },
                            lineHeight: {
                                base: "1.3",
                                md: "1.2"
                            },
                            children: t("testimonials_title", "What Our Users Say")
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                            fontSize: {
                                base: "md",
                                md: "lg"
                            },
                            textAlign: "center",
                            maxW: "3xl",
                            px: {
                                base: 2,
                                md: 0
                            },
                            color: "gray.600",
                            children: t("testimonials_description", "Join thousands of users who have transformed their experience with {appname}.", {
                                appname
                            })
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {
                            direction: {
                                base: "column",
                                sm: "row"
                            },
                            spacing: {
                                base: 3,
                                md: 4
                            },
                            gap: {
                                base: 3,
                                md: 4
                            },
                            width: "100%",
                            justify: "center",
                            align: "center",
                            flexWrap: "wrap",
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {
                                    colorScheme: "green",
                                    p: {
                                        base: 1.5,
                                        md: 2
                                    },
                                    borderRadius: "md",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    width: {
                                        base: "full",
                                        sm: "auto"
                                    },
                                    textAlign: "center",
                                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                                        justify: {
                                            base: "center",
                                            sm: "flex-start"
                                        },
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {
                                                as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaStar,
                                                boxSize: {
                                                    base: 3,
                                                    md: 4
                                                }
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                                                children: t("testimonials_rating", "{rating} average rating", {
                                                    rating
                                                })
                                            })
                                        ]
                                    })
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {
                                    colorScheme: "blue",
                                    p: {
                                        base: 1.5,
                                        md: 2
                                    },
                                    borderRadius: "md",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    width: {
                                        base: "full",
                                        sm: "auto"
                                    },
                                    textAlign: "center",
                                    children: t("testimonials_users", "10,000+ active users", {
                                        users
                                    })
                                })
                            ]
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.SimpleGrid, {
                    columns: {
                        base: 1,
                        md: 2,
                        lg: 3
                    },
                    spacing: {
                        base: 5,
                        md: 8
                    },
                    width: "100%",
                    children: testimonialsList.map((testimonial, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(TestimonialCard, {
                            ...testimonial
                        }, idx))
                })
            ]
        })
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Testimonials);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;