"use strict";
(() => {
var exports = {};
exports.id = 9236;
exports.ids = [9236];
exports.modules = {

/***/ 6378:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Home),
/* harmony export */   "getStaticProps": () => (/* binding */ getStaticProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(968);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5460);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7426);
/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8534);
/* harmony import */ var _components_CardGenerator_Artifacts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6734);
/* harmony import */ var _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1082);
/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6611);
/* harmony import */ var _components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(1692);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_Header__WEBPACK_IMPORTED_MODULE_6__, _components_CardGenerator_Artifacts__WEBPACK_IMPORTED_MODULE_7__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_8__, qrcode_react__WEBPACK_IMPORTED_MODULE_9__, _components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_10__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_Header__WEBPACK_IMPORTED_MODULE_6__, _components_CardGenerator_Artifacts__WEBPACK_IMPORTED_MODULE_7__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_8__, qrcode_react__WEBPACK_IMPORTED_MODULE_9__, _components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);











const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_4___default()().publicRuntimeConfig;
function Home() {
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_8__/* .ShareModalProvider */ .$, {
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
            width: "100%",
            position: "relative",
            overflowY: "hidden",
            gap: "4px",
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_Header__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                    leftLink: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link, {
                        href: basePath,
                        fontSize: "2xl",
                        fontWeight: "bold",
                        color: "blue.500",
                        isExternal: true,
                        children: "FunBlocks AI Tools"
                    })
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    style: {
                        display: "none"
                    },
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(qrcode_react__WEBPACK_IMPORTED_MODULE_9__.QRCodeCanvas, {
                        value: `https://www.funblocks.net${basePath}`,
                        size: 100,
                        id: "qrcode-canvas"
                    })
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
                    overflowY: "auto",
                    height: `calc(100vh - 76px)`,
                    alignItems: "center",
                    width: "100%",
                    py: 10,
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_Artifacts__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                        collection: "my",
                        app: Object.values(_utils_constants__WEBPACK_IMPORTED_MODULE_5__/* .APP_TYPE */ .IF)
                    })
                })
            ]
        })
    });
}
async function getStaticProps({ locale  }) {
    return {
        props: {
            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__.serverSideTranslations)(locale, [
                "common"
            ])
        }
    };
} // export async function getServerSideProps(context) {
 //   // 获取初始数据
 //   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
 //   return {
 //     props: {
 //       initial_showcases: data,
 //     },
 //   };
 // }

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 5692:
/***/ ((module) => {

module.exports = require("@mui/material");

/***/ }),

/***/ 9048:
/***/ ((module) => {

module.exports = require("@mui/material/CircularProgress");

/***/ }),

/***/ 6999:
/***/ ((module) => {

module.exports = require("@react-oauth/google");

/***/ }),

/***/ 3665:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap");

/***/ }),

/***/ 9327:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/ChevronDown");

/***/ }),

/***/ 39:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/FileText");

/***/ }),

/***/ 7817:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/Magic");

/***/ }),

/***/ 450:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/TextLeft");

/***/ }),

/***/ 9077:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/FlowCascade");

/***/ }),

/***/ 8847:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/Link");

/***/ }),

/***/ 7636:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular");

/***/ }),

/***/ 1977:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxChecked");

/***/ }),

/***/ 7520:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxUnchecked");

/***/ }),

/***/ 3770:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/Edit");

/***/ }),

/***/ 3620:
/***/ ((module) => {

module.exports = require("@styled-icons/material");

/***/ }),

/***/ 384:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Close");

/***/ }),

/***/ 8953:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Lightbulb");

/***/ }),

/***/ 6131:
/***/ ((module) => {

module.exports = require("html-to-image");

/***/ }),

/***/ 6333:
/***/ ((module) => {

module.exports = require("immutability-helper");

/***/ }),

/***/ 1377:
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ 5460:
/***/ ((module) => {

module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ 4558:
/***/ ((module) => {

module.exports = require("next/config");

/***/ }),

/***/ 968:
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ 1853:
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ 6689:
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ 997:
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 6134:
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ 2210:
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ 4115:
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ 2728:
/***/ ((module) => {

module.exports = import("@xyflow/react");;

/***/ }),

/***/ 1024:
/***/ ((module) => {

module.exports = import("mermaid");;

/***/ }),

/***/ 6611:
/***/ ((module) => {

module.exports = import("qrcode.react");;

/***/ }),

/***/ 3196:
/***/ ((module) => {

module.exports = import("react-dnd");;

/***/ }),

/***/ 1152:
/***/ ((module) => {

module.exports = import("react-dnd-html5-backend");;

/***/ }),

/***/ 7987:
/***/ ((module) => {

module.exports = import("react-i18next");;

/***/ }),

/***/ 1301:
/***/ ((module) => {

module.exports = import("react-icons/fa");;

/***/ }),

/***/ 6905:
/***/ ((module) => {

module.exports = import("react-icons/md");;

/***/ }),

/***/ 4009:
/***/ ((module) => {

module.exports = import("react-intersection-observer");;

/***/ }),

/***/ 3135:
/***/ ((module) => {

module.exports = import("react-markdown");;

/***/ }),

/***/ 2017:
/***/ ((module) => {

module.exports = import("react-share");;

/***/ }),

/***/ 9521:
/***/ ((module) => {

module.exports = import("rehype-katex");;

/***/ }),

/***/ 1871:
/***/ ((module) => {

module.exports = import("rehype-raw");;

/***/ }),

/***/ 6809:
/***/ ((module) => {

module.exports = import("remark-gfm");;

/***/ }),

/***/ 9832:
/***/ ((module) => {

module.exports = import("remark-math");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [1951,4743,101,6734,8534], () => (__webpack_exec__(6378)));
module.exports = __webpack_exports__;

})();