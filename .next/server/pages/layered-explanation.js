"use strict";
(() => {
var exports = {};
exports.id = 3355;
exports.ids = [3355];
exports.modules = {

/***/ 5904:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Home),
/* harmony export */   "getStaticProps": () => (/* binding */ getStaticProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(968);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9199);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5460);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7426);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__]);
_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];





function Home() {
    const currentDate = new Date().toISOString().split("T")[0];
    // FAQ structured data for SEO
    const faqStructuredData = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "What makes MindLadder different from other learning tools?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "MindLadder's unique 8-tier progressive learning system sets it apart from traditional learning tools. While most educational resources present information in a linear fashion, MindLadder builds knowledge in layers—starting with intuitive analogies and gradually ascending to sophisticated interconnections. This structured approach mimics how the human brain naturally builds understanding, making complex topics more accessible and memorable."
                }
            },
            {
                "@type": "Question",
                "name": "Can MindLadder help with any subject?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes! MindLadder is designed to work with virtually any concept or subject matter. Whether you're tackling quantum physics, financial markets, historical events, literary analysis, or programming concepts, our AI engine adapts to create appropriate knowledge ladders. The system excels at breaking down complex topics into manageable layers regardless of the field."
                }
            },
            {
                "@type": "Question",
                "name": "How does the 8-tier system work?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Our 8-tier system creates a progressive journey through any concept: 1) Simple Analogy - relates the concept to everyday experiences, 2) Basic Definition - provides clear fundamental explanation, 3) Key Components - breaks down main elements, 4) Functional Relationships - shows how components interact, 5) Contextual Applications - demonstrates real-world usage, 6) Advanced Interconnections - reveals links to related concepts, 7) Counterintuitive Aspects - explores surprising or paradoxical elements, and 8) Expert Insights - presents cutting-edge understanding and future directions."
                }
            },
            {
                "@type": "Question",
                "name": "Is MindLadder suitable for students?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Absolutely! MindLadder is an excellent tool for students at all levels. For K-12 students, it helps build foundational understanding of challenging subjects. College students can use it to master complex theories and concepts. Graduate students find it valuable for exploring interconnections between advanced topics. The progressive nature of the knowledge ladders makes it adaptable to any educational level."
                }
            },
            {
                "@type": "Question",
                "name": "Can teachers use MindLadder in their curriculum?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, MindLadder is a powerful resource for educators. Teachers can use it to create comprehensive lesson plans that naturally progress from basic to advanced understanding. It's excellent for designing differentiated instruction that meets students at their current level while providing clear pathways to deeper knowledge. The visual nature of the mind maps also makes complex topics more engaging and accessible for diverse learning styles."
                }
            },
            {
                "@type": "Question",
                "name": "How accurate is the information provided by MindLadder?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "MindLadder is powered by advanced AI that draws on extensive knowledge bases to create accurate, up-to-date information. Our system is designed to present well-established facts and theories while clearly indicating areas of ongoing research or debate. However, as with any AI tool, we recommend verifying critical information with authoritative sources, especially for specialized professional or academic applications."
                }
            },
            {
                "@type": "Question",
                "name": "How does MindLadder compare to traditional mind mapping tools?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Unlike traditional mind mapping tools that require you to manually create connections, MindLadder uses AI to automatically generate comprehensive knowledge structures. Traditional tools typically create flat, single-level maps, while MindLadder builds multi-dimensional ladders with 8 distinct cognitive layers. Our system also dynamically expands based on your interests—each node can be explored further with additional ladders, creating a truly interactive learning experience that traditional static mind maps cannot match."
                }
            },
            {
                "@type": "Question",
                "name": "Can MindLadder help with exam preparation and study efficiency?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Absolutely! MindLadder is an excellent exam preparation tool that significantly improves study efficiency. By organizing information in progressive layers, it helps you build a solid foundation before tackling complex details—exactly what's needed for comprehensive exam preparation. The visual nature of the knowledge ladders enhances memory retention, while the interactive exploration helps identify and strengthen weak areas in your understanding. Many students report cutting their study time by 30-40% while achieving better comprehension using our structured approach."
                }
            },
            {
                "@type": "Question",
                "name": "Does MindLadder work for visual learners and those with different learning styles?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "MindLadder is designed to accommodate multiple learning styles, making it particularly effective for visual learners. The graphical representation of knowledge ladders provides clear visual pathways through complex topics. For auditory learners, the detailed explanations at each level can be read aloud using screen readers. Kinesthetic learners benefit from the interactive nature of clicking through and expanding different branches based on their interests. This multi-modal approach ensures that regardless of your preferred learning style, MindLadder offers an engaging and effective educational experience."
                }
            },
            {
                "@type": "Question",
                "name": "How does MindLadder help with knowledge retention and long-term memory?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "MindLadder significantly enhances knowledge retention through several cognitive science principles. First, the progressive disclosure approach aligns with how our brains naturally build understanding, creating stronger neural connections. Second, the visual mapping of concepts creates spatial memory associations that improve recall. Third, the interactive exploration engages active learning, which is proven to enhance retention compared to passive reading. Finally, by connecting new information to existing knowledge through analogies and relationships, MindLadder leverages the power of associative memory, making complex concepts stick for the long term."
                }
            },
            {
                "@type": "Question",
                "name": "Can MindLadder be used for professional development and workplace learning?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "MindLadder is an invaluable tool for professional development and workplace learning. Organizations use it to onboard new employees by creating comprehensive knowledge ladders for company processes, products, and industry concepts. Teams use it to break down complex projects into manageable components with clear relationships. For individual professional development, MindLadder helps quickly master new skills and industry trends by providing both high-level overviews and detailed deep dives. The ability to share knowledge ladders also facilitates knowledge transfer between team members and departments."
                }
            },
            {
                "@type": "Question",
                "name": "Is MindLadder accessible for users with disabilities?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, accessibility is a core priority for MindLadder. Our interface is designed to be compatible with screen readers for visually impaired users, with all nodes and connections properly labeled for audio navigation. We support keyboard navigation for users with motor limitations. The color schemes are tested for color blindness compatibility, and we offer high-contrast modes. The progressive nature of our knowledge ladders is particularly helpful for users with cognitive disabilities, as it breaks complex topics into manageable chunks. We continuously work with accessibility experts to ensure our platform remains inclusive for all users."
                }
            },
            {
                "@type": "Question",
                "name": "How does MindLadder compare to traditional textbooks and online courses?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Unlike linear textbooks and courses that present information in a fixed sequence, MindLadder creates personalized, interactive knowledge structures that adapt to your interests and learning pace. Traditional educational resources often struggle to connect related concepts across chapters or modules, while MindLadder explicitly maps these relationships, creating a more cohesive understanding. Our tool also excels at presenting multiple perspectives on complex topics, whereas traditional resources may present only one viewpoint. Additionally, MindLadder's visual nature engages more of your cognitive faculties than text-heavy resources, leading to better retention and deeper understanding."
                }
            },
            {
                "@type": "Question",
                "name": "Can MindLadder help with learning difficult STEM subjects like physics, mathematics, and computer science?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Absolutely! MindLadder excels at making complex STEM subjects more accessible. For physics, it breaks down abstract concepts like quantum mechanics into intuitive analogies before building to mathematical formulations. In mathematics, it helps visualize abstract structures and connects formulas to real-world applications. For computer science, it maps relationships between algorithms, data structures, and programming paradigms while providing concrete examples. The 8-tier approach is particularly valuable for STEM subjects, as it bridges the gap between intuitive understanding and formal rigor, helping learners overcome the common hurdle of connecting conceptual understanding with technical implementation."
                }
            },
            {
                "@type": "Question",
                "name": "How does MindLadder incorporate the latest research in cognitive science and educational psychology?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "MindLadder's design is deeply informed by cognitive science research. Our 8-tier system aligns with findings on schema construction and cognitive load theory, gradually building mental frameworks while managing information complexity. We incorporate principles of spaced repetition by encouraging revisiting of concepts at optimal intervals. The visual mapping leverages dual coding theory, engaging both verbal and visual processing channels for stronger memory formation. Our approach to counterintuitive aspects draws on research about desirable difficulties and productive failure, which show that wrestling with challenging concepts enhances long-term retention. We continuously update our methodology based on new research in educational neuroscience to optimize the learning experience."
                }
            },
            {
                "@type": "Question",
                "name": "Can MindLadder integrate with other educational tools and learning management systems?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, MindLadder offers integration capabilities with popular learning management systems (LMS) like Canvas, Moodle, and Blackboard. Educators can embed knowledge ladders directly into course materials, and students can export their explorations to various formats for inclusion in assignments. We provide API access for educational technology developers who want to incorporate our layered learning approach into their applications. For enterprise users, we offer single sign-on integration with corporate learning platforms and knowledge management systems. Our export options include common formats like PDF, PNG, interactive HTML, and even presentation slides, making it easy to incorporate MindLadder content into existing workflows."
                }
            }
        ]
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("title", {
                        children: "AI MindLadder: Climb to Complete Understanding with 8-Tier Learning System | FunBlocks AI Tools"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "description",
                        content: "Transform your learning experience with AI MindLadder's progressive 8-tier system. Our AI-powered tool guides you from simple analogies to expert insights on any topic. Boost knowledge retention by 40% with visual concept mapping. Perfect for students, teachers, professionals, and lifelong learners seeking deeper understanding."
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "keywords",
                        content: "AI MindLadder, layered explanation, progressive learning, concept understanding, mind maps, knowledge visualization, Feynman technique, educational tool, learning journey, knowledge ladder, step-by-step learning, interactive learning, visual learning, deep understanding, 8-tier learning system, educational AI, learning technology, concept mapping, knowledge visualization, educational innovation, exam preparation, study efficiency, knowledge retention, long-term memory, cognitive science, professional development, workplace learning, accessibility, different learning styles, visual learners, auditory learners, kinesthetic learners, traditional mind mapping, multi-dimensional learning, AI education platform, progressive learning tool, cognitive learning app, visual knowledge mapping, spaced repetition, educational psychology, learning efficiency, memory enhancement, conceptual understanding, mental models, knowledge scaffolding, educational neuroscience, learning acceleration, study techniques, exam preparation tool, knowledge organization"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:type",
                        content: "website"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:url",
                        content: "https://www.funblocks.net/aitools/layered-explanation"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:title",
                        content: "AI MindLadder: Climb to Complete Understanding with 8-Tier Learning System | FunBlocks AI Tools"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:description",
                        content: "Transform your learning experience with AI MindLadder's progressive 8-tier system. Our AI-powered tool guides you from simple analogies to expert insights on any topic. Boost knowledge retention by 40% with visual concept mapping."
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:image",
                        content: "https://www.funblocks.net/img/og-mindladder.png"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "twitter:card",
                        content: "summary_large_image"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "twitter:url",
                        content: "https://www.funblocks.net/aitools/layered-explanation"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "twitter:title",
                        content: "AI MindLadder: Climb to Complete Understanding with 8-Tier Learning System | FunBlocks AI Tools"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "twitter:description",
                        content: "Transform your learning experience with AI MindLadder's progressive 8-tier system. Our AI-powered tool guides you from simple analogies to expert insights on any topic. Boost knowledge retention by 40% with visual concept mapping."
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "twitter:image",
                        content: "https://www.funblocks.net/img/og-mindladder.png"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify({
                                "@context": "https://schema.org",
                                "@type": "SoftwareApplication",
                                "name": "AI MindLadder",
                                "applicationCategory": "EducationalApplication",
                                "operatingSystem": "Web",
                                "offers": {
                                    "@type": "Offer",
                                    "price": "0",
                                    "priceCurrency": "USD"
                                },
                                "description": "Revolutionary AI-powered learning tool that transforms how you understand complex concepts through interactive knowledge ladders. With just a single input, our advanced AI engine creates comprehensive, multi-layered mind maps that guide you through any topic—from intuitive basics to profound insights. Proven to boost knowledge retention by up to 40% compared to traditional learning methods.",
                                "featureList": [
                                    "8-Tier Learning Ascent",
                                    "Layer-by-Layer Exploration",
                                    "Summit Insights",
                                    "Progressive disclosure learning",
                                    "Multiple perspectives",
                                    "Interactive node expansion",
                                    "Visual knowledge mapping",
                                    "Cognitive science-based approach",
                                    "Spaced repetition integration",
                                    "Customizable learning paths",
                                    "Cross-concept connections",
                                    "Expert insights at highest levels"
                                ],
                                "aggregateRating": {
                                    "@type": "AggregateRating",
                                    "ratingValue": "4.8",
                                    "ratingCount": "125"
                                },
                                "datePublished": "2024-05-01",
                                "dateModified": currentDate
                            })
                        }
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify(faqStructuredData)
                        }
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify({
                                "@context": "https://schema.org",
                                "@type": "HowTo",
                                "name": "How to Use MindLadder for Effective Learning",
                                "description": "Learn how to use MindLadder's 8-tier system to master any complex concept through progressive layers of understanding.",
                                "totalTime": "PT10M",
                                "tool": [
                                    {
                                        "@type": "HowToTool",
                                        "name": "MindLadder AI Tool"
                                    },
                                    {
                                        "@type": "HowToTool",
                                        "name": "Web Browser"
                                    }
                                ],
                                "step": [
                                    {
                                        "@type": "HowToStep",
                                        "name": "Enter your concept",
                                        "text": "Type any concept, topic, or question you want to understand in the input field.",
                                        "image": "https://www.funblocks.net/img/portfolio/fullsize/mindladder_step1.png",
                                        "url": "https://www.funblocks.net/aitools/layered-explanation#how-it-works"
                                    },
                                    {
                                        "@type": "HowToStep",
                                        "name": "Generate your knowledge ladder",
                                        "text": "Click 'Generate' and watch as AI constructs your 8-tier knowledge ladder, from simple analogies to expert insights.",
                                        "image": "https://www.funblocks.net/img/portfolio/fullsize/mindladder_step2.png",
                                        "url": "https://www.funblocks.net/aitools/layered-explanation#how-it-works"
                                    },
                                    {
                                        "@type": "HowToStep",
                                        "name": "Explore interactively",
                                        "text": "Click on any node to see detailed explanations or expand branches for deeper understanding of specific aspects.",
                                        "image": "https://www.funblocks.net/img/portfolio/fullsize/mindladder_step3.png",
                                        "url": "https://www.funblocks.net/aitools/layered-explanation#how-it-works"
                                    },
                                    {
                                        "@type": "HowToStep",
                                        "name": "Climb to mastery",
                                        "text": "Progress through all 8 tiers, from intuitive analogies to expert insights, until you've achieved complete understanding.",
                                        "image": "https://www.funblocks.net/img/portfolio/fullsize/mindladder_step4.png",
                                        "url": "https://www.funblocks.net/aitools/layered-explanation#how-it-works"
                                    }
                                ]
                            })
                        }
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("link", {
                        rel: "canonical",
                        href: "https://www.funblocks.net/aitools/layered-explanation"
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                app: _utils_constants__WEBPACK_IMPORTED_MODULE_4__/* .APP_TYPE.layeredExplanation */ .IF.layeredExplanation
            })
        ]
    });
}
async function getStaticProps({ locale  }) {
    return {
        props: {
            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__.serverSideTranslations)(locale, [
                "layered",
                "common"
            ])
        }
    };
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 5692:
/***/ ((module) => {

module.exports = require("@mui/material");

/***/ }),

/***/ 9048:
/***/ ((module) => {

module.exports = require("@mui/material/CircularProgress");

/***/ }),

/***/ 6999:
/***/ ((module) => {

module.exports = require("@react-oauth/google");

/***/ }),

/***/ 3665:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap");

/***/ }),

/***/ 9327:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/ChevronDown");

/***/ }),

/***/ 39:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/FileText");

/***/ }),

/***/ 7817:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/Magic");

/***/ }),

/***/ 450:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/TextLeft");

/***/ }),

/***/ 9077:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/FlowCascade");

/***/ }),

/***/ 8847:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/Link");

/***/ }),

/***/ 7636:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular");

/***/ }),

/***/ 1977:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxChecked");

/***/ }),

/***/ 7520:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxUnchecked");

/***/ }),

/***/ 3770:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/Edit");

/***/ }),

/***/ 3620:
/***/ ((module) => {

module.exports = require("@styled-icons/material");

/***/ }),

/***/ 384:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Close");

/***/ }),

/***/ 8953:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Lightbulb");

/***/ }),

/***/ 6131:
/***/ ((module) => {

module.exports = require("html-to-image");

/***/ }),

/***/ 6333:
/***/ ((module) => {

module.exports = require("immutability-helper");

/***/ }),

/***/ 1377:
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ 5460:
/***/ ((module) => {

module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ 4558:
/***/ ((module) => {

module.exports = require("next/config");

/***/ }),

/***/ 3280:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/app-router-context.js");

/***/ }),

/***/ 3539:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/detect-domain-locale.js");

/***/ }),

/***/ 4014:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/normalize-locale-path.js");

/***/ }),

/***/ 4964:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router-context.js");

/***/ }),

/***/ 3431:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-locale.js");

/***/ }),

/***/ 1751:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix.js");

/***/ }),

/***/ 3938:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/format-url.js");

/***/ }),

/***/ 1109:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/is-local-url.js");

/***/ }),

/***/ 8854:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/parse-path.js");

/***/ }),

/***/ 3297:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash.js");

/***/ }),

/***/ 7782:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/resolve-href.js");

/***/ }),

/***/ 9232:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ 968:
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ 1853:
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ 6689:
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ 997:
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 6621:
/***/ ((module) => {

module.exports = require("seedrandom");

/***/ }),

/***/ 7342:
/***/ ((module) => {

module.exports = require("url-regex");

/***/ }),

/***/ 6134:
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ 2210:
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ 4115:
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ 2728:
/***/ ((module) => {

module.exports = import("@xyflow/react");;

/***/ }),

/***/ 6197:
/***/ ((module) => {

module.exports = import("framer-motion");;

/***/ }),

/***/ 1024:
/***/ ((module) => {

module.exports = import("mermaid");;

/***/ }),

/***/ 6611:
/***/ ((module) => {

module.exports = import("qrcode.react");;

/***/ }),

/***/ 3196:
/***/ ((module) => {

module.exports = import("react-dnd");;

/***/ }),

/***/ 1152:
/***/ ((module) => {

module.exports = import("react-dnd-html5-backend");;

/***/ }),

/***/ 7987:
/***/ ((module) => {

module.exports = import("react-i18next");;

/***/ }),

/***/ 7425:
/***/ ((module) => {

module.exports = import("react-icons/ai");;

/***/ }),

/***/ 6157:
/***/ ((module) => {

module.exports = import("react-icons/bi");;

/***/ }),

/***/ 1301:
/***/ ((module) => {

module.exports = import("react-icons/fa");;

/***/ }),

/***/ 6542:
/***/ ((module) => {

module.exports = import("react-icons/gi");;

/***/ }),

/***/ 6905:
/***/ ((module) => {

module.exports = import("react-icons/md");;

/***/ }),

/***/ 4009:
/***/ ((module) => {

module.exports = import("react-intersection-observer");;

/***/ }),

/***/ 3135:
/***/ ((module) => {

module.exports = import("react-markdown");;

/***/ }),

/***/ 2017:
/***/ ((module) => {

module.exports = import("react-share");;

/***/ }),

/***/ 9521:
/***/ ((module) => {

module.exports = import("rehype-katex");;

/***/ }),

/***/ 1871:
/***/ ((module) => {

module.exports = import("rehype-raw");;

/***/ }),

/***/ 6809:
/***/ ((module) => {

module.exports = import("remark-gfm");;

/***/ }),

/***/ 9832:
/***/ ((module) => {

module.exports = import("remark-math");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [8797,1664,1951,4743,101,5408,6734,9199], () => (__webpack_exec__(5904)));
module.exports = __webpack_exports__;

})();