"use strict";
(() => {
var exports = {};
exports.id = 5557;
exports.ids = [5557];
exports.modules = {

/***/ 5040:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Home),
/* harmony export */   "getStaticProps": () => (/* binding */ getStaticProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2210);
/* harmony import */ var _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9199);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5460);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7426);
/* harmony import */ var _components_common_SEO__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(9173);
/* harmony import */ var _components_common_SEOStructuredData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(4665);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_8__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);









function Home() {
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_4___default()().publicRuntimeConfig;
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)("graphics");
    // Enhanced FAQ data for structured data
    const faqs = [
        {
            question: t("graphics_faq_1_q", "What is FunBlocks AI Graphics?"),
            answer: t("graphics_faq_1_a", "FunBlocks AI Graphics is a tool that uses large language models (LLM) technology to generate interesting and insightful SVG infographic cards. It can help users create shareable content suitable for social media, presentations, or personal notes.")
        },
        {
            question: t("graphics_faq_2_q", "What can FunBlocks AI Graphics do?"),
            answer: t("graphics_faq_2_a", "FunBlocks AI Graphics can generate interesting and insightful SVG infographic cards suitable for social media, presentations, personal notes, or just for fun.")
        },
        {
            question: t("graphics_faq_3_q", "Do I need design skills to use this tool?"),
            answer: t("graphics_faq_3_a", "Not at all! Our AI handles the design part. You just describe what you want, and the AI will generate professional graphics for you.")
        },
        {
            question: t("graphics_faq_4_q", "What types of graphics can I create?"),
            answer: t("graphics_faq_4_a", "You can create various types of visual content, including infographics, flowcharts, process diagrams, data charts, timelines, comparison tables, and more.")
        },
        {
            question: "How does the AI Infographic Generator work?",
            answer: "Our AI Infographic Generator analyzes your input text or data and automatically creates visually appealing infographics that effectively communicate your information. It handles layout, color schemes, typography, and visual elements to produce professional-quality results in seconds."
        },
        {
            question: "Can I create flowcharts with this AI tool?",
            answer: "Yes, our AI Flowchart Generator is specifically designed to create clear, professional flowcharts from your process descriptions. Simply describe the process or workflow, and the AI will generate a structured flowchart with proper connections, decision points, and visual hierarchy."
        },
        {
            question: "What types of data charts can I create?",
            answer: "You can create a wide variety of data charts including bar charts, pie charts, line graphs, area charts, scatter plots, bubble charts, radar charts, and more. Our AI analyzes your data and recommends the most effective chart type for your specific information."
        },
        {
            question: "Does the tool generate SVG code that I can edit?",
            answer: "Yes, our AI SVG Code Generator creates editable SVG code that you can further customize in any vector graphics editor. This gives you the flexibility to use our AI-generated graphics as a starting point and then fine-tune them to your exact specifications."
        },
        {
            question: "How can AI-generated graphics improve my presentations?",
            answer: "AI-generated graphics can transform complex data and concepts into clear, engaging visuals that capture attention and improve understanding. Studies show that presentations with quality visuals are 43% more persuasive and information retention increases by up to 65% when paired with relevant images."
        },
        {
            question: "Can I use these graphics for commercial purposes?",
            answer: "Yes, all graphics generated by our AI tool can be used for both personal and commercial purposes. You retain full ownership of the content you create with our tool, making it perfect for business presentations, marketing materials, educational content, and more."
        },
        {
            question: "How does the AI ensure my graphics are visually appealing?",
            answer: "Our AI is trained on design principles including color theory, typography, visual hierarchy, and composition. It automatically applies these principles to create balanced, professional graphics that effectively communicate your information while maintaining visual appeal."
        },
        {
            question: "What makes FunBlocks AI Graphics different from other AI design tools?",
            answer: "FunBlocks AI Graphics specializes in information-rich visual content like infographics, flowcharts, and data visualizations, unlike general AI image generators. Our tool understands the principles of data visualization and information design, creating graphics that are not just visually appealing but also communicate complex information effectively."
        }
    ];
    // Enhanced Schema.org structured data for rich results
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "FunBlocks AI Graphics Generator",
        "applicationCategory": "DesignApplication",
        "operatingSystem": "Web",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "description": "Create stunning infographics, flowcharts, data charts, and SVG code in seconds with our AI-powered graphics generator. Transform complex information into engaging visuals without design skills.",
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "1250"
        },
        "featureList": [
            "AI Infographic Generator",
            "AI Flowchart Generator",
            "Data Chart Creator",
            "SVG Code Generator",
            "Visual Content Creation",
            "Process Diagram Designer",
            "Timeline Generator",
            "Comparison Table Maker",
            "Concept Map Builder",
            "Data Visualization Tool"
        ],
        "applicationSubCategory": "Data Visualization",
        "releaseNotes": "Latest version includes enhanced flowchart capabilities and improved data visualization options",
        "screenshot": "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_swot.png",
        "softwareVersion": "2.0"
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_common_SEO__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                title: "AI Graphics Generator: Create Infographics, Flowcharts & Data Visualizations | FunBlocks AI",
                description: "Transform complex information into stunning infographics, flowcharts, and data visualizations in seconds with our AI Graphics Generator. No design skills needed - perfect for presentations, social media, and reports.",
                keywords: "AI Infographic generator, AI flowchart generator, data chart creator, AI SVG Code generator, data visualization, visual content, infographic maker, flowchart maker, chart generator, SVG creator, AI graphics tool, process diagram, timeline generator, concept map, comparison table, FunBlocks AI",
                image: "/og-image.png",
                url: basePath + "/graphics",
                canonical: `https://www.funblocks.net${basePath}/graphics`
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_common_SEOStructuredData__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                faqs: faqs
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("script", {
                type: "application/ld+json",
                dangerouslySetInnerHTML: {
                    __html: JSON.stringify(schemaData)
                }
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                app: _utils_constants__WEBPACK_IMPORTED_MODULE_5__/* .APP_TYPE.graphics */ .IF.graphics
            })
        ]
    });
}
async function getStaticProps({ locale  }) {
    return {
        props: {
            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__.serverSideTranslations)(locale, [
                "common",
                "graphics"
            ])
        }
    };
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 5692:
/***/ ((module) => {

module.exports = require("@mui/material");

/***/ }),

/***/ 9048:
/***/ ((module) => {

module.exports = require("@mui/material/CircularProgress");

/***/ }),

/***/ 6999:
/***/ ((module) => {

module.exports = require("@react-oauth/google");

/***/ }),

/***/ 3665:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap");

/***/ }),

/***/ 9327:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/ChevronDown");

/***/ }),

/***/ 39:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/FileText");

/***/ }),

/***/ 7817:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/Magic");

/***/ }),

/***/ 450:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/TextLeft");

/***/ }),

/***/ 9077:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/FlowCascade");

/***/ }),

/***/ 8847:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/Link");

/***/ }),

/***/ 7636:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular");

/***/ }),

/***/ 1977:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxChecked");

/***/ }),

/***/ 7520:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxUnchecked");

/***/ }),

/***/ 3770:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/Edit");

/***/ }),

/***/ 3620:
/***/ ((module) => {

module.exports = require("@styled-icons/material");

/***/ }),

/***/ 384:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Close");

/***/ }),

/***/ 8953:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Lightbulb");

/***/ }),

/***/ 6131:
/***/ ((module) => {

module.exports = require("html-to-image");

/***/ }),

/***/ 6333:
/***/ ((module) => {

module.exports = require("immutability-helper");

/***/ }),

/***/ 1377:
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ 5460:
/***/ ((module) => {

module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ 4558:
/***/ ((module) => {

module.exports = require("next/config");

/***/ }),

/***/ 3280:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/app-router-context.js");

/***/ }),

/***/ 3539:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/detect-domain-locale.js");

/***/ }),

/***/ 4014:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/normalize-locale-path.js");

/***/ }),

/***/ 4964:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router-context.js");

/***/ }),

/***/ 3431:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-locale.js");

/***/ }),

/***/ 1751:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix.js");

/***/ }),

/***/ 3938:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/format-url.js");

/***/ }),

/***/ 1109:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/is-local-url.js");

/***/ }),

/***/ 8854:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/parse-path.js");

/***/ }),

/***/ 3297:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash.js");

/***/ }),

/***/ 7782:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/resolve-href.js");

/***/ }),

/***/ 9232:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ 968:
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ 1853:
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ 6689:
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ 997:
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 6621:
/***/ ((module) => {

module.exports = require("seedrandom");

/***/ }),

/***/ 7342:
/***/ ((module) => {

module.exports = require("url-regex");

/***/ }),

/***/ 6134:
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ 2210:
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ 4115:
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ 2728:
/***/ ((module) => {

module.exports = import("@xyflow/react");;

/***/ }),

/***/ 6197:
/***/ ((module) => {

module.exports = import("framer-motion");;

/***/ }),

/***/ 1024:
/***/ ((module) => {

module.exports = import("mermaid");;

/***/ }),

/***/ 6611:
/***/ ((module) => {

module.exports = import("qrcode.react");;

/***/ }),

/***/ 3196:
/***/ ((module) => {

module.exports = import("react-dnd");;

/***/ }),

/***/ 1152:
/***/ ((module) => {

module.exports = import("react-dnd-html5-backend");;

/***/ }),

/***/ 7987:
/***/ ((module) => {

module.exports = import("react-i18next");;

/***/ }),

/***/ 7425:
/***/ ((module) => {

module.exports = import("react-icons/ai");;

/***/ }),

/***/ 6157:
/***/ ((module) => {

module.exports = import("react-icons/bi");;

/***/ }),

/***/ 1301:
/***/ ((module) => {

module.exports = import("react-icons/fa");;

/***/ }),

/***/ 6542:
/***/ ((module) => {

module.exports = import("react-icons/gi");;

/***/ }),

/***/ 6905:
/***/ ((module) => {

module.exports = import("react-icons/md");;

/***/ }),

/***/ 4009:
/***/ ((module) => {

module.exports = import("react-intersection-observer");;

/***/ }),

/***/ 3135:
/***/ ((module) => {

module.exports = import("react-markdown");;

/***/ }),

/***/ 2017:
/***/ ((module) => {

module.exports = import("react-share");;

/***/ }),

/***/ 9521:
/***/ ((module) => {

module.exports = import("rehype-katex");;

/***/ }),

/***/ 1871:
/***/ ((module) => {

module.exports = import("rehype-raw");;

/***/ }),

/***/ 6809:
/***/ ((module) => {

module.exports = import("remark-gfm");;

/***/ }),

/***/ 9832:
/***/ ((module) => {

module.exports = import("remark-math");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [8797,1664,1951,4743,101,5408,6734,9199,7425,4665], () => (__webpack_exec__(5040)));
module.exports = __webpack_exports__;

})();