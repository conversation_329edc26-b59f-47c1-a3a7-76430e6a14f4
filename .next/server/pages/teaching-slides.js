"use strict";
(() => {
var exports = {};
exports.id = 3942;
exports.ids = [3942];
exports.modules = {

/***/ 4118:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Home),
/* harmony export */   "getStaticProps": () => (/* binding */ getStaticProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(968);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9199);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5460);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7426);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_5__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__]);
_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];






function Home() {
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_5___default()().publicRuntimeConfig;
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://www.funblocks.net";
    const canonicalUrl = `${siteUrl}${basePath}/teaching-slides`;
    // Schema.org structured data for rich results
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "FunBlocks AI EduSlides",
        "applicationCategory": "EducationalApplication",
        "operatingSystem": "Web",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "description": "AI-powered educational slide generation tool designed specifically for educators. Create professional teaching slides with pedagogical frameworks in minutes.",
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "ratingCount": "485"
        },
        "featureList": [
            "AI-powered slide generation",
            "Multiple pedagogical frameworks",
            "Rich multimedia support",
            "Teaching notes and guidance",
            "Interactive elements",
            "Assessment strategies",
            "Professional design principles",
            "Educational effectiveness"
        ],
        "creator": {
            "@type": "Organization",
            "name": "FunBlocks AI",
            "url": "https://www.funblocks.net"
        }
    };
    // FAQ structured data
    const faqData = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "How does FunBlocks AI EduSlides enhance teaching preparation?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "FunBlocks AI EduSlides revolutionizes teaching preparation by automatically generating comprehensive, framework-based teaching slides from any topic. It combines AI technology with proven pedagogical frameworks to create professional-quality educational content in minutes, not hours."
                }
            },
            {
                "@type": "Question",
                "name": "What teaching frameworks are supported?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "FunBlocks AI EduSlides supports multiple authoritative pedagogical frameworks including Bloom's Taxonomy, Marzano's Strategies, 5E Teaching Model, ADDIE Model, Gagne's Nine Events of Instruction, and Constructivist Learning approaches."
                }
            },
            {
                "@type": "Question",
                "name": "How is it different from regular presentation tools?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Unlike general presentation tools, FunBlocks AI EduSlides is specifically designed for education. It automatically incorporates pedagogical principles, generates teaching notes, suggests interactive activities, and creates assessment strategies."
                }
            },
            {
                "@type": "Question",
                "name": "Can I customize the generated teaching slides?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Absolutely! While AI generates the initial structure based on educational frameworks, you have complete control over customization. You can modify content, adjust timing, add resources, and tailor the slides to your specific classroom needs."
                }
            },
            {
                "@type": "Question",
                "name": "Is it suitable for different educational levels?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, FunBlocks AI EduSlides adapts to all educational levels from K-12 to higher education and corporate training. The AI automatically adjusts complexity, language, and activities based on the target audience."
                }
            },
            {
                "@type": "Question",
                "name": "Does it support multimedia content?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes! FunBlocks AI EduSlides automatically generates rich multimedia content including tables, mathematical formulas (LaTeX), flowcharts (Mermaid), comparison cards, and various visualization formats."
                }
            },
            {
                "@type": "Question",
                "name": "Is there a fee to use FunBlocks AI EduSlides?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "FunBlocks AI EduSlides offers free usage for all users. New users can enjoy a free trial, and all users have 10 free AI requests per day, simply by logging in."
                }
            }
        ]
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("title", {
                        children: "FunBlocks AI EduSlides: AI-Powered Teaching Slide Generator | Educational Design Tool"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "description",
                        content: "Transform your teaching with FunBlocks AI EduSlides. Generate professional, framework-based teaching slides in minutes. Support for Bloom's Taxonomy, 5E Model, Marzano's Strategies, and more. Perfect for K-12, higher education, and corporate training."
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "keywords",
                        content: "AI teaching slides, educational slide generator, teaching slide maker, AI lesson slides, pedagogical frameworks, Bloom's taxonomy slides, 5E model slides, Marzano strategies, educational design tool, teaching preparation, AI courseware, interactive teaching slides, educational technology, instructional design AI, teaching materials generator, classroom slides AI, educational content creation, AI for educators, teaching slide templates, professional teaching slides"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:title",
                        content: "FunBlocks AI EduSlides: AI-Powered Teaching Slide Generator"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:description",
                        content: "Create professional teaching slides with AI assistance. Support for multiple pedagogical frameworks, rich multimedia content, and comprehensive teaching notes. Perfect for educators at all levels."
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:type",
                        content: "website"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:url",
                        content: canonicalUrl
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:image",
                        content: `${siteUrl}/img/portfolio/thumbnails/aitools_teaching_slides_generated.png`
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:card",
                        content: "summary_large_image"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:title",
                        content: "FunBlocks AI EduSlides: AI-Powered Teaching Slide Generator"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:description",
                        content: "Transform your teaching preparation with AI-generated slides based on proven pedagogical frameworks. Create engaging, professional educational content in minutes."
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:image",
                        content: `${siteUrl}/img/portfolio/thumbnails/aitools_teaching_slides_generated.png`
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "robots",
                        content: "index, follow"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "author",
                        content: "FunBlocks AI"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "publisher",
                        content: "FunBlocks AI"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "copyright",
                        content: "FunBlocks AI"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "language",
                        content: "en"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "revisit-after",
                        content: "7 days"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "distribution",
                        content: "global"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "rating",
                        content: "general"
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("link", {
                        rel: "canonical",
                        href: canonicalUrl
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify(schemaData)
                        }
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify(faqData)
                        }
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                app: _utils_constants__WEBPACK_IMPORTED_MODULE_4__/* .APP_TYPE.teachingSlides */ .IF.teachingSlides
            })
        ]
    });
}
async function getStaticProps({ locale  }) {
    return {
        props: {
            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__.serverSideTranslations)(locale, [
                "teaching-slides",
                "common"
            ])
        }
    };
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 5692:
/***/ ((module) => {

module.exports = require("@mui/material");

/***/ }),

/***/ 9048:
/***/ ((module) => {

module.exports = require("@mui/material/CircularProgress");

/***/ }),

/***/ 6999:
/***/ ((module) => {

module.exports = require("@react-oauth/google");

/***/ }),

/***/ 3665:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap");

/***/ }),

/***/ 9327:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/ChevronDown");

/***/ }),

/***/ 39:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/FileText");

/***/ }),

/***/ 7817:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/Magic");

/***/ }),

/***/ 450:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/TextLeft");

/***/ }),

/***/ 9077:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/FlowCascade");

/***/ }),

/***/ 8847:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/Link");

/***/ }),

/***/ 7636:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular");

/***/ }),

/***/ 1977:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxChecked");

/***/ }),

/***/ 7520:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxUnchecked");

/***/ }),

/***/ 3770:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/Edit");

/***/ }),

/***/ 3620:
/***/ ((module) => {

module.exports = require("@styled-icons/material");

/***/ }),

/***/ 384:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Close");

/***/ }),

/***/ 8953:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Lightbulb");

/***/ }),

/***/ 6131:
/***/ ((module) => {

module.exports = require("html-to-image");

/***/ }),

/***/ 6333:
/***/ ((module) => {

module.exports = require("immutability-helper");

/***/ }),

/***/ 1377:
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ 5460:
/***/ ((module) => {

module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ 4558:
/***/ ((module) => {

module.exports = require("next/config");

/***/ }),

/***/ 3280:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/app-router-context.js");

/***/ }),

/***/ 3539:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/detect-domain-locale.js");

/***/ }),

/***/ 4014:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/normalize-locale-path.js");

/***/ }),

/***/ 4964:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router-context.js");

/***/ }),

/***/ 3431:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-locale.js");

/***/ }),

/***/ 1751:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix.js");

/***/ }),

/***/ 3938:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/format-url.js");

/***/ }),

/***/ 1109:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/is-local-url.js");

/***/ }),

/***/ 8854:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/parse-path.js");

/***/ }),

/***/ 3297:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash.js");

/***/ }),

/***/ 7782:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/resolve-href.js");

/***/ }),

/***/ 9232:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ 968:
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ 1853:
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ 6689:
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ 997:
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 6621:
/***/ ((module) => {

module.exports = require("seedrandom");

/***/ }),

/***/ 7342:
/***/ ((module) => {

module.exports = require("url-regex");

/***/ }),

/***/ 6134:
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ 2210:
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ 4115:
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ 2728:
/***/ ((module) => {

module.exports = import("@xyflow/react");;

/***/ }),

/***/ 6197:
/***/ ((module) => {

module.exports = import("framer-motion");;

/***/ }),

/***/ 1024:
/***/ ((module) => {

module.exports = import("mermaid");;

/***/ }),

/***/ 6611:
/***/ ((module) => {

module.exports = import("qrcode.react");;

/***/ }),

/***/ 3196:
/***/ ((module) => {

module.exports = import("react-dnd");;

/***/ }),

/***/ 1152:
/***/ ((module) => {

module.exports = import("react-dnd-html5-backend");;

/***/ }),

/***/ 7987:
/***/ ((module) => {

module.exports = import("react-i18next");;

/***/ }),

/***/ 7425:
/***/ ((module) => {

module.exports = import("react-icons/ai");;

/***/ }),

/***/ 6157:
/***/ ((module) => {

module.exports = import("react-icons/bi");;

/***/ }),

/***/ 1301:
/***/ ((module) => {

module.exports = import("react-icons/fa");;

/***/ }),

/***/ 6542:
/***/ ((module) => {

module.exports = import("react-icons/gi");;

/***/ }),

/***/ 6905:
/***/ ((module) => {

module.exports = import("react-icons/md");;

/***/ }),

/***/ 4009:
/***/ ((module) => {

module.exports = import("react-intersection-observer");;

/***/ }),

/***/ 3135:
/***/ ((module) => {

module.exports = import("react-markdown");;

/***/ }),

/***/ 2017:
/***/ ((module) => {

module.exports = import("react-share");;

/***/ }),

/***/ 9521:
/***/ ((module) => {

module.exports = import("rehype-katex");;

/***/ }),

/***/ 1871:
/***/ ((module) => {

module.exports = import("rehype-raw");;

/***/ }),

/***/ 6809:
/***/ ((module) => {

module.exports = import("remark-gfm");;

/***/ }),

/***/ 9832:
/***/ ((module) => {

module.exports = import("remark-math");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [8797,1664,1951,4743,101,5408,6734,9199], () => (__webpack_exec__(4118)));
module.exports = __webpack_exports__;

})();