"use strict";
(() => {
var exports = {};
exports.id = 5405;
exports.ids = [5405];
exports.modules = {

/***/ 3486:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Home),
/* harmony export */   "getServerSideProps": () => (/* binding */ getServerSideProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(968);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var _components_Home_Main__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(906);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5460);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _components_common_SEO__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(9173);
/* harmony import */ var _components_common_SEOStructuredData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(4665);
/* harmony import */ var _components_common_Footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(9030);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_9__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_Home_Main__WEBPACK_IMPORTED_MODULE_3__, _components_common_Footer__WEBPACK_IMPORTED_MODULE_8__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_Home_Main__WEBPACK_IMPORTED_MODULE_3__, _components_common_Footer__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);










function Home() {
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_5___default()().publicRuntimeConfig;
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)("common");
    // FAQ data for structured data
    const faqs = [
        {
            question: t("platform_faq_1_q"),
            answer: t("platform_faq_1_a")
        },
        {
            question: t("platform_faq_2_q"),
            answer: t("platform_faq_2_a")
        },
        {
            question: t("platform_faq_3_q"),
            answer: t("platform_faq_3_a")
        },
        {
            question: t("platform_faq_4_q"),
            answer: t("platform_faq_4_a")
        },
        {
            question: t("platform_faq_5_q"),
            answer: t("platform_faq_5_a")
        },
        {
            question: t("platform_faq_6_q"),
            answer: t("platform_faq_6_a")
        },
        {
            question: t("platform_faq_7_q"),
            answer: t("platform_faq_7_a")
        },
        {
            question: t("platform_faq_8_q"),
            answer: t("platform_faq_8_a")
        },
        {
            question: t("platform_faq_9_q"),
            answer: t("platform_faq_9_a")
        },
        {
            question: t("platform_faq_10_q"),
            answer: t("platform_faq_10_a")
        },
        {
            question: t("platform_faq_11_q"),
            answer: t("platform_faq_11_a")
        },
        {
            question: t("platform_faq_12_q"),
            answer: t("platform_faq_12_a")
        },
        {
            question: t("platform_faq_13_q"),
            answer: t("platform_faq_13_a")
        },
        {
            question: t("platform_faq_14_q"),
            answer: t("platform_faq_14_a")
        },
        {
            question: t("platform_faq_15_q"),
            answer: t("platform_faq_15_a")
        }
    ];
    const siteUrl = `https://www.funblocks.net${basePath}`;
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_common_SEO__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
                title: "FunBlocks AI - Visual AI Tools for Learning, Productivity & Creativity | Mind Maps, Infographics & More",
                description: "Transform your thinking with FunBlocks AI's visual tools suite: AI Mindmap, MindLadder, Infographic Generator, Slides Creator, Brainstorming Assistant, and more. Our research-backed tools help students, professionals, and creatives visualize complex concepts, enhance learning, and boost productivity through AI-powered visual thinking.",
                keywords: "AI visual tools, AI mindmap generator, AI infographic maker, AI presentation creator, visual learning tools, educational AI tools, AI brainstorming, mental models, cognitive learning tools, productivity AI, creative thinking tools, AI slides maker, visual thinking, concept visualization, learning frameworks, Bloom's taxonomy, Marzano framework, SOLO taxonomy, AI education tools, professional visualization tools",
                image: "/og-image.png",
                url: siteUrl,
                canonical: siteUrl
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_common_SEOStructuredData__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                faqs: faqs
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_Home_Main__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {})
            })
        ]
    });
}
// Using getServerSideProps instead of getStaticProps to ensure the latest data is fetched on each request
async function getServerSideProps({ locale  }) {
    return {
        props: {
            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4__.serverSideTranslations)(locale, [
                "common"
            ]),
            metaTitle: "FunBlocks AI - Visual AI Tools for Learning, Productivity & Creativity | Mind Maps, Infographics & More",
            metaDescription: "Transform your thinking with FunBlocks AI's visual tools suite: AI Mindmap, MindLadder, Infographic Generator, Slides Creator, Brainstorming Assistant, and more. Our research-backed tools help students, professionals, and creatives visualize complex concepts, enhance learning, and boost productivity through AI-powered visual thinking.",
            metaKeywords: "AI visual tools, AI mindmap generator, AI infographic maker, AI presentation creator, visual learning tools, educational AI tools, AI brainstorming, mental models, cognitive learning tools, productivity AI, creative thinking tools, AI slides maker, visual thinking, concept visualization, learning frameworks, Bloom's taxonomy, Marzano framework, SOLO taxonomy, AI education tools, professional visualization tools",
            metaImage: "/og-image.png",
            metaUrl: `https://www.funblocks.net${next_config__WEBPACK_IMPORTED_MODULE_5___default()().publicRuntimeConfig.basePath}`
        }
    };
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 6999:
/***/ ((module) => {

module.exports = require("@react-oauth/google");

/***/ }),

/***/ 1377:
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ 5460:
/***/ ((module) => {

module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ 4558:
/***/ ((module) => {

module.exports = require("next/config");

/***/ }),

/***/ 968:
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ 1853:
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ 6689:
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ 997:
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 6134:
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ 2210:
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ 6197:
/***/ ((module) => {

module.exports = import("framer-motion");;

/***/ }),

/***/ 1301:
/***/ ((module) => {

module.exports = import("react-icons/fa");;

/***/ }),

/***/ 6905:
/***/ ((module) => {

module.exports = import("react-icons/md");;

/***/ }),

/***/ 9034:
/***/ ((module) => {

module.exports = import("react-icons/si");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [1951,4743,5408,7425,8534,4665,906], () => (__webpack_exec__(3486)));
module.exports = __webpack_exports__;

})();