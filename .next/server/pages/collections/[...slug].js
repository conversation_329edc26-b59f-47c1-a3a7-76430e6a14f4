"use strict";
(() => {
var exports = {};
exports.id = 3789;
exports.ids = [3789];
exports.modules = {

/***/ 3038:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Home),
/* harmony export */   "getServerSideProps": () => (/* binding */ getServerSideProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(968);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9199);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5460);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7426);
/* harmony import */ var _components_common_SEO__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(9173);
/* harmony import */ var _utils_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3214);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_3__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);









const SEO_Data = {
    graphics: {
        title: "AI Graphics Generator: Infographics, Flowcharts, Data Charts & SVG Code | FunBlocks AI",
        description: "Create professional infographics, flowcharts, data charts, and SVG code with our AI Graphics Generator. Transform complex information into stunning visuals in seconds - no design skills needed.",
        keywords: "AI Infographic generator, AI flowchart generator, data chart creator, AI SVG Code generator, data visualization, visual content, infographic maker, flowchart maker, chart generator, SVG creator, AI graphics tool, FunBlocks AI"
    },
    mindmap: {
        title: "AI Reading Map: Mind Map Guided Reading | FunBlocks AI Tools",
        description: "Unlock deeper insights from your reading with AI Reading Map, a FunBlocks AI tool that creates structured reading guides and mind maps to enhance comprehension and knowledge retention.",
        keywords: "AI reading assistant, reading comprehension, mind mapping, knowledge discovery, book analysis, reading efficiency, intelligent reading, AI learning, educational tools, knowledge management, reading guide, FunBlocks AI"
    },
    reading: {
        title: "AI Reading Map: Mind Map Guided Reading | FunBlocks AI Tools",
        description: "Unlock deeper insights from your reading with AI Reading Map, a FunBlocks AI tool that creates structured reading guides and mind maps to enhance comprehension and knowledge retention.",
        keywords: "AI reading assistant, reading comprehension, mind mapping, knowledge discovery, book analysis, reading efficiency, intelligent reading, AI learning, educational tools, knowledge management, reading guide, FunBlocks AI"
    },
    movie: {
        title: "AI CineMap: AI-Powered Film Analysis & Mind Maps | FunBlocks AI Tools",
        description: "Unlock deeper insights into your favorite films with AI CineMap. Generate interactive mind maps analyzing narrative structure, cinematography, themes, and cultural context. Perfect for film enthusiasts, students, and content creators.",
        keywords: "AI film analysis, film mind map, cinema analysis, movie analysis, film studies, film education, narrative structure, cinematography, thematic analysis, cultural context, AI tools, film enthusiasts, content creators, interactive visualization, FunBlocks AI"
    }
};
function Home({ app , initial_items  }) {
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_5___default()().publicRuntimeConfig;
    let appPath = _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE */ .IF[app?.toLowerCase()];
    if (!appPath) {
        appPath = _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.mindmap */ .IF.mindmap;
    }
    const seo_data = SEO_Data[appPath.toLowerCase()];
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            seo_data && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_common_SEO__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                title: seo_data.title,
                description: seo_data.description,
                keywords: seo_data.keywords,
                image: "/og-image.png",
                url: basePath + "/" + appPath.toLowerCase()
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_Main__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
                app: appPath,
                isCollection: true,
                initial_showcases: initial_items
            })
        ]
    });
}
// export async function getStaticProps({ params, locale }) {
//     console.log('params...........', params, locale)
//     return {
//         props: {
//             ...(await serverSideTranslations(locale, ['common'])),
//         },
//     }
// }
async function getServerSideProps({ params , locale  }) {
    const mode = params?.slug[0] === _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.movie */ .IF.movie && "movie" || "book";
    const initial_items = await (0,_utils_data__WEBPACK_IMPORTED_MODULE_8__/* .fetchShowcases */ .v)(_utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.mindmap */ .IF.mindmap, mode);
    return {
        props: {
            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_4__.serverSideTranslations)(locale, [
                "common"
            ]),
            app: params?.slug[0] || "mindmap",
            initial_items
        }
    };
} // export async function getStaticPaths({ locales }) {
 //     // 原始的静态路径
 //     const slugs = [
 //       ["graphics"],
 //       ["mindmap"],
 //       ["brainstorming"],
 //       ["decision"],
 //     ];
 //     // 为每种语言生成路径
 //     const paths = slugs.flatMap((slug) =>
 //       locales.map((locale) => ({
 //         params: { slug }, // 保持动态路由参数
 //         locale,           // 指定语言环境
 //       }))
 //     );
 //     console.log(JSON.stringify(paths, null, 4));
 //     return {
 //       paths,
 //       fallback: false, // 未列出的路径返回 404
 //     };
 //   }
 //   export async function getStaticProps({ params, locale }) {
 //     const { slug } = params;
 //     return {
 //       props: {
 //         ...(await serverSideTranslations(locale, ['common'])), // 加载语言翻译
 //         app: slug[0] || '', // 动态参数
 //       },
 //     };
 //   }

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 3214:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "v": () => (/* binding */ fetchShowcases)
/* harmony export */ });
/* harmony import */ var _apiUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7597);
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7426);


const fetchShowcases = async (app, mode, init_size = 500)=>{
    const service = app === _constants__WEBPACK_IMPORTED_MODULE_0__/* .APP_TYPE.graphics */ .IF.graphics && "aiinsights" || "";
    try {
        const response = await fetch(`${_apiUtils__WEBPACK_IMPORTED_MODULE_1__/* .apiUrl */ .J}/ai/showcase_artifacts?app=${app}&mode=${mode || ""}&service=${service}&pageNum=0&pageSize=${init_size}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data?.data?.length) {
            return data.data;
        }
    } catch (error) {
        console.error("Fetch error:", error);
        throw error; // Rethrow the error after logging it
    }
};


/***/ }),

/***/ 5692:
/***/ ((module) => {

module.exports = require("@mui/material");

/***/ }),

/***/ 9048:
/***/ ((module) => {

module.exports = require("@mui/material/CircularProgress");

/***/ }),

/***/ 6999:
/***/ ((module) => {

module.exports = require("@react-oauth/google");

/***/ }),

/***/ 3665:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap");

/***/ }),

/***/ 9327:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/ChevronDown");

/***/ }),

/***/ 39:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/FileText");

/***/ }),

/***/ 7817:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/Magic");

/***/ }),

/***/ 450:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/TextLeft");

/***/ }),

/***/ 9077:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/FlowCascade");

/***/ }),

/***/ 8847:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/Link");

/***/ }),

/***/ 7636:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular");

/***/ }),

/***/ 1977:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxChecked");

/***/ }),

/***/ 7520:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxUnchecked");

/***/ }),

/***/ 3770:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/Edit");

/***/ }),

/***/ 3620:
/***/ ((module) => {

module.exports = require("@styled-icons/material");

/***/ }),

/***/ 384:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Close");

/***/ }),

/***/ 8953:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Lightbulb");

/***/ }),

/***/ 6131:
/***/ ((module) => {

module.exports = require("html-to-image");

/***/ }),

/***/ 6333:
/***/ ((module) => {

module.exports = require("immutability-helper");

/***/ }),

/***/ 1377:
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ 5460:
/***/ ((module) => {

module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ 4558:
/***/ ((module) => {

module.exports = require("next/config");

/***/ }),

/***/ 3280:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/app-router-context.js");

/***/ }),

/***/ 3539:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/detect-domain-locale.js");

/***/ }),

/***/ 4014:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/i18n/normalize-locale-path.js");

/***/ }),

/***/ 4964:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router-context.js");

/***/ }),

/***/ 3431:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-locale.js");

/***/ }),

/***/ 1751:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix.js");

/***/ }),

/***/ 3938:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/format-url.js");

/***/ }),

/***/ 1109:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/is-local-url.js");

/***/ }),

/***/ 8854:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/parse-path.js");

/***/ }),

/***/ 3297:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash.js");

/***/ }),

/***/ 7782:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/router/utils/resolve-href.js");

/***/ }),

/***/ 9232:
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ 968:
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ 1853:
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ 6689:
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ 997:
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 6621:
/***/ ((module) => {

module.exports = require("seedrandom");

/***/ }),

/***/ 7342:
/***/ ((module) => {

module.exports = require("url-regex");

/***/ }),

/***/ 6134:
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ 2210:
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ 4115:
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ 2728:
/***/ ((module) => {

module.exports = import("@xyflow/react");;

/***/ }),

/***/ 6197:
/***/ ((module) => {

module.exports = import("framer-motion");;

/***/ }),

/***/ 1024:
/***/ ((module) => {

module.exports = import("mermaid");;

/***/ }),

/***/ 6611:
/***/ ((module) => {

module.exports = import("qrcode.react");;

/***/ }),

/***/ 3196:
/***/ ((module) => {

module.exports = import("react-dnd");;

/***/ }),

/***/ 1152:
/***/ ((module) => {

module.exports = import("react-dnd-html5-backend");;

/***/ }),

/***/ 7987:
/***/ ((module) => {

module.exports = import("react-i18next");;

/***/ }),

/***/ 7425:
/***/ ((module) => {

module.exports = import("react-icons/ai");;

/***/ }),

/***/ 6157:
/***/ ((module) => {

module.exports = import("react-icons/bi");;

/***/ }),

/***/ 1301:
/***/ ((module) => {

module.exports = import("react-icons/fa");;

/***/ }),

/***/ 6542:
/***/ ((module) => {

module.exports = import("react-icons/gi");;

/***/ }),

/***/ 6905:
/***/ ((module) => {

module.exports = import("react-icons/md");;

/***/ }),

/***/ 4009:
/***/ ((module) => {

module.exports = import("react-intersection-observer");;

/***/ }),

/***/ 3135:
/***/ ((module) => {

module.exports = import("react-markdown");;

/***/ }),

/***/ 2017:
/***/ ((module) => {

module.exports = import("react-share");;

/***/ }),

/***/ 9521:
/***/ ((module) => {

module.exports = import("rehype-katex");;

/***/ }),

/***/ 1871:
/***/ ((module) => {

module.exports = import("rehype-raw");;

/***/ }),

/***/ 6809:
/***/ ((module) => {

module.exports = import("remark-gfm");;

/***/ }),

/***/ 9832:
/***/ ((module) => {

module.exports = import("remark-math");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [8797,1664,1951,4743,101,5408,6734,9199,7425], () => (__webpack_exec__(3038)));
module.exports = __webpack_exports__;

})();