{"version": 1, "config": {"env": {"NEXT_PUBLIC_API_URL": "https://www.funblocks.net/service", "PRICING_URL": "https://app.funblocks.net/#/aiplans"}, "webpack": null, "webpackDevMiddleware": null, "eslint": {"ignoreDuringBuilds": false}, "typescript": {"ignoreBuildErrors": false, "tsconfigPath": "tsconfig.json"}, "distDir": ".next", "cleanDistDir": true, "assetPrefix": "/aitools", "configOrigin": "next.config.js", "useFileSystemPublicRoutes": true, "generateEtags": true, "pageExtensions": ["tsx", "ts", "jsx", "js"], "target": "server", "poweredByHeader": true, "compress": true, "analyticsId": "", "images": {"deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/aitools/_next/image", "loader": "default", "loaderFile": "", "domains": ["www.google.com", "www.gstatic.com", "www.recaptcha.net"], "disableStaticImages": false, "minimumCacheTTL": 60, "formats": ["image/webp"], "dangerouslyAllowSVG": false, "contentSecurityPolicy": "script-src 'none'; frame-src 'none'; sandbox;", "contentDispositionType": "inline", "remotePatterns": [], "unoptimized": false}, "devIndicators": {"buildActivity": true, "buildActivityPosition": "bottom-right"}, "onDemandEntries": {"maxInactiveAge": 15000, "pagesBufferLength": 2}, "amp": {"canonicalBase": "/aitools"}, "basePath": "/aitools", "sassOptions": {}, "trailingSlash": false, "i18n": {"defaultLocale": "en", "locales": ["en", "zh"]}, "productionBrowserSourceMaps": false, "optimizeFonts": true, "excludeDefaultMomentLocales": true, "serverRuntimeConfig": {}, "publicRuntimeConfig": {"basePath": "/aitools", "basePathGraphics": "/aitools/graphics", "basePathMindmap": "/aitools/mindmap"}, "reactStrictMode": false, "httpAgentOptions": {"keepAlive": true}, "outputFileTracing": true, "staticPageGenerationTimeout": 60, "swcMinify": true, "experimental": {"clientRouterFilter": false, "preCompiledNextServer": false, "fetchCacheKeyPrefix": "", "middlewarePrefetch": "flexible", "optimisticClientCache": true, "manualClientBasePath": false, "legacyBrowsers": false, "newNextLinkBehavior": true, "cpus": 7, "sharedPool": true, "isrFlushToDisk": true, "workerThreads": false, "pageEnv": false, "optimizeCss": false, "nextScriptWorkers": false, "scrollRestoration": false, "externalDir": false, "disableOptimizedLoading": false, "gzipSize": true, "swcFileReading": true, "craCompat": false, "esmExternals": true, "appDir": false, "isrMemoryCacheSize": 52428800, "fullySpecified": false, "outputFileTracingRoot": "/Users/<USER>/workspace/wise-card", "swcTraceProfiling": false, "forceSwcTransforms": false, "largePageDataBytes": 128000, "enableUndici": false, "adjustFontFallbacks": false, "adjustFontFallbacksWithSizeAdjust": false, "typedRoutes": false, "instrumentationHook": false, "trustHostHeader": false}, "configFileName": "next.config.js"}, "appDir": "/Users/<USER>/workspace/wise-card", "relativeAppDir": "", "files": [".next/routes-manifest.json", ".next/server/pages-manifest.json", ".next/build-manifest.json", ".next/prerender-manifest.json", ".next/server/middleware-manifest.json", ".next/server/middleware-build-manifest.js", ".next/server/middleware-react-loadable-manifest.js", ".next/react-loadable-manifest.json", ".next/server/font-manifest.json", ".next/BUILD_ID", ".next/server/font-loader-manifest.js", ".next/server/font-loader-manifest.json"], "ignore": ["node_modules/next/dist/compiled/@ampproject/toolbox-optimizer/**/*"]}