:root{--primary:#4a6fff;--primary-dark:#3a5bdf;--secondary:#ff6b6b;--dark:#333;--light:#fff;--gray:#f5f7fa;--text:#333;--text-light:#555}body,html{padding:0;margin:0;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}a{color:inherit;text-decoration:none}*{box-sizing:border-box}.fill-available{width:100%;width:-webkit-fill-available;width:fill-available}.container{width:100%;padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto}@media (min-width:576px){.container{max-width:540px}}@media (min-width:768px){.container{max-width:720px}}@media (min-width:992px){.container{max-width:960px}}@media (min-width:1200px){.container{max-width:1140px}}.container-fluid,.container-lg,.container-md,.container-sm,.container-xl{width:100%;padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto}@media (min-width:576px){.container,.container-sm{max-width:540px}}@media (min-width:768px){.container,.container-md,.container-sm{max-width:720px}}@media (min-width:992px){.container,.container-lg,.container-md,.container-sm{max-width:960px}}@media (min-width:1200px){.container,.container-lg,.container-md,.container-sm,.container-xl{max-width:1140px}}.markdown-content{line-height:1.6;font-size:14px;color:#333}.markdown-content h1,.markdown-content h2,.markdown-content h3,.markdown-content h4,.markdown-content h5,.markdown-content h6{margin-top:12px;margin-bottom:8px;font-weight:600;line-height:1.25}.markdown-content h1{font-size:2em}.markdown-content h2{font-size:1.5em}.markdown-content h3{font-size:1.25em}.markdown-content p{margin-bottom:2px}.markdown-content code{padding:.2em .4em;background-color:rgba(27,31,35,.05);border-radius:3px}.markdown-content pre{padding:16px;overflow:auto;background-color:#f6f8fa;border-radius:3px}.markdown-content blockquote{padding:0 1em;color:#6a737d;border-left:.25em solid #dfe2e5;margin:0 0 16px}.markdown-content ol,.markdown-content ul{padding-left:2em;margin-bottom:16px}.gradient-border-textarea-bg{padding:10px;box-sizing:border-box;border:2px solid transparent;border-radius:8px;background-clip:padding-box,border-box;background-origin:border-box;background-image:linear-gradient(#fff,#fff),linear-gradient(to bottom right,#1e90ff,#f0f 50%,#f0f 0,#1e90ff)}.node{border-radius:4px;border:1px solid #fff;background-color:#fff;box-sizing:border-box}.react-flow__resize-control.handle{width:7px;height:7px;border-radius:100%;background-color:transparent;border-color:transparent}.react-flow__node{-webkit-user-select:auto;-moz-user-select:auto;user-select:auto}.circle-button{width:30px;height:30px;border-radius:15px;background-color:#fff;text-align:center;line-height:50px;cursor:pointer;border:1px solid gray;transition:background-color .3s ease,color .3s ease;align-items:center;justify-content:center;display:flex}.circle-button:hover{background-color:#f1f1f1}.circle-button-borderless{width:28px;height:28px;border-radius:14px;cursor:pointer;transition:background-color .3s ease,color .3s ease;align-items:center;justify-content:center;display:flex}.circle-button-borderless:hover{opacity:.5}.hoverStand{cursor:pointer;padding:8px 6px;margin:0;display:flex;align-items:center;font-size:14px;border-radius:3px;-webkit-user-select:none;-moz-user-select:none;user-select:none}.hoverStand:hover{background-color:rgba(0,0,0,.1)}.hover_shadow:hover{opacity:.8;box-shadow:0 0 8px #bbb}.transparent-background{border-radius:4px;transition:background-color .3s ease;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer}.transparent-background:hover{opacity:.8}.react-flow__node.highlight{background-color:#ff0072;color:#fff}.simple-floatingedges{flex-direction:column;display:flex;flex-grow:1;height:100%}.simple-floatingedges .react-flow__handle{width:10px;height:10px;background-color:#ccc}.simple-floatingedges .react-flow__handle-top{top:-17px}.simple-floatingedges .react-flow__handle-bottom{bottom:-17px}.simple-floatingedges .react-flow__handle-left{left:-17px}.simple-floatingedges .react-flow__handle-right{right:-17px}.simple-floatingedges .react-flow__node-custom{background:#fff;border:1px solid #1a192b;border-radius:3px;color:#222;font-size:12px;padding:10px;text-align:center;width:150px}.react-flow__edge-selector{pointer-events:all}.edgebutton{width:20px;height:20px;background:#fff;border:1px solid #555;cursor:pointer;border-radius:50%;font-size:12px;line-height:1;text-align:center;display:flex;align-items:center;justify-content:center;box-shadow:0 0 6px 2px rgba(0,0,0,.08);transition:box-shadow .2s ease-in-out,background-color .2s ease-in-out}.edgebutton:hover{box-shadow:0 0 6px 2px rgba(0,0,0,.15);background-color:#f0f0f0}