(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5408],{50980:function(e,t,i){"use strict";var s=i(85893),o=i(68029),r=i(88329),n=i(12553),a=i(50232),l=i(74032),c=i(37945),d=i(8186),h=i(90092),m=i(38491),p=i(43321),b=i(31837),x=i(52091),g=i(51306);let u=e=>{let{icon:t,title:i,industry:p,challenge:b,solution:g,results:u,imageSrc:f,onClick:w}=e;return(0,s.jsxs)(o.x,{bg:"white",rounded:"xl",shadow:"md",borderWidth:"1px",overflow:"hidden",h:"100%",transition:"transform 0.3s, box-shadow 0.3s",_hover:{transform:"translateY(-5px)",shadow:"lg"},display:"flex",flexDirection:"column",role:"group",touchAction:"manipulation",children:[(0,s.jsxs)(r.k,{p:{base:4,md:5},alignItems:"center",borderBottomWidth:"1px",borderBottomColor:"gray.100",bg:"blue.50",flexWrap:"wrap",gap:{base:2,md:0},children:[(0,s.jsx)(n.J,{as:t,w:{base:5,md:6},h:{base:5,md:6},color:"blue.500",mr:3,flexShrink:0,_groupHover:{transform:"scale(1.1)"},transition:"transform 0.2s"}),(0,s.jsxs)(o.x,{children:[(0,s.jsx)(a.X,{size:{base:"sm",md:"md"},lineHeight:"shorter",children:i}),(0,s.jsx)(l.C,{colorScheme:"purple",mt:1,fontSize:{base:"xs",md:"sm"},px:{base:1.5,md:2},py:{base:.5,md:1},children:p})]})]}),f&&(0,s.jsx)(c.E,{src:f,alt:i,height:{base:"160px",sm:"180px",md:"240px",lg:"360px"},width:"100%",objectFit:"contain",loading:"lazy"}),(0,s.jsx)(o.x,{p:{base:4,md:5},flex:"1",children:(0,s.jsxs)(d.g,{align:"start",spacing:{base:3,md:4},children:[(0,s.jsxs)(o.x,{children:[(0,s.jsx)(h.x,{fontWeight:"bold",color:"red.500",fontSize:{base:"sm",md:"md"},children:"Challenge:"}),(0,s.jsx)(h.x,{color:"gray.600",fontSize:{base:"sm",md:"md"},lineHeight:"1.6",children:b})]}),(0,s.jsxs)(o.x,{children:[(0,s.jsx)(h.x,{fontWeight:"bold",color:"blue.500",fontSize:{base:"sm",md:"md"},children:"Solution:"}),(0,s.jsx)(h.x,{color:"gray.600",fontSize:{base:"sm",md:"md"},lineHeight:"1.6",children:g})]}),u&&(0,s.jsxs)(o.x,{children:[(0,s.jsx)(h.x,{fontWeight:"bold",color:"green.500",fontSize:{base:"sm",md:"md"},children:"Results:"}),(0,s.jsx)(h.x,{color:"gray.600",fontSize:{base:"sm",md:"md"},lineHeight:"1.6",children:u})]})]})}),(0,s.jsx)(o.x,{p:{base:3,md:4},borderTopWidth:"1px",borderTopColor:"gray.100",bg:"gray.50",_groupHover:{bg:"blue.50"},transition:"background 0.2s",children:(0,s.jsx)(r.k,{justify:"flex-end",children:(0,s.jsx)(m.z,{rightIcon:(0,s.jsx)(x.Z1Y,{}),colorScheme:"blue",variant:"ghost",size:{base:"sm",md:"md"},onClick:w||(()=>window.scrollTo({top:0,behavior:"smooth"})),height:{base:"36px",md:"40px"},_groupHover:{bg:"blue.100",transform:"translateX(4px)"},transition:"all 0.2s",children:"Try it yourself"})})})]})},f=e=>{let{caseStudies:t,description:i,appname:r,onClick:n}=e,{t:c}=(0,b.$G)("common");return(0,s.jsx)(g.Z,{bg:"gray.50",id:"case-studies",children:(0,s.jsxs)(d.g,{spacing:{base:6,md:8},width:"100%",px:{base:3,md:0},children:[(0,s.jsx)(l.C,{colorScheme:"green",fontSize:{base:"sm",md:"md"},px:{base:2,md:3},py:{base:.5,md:1},borderRadius:"full",children:c("case_studies_badge","Real-World Applications")}),(0,s.jsx)(a.X,{size:{base:"md",md:"lg"},textAlign:"center",bgGradient:"linear(to-r, blue.400, green.400)",bgClip:"text",px:{base:2,md:0},lineHeight:{base:"1.3",md:"1.2"},children:c("case_studies_title","Success Stories: {appname} in Action",{appname:r})}),(0,s.jsx)(h.x,{fontSize:{base:"sm",md:"lg"},color:"gray.600",textAlign:"center",maxW:"2xl",mb:{base:2,md:4},px:{base:2,md:0},lineHeight:"1.6",children:i}),(0,s.jsx)(p.M,{columns:{base:1,md:2},spacing:{base:5,md:8},width:"100%",children:t.map((e,t)=>(0,s.jsx)(u,{icon:e.icon,title:e.title,industry:e.industry,challenge:e.challenge,solution:e.solution,results:e.results,imageSrc:e.imageSrc,onClick:n},t))}),(0,s.jsxs)(o.x,{textAlign:"center",mt:{base:4,md:6},width:"100%",px:{base:2,md:0},children:[(0,s.jsx)(h.x,{fontSize:{base:"md",md:"lg"},fontWeight:"medium",mb:{base:3,md:4},color:"gray.700",children:c("case_studies_cta_text","Ready to create your own success story?")}),(0,s.jsx)(m.z,{colorScheme:"blue",size:{base:"md",md:"lg"},rightIcon:(0,s.jsx)(x.Z1Y,{}),onClick:n||(()=>window.scrollTo({top:0,behavior:"smooth"})),px:{base:5,md:8},py:{base:6,md:6},height:{base:"48px",md:"auto"},borderRadius:"full",_hover:{transform:"translateY(-2px)"},transition:"all 0.3s",children:c("case_studies_cta_button","Start Creating Now")})]})]})})};t.Z=f},17157:function(e,t,i){"use strict";var s=i(85893);i(67294);var o=i(31046),r=i(96254),n=i(8186),a=i(50232),l=i(90092),c=i(88329),d=i(74032),h=i(68029),m=i(74703),p=i(21901),b=i(57167),x=i(4476),g=i(6007),u=i(43416),f=i(73636),w=i(61771),j=i(12553),_=i(52091),y=i(31837),k=i(51306);let v=e=>{var t;let{namespace:i="common",title:v="comparison_title",description:S="comparison_description",features:A,columns:I,bg:z="white",id:C,highlightColumn:M,productName:W="our product"}=e,{t:B}=(0,y.$G)(i),L=(0,o.ff)("purple.100","purple.900"),R=(0,o.ff)("purple.50","purple.800"),T=(0,o.ff)("gray.200","gray.700"),F=[{key:"product",label:B("comparison_product",W),highlight:!0},{key:"competitor1",label:B("comparison_competitor1","Competitor A")},{key:"competitor2",label:B("comparison_competitor2","Competitor B")},{key:"competitor3",label:B("comparison_competitor3","Competitor C")}],P=[{name:B("comparison_feature_1","AI-Generated Content"),tooltip:B("comparison_feature_1_tooltip","Content is automatically generated by artificial intelligence"),mindladder:!0,traditional:!1,mindmap:!1,online:!0},{name:B("comparison_feature_2","Progressive Complexity Layers"),tooltip:B("comparison_feature_2_tooltip","Content is organized in increasing levels of complexity"),mindladder:!0,traditional:!1,mindmap:!1,online:!1},{name:B("comparison_feature_3","Interactive Exploration"),tooltip:B("comparison_feature_3_tooltip","Users can click to explore concepts in more detail"),mindladder:!0,traditional:!1,mindmap:!0,online:!1},{name:B("comparison_feature_4","Visual Knowledge Mapping"),tooltip:B("comparison_feature_4_tooltip","Information is presented in visual, connected format"),mindladder:!0,traditional:!1,mindmap:!0,online:!1},{name:B("comparison_feature_5","Personalized Learning Paths"),tooltip:B("comparison_feature_5_tooltip","Content adapts based on user interests and interactions"),mindladder:!0,traditional:!1,mindmap:!1,online:!0},{name:B("comparison_feature_6","Multiple Perspectives"),tooltip:B("comparison_feature_6_tooltip","Presents different viewpoints on the same topic"),mindladder:!0,traditional:!1,mindmap:!1,online:!1},{name:B("comparison_feature_7","Counterintuitive Insights"),tooltip:B("comparison_feature_7_tooltip","Highlights surprising or paradoxical aspects of concepts"),mindladder:!0,traditional:!1,mindmap:!1,online:!1},{name:B("comparison_feature_8","Expert-Level Content"),tooltip:B("comparison_feature_8_tooltip","Provides advanced, cutting-edge information"),mindladder:!0,traditional:!0,mindmap:!1,online:!0},{name:B("comparison_feature_9","Cross-Concept Connections"),tooltip:B("comparison_feature_9_tooltip","Shows relationships between different concepts"),mindladder:!0,traditional:!1,mindmap:!0,online:!1},{name:B("comparison_feature_10","One-Click Generation"),tooltip:B("comparison_feature_10_tooltip","Creates complete learning resources with a single input"),mindladder:!0,traditional:!1,mindmap:!1,online:!1}],H=I||F,D=M||(null===(t=H.find(e=>e.highlight))||void 0===t?void 0:t.key)||H[0].key;return(0,s.jsx)(k.Z,{bg:z,id:C,children:(0,s.jsxs)(r.W,{maxW:"container.xl",px:{base:3,md:6},children:[(0,s.jsxs)(n.g,{spacing:{base:6,md:8},mb:{base:8,md:10},children:[(0,s.jsx)(a.X,{as:"h2",size:{base:"lg",md:"xl"},textAlign:"center",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",px:{base:2,md:0},lineHeight:{base:"1.3",md:"1.2"},children:v}),(0,s.jsx)(l.x,{fontSize:{base:"sm",md:"lg"},textAlign:"center",maxW:"3xl",px:{base:2,md:0},color:"gray.600",lineHeight:"1.6",children:S}),(0,s.jsxs)(c.k,{gap:{base:3,md:4},flexWrap:"wrap",justify:"center",width:"100%",children:[(0,s.jsx)(d.C,{colorScheme:"purple",p:{base:1.5,md:2},borderRadius:"md",fontSize:{base:"xs",md:"md"},textAlign:"center",children:B("comparison_winner","Winner in Key Categories")}),(0,s.jsx)(d.C,{colorScheme:"blue",p:{base:1.5,md:2},borderRadius:"md",fontSize:{base:"xs",md:"md"},textAlign:"center",children:B("comparison_value","Best Value")})]})]}),(0,s.jsx)(h.x,{overflowX:"auto",width:"100%",sx:{WebkitOverflowScrolling:"touch",scrollbarWidth:"thin","&::-webkit-scrollbar":{width:"6px",height:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.2)",borderRadius:"3px"}},children:(0,s.jsxs)(m.i,{variant:"simple",size:{base:"sm",md:"md"},borderWidth:"1px",borderColor:T,borderRadius:"lg",style:{minWidth:"650px"},children:[(0,s.jsx)(p.h,{children:(0,s.jsxs)(b.Tr,{bg:L,children:[(0,s.jsx)(x.Th,{borderBottomWidth:"2px",fontSize:{base:"xs",md:"sm"},py:{base:3,md:4},px:{base:2,md:4},children:B("comparison_feature","Feature")}),H.map(e=>(0,s.jsx)(x.Th,{borderBottomWidth:"2px",bg:e.key===D?"purple.100":void 0,color:e.key===D?"purple.800":void 0,fontSize:{base:"xs",md:"sm"},py:{base:3,md:4},px:{base:2,md:4},textAlign:"center",children:e.label},e.key))]})}),(0,s.jsx)(g.p,{children:(A||P).map((e,t)=>(0,s.jsxs)(b.Tr,{bg:t%2==0?"white":"gray.50",_hover:{bg:"gray.100"},transition:"background 0.2s",children:[(0,s.jsx)(u.Td,{fontWeight:"medium",py:{base:2,md:3},px:{base:2,md:4},fontSize:{base:"xs",md:"sm"},children:(0,s.jsxs)(f.U,{spacing:{base:1,md:2},children:[(0,s.jsx)(l.x,{children:e.name}),e.tooltip&&(0,s.jsx)(w.u,{label:e.tooltip,placement:"top",fontSize:{base:"xs",md:"sm"},hasArrow:!0,children:(0,s.jsx)("span",{children:(0,s.jsx)(j.J,{as:_.DAO,color:"gray.400",boxSize:{base:3,md:4}})})})]})}),H.map(t=>(0,s.jsx)(u.Td,{bg:t.key===D?R:void 0,color:t.key===D?"purple.800":void 0,py:{base:2,md:3},px:{base:2,md:4},textAlign:"center",children:e[t.key]?(0,s.jsx)(j.J,{as:_.l_A,color:"green.500",boxSize:{base:4,md:5}}):(0,s.jsx)(j.J,{as:_.aHS,color:"red.500",boxSize:{base:4,md:5}})},t.key))]},t))})]})})]})})};t.Z=v},19030:function(e,t,i){"use strict";var s=i(85893),o=i(22253),r=i.n(o);i(51306),t.Z=function(){return(0,s.jsx)("footer",{className:r().footer,children:(0,s.jsxs)("div",{className:"container",children:[(0,s.jsxs)("div",{className:r().footerContainer,children:[(0,s.jsxs)("div",{className:r().footerLinks,style:{marginRight:"20px"},children:[(0,s.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,s.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,s.jsxs)("div",{className:r().footerLinks,children:[(0,s.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,s.jsxs)("ul",{style:{marginLeft:14},children:[(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/welcome_extension",children:"FunBlocks AI Extension"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})})]})]}),(0,s.jsxs)("div",{className:r().footerLinks,children:[(0,s.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,s.jsxs)("ul",{style:{marginLeft:14},children:[(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})})]})]}),(0,s.jsxs)("div",{className:r().footerLinks,children:[(0,s.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,s.jsx)("ul",{style:{marginLeft:14},children:(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,s.jsx)("div",{className:r().footerContainer,children:(0,s.jsxs)("div",{className:r().footerLinks,children:[(0,s.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,s.jsxs)("div",{className:r().toolsGrid,children:[(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,s.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,s.jsx)("div",{className:r().copyright,children:(0,s.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},43706:function(e,t,i){"use strict";var s=i(85893);i(67294);var o=i(31046),r=i(43695),n=i(58779),a=i(88329),l=i(12553),c=i(50232),d=i(8455),h=i(90092),m=i(77475),p=i(8186),b=i(73636),x=i(57879),g=i(96254),u=i(38491),f=i(43321),w=i(52091),j=i(31837),_=i(51306);let y=e=>{let{title:t,description:i,icon:g,citations:u,color:f}=e,j=(0,o.ff)("white","gray.800"),_=(0,o.ff)("gray.200","gray.700"),y=(0,o.ff)("gray.600","gray.300"),k=(0,o.ff)("gray.800","white");return(0,s.jsxs)(r.Z,{bg:j,borderWidth:"1px",borderColor:_,borderRadius:"lg",overflow:"hidden",boxShadow:"md",height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s",_hover:{transform:"translateY(-4px)",boxShadow:"lg",borderColor:"".concat(f,".200")},role:"group",children:[(0,s.jsx)(n.O,{pb:0,pt:{base:4,md:5},px:{base:4,md:5},children:(0,s.jsxs)(a.k,{align:"center",mb:3,flexWrap:{base:"wrap",sm:"nowrap"},gap:{base:2,sm:0},children:[(0,s.jsx)(a.k,{w:{base:10,md:12},h:{base:10,md:12},align:"center",justify:"center",borderRadius:"full",bg:"".concat(f,".100"),color:"".concat(f,".500"),mr:{base:3,md:4},transition:"all 0.2s",_groupHover:{transform:"scale(1.05)",bg:"".concat(f,".200")},flexShrink:0,children:(0,s.jsx)(l.J,{as:g,boxSize:{base:5,md:6}})}),(0,s.jsx)(c.X,{size:{base:"sm",md:"md"},fontWeight:"semibold",color:k,lineHeight:"shorter",children:t})]})}),(0,s.jsx)(d.e,{pt:{base:1,md:2},pb:{base:3,md:4},px:{base:4,md:5},flex:"1",children:(0,s.jsx)(h.x,{fontSize:{base:"xs",md:"sm"},color:y,lineHeight:"1.6",children:i})}),(0,s.jsx)(m.e,{pt:0,pb:{base:4,md:5},px:{base:4,md:5},bg:"".concat(f,".50"),borderTop:"1px solid",borderColor:"".concat(f,".100"),children:(0,s.jsxs)(p.g,{align:"start",spacing:{base:1,md:1.5},width:"100%",children:[(0,s.jsx)(h.x,{fontSize:{base:"2xs",md:"xs"},fontWeight:"bold",color:"".concat(f,".700"),textTransform:"uppercase",letterSpacing:"wider",children:u.length>1?"Research Citations:":"Research Citation:"}),u.map((e,t)=>(0,s.jsxs)(b.U,{spacing:{base:1,md:2},fontSize:{base:"2xs",md:"xs"},color:y,align:"flex-start",children:[(0,s.jsx)(h.x,{noOfLines:1,maxW:{base:"90%",md:"90%"},children:e.text}),e.url&&(0,s.jsx)(x.r,{href:e.url,isExternal:!0,color:"".concat(f,".500"),_hover:{textDecoration:"none",color:"".concat(f,".600")},"aria-label":"View research citation",flexShrink:0,children:(0,s.jsx)(l.J,{as:w.CkN,boxSize:{base:2.5,md:3},transition:"transform 0.2s",_hover:{transform:"scale(1.2)"}})})]},t))]})})]})},k=e=>{let{namespace:t="common",title:i="research_title",description:o="research_description",researchAreas:r,bg:n="gray.50",id:d,buttons:m,productName:b="our product"}=e,{t:x}=(0,j.$G)(t),k=[{label:x("research_button_science","Science-Based"),icon:w.Qkp,colorScheme:"blue"},{label:x("research_button_data","Data-Driven"),icon:w.Op,colorScheme:"green"}],v=[{title:x("research_area_1_title","Research Area 1"),description:x("research_area_1_description","".concat(b," is built on solid research foundations in this area, providing significant benefits to users.")),icon:w.Qkp,color:"blue",citations:[{text:"Sweller, J. (2011). Cognitive load theory. Psychology of Learning and Motivation, 55, 37-76.",url:"https://doi.org/10.1016/B978-0-12-387691-1.00002-8"},{text:"Paas, F., & Ayres, P. (2014). Cognitive load theory: A broader view on the role of memory in learning and education. Educational Psychology Review, 26(2), 191-195.",url:"https://doi.org/10.1007/s10648-014-9263-5"}]},{title:x("research_area_2_title","Dual Coding Theory"),description:x("research_area_2_description","By combining visual mind maps with textual explanations, MindLadder leverages dual coding theory to engage multiple cognitive channels, enhancing memory formation and recall."),icon:w.iih,color:"purple",citations:[{text:"Paivio, A. (2014). Mind and its evolution: A dual coding theoretical approach. Psychology Press.",url:"https://doi.org/10.4324/9781315785233"},{text:"Clark, J. M., & Paivio, A. (1991). Dual coding theory and education. Educational Psychology Review, 3(3), 149-210.",url:"https://doi.org/10.1007/**********"}]},{title:x("research_area_3_title","Spaced Repetition"),description:x("research_area_3_description","MindLadder's layered approach naturally implements spaced repetition principles, revisiting core concepts at increasing levels of complexity to optimize long-term retention."),icon:w.Op,color:"green",citations:[{text:"Kang, S. H. (2016). Spaced repetition promotes efficient and effective learning: Policy implications for instruction. Policy Insights from the Behavioral and Brain Sciences, 3(1), 12-19.",url:"https://doi.org/10.1177/2372732215624708"},{text:"Dunlosky, J., et al. (2013). Improving students' learning with effective learning techniques: Promising directions from cognitive and educational psychology. Psychological Science in the Public Interest, 14(1), 4-58.",url:"https://doi.org/10.1177/1529100612453266"}]},{title:x("research_area_4_title","Desirable Difficulties"),description:x("research_area_4_description",'The counterintuitive aspects in MindLadder\'s higher tiers create "desirable difficulties" that enhance learning by requiring deeper processing and more effortful retrieval.'),icon:w.Mp$,color:"orange",citations:[{text:"Bjork, R. A., & Bjork, E. L. (2020). Desirable difficulties in theory and practice. Journal of Applied Research in Memory and Cognition, 9(4), 475-479.",url:"https://doi.org/10.1016/j.jarmac.2020.09.003"},{text:"Soderstrom, N. C., & Bjork, R. A. (2015). Learning versus performance: An integrative review. Perspectives on Psychological Science, 10(2), 176-199.",url:"https://doi.org/10.1177/1745691615569000"}]}],S=r||v,A=m||k;return(0,s.jsx)(_.Z,{bg:n,id:d,children:(0,s.jsxs)(g.W,{maxW:"container.xl",px:{base:3,md:6},children:[(0,s.jsxs)(p.g,{spacing:{base:6,md:8},mb:{base:8,md:12},children:[(0,s.jsx)(c.X,{as:"h2",size:{base:"lg",md:"xl"},textAlign:"center",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",px:{base:2,md:0},lineHeight:{base:"1.3",md:"1.2"},children:i}),(0,s.jsx)(h.x,{fontSize:{base:"sm",md:"lg"},textAlign:"center",maxW:"3xl",px:{base:2,md:0},color:"gray.600",lineHeight:"1.6",children:o}),A.length>0&&(0,s.jsx)(a.k,{gap:{base:2,md:4},flexWrap:"wrap",justify:"center",width:"100%",px:{base:2,md:0},children:A.map((e,t)=>(0,s.jsx)(u.z,{size:{base:"xs",md:"sm"},colorScheme:e.colorScheme||"blue",variant:"outline",leftIcon:e.icon&&(0,s.jsx)(l.J,{as:e.icon,boxSize:{base:3,md:4}}),px:{base:2,md:3},py:{base:1,md:2},borderRadius:"full",fontWeight:"medium",_hover:{transform:"translateY(-2px)",bg:"".concat(e.colorScheme||"blue",".50")},transition:"all 0.2s",children:e.label},t))})]}),(0,s.jsx)(f.M,{columns:{base:1,sm:2,lg:S.length},spacing:{base:4,md:6},width:"100%",children:S.map((e,t)=>(0,s.jsx)(y,{...e},t))})]})})};t.Z=k},51306:function(e,t,i){"use strict";var s=i(85893),o=i(91816),r=i(68029),n=i(8186),a=i(50232);let l=e=>{let{title:t,children:i,bg:l="white",gap:c,id:d,bgGradient:h}=e,m=(0,o.S)({base:!0,md:!1});return(0,s.jsx)(r.x,{id:d,bg:l,py:"4rem",alignItems:"center",width:"100%",bgGradient:h,children:(0,s.jsxs)(n.g,{className:"container",gap:c,alignItems:"center",children:[t&&(0,s.jsx)(a.X,{fontSize:m?"2xl":"3xl",mb:m?4:8,textAlign:"center",bgGradient:"linear(to-r, blue.400, purple.400)",bgClip:"text",children:t}),i]})})};t.Z=l},80966:function(e,t,i){"use strict";var s=i(85893);i(67294);var o=i(31046),r=i(68029),n=i(12553),a=i(90092),l=i(88329),c=i(73636),d=i(46572),h=i(96254),m=i(8186),p=i(50232),b=i(74032),x=i(43321),g=i(52091),u=i(31837),f=i(51306);let w=e=>{let{content:t,author:i,role:h,organization:m,rating:p,image:b}=e,x=(0,o.ff)("white","gray.800"),u=(0,o.ff)("gray.200","gray.700"),f=(0,o.ff)("gray.600","gray.300"),w=(0,o.ff)("gray.800","white"),j=(0,o.ff)("gray.500","gray.400");return(0,s.jsxs)(r.x,{bg:x,p:{base:4,sm:5,md:6},borderRadius:"lg",borderWidth:"1px",borderColor:u,boxShadow:"md",position:"relative",height:"100%",display:"flex",flexDirection:"column",transition:"all 0.3s",_hover:{transform:"translateY(-4px)",boxShadow:"lg"},children:[(0,s.jsx)(n.J,{as:g.fkU,position:"absolute",top:{base:3,md:4},left:{base:3,md:4},color:"purple.100",boxSize:{base:6,md:8},opacity:.6}),(0,s.jsx)(r.x,{mb:{base:4,md:6},pt:{base:5,md:6},pl:{base:5,md:6},flex:"1",children:(0,s.jsxs)(a.x,{fontSize:{base:"sm",md:"md"},fontStyle:"italic",color:f,lineHeight:"1.6",children:['"',t,'"']})}),(0,s.jsxs)(l.k,{direction:{base:"column",sm:"row"},align:{base:"flex-start",sm:"center"},spacing:{base:2,md:4},wrap:"wrap",gap:{base:2,md:3},children:[(0,s.jsxs)(c.U,{spacing:{base:3,md:4},align:"center",flex:"1",minW:"0",children:[(0,s.jsx)(d.q,{size:{base:"sm",md:"md"},name:i,src:b,boxShadow:"sm"}),(0,s.jsxs)(r.x,{minW:"0",children:[(0,s.jsx)(a.x,{fontWeight:"bold",fontSize:{base:"sm",md:"md"},color:w,noOfLines:1,children:i}),(0,s.jsx)(a.x,{fontSize:{base:"xs",md:"sm"},color:j,noOfLines:1,children:h}),(0,s.jsx)(a.x,{fontSize:{base:"2xs",md:"xs"},color:j,noOfLines:1,children:m})]})]}),(0,s.jsxs)(c.U,{spacing:1,justify:{base:"flex-start",sm:"flex-end"},alignSelf:{base:"flex-start",sm:"center"},mt:{base:1,sm:0},children:[[...Array(Math.floor(p))].map((e,t)=>(0,s.jsx)(n.J,{as:g.QJe,color:"yellow.400",boxSize:{base:3,md:4}},t)),p%1!=0&&(0,s.jsx)(n.J,{as:g.TtB,color:"yellow.400",boxSize:{base:3,md:4}})]})]})]})},j=e=>{let{namespace:t="common",appname:i="FunBlocks AI",rating:o="4.7",users:r="15,000",testimonials:d,bg:j="white",id:_}=e,{t:y}=(0,u.$G)(t),k=[{content:y("testimonial_1_content","MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I've cut my study time by 30% while improving my grades!"),author:"Emily Johnson",role:y("testimonial_1_role","Medical Student"),organization:"Stanford University",rating:5,image:"https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"},{content:y("testimonial_2_content","As a high school physics teacher, I've been using MindLadder to create learning materials for my students. The 8-tier system is brilliant for gradually building understanding of difficult concepts like quantum mechanics. My students' test scores have improved by 27% since implementing these knowledge ladders."),author:"David Martinez",role:y("testimonial_2_role","Physics Teacher"),organization:"Westlake High School",rating:5,image:"https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"},{content:y("testimonial_3_content","Our engineering team uses MindLadder to onboard new hires to our complex software architecture. The visual nature combined with progressive complexity layers has reduced our onboarding time by 40%. It's now our standard tool for knowledge transfer across departments."),author:"Sarah Chen",role:y("testimonial_3_role","Engineering Director"),organization:"Tesla",rating:4.5,image:"https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"},{content:y("testimonial_4_content","As someone with ADHD, traditional learning resources often overwhelm me. MindLadder's visual approach and ability to explore concepts at my own pace has been a game-changer. For the first time, I can see how different ideas connect and build on each other."),author:"Michael Rodriguez",role:y("testimonial_4_role","Software Developer"),organization:"Freelance",rating:5,image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"},{content:y("testimonial_5_content","I've been using MindLadder to prepare for my MBA courses. The way it connects business concepts through multiple layers of understanding has given me an edge in class discussions. I especially appreciate how it highlights counterintuitive aspects that challenge my assumptions."),author:"Jennifer Park",role:y("testimonial_5_role","MBA Student"),organization:"Harvard Business School",rating:4.5,image:"https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"},{content:y("testimonial_6_content","Our research team uses MindLadder to map interdisciplinary connections between AI ethics and policy. The ability to start with simple analogies and build to expert-level insights has helped us communicate complex ideas to stakeholders from diverse backgrounds."),author:"Dr. James Wilson",role:y("testimonial_6_role","Research Director"),organization:"MIT Media Lab",rating:5,image:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"}];return(0,s.jsx)(f.Z,{bg:j,id:_,children:(0,s.jsxs)(h.W,{maxW:"container.xl",px:{base:3,md:6},children:[(0,s.jsxs)(m.g,{spacing:{base:6,md:8},mb:{base:8,md:12},children:[(0,s.jsx)(p.X,{as:"h2",size:{base:"lg",md:"xl"},textAlign:"center",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",px:{base:2,md:0},lineHeight:{base:"1.3",md:"1.2"},children:y("testimonials_title","What Our Users Say")}),(0,s.jsx)(a.x,{fontSize:{base:"md",md:"lg"},textAlign:"center",maxW:"3xl",px:{base:2,md:0},color:"gray.600",children:y("testimonials_description","Join thousands of users who have transformed their experience with {appname}.",{appname:i})}),(0,s.jsxs)(l.k,{direction:{base:"column",sm:"row"},spacing:{base:3,md:4},gap:{base:3,md:4},width:"100%",justify:"center",align:"center",flexWrap:"wrap",children:[(0,s.jsx)(b.C,{colorScheme:"green",p:{base:1.5,md:2},borderRadius:"md",fontSize:{base:"sm",md:"md"},width:{base:"full",sm:"auto"},textAlign:"center",children:(0,s.jsxs)(c.U,{justify:{base:"center",sm:"flex-start"},children:[(0,s.jsx)(n.J,{as:g.QJe,boxSize:{base:3,md:4}}),(0,s.jsx)(a.x,{children:y("testimonials_rating","{rating} average rating",{rating:o})})]})}),(0,s.jsx)(b.C,{colorScheme:"blue",p:{base:1.5,md:2},borderRadius:"md",fontSize:{base:"sm",md:"md"},width:{base:"full",sm:"auto"},textAlign:"center",children:y("testimonials_users","10,000+ active users",{users:r})})]})]}),(0,s.jsx)(x.M,{columns:{base:1,md:2,lg:3},spacing:{base:5,md:8},width:"100%",children:(d||k).map((e,t)=>(0,s.jsx)(w,{...e},t))})]})})};t.Z=j},22253:function(e){e.exports={footer:"Footer_footer__GeF4n",footerContainer:"Footer_footerContainer__SNoPe",footerLogo:"Footer_footerLogo__1_fbO",footerLinks:"Footer_footerLinks__ylCiD",toolsGrid:"Footer_toolsGrid__YVg3f",copyright:"Footer_copyright__fqH5S"}}}]);