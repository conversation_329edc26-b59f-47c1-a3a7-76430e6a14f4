"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9769],{5108:function(e,t,n){n.d(t,{Aq:function(){return tV},HH:function(){return eE},Rr:function(){return tI},SM:function(){return t_},Sj:function(){return tD},ZX:function(){return tY},_K:function(){return eo},hR:function(){return ex},ll:function(){return tO},oR:function(){return p},x$:function(){return tj}});var o,r,i=n(85893),a=n(83840),l=n(12761),s=n(67294),d=n(52464),c=n(76248);n(73935);let u=(0,s.createContext)(null),g=u.Provider,f=l.Qj.error001();function p(e,t){let n=(0,s.useContext)(u);if(null===n)throw Error(f);return(0,d.s)(n,e,t)}function m(){let e=(0,s.useContext)(u);if(null===e)throw Error(f);return(0,s.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe}),[e])}let h={display:"none"},y={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},v="react-flow__node-desc",w="react-flow__edge-desc",b=e=>e.ariaLiveMessage;function x({rfId:e}){let t=p(b);return(0,i.jsx)("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:y,children:t})}function S({rfId:e,disableKeyboardA11y:t}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{id:`${v}-${e}`,style:h,children:["Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "]}),(0,i.jsx)("div",{id:`${w}-${e}`,style:h,children:"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."}),!t&&(0,i.jsx)(x,{rfId:e})]})}let C=e=>e.userSelectionActive?"none":"all",E=(0,s.forwardRef)(({position:e="top-left",children:t,className:n,style:o,...r},l)=>{let s=p(C),d=`${e}`.split("-");return(0,i.jsx)("div",{className:(0,a.Z)(["react-flow__panel",n,...d]),style:{...o,pointerEvents:s},ref:l,...r,children:t})});function k({proOptions:e,position:t="bottom-right"}){return e?.hideAttribution?null:(0,i.jsx)(E,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:(0,i.jsx)("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}let N=e=>{let t=[],n=[];for(let[,n]of e.nodeLookup)n.selected&&t.push(n.internals.userNode);for(let[,t]of e.edgeLookup)t.selected&&n.push(t);return{selectedNodes:t,selectedEdges:n}},M=e=>e.id;function j(e,t){return(0,c.X)(e.selectedNodes.map(M),t.selectedNodes.map(M))&&(0,c.X)(e.selectedEdges.map(M),t.selectedEdges.map(M))}function P({onSelectionChange:e}){let t=m(),{selectedNodes:n,selectedEdges:o}=p(N,j);return(0,s.useEffect)(()=>{let r={nodes:n,edges:o};e?.(r),t.getState().onSelectionChangeHandlers.forEach(e=>e(r))},[n,o,e]),null}let _=e=>!!e.onSelectionChangeHandlers;function R({onSelectionChange:e}){let t=p(_);return e||t?(0,i.jsx)(P,{onSelectionChange:e}):null}let D=[0,0],I={x:0,y:0,zoom:1},O=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","rfId"],A=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setPaneClickDistance:e.setPaneClickDistance}),L={translateExtent:l.k5,nodeOrigin:D,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function $(e){let{setNodes:t,setEdges:n,setMinZoom:o,setMaxZoom:r,setTranslateExtent:i,setNodeExtent:a,reset:l,setDefaultNodesAndEdges:d,setPaneClickDistance:u}=p(A,c.X),g=m();(0,s.useEffect)(()=>(d(e.defaultNodes,e.defaultEdges),()=>{f.current=L,l()}),[]);let f=(0,s.useRef)(L);return(0,s.useEffect)(()=>{for(let l of O){let s=e[l],d=f.current[l];s!==d&&void 0!==e[l]&&("nodes"===l?t(s):"edges"===l?n(s):"minZoom"===l?o(s):"maxZoom"===l?r(s):"translateExtent"===l?i(s):"nodeExtent"===l?a(s):"paneClickDistance"===l?u(s):"fitView"===l?g.setState({fitViewOnInit:s}):"fitViewOptions"===l?g.setState({fitViewOnInitOptions:s}):g.setState({[l]:s}))}f.current=e},O.map(t=>e[t])),null}function B(){return"undefined"!=typeof window&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null}let z="undefined"!=typeof document?document:null;function V(e=null,t={target:z,actInsideInputWithModifier:!0}){let[n,o]=(0,s.useState)(!1),r=(0,s.useRef)(!1),i=(0,s.useRef)(new Set([])),[a,d]=(0,s.useMemo)(()=>{if(null!==e){let t=Array.isArray(e)?e:[e],n=t.filter(e=>"string"==typeof e).map(e=>e.replace("+","\n").replace("\n\n","\n+").split("\n")),o=n.reduce((e,t)=>e.concat(...t),[]);return[n,o]}return[[],[]]},[e]);return(0,s.useEffect)(()=>{let n=t?.target||z;if(null!==e){let e=e=>{r.current=e.ctrlKey||e.metaKey||e.shiftKey;let n=(!r.current||r.current&&!t.actInsideInputWithModifier)&&(0,l.s$)(e);if(n)return!1;let s=H(e.code,d);i.current.add(e[s]),Z(a,i.current,!1)&&(e.preventDefault(),o(!0))},s=e=>{let n=(!r.current||r.current&&!t.actInsideInputWithModifier)&&(0,l.s$)(e);if(n)return!1;let s=H(e.code,d);Z(a,i.current,!0)?(o(!1),i.current.clear()):i.current.delete(e[s]),"Meta"===e.key&&i.current.clear(),r.current=!1},c=()=>{i.current.clear(),o(!1)};return n?.addEventListener("keydown",e),n?.addEventListener("keyup",s),window.addEventListener("blur",c),window.addEventListener("contextmenu",c),()=>{n?.removeEventListener("keydown",e),n?.removeEventListener("keyup",s),window.removeEventListener("blur",c),window.removeEventListener("contextmenu",c)}}},[e,o]),n}function Z(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function H(e,t){return t.includes(e)?"code":"key"}let F=()=>{let e=m();return(0,s.useMemo)(()=>({zoomIn:t=>{let{panZoom:n}=e.getState();return n?n.scaleBy(1.2,{duration:t?.duration}):Promise.resolve(!1)},zoomOut:t=>{let{panZoom:n}=e.getState();return n?n.scaleBy(1/1.2,{duration:t?.duration}):Promise.resolve(!1)},zoomTo:(t,n)=>{let{panZoom:o}=e.getState();return o?o.scaleTo(t,{duration:n?.duration}):Promise.resolve(!1)},getZoom:()=>e.getState().transform[2],setViewport:async(t,n)=>{let{transform:[o,r,i],panZoom:a}=e.getState();return a?(await a.setViewport({x:t.x??o,y:t.y??r,zoom:t.zoom??i},{duration:n?.duration}),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{let[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>{let{nodeLookup:n,minZoom:o,maxZoom:r,panZoom:i,domNode:a}=e.getState();if(!i||!a)return Promise.resolve(!1);let s=(0,l.J6)(n,t),{width:d,height:c}=(0,l.t_)(a);return(0,l.Qu)({nodes:s,width:d,height:c,minZoom:o,maxZoom:r,panZoom:i},t)},setCenter:async(t,n,o)=>{let{width:r,height:i,maxZoom:a,panZoom:l}=e.getState(),s=void 0!==o?.zoom?o.zoom:a;return l?(await l.setViewport({x:r/2-t*s,y:i/2-n*s,zoom:s},{duration:o?.duration}),Promise.resolve(!0)):Promise.resolve(!1)},fitBounds:async(t,n)=>{let{width:o,height:r,minZoom:i,maxZoom:a,panZoom:s}=e.getState(),d=(0,l.$i)(t,o,r,i,a,n?.padding??.1);return s?(await s.setViewport(d,{duration:n?.duration}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:(t,n={snapToGrid:!0})=>{let{transform:o,snapGrid:r,domNode:i}=e.getState();if(!i)return t;let{x:a,y:s}=i.getBoundingClientRect(),d={x:t.x-a,y:t.y-s};return(0,l.m)(d,o,n.snapToGrid,r)},flowToScreenPosition:t=>{let{transform:n,domNode:o}=e.getState();if(!o)return t;let{x:r,y:i}=o.getBoundingClientRect(),a=(0,l.oj)(t,n);return{x:a.x+r,y:a.y+i}}}),[])};function X(e,t){let n=[],o=new Map,r=[];for(let t of e){if("add"===t.type){r.push(t);continue}if("remove"===t.type||"replace"===t.type)o.set(t.id,[t]);else{let e=o.get(t.id);e?e.push(t):o.set(t.id,[t])}}for(let e of t){let t=o.get(e.id);if(!t){n.push(e);continue}if("remove"===t[0].type)continue;if("replace"===t[0].type){n.push({...t[0].item});continue}let r={...e};for(let e of t)!function(e,t){switch(e.type){case"select":t.selected=e.selected;break;case"position":void 0!==e.position&&(t.position=e.position),void 0!==e.dragging&&(t.dragging=e.dragging);break;case"dimensions":void 0!==e.dimensions&&(t.measured??={},t.measured.width=e.dimensions.width,t.measured.height=e.dimensions.height,e.setAttributes&&(t.width=e.dimensions.width,t.height=e.dimensions.height)),"boolean"==typeof e.resizing&&(t.resizing=e.resizing)}}(e,r);n.push(r)}return r.length&&r.forEach(e=>{void 0!==e.index?n.splice(e.index,0,{...e.item}):n.push({...e.item})}),n}function T(e,t){return{id:e,type:"select",selected:t}}function W(e,t=new Set,n=!1){let o=[];for(let[r,i]of e){let e=t.has(r);(void 0!==i.selected||e)&&i.selected!==e&&(n&&(i.selected=e),o.push(T(i.id,e)))}return o}function K({items:e=[],lookup:t}){let n=[],o=new Map(e.map(e=>[e.id,e]));for(let[o,r]of e.entries()){let e=t.get(r.id),i=e?.internals?.userNode??e;void 0!==i&&i!==r&&n.push({id:r.id,item:r,type:"replace"}),void 0===i&&n.push({item:r,type:"add",index:o})}for(let[e]of t){let t=o.get(e);void 0===t&&n.push({id:e,type:"remove"})}return n}function Q(e){return{id:e.id,type:"remove"}}let Y=e=>(0,l.Vt)(e),U=e=>(0,l.J3)(e);function G(e){return(0,s.forwardRef)(e)}let J="undefined"!=typeof window?s.useLayoutEffect:s.useEffect;function q(e){let[t,n]=(0,s.useState)(BigInt(0)),[o]=(0,s.useState)(()=>{var e;let t;return e=()=>n(e=>e+BigInt(1)),t=[],{get:()=>t,reset:()=>{t=[]},push:n=>{t.push(n),e()}}});return J(()=>{let t=o.get();t.length&&(e(t),o.reset())},[t]),o}let ee=(0,s.createContext)(null);function et({children:e}){let t=m(),n=(0,s.useCallback)(e=>{let{nodes:n=[],setNodes:o,hasDefaultNodes:r,onNodesChange:i,nodeLookup:a}=t.getState(),l=n;for(let t of e)l="function"==typeof t?t(l):t;r?o(l):i&&i(K({items:l,lookup:a}))},[]),o=q(n),r=(0,s.useCallback)(e=>{let{edges:n=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:i,edgeLookup:a}=t.getState(),l=n;for(let t of e)l="function"==typeof t?t(l):t;r?o(l):i&&i(K({items:l,lookup:a}))},[]),a=q(r),l=(0,s.useMemo)(()=>({nodeQueue:o,edgeQueue:a}),[]);return(0,i.jsx)(ee.Provider,{value:l,children:e})}let en=e=>!!e.panZoom;function eo(){let e=F(),t=m(),n=function(){let e=(0,s.useContext)(ee);if(!e)throw Error("useBatchContext must be used within a BatchProvider");return e}(),o=p(en),r=(0,s.useMemo)(()=>{let e=e=>t.getState().nodeLookup.get(e),o=e=>{n.nodeQueue.push(e)},r=e=>{n.edgeQueue.push(e)},i=e=>{let{nodeLookup:n,nodeOrigin:o}=t.getState(),r=Y(e)?e:n.get(e.id),i=r.parentId?(0,l.ZB)(r.position,r.measured,r.parentId,n,o):r.position,a={...r,position:i,width:r.measured?.width??r.width,height:r.measured?.height??r.height};return(0,l.PS)(a)},a=(e,t,n={replace:!1})=>{o(o=>o.map(o=>{if(o.id===e){let e="function"==typeof t?t(o):t;return n.replace&&Y(e)?e:{...o,...e}}return o}))},s=(e,t,n={replace:!1})=>{r(o=>o.map(o=>{if(o.id===e){let e="function"==typeof t?t(o):t;return n.replace&&U(e)?e:{...o,...e}}return o}))};return{getNodes:()=>t.getState().nodes.map(e=>({...e})),getNode:t=>e(t)?.internals.userNode,getInternalNode:e,getEdges:()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},getEdge:e=>t.getState().edgeLookup.get(e),setNodes:o,setEdges:r,addNodes:e=>{let t=Array.isArray(e)?e:[e];n.nodeQueue.push(e=>[...e,...t])},addEdges:e=>{let t=Array.isArray(e)?e:[e];n.edgeQueue.push(e=>[...e,...t])},toObject:()=>{let{nodes:e=[],edges:n=[],transform:o}=t.getState(),[r,i,a]=o;return{nodes:e.map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:r,y:i,zoom:a}}},deleteElements:async({nodes:e=[],edges:n=[]})=>{let{nodes:o,edges:r,onNodesDelete:i,onEdgesDelete:a,triggerNodeChanges:s,triggerEdgeChanges:d,onDelete:c,onBeforeDelete:u}=t.getState(),{nodes:g,edges:f}=await (0,l.WD)({nodesToRemove:e,edgesToRemove:n,nodes:o,edges:r,onBeforeDelete:u}),p=f.length>0,m=g.length>0;if(p){let e=f.map(Q);a?.(f),d(e)}if(m){let e=g.map(Q);i?.(g),s(e)}return(m||p)&&c?.({nodes:g,edges:f}),{deletedNodes:g,deletedEdges:f}},getIntersectingNodes:(e,n=!0,o)=>{let r=(0,l.J$)(e),a=r?e:i(e),s=void 0!==o;return a?(o||t.getState().nodes).filter(o=>{let i=t.getState().nodeLookup.get(o.id);if(i&&!r&&(o.id===e.id||!i.internals.positionAbsolute))return!1;let d=(0,l.PS)(s?o:i),c=(0,l.lp)(d,a);return n&&c>0||c>=a.width*a.height}):[]},isNodeIntersecting:(e,t,n=!0)=>{let o=(0,l.J$)(e),r=o?e:i(e);if(!r)return!1;let a=(0,l.lp)(r,t);return n&&a>0||a>=r.width*r.height},updateNode:a,updateNodeData:(e,t,n={replace:!1})=>{a(e,e=>{let o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},n)},updateEdge:s,updateEdgeData:(e,t,n={replace:!1})=>{s(e,e=>{let o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},n)},getNodesBounds:e=>{let{nodeLookup:n,nodeOrigin:o}=t.getState();return(0,l.RX)(e,{nodeLookup:n,nodeOrigin:o})},getHandleConnections:({type:e,id:n,nodeId:o})=>Array.from(t.getState().connectionLookup.get(`${o}-${e}-${n??null}`)?.values()??[])}},[]);return(0,s.useMemo)(()=>({...r,...e,viewportInitialized:o}),[o])}let er=e=>e.selected,ei={actInsideInputWithModifier:!1},ea="undefined"!=typeof window?window:void 0,el={position:"absolute",width:"100%",height:"100%",top:0,left:0},es=e=>({userSelectionActive:e.userSelectionActive,lib:e.lib});function ed({onPaneContextMenu:e,zoomOnScroll:t=!0,zoomOnPinch:n=!0,panOnScroll:o=!1,panOnScrollSpeed:r=.5,panOnScrollMode:a=l.IY.Free,zoomOnDoubleClick:d=!0,panOnDrag:u=!0,defaultViewport:g,translateExtent:f,minZoom:h,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:w=!0,children:b,noWheelClassName:x,noPanClassName:S,onViewportChange:C,isControlledViewport:E,paneClickDistance:k}){let N=m(),M=(0,s.useRef)(null),{userSelectionActive:j,lib:P}=p(es,c.X),_=V(v),R=(0,s.useRef)();!function(e){let t=m();(0,s.useEffect)(()=>{let n=()=>{if(!e.current)return!1;let n=(0,l.t_)(e.current);(0===n.height||0===n.width)&&t.getState().onError?.("004",l.Qj.error004()),t.setState({width:n.width||500,height:n.height||500})};if(e.current){n(),window.addEventListener("resize",n);let t=new ResizeObserver(()=>n());return t.observe(e.current),()=>{window.removeEventListener("resize",n),t&&e.current&&t.unobserve(e.current)}}},[])}(M);let D=(0,s.useCallback)(e=>{C?.({x:e[0],y:e[1],zoom:e[2]}),E||N.setState({transform:e})},[C,E]);return(0,s.useEffect)(()=>{if(M.current){R.current=(0,l.X6)({domNode:M.current,minZoom:h,maxZoom:y,translateExtent:f,viewport:g,paneClickDistance:k,onDraggingChange:e=>N.setState({paneDragging:e}),onPanZoomStart:(e,t)=>{let{onViewportChangeStart:n,onMoveStart:o}=N.getState();o?.(e,t),n?.(t)},onPanZoom:(e,t)=>{let{onViewportChange:n,onMove:o}=N.getState();o?.(e,t),n?.(t)},onPanZoomEnd:(e,t)=>{let{onViewportChangeEnd:n,onMoveEnd:o}=N.getState();o?.(e,t),n?.(t)}});let{x:e,y:t,zoom:n}=R.current.getViewport();return N.setState({panZoom:R.current,transform:[e,t,n],domNode:M.current.closest(".react-flow")}),()=>{R.current?.destroy()}}},[]),(0,s.useEffect)(()=>{R.current?.update({onPaneContextMenu:e,zoomOnScroll:t,zoomOnPinch:n,panOnScroll:o,panOnScrollSpeed:r,panOnScrollMode:a,zoomOnDoubleClick:d,panOnDrag:u,zoomActivationKeyPressed:_,preventScrolling:w,noPanClassName:S,userSelectionActive:j,noWheelClassName:x,lib:P,onTransformChange:D})},[e,t,n,o,r,a,d,u,_,w,S,j,x,P,D]),(0,i.jsx)("div",{className:"react-flow__renderer",ref:M,style:el,children:b})}let ec=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function eu(){let{userSelectionActive:e,userSelectionRect:t}=p(ec,c.X);return e&&t?(0,i.jsx)("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}let eg=(e,t)=>n=>{n.target===t.current&&e?.(n)},ef=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging});function ep({isSelecting:e,selectionKeyPressed:t,selectionMode:n=l.oW.Full,panOnDrag:o,selectionOnDrag:r,onSelectionStart:d,onSelectionEnd:u,onPaneClick:g,onPaneContextMenu:f,onPaneScroll:h,onPaneMouseEnter:y,onPaneMouseMove:v,onPaneMouseLeave:w,children:b}){let x=(0,s.useRef)(null),S=m(),C=(0,s.useRef)(0),E=(0,s.useRef)(0),k=(0,s.useRef)(),N=(0,s.useRef)(new Map),{userSelectionActive:M,elementsSelectable:j,dragging:P}=p(ef,c.X),_=j&&(e||M),R=(0,s.useRef)(!1),D=(0,s.useRef)(!1),I=()=>{S.setState({userSelectionActive:!1,userSelectionRect:null}),C.current=0,E.current=0},O=e=>{if(R.current){R.current=!1;return}g?.(e),S.getState().resetSelectedElements(),S.setState({nodesSelectionActive:!1})},A=e=>{if(Array.isArray(o)&&o?.includes(2)){e.preventDefault();return}f?.(e)},L=t=>{let{resetSelectedElements:n,domNode:o,edgeLookup:r}=S.getState();if(k.current=o?.getBoundingClientRect(),!j||!e||0!==t.button||t.target!==x.current||!k.current)return;for(let[e,n]of(t.target?.setPointerCapture?.(t.pointerId),D.current=!0,R.current=!1,N.current=new Map,r))N.current.set(n.source,N.current.get(n.source)?.add(e)||new Set([e])),N.current.set(n.target,N.current.get(n.target)?.add(e)||new Set([e]));let{x:i,y:a}=(0,l.wv)(t.nativeEvent,k.current);n(),S.setState({userSelectionRect:{width:0,height:0,startX:i,startY:a,x:i,y:a}}),d?.(t)},$=e=>{let{userSelectionRect:t,edgeLookup:o,transform:r,nodeLookup:i,triggerNodeChanges:a,triggerEdgeChanges:s}=S.getState();if(!k.current||!t)return;R.current=!0;let{x:d,y:c}=(0,l.wv)(e.nativeEvent,k.current),{startX:u,startY:g}=t,f={startX:u,startY:g,x:d<u?d:u,y:c<g?c:g,width:Math.abs(d-u),height:Math.abs(c-g)},p=(0,l.f5)(i,f,r,n===l.oW.Partial,!0),m=new Set,h=new Set;for(let e of p){h.add(e.id);let t=N.current.get(e.id);if(t)for(let e of t)m.add(e)}if(C.current!==h.size){C.current=h.size;let e=W(i,h,!0);a(e)}if(E.current!==m.size){E.current=m.size;let e=W(o,m);s(e)}S.setState({userSelectionRect:f,userSelectionActive:!0,nodesSelectionActive:!1})},B=e=>{if(0!==e.button||!D.current)return;e.target?.releasePointerCapture?.(e.pointerId);let{userSelectionRect:n}=S.getState();!M&&n&&e.target===x.current&&O?.(e),C.current>0&&S.setState({nodesSelectionActive:!0}),I(),u?.(e),(t||r)&&(R.current=!1),D.current=!1},z=!0===o||Array.isArray(o)&&o.includes(0);return(0,i.jsxs)("div",{className:(0,a.Z)(["react-flow__pane",{draggable:z,dragging:P,selection:e}]),onClick:_?void 0:eg(O,x),onContextMenu:eg(A,x),onWheel:eg(h?e=>h(e):void 0,x),onPointerEnter:_?void 0:y,onPointerDown:_?L:v,onPointerMove:_?$:v,onPointerUp:_?B:void 0,onPointerLeave:w,ref:x,style:el,children:[b,(0,i.jsx)(eu,{})]})}function em({id:e,store:t,unselect:n=!1,nodeRef:o}){let{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeLookup:s,onError:d}=t.getState(),c=s.get(e);if(!c){d?.("012",l.Qj.error012(e));return}t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame(()=>o?.current?.blur())):r([e])}function eh({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:o,nodeId:r,isSelectable:i,nodeClickDistance:a}){let d=m(),[c,u]=(0,s.useState)(!1),g=(0,s.useRef)();return(0,s.useEffect)(()=>{g.current=(0,l.oC)({getStoreItems:()=>d.getState(),onNodeMouseDown:t=>{em({id:t,store:d,nodeRef:e})},onDragStart:()=>{u(!0)},onDragStop:()=>{u(!1)}})},[]),(0,s.useEffect)(()=>{if(t)g.current?.destroy();else if(e.current)return g.current?.update({noDragClassName:n,handleSelector:o,domNode:e.current,isSelectable:i,nodeId:r,nodeClickDistance:a}),()=>{g.current?.destroy()}},[n,o,t,i,e,r]),c}let ey=e=>t=>t.selected&&(t.draggable||e&&void 0===t.draggable);function ev(){let e=m(),t=(0,s.useCallback)(t=>{let{nodeExtent:n,snapToGrid:o,snapGrid:r,nodesDraggable:i,onError:a,updateNodePositions:s,nodeLookup:d,nodeOrigin:c}=e.getState(),u=new Map,g=ey(i),f=o?r[0]:5,p=o?r[1]:5,m=t.direction.x*f*t.factor,h=t.direction.y*p*t.factor;for(let[,e]of d){if(!g(e))continue;let t={x:e.internals.positionAbsolute.x+m,y:e.internals.positionAbsolute.y+h};o&&(t=(0,l._2)(t,r));let{position:i,positionAbsolute:s}=(0,l.q7)({nodeId:e.id,nextPosition:t,nodeLookup:d,nodeExtent:n,nodeOrigin:c,onError:a});e.position=i,e.internals.positionAbsolute=s,u.set(e.id,e)}s(u)},[]);return t}let ew=(0,s.createContext)(null),eb=ew.Provider;ew.Consumer;let ex=()=>{let e=(0,s.useContext)(ew);return e},eS=e=>({connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName,rfId:e.rfId}),eC=(e,t,n)=>o=>{let{connectionClickStartHandle:r,connectionMode:i,connection:a}=o,{fromHandle:s,toHandle:d,isValid:c}=a,u=d?.nodeId===e&&d?.id===t&&d?.type===n;return{connectingFrom:s?.nodeId===e&&s?.id===t&&s?.type===n,connectingTo:u,clickConnecting:r?.nodeId===e&&r?.id===t&&r?.type===n,isPossibleEndHandle:i===l.jD.Strict?s?.type!==n:e!==s?.nodeId||t!==s?.id,connectionInProcess:!!s,valid:u&&c}},eE=(0,s.memo)(G(function({type:e="source",position:t=l.Ly.Top,isValidConnection:n,isConnectable:o=!0,isConnectableStart:r=!0,isConnectableEnd:s=!0,id:d,onConnect:u,children:g,className:f,onMouseDown:h,onTouchStart:y,...v},w){let b=d||null,x="target"===e,S=m(),C=ex(),{connectOnClick:E,noPanClassName:k,rfId:N}=p(eS,c.X),{connectingFrom:M,connectingTo:j,clickConnecting:P,isPossibleEndHandle:_,connectionInProcess:R,valid:D}=p(eC(C,b,e),c.X);C||S.getState().onError?.("010",l.Qj.error010());let I=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=S.getState(),r={...t,...e};if(o){let{edges:e,setEdges:t}=S.getState();t((0,l.Z_)(r,e))}n?.(r),u?.(r)},O=e=>{if(!C)return;let t=(0,l.N5)(e.nativeEvent);if(r&&(t&&0===e.button||!t)){let t=S.getState();l.Ql.onPointerDown(e.nativeEvent,{autoPanOnConnect:t.autoPanOnConnect,connectionMode:t.connectionMode,connectionRadius:t.connectionRadius,domNode:t.domNode,nodeLookup:t.nodeLookup,lib:t.lib,isTarget:x,handleId:b,nodeId:C,flowId:t.rfId,panBy:t.panBy,cancelConnection:t.cancelConnection,onConnectStart:t.onConnectStart,onConnectEnd:t.onConnectEnd,updateConnection:t.updateConnection,onConnect:I,isValidConnection:n||t.isValidConnection,getTransform:()=>S.getState().transform,getFromHandle:()=>S.getState().connection.fromHandle,autoPanSpeed:t.autoPanSpeed})}t?h?.(e):y?.(e)},A=t=>{let{onClickConnectStart:o,onClickConnectEnd:i,connectionClickStartHandle:a,connectionMode:s,isValidConnection:d,lib:c,rfId:u,nodeLookup:g,connection:f}=S.getState();if(!C||!a&&!r)return;if(!a){o?.(t.nativeEvent,{nodeId:C,handleId:b,handleType:e}),S.setState({connectionClickStartHandle:{nodeId:C,type:e,id:b}});return}let p=(0,l.S2)(t.target),{connection:m,isValid:h}=l.Ql.isValid(t.nativeEvent,{handle:{nodeId:C,id:b,type:e},connectionMode:s,fromNodeId:a.nodeId,fromHandleId:a.id||null,fromType:a.type,isValidConnection:n||d,flowId:u,doc:p,lib:c,nodeLookup:g});h&&m&&I(m);let y=structuredClone(f);delete y.inProgress,y.toPosition=y.toHandle?y.toHandle.position:null,i?.(t,y),S.setState({connectionClickStartHandle:null})};return(0,i.jsx)("div",{"data-handleid":b,"data-nodeid":C,"data-handlepos":t,"data-id":`${N}-${C}-${b}-${e}`,className:(0,a.Z)(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",k,f,{source:!x,target:x,connectable:o,connectablestart:r,connectableend:s,clickconnecting:P,connectingfrom:M,connectingto:j,valid:D,connectionindicator:o&&(!R||_)&&(R?s:r)}]),onMouseDown:O,onTouchStart:O,onClick:E?A:void 0,ref:w,...v,children:g})})),ek={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},eN={input:function({data:e,isConnectable:t,sourcePosition:n=l.Ly.Bottom}){return(0,i.jsxs)(i.Fragment,{children:[e?.label,(0,i.jsx)(eE,{type:"source",position:n,isConnectable:t})]})},default:function({data:e,isConnectable:t,targetPosition:n=l.Ly.Top,sourcePosition:o=l.Ly.Bottom}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eE,{type:"target",position:n,isConnectable:t}),e?.label,(0,i.jsx)(eE,{type:"source",position:o,isConnectable:t})]})},output:function({data:e,isConnectable:t,targetPosition:n=l.Ly.Top}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eE,{type:"target",position:n,isConnectable:t}),e?.label]})},group:function(){return null}},eM=e=>{let{width:t,height:n,x:o,y:r}=(0,l.W0)(e.nodeLookup,{filter:e=>!!e.selected});return{width:(0,l.kE)(t)?t:null,height:(0,l.kE)(n)?n:null,userSelectionActive:e.userSelectionActive,transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]}) translate(${o}px,${r}px)`}};function ej({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){let o=m(),{width:r,height:l,transformString:d,userSelectionActive:u}=p(eM,c.X),g=ev(),f=(0,s.useRef)(null);if((0,s.useEffect)(()=>{n||f.current?.focus({preventScroll:!0})},[n]),eh({nodeRef:f}),u||!r||!l)return null;let h=e=>{Object.prototype.hasOwnProperty.call(ek,e.key)&&(e.preventDefault(),g({direction:ek[e.key],factor:e.shiftKey?4:1}))};return(0,i.jsx)("div",{className:(0,a.Z)(["react-flow__nodesselection","react-flow__container",t]),style:{transform:d},children:(0,i.jsx)("div",{ref:f,className:"react-flow__nodesselection-rect",onContextMenu:e?t=>{let n=o.getState().nodes.filter(e=>e.selected);e(t,n)}:void 0,tabIndex:n?void 0:-1,onKeyDown:n?void 0:h,style:{width:r,height:l}})})}let eP="undefined"!=typeof window?window:void 0,e_=e=>({nodesSelectionActive:e.nodesSelectionActive,userSelectionActive:e.userSelectionActive});function eR({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:a,onPaneScroll:l,paneClickDistance:d,deleteKeyCode:c,selectionKeyCode:u,selectionOnDrag:g,selectionMode:f,onSelectionStart:h,onSelectionEnd:y,multiSelectionKeyCode:v,panActivationKeyCode:w,zoomActivationKeyCode:b,elementsSelectable:x,zoomOnScroll:S,zoomOnPinch:C,panOnScroll:E,panOnScrollSpeed:k,panOnScrollMode:N,zoomOnDoubleClick:M,panOnDrag:j,defaultViewport:P,translateExtent:_,minZoom:R,maxZoom:D,preventScrolling:I,onSelectionContextMenu:O,noWheelClassName:A,noPanClassName:L,disableKeyboardA11y:$,onViewportChange:B,isControlledViewport:z}){let{nodesSelectionActive:Z,userSelectionActive:H}=p(e_),F=V(u,{target:eP}),X=V(w,{target:eP}),T=X||j,W=g&&!0!==T;return!function({deleteKeyCode:e,multiSelectionKeyCode:t}){let n=m(),{deleteElements:o}=eo(),r=V(e,ei),i=V(t,{target:ea});(0,s.useEffect)(()=>{if(r){let{edges:e,nodes:t}=n.getState();o({nodes:t.filter(er),edges:e.filter(er)}),n.setState({nodesSelectionActive:!1})}},[r]),(0,s.useEffect)(()=>{n.setState({multiSelectionActive:i})},[i])}({deleteKeyCode:c,multiSelectionKeyCode:v}),(0,i.jsx)(ed,{onPaneContextMenu:a,elementsSelectable:x,zoomOnScroll:S,zoomOnPinch:C,panOnScroll:X||E,panOnScrollSpeed:k,panOnScrollMode:N,zoomOnDoubleClick:M,panOnDrag:!F&&T,defaultViewport:P,translateExtent:_,minZoom:R,maxZoom:D,zoomActivationKeyCode:b,preventScrolling:I,noWheelClassName:A,noPanClassName:L,onViewportChange:B,isControlledViewport:z,paneClickDistance:d,children:(0,i.jsxs)(ep,{onSelectionStart:h,onSelectionEnd:y,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:a,onPaneScroll:l,panOnDrag:T,isSelecting:!!(F||H||W),selectionMode:f,selectionKeyPressed:F,selectionOnDrag:W,children:[e,Z&&(0,i.jsx)(ej,{onSelectionContextMenu:O,noPanClassName:L,disableKeyboardA11y:$})]})})}eR.displayName="FlowRenderer";let eD=(0,s.memo)(eR),eI=e=>t=>e?(0,l.f5)(t.nodeLookup,{x:0,y:0,width:t.width,height:t.height},t.transform,!0).map(e=>e.id):Array.from(t.nodeLookup.keys()),eO=e=>e.updateNodeInternals;function eA({id:e,onClick:t,onMouseEnter:n,onMouseMove:o,onMouseLeave:r,onContextMenu:d,onDoubleClick:u,nodesDraggable:g,elementsSelectable:f,nodesConnectable:h,nodesFocusable:y,resizeObserver:w,noDragClassName:b,noPanClassName:x,disableKeyboardA11y:S,rfId:C,nodeTypes:E,nodeExtent:k,nodeClickDistance:N,onError:M}){let{node:j,internals:P,isParent:_}=p(t=>{let n=t.nodeLookup.get(e),o=t.parentLookup.has(e);return{node:n,internals:n.internals,isParent:o}},c.X),R=j.type||"default",D=E?.[R]||eN[R];void 0===D&&(M?.("003",l.Qj.error003(R)),R="default",D=eN.default);let I=!!(j.draggable||g&&void 0===j.draggable),O=!!(j.selectable||f&&void 0===j.selectable),A=!!(j.connectable||h&&void 0===j.connectable),L=!!(j.focusable||y&&void 0===j.focusable),$=m(),B=(0,l.nb)(j),z=function({node:e,nodeType:t,hasDimensions:n,resizeObserver:o}){let r=m(),i=(0,s.useRef)(null),a=(0,s.useRef)(null),l=(0,s.useRef)(e.sourcePosition),d=(0,s.useRef)(e.targetPosition),c=(0,s.useRef)(t),u=n&&!!e.internals.handleBounds;return(0,s.useEffect)(()=>{!i.current||e.hidden||u&&a.current===i.current||(a.current&&o?.unobserve(a.current),o?.observe(i.current),a.current=i.current)},[u,e.hidden]),(0,s.useEffect)(()=>()=>{a.current&&(o?.unobserve(a.current),a.current=null)},[]),(0,s.useEffect)(()=>{if(i.current){let n=c.current!==t,o=l.current!==e.sourcePosition,a=d.current!==e.targetPosition;(n||o||a)&&(c.current=t,l.current=e.sourcePosition,d.current=e.targetPosition,r.getState().updateNodeInternals(new Map([[e.id,{id:e.id,nodeElement:i.current,force:!0}]])))}},[e.id,t,e.sourcePosition,e.targetPosition]),i}({node:j,nodeType:R,hasDimensions:B,resizeObserver:w}),V=eh({nodeRef:z,disabled:j.hidden||!I,noDragClassName:b,handleSelector:j.dragHandle,nodeId:e,isSelectable:O,nodeClickDistance:N}),Z=ev();if(j.hidden)return null;let H=(0,l.Rf)(j),F=void 0===j.internals.handleBounds?{width:j.width??j.initialWidth??j.style?.width,height:j.height??j.initialHeight??j.style?.height}:{width:j.width??j.style?.width,height:j.height??j.style?.height},X=n=>{let{selectNodesOnDrag:o,nodeDragThreshold:r}=$.getState();O&&(!o||!I||r>0)&&em({id:e,store:$,nodeRef:z}),t&&t(n,{...P.userNode})},T=t=>{if(!(0,l.s$)(t.nativeEvent)&&!S){if(l.wQ.includes(t.key)&&O){let n="Escape"===t.key;em({id:e,store:$,unselect:n,nodeRef:z})}else I&&j.selected&&Object.prototype.hasOwnProperty.call(ek,t.key)&&(t.preventDefault(),$.setState({ariaLiveMessage:`Moved selected node ${t.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~P.positionAbsolute.x}, y: ${~~P.positionAbsolute.y}`}),Z({direction:ek[t.key],factor:t.shiftKey?4:1}))}};return(0,i.jsx)("div",{className:(0,a.Z)(["react-flow__node",`react-flow__node-${R}`,{[x]:I},j.className,{selected:j.selected,selectable:O,parent:_,draggable:I,dragging:V}]),ref:z,style:{zIndex:P.z,transform:`translate(${P.positionAbsolute.x}px,${P.positionAbsolute.y}px)`,pointerEvents:O||I||t||n||o||r?"all":"none",visibility:B?"visible":"hidden",...j.style,...F},"data-id":e,"data-testid":`rf__node-${e}`,onMouseEnter:n?e=>n(e,{...P.userNode}):void 0,onMouseMove:o?e=>o(e,{...P.userNode}):void 0,onMouseLeave:r?e=>r(e,{...P.userNode}):void 0,onContextMenu:d?e=>d(e,{...P.userNode}):void 0,onClick:X,onDoubleClick:u?e=>u(e,{...P.userNode}):void 0,onKeyDown:L?T:void 0,tabIndex:L?0:void 0,role:L?"button":void 0,"aria-describedby":S?void 0:`${v}-${C}`,"aria-label":j.ariaLabel,children:(0,i.jsx)(eb,{value:e,children:(0,i.jsx)(D,{id:e,data:j.data,type:R,positionAbsoluteX:P.positionAbsolute.x,positionAbsoluteY:P.positionAbsolute.y,selected:j.selected,selectable:O,draggable:I,deletable:j.deletable??!0,isConnectable:A,sourcePosition:j.sourcePosition,targetPosition:j.targetPosition,dragging:V,dragHandle:j.dragHandle,zIndex:P.z,parentId:j.parentId,...H})})})}let eL=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,onError:e.onError});function e$(e){let{nodesDraggable:t,nodesConnectable:n,nodesFocusable:o,elementsSelectable:r,onError:a}=p(eL,c.X),l=function(e){let t=p((0,s.useCallback)(eI(e),[e]),c.X);return t}(e.onlyRenderVisibleElements),d=function(){let e=p(eO),[t]=(0,s.useState)(()=>"undefined"==typeof ResizeObserver?null:new ResizeObserver(t=>{let n=new Map;t.forEach(e=>{let t=e.target.getAttribute("data-id");n.set(t,{id:t,nodeElement:e.target,force:!0})}),e(n)}));return(0,s.useEffect)(()=>()=>{t?.disconnect()},[t]),t}();return(0,i.jsx)("div",{className:"react-flow__nodes",style:el,children:l.map(l=>(0,i.jsx)(eA,{id:l,nodeTypes:e.nodeTypes,nodeExtent:e.nodeExtent,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,resizeObserver:d,nodesDraggable:t,nodesConnectable:n,nodesFocusable:o,elementsSelectable:r,nodeClickDistance:e.nodeClickDistance,onError:a},l))})}e$.displayName="NodeRenderer";let eB=(0,s.memo)(e$),ez=({color:e="none",strokeWidth:t=1})=>(0,i.jsx)("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),eV=({color:e="none",strokeWidth:t=1})=>(0,i.jsx)("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"}),eZ={[l.QZ.Arrow]:ez,[l.QZ.ArrowClosed]:eV},eH=({id:e,type:t,color:n,width:o=12.5,height:r=12.5,markerUnits:a="strokeWidth",strokeWidth:d,orient:c="auto-start-reverse"})=>{let u=function(e){let t=m(),n=(0,s.useMemo)(()=>{let n=Object.prototype.hasOwnProperty.call(eZ,e);return n?eZ[e]:(t.getState().onError?.("009",l.Qj.error009(e)),null)},[e]);return n}(t);return u?(0,i.jsx)("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${o}`,markerHeight:`${r}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:c,refX:"0",refY:"0",children:(0,i.jsx)(u,{color:n,strokeWidth:d})}):null},eF=({defaultColor:e,rfId:t})=>{let n=p(e=>e.edges),o=p(e=>e.defaultEdgeOptions),r=(0,s.useMemo)(()=>{let r=(0,l.n3)(n,{id:t,defaultColor:e,defaultMarkerStart:o?.markerStart,defaultMarkerEnd:o?.markerEnd});return r},[n,o,t,e]);return r.length?(0,i.jsx)("svg",{className:"react-flow__marker",children:(0,i.jsx)("defs",{children:r.map(e=>(0,i.jsx)(eH,{id:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient},e.id))})}):null};eF.displayName="MarkerDefinitions";var eX=(0,s.memo)(eF);function eT({x:e,y:t,label:n,labelStyle:o={},labelShowBg:r=!0,labelBgStyle:l={},labelBgPadding:d=[2,4],labelBgBorderRadius:c=2,children:u,className:g,...f}){let[p,m]=(0,s.useState)({x:1,y:0,width:0,height:0}),h=(0,a.Z)(["react-flow__edge-textwrapper",g]),y=(0,s.useRef)(null);return((0,s.useEffect)(()=>{if(y.current){let e=y.current.getBBox();m({x:e.x,y:e.y,width:e.width,height:e.height})}},[n]),void 0!==n&&n)?(0,i.jsxs)("g",{transform:`translate(${e-p.width/2} ${t-p.height/2})`,className:h,visibility:p.width?"visible":"hidden",...f,children:[r&&(0,i.jsx)("rect",{width:p.width+2*d[0],x:-d[0],y:-d[1],height:p.height+2*d[1],className:"react-flow__edge-textbg",style:l,rx:c,ry:c}),(0,i.jsx)("text",{className:"react-flow__edge-text",y:p.height/2,dy:"0.3em",ref:y,style:o,children:n}),u]}):null}eT.displayName="EdgeText";let eW=(0,s.memo)(eT);function eK({path:e,labelX:t,labelY:n,label:o,labelStyle:r,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,interactionWidth:g=20,...f}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("path",{...f,d:e,fill:"none",className:(0,a.Z)(["react-flow__edge-path",f.className])}),g&&(0,i.jsx)("path",{d:e,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),o&&(0,l.kE)(t)&&(0,l.kE)(n)?(0,i.jsx)(eW,{x:t,y:n,label:o,labelStyle:r,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u}):null]})}function eQ({pos:e,x1:t,y1:n,x2:o,y2:r}){return e===l.Ly.Left||e===l.Ly.Right?[.5*(t+o),n]:[t,.5*(n+r)]}function eY({sourceX:e,sourceY:t,sourcePosition:n=l.Ly.Bottom,targetX:o,targetY:r,targetPosition:i=l.Ly.Top}){let[a,s]=eQ({pos:n,x1:e,y1:t,x2:o,y2:r}),[d,c]=eQ({pos:i,x1:o,y1:r,x2:e,y2:t}),[u,g,f,p]=(0,l.lM)({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:a,sourceControlY:s,targetControlX:d,targetControlY:c});return[`M${e},${t} C${a},${s} ${d},${c} ${o},${r}`,u,g,f,p]}function eU(e){return(0,s.memo)(({id:t,sourceX:n,sourceY:o,targetX:r,targetY:a,sourcePosition:s=l.Ly.Bottom,targetPosition:d=l.Ly.Top,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:f,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:y,markerStart:v,interactionWidth:w})=>{let[b,x,S]=eY({sourceX:n,sourceY:o,sourcePosition:s,targetX:r,targetY:a,targetPosition:d}),C=e.isInternal?void 0:t;return(0,i.jsx)(eK,{id:C,path:b,labelX:x,labelY:S,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:f,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:y,markerStart:v,interactionWidth:w})})}let eG=eU({isInternal:!1}),eJ=eU({isInternal:!0});function eq(e){return(0,s.memo)(({id:t,sourceX:n,sourceY:o,targetX:r,targetY:a,label:s,labelStyle:d,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:f,style:p,sourcePosition:m=l.Ly.Bottom,targetPosition:h=l.Ly.Top,markerEnd:y,markerStart:v,pathOptions:w,interactionWidth:b})=>{let[x,S,C]=(0,l.OW)({sourceX:n,sourceY:o,sourcePosition:m,targetX:r,targetY:a,targetPosition:h,borderRadius:w?.borderRadius,offset:w?.offset}),E=e.isInternal?void 0:t;return(0,i.jsx)(eK,{id:E,path:x,labelX:S,labelY:C,label:s,labelStyle:d,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:f,style:p,markerEnd:y,markerStart:v,interactionWidth:b})})}eG.displayName="SimpleBezierEdge",eJ.displayName="SimpleBezierEdgeInternal";let e0=eq({isInternal:!1}),e1=eq({isInternal:!0});function e3(e){return(0,s.memo)(({id:t,...n})=>{let o=e.isInternal?void 0:t;return(0,i.jsx)(e0,{...n,id:o,pathOptions:(0,s.useMemo)(()=>({borderRadius:0,offset:n.pathOptions?.offset}),[n.pathOptions?.offset])})})}e0.displayName="SmoothStepEdge",e1.displayName="SmoothStepEdgeInternal";let e2=e3({isInternal:!1}),e5=e3({isInternal:!0});function e4(e){return(0,s.memo)(({id:t,sourceX:n,sourceY:o,targetX:r,targetY:a,label:s,labelStyle:d,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:f,style:p,markerEnd:m,markerStart:h,interactionWidth:y})=>{let[v,w,b]=(0,l.Hm)({sourceX:n,sourceY:o,targetX:r,targetY:a}),x=e.isInternal?void 0:t;return(0,i.jsx)(eK,{id:x,path:v,labelX:w,labelY:b,label:s,labelStyle:d,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:f,style:p,markerEnd:m,markerStart:h,interactionWidth:y})})}e2.displayName="StepEdge",e5.displayName="StepEdgeInternal";let e6=e4({isInternal:!1}),e8=e4({isInternal:!0});function e7(e){return(0,s.memo)(({id:t,sourceX:n,sourceY:o,targetX:r,targetY:a,sourcePosition:s=l.Ly.Bottom,targetPosition:d=l.Ly.Top,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:f,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:y,markerStart:v,pathOptions:w,interactionWidth:b})=>{let[x,S,C]=(0,l.OQ)({sourceX:n,sourceY:o,sourcePosition:s,targetX:r,targetY:a,targetPosition:d,curvature:w?.curvature}),E=e.isInternal?void 0:t;return(0,i.jsx)(eK,{id:E,path:x,labelX:S,labelY:C,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:f,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:y,markerStart:v,interactionWidth:b})})}e6.displayName="StraightEdge",e8.displayName="StraightEdgeInternal";let e9=e7({isInternal:!1}),te=e7({isInternal:!0});e9.displayName="BezierEdge",te.displayName="BezierEdgeInternal";let tt={default:te,straight:e8,step:e5,smoothstep:e1,simplebezier:eJ},tn={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},to=(e,t,n)=>n===l.Ly.Left?e-t:n===l.Ly.Right?e+t:e,tr=(e,t,n)=>n===l.Ly.Top?e-t:n===l.Ly.Bottom?e+t:e,ti="react-flow__edgeupdater";function ta({position:e,centerX:t,centerY:n,radius:o=10,onMouseDown:r,onMouseEnter:l,onMouseOut:s,type:d}){return(0,i.jsx)("circle",{onMouseDown:r,onMouseEnter:l,onMouseOut:s,className:(0,a.Z)([ti,`${ti}-${d}`]),cx:to(t,o,e),cy:tr(n,o,e),r:o,stroke:"transparent",fill:"transparent"})}function tl({isReconnectable:e,reconnectRadius:t,edge:n,sourceX:o,sourceY:r,targetX:a,targetY:s,sourcePosition:d,targetPosition:c,onReconnect:u,onReconnectStart:g,onReconnectEnd:f,setReconnecting:p,setUpdateHover:h}){let y=m(),v=(e,t)=>{if(0!==e.button)return;let{autoPanOnConnect:o,domNode:r,isValidConnection:i,connectionMode:a,connectionRadius:s,lib:d,onConnectStart:c,onConnectEnd:m,cancelConnection:h,nodeLookup:v,rfId:w,panBy:b,updateConnection:x}=y.getState(),S="target"===t.type;p(!0),g?.(e,n,t.type);let C=(e,o)=>{p(!1),f?.(e,n,t.type,o)},E=e=>u?.(n,e);l.Ql.onPointerDown(e.nativeEvent,{autoPanOnConnect:o,connectionMode:a,connectionRadius:s,domNode:r,handleId:t.id,nodeId:t.nodeId,nodeLookup:v,isTarget:S,edgeUpdaterType:t.type,lib:d,flowId:w,cancelConnection:h,panBy:b,isValidConnection:i,onConnect:E,onConnectStart:c,onConnectEnd:m,onReconnectEnd:C,updateConnection:x,getTransform:()=>y.getState().transform,getFromHandle:()=>y.getState().connection.fromHandle})},w=e=>v(e,{nodeId:n.target,id:n.targetHandle??null,type:"target"}),b=e=>v(e,{nodeId:n.source,id:n.sourceHandle??null,type:"source"}),x=()=>h(!0),S=()=>h(!1);return(0,i.jsxs)(i.Fragment,{children:[(!0===e||"source"===e)&&(0,i.jsx)(ta,{position:d,centerX:o,centerY:r,radius:t,onMouseDown:w,onMouseEnter:x,onMouseOut:S,type:"source"}),(!0===e||"target"===e)&&(0,i.jsx)(ta,{position:c,centerX:a,centerY:s,radius:t,onMouseDown:b,onMouseEnter:x,onMouseOut:S,type:"target"})]})}function ts({id:e,edgesFocusable:t,edgesReconnectable:n,elementsSelectable:o,onClick:r,onDoubleClick:d,onContextMenu:u,onMouseEnter:g,onMouseMove:f,onMouseLeave:h,reconnectRadius:y,onReconnect:v,onReconnectStart:b,onReconnectEnd:x,rfId:S,edgeTypes:C,noPanClassName:E,onError:k,disableKeyboardA11y:N}){let M=p(t=>t.edgeLookup.get(e)),j=p(e=>e.defaultEdgeOptions),P=(M=j?{...j,...M}:M).type||"default",_=C?.[P]||tt[P];void 0===_&&(k?.("011",l.Qj.error011(P)),P="default",_=tt.default);let R=!!(M.focusable||t&&void 0===M.focusable),D=void 0!==v&&(M.reconnectable||n&&void 0===M.reconnectable),I=!!(M.selectable||o&&void 0===M.selectable),O=(0,s.useRef)(null),[A,L]=(0,s.useState)(!1),[$,B]=(0,s.useState)(!1),z=m(),{zIndex:V,sourceX:Z,sourceY:H,targetX:F,targetY:X,sourcePosition:T,targetPosition:W}=p((0,s.useCallback)(t=>{let n=t.nodeLookup.get(M.source),o=t.nodeLookup.get(M.target);if(!n||!o)return{zIndex:M.zIndex,...tn};let r=(0,l.JU)({id:e,sourceNode:n,targetNode:o,sourceHandle:M.sourceHandle||null,targetHandle:M.targetHandle||null,connectionMode:t.connectionMode,onError:k}),i=(0,l.xx)({selected:M.selected,zIndex:M.zIndex,sourceNode:n,targetNode:o,elevateOnSelect:t.elevateEdgesOnSelect});return{zIndex:i,...r||tn}},[M.source,M.target,M.sourceHandle,M.targetHandle,M.selected,M.zIndex]),c.X),K=(0,s.useMemo)(()=>M.markerStart?`url('#${(0,l.dW)(M.markerStart,S)}')`:void 0,[M.markerStart,S]),Q=(0,s.useMemo)(()=>M.markerEnd?`url('#${(0,l.dW)(M.markerEnd,S)}')`:void 0,[M.markerEnd,S]);if(M.hidden||null===Z||null===H||null===F||null===X)return null;let Y=t=>{let{addSelectedEdges:n,unselectNodesAndEdges:o,multiSelectionActive:i}=z.getState();I&&(z.setState({nodesSelectionActive:!1}),M.selected&&i?(o({nodes:[],edges:[M]}),O.current?.blur()):n([e])),r&&r(t,M)},U=d?e=>{d(e,{...M})}:void 0,G=u?e=>{u(e,{...M})}:void 0,J=g?e=>{g(e,{...M})}:void 0,q=f?e=>{f(e,{...M})}:void 0,ee=h?e=>{h(e,{...M})}:void 0,et=t=>{if(!N&&l.wQ.includes(t.key)&&I){let{unselectNodesAndEdges:n,addSelectedEdges:o}=z.getState(),r="Escape"===t.key;r?(O.current?.blur(),n({edges:[M]})):o([e])}};return(0,i.jsx)("svg",{style:{zIndex:V},children:(0,i.jsxs)("g",{className:(0,a.Z)(["react-flow__edge",`react-flow__edge-${P}`,M.className,E,{selected:M.selected,animated:M.animated,inactive:!I&&!r,updating:A,selectable:I}]),onClick:Y,onDoubleClick:U,onContextMenu:G,onMouseEnter:J,onMouseMove:q,onMouseLeave:ee,onKeyDown:R?et:void 0,tabIndex:R?0:void 0,role:R?"button":"img","data-id":e,"data-testid":`rf__edge-${e}`,"aria-label":null===M.ariaLabel?void 0:M.ariaLabel||`Edge from ${M.source} to ${M.target}`,"aria-describedby":R?`${w}-${S}`:void 0,ref:O,children:[!$&&(0,i.jsx)(_,{id:e,source:M.source,target:M.target,type:M.type,selected:M.selected,animated:M.animated,selectable:I,deletable:M.deletable??!0,label:M.label,labelStyle:M.labelStyle,labelShowBg:M.labelShowBg,labelBgStyle:M.labelBgStyle,labelBgPadding:M.labelBgPadding,labelBgBorderRadius:M.labelBgBorderRadius,sourceX:Z,sourceY:H,targetX:F,targetY:X,sourcePosition:T,targetPosition:W,data:M.data,style:M.style,sourceHandleId:M.sourceHandle,targetHandleId:M.targetHandle,markerStart:K,markerEnd:Q,pathOptions:"pathOptions"in M?M.pathOptions:void 0,interactionWidth:M.interactionWidth}),D&&(0,i.jsx)(tl,{edge:M,isReconnectable:D,reconnectRadius:y,onReconnect:v,onReconnectStart:b,onReconnectEnd:x,sourceX:Z,sourceY:H,targetX:F,targetY:X,sourcePosition:T,targetPosition:W,setUpdateHover:L,setReconnecting:B})]})})}let td=e=>({edgesFocusable:e.edgesFocusable,edgesReconnectable:e.edgesReconnectable,elementsSelectable:e.elementsSelectable,connectionMode:e.connectionMode,onError:e.onError});function tc({defaultMarkerColor:e,onlyRenderVisibleElements:t,rfId:n,edgeTypes:o,noPanClassName:r,onReconnect:a,onEdgeContextMenu:d,onEdgeMouseEnter:u,onEdgeMouseMove:g,onEdgeMouseLeave:f,onEdgeClick:m,reconnectRadius:h,onEdgeDoubleClick:y,onReconnectStart:v,onReconnectEnd:w,disableKeyboardA11y:b}){let{edgesFocusable:x,edgesReconnectable:S,elementsSelectable:C,onError:E}=p(td,c.X),k=function(e){let t=p((0,s.useCallback)(t=>{if(!e)return t.edges.map(e=>e.id);let n=[];if(t.width&&t.height)for(let e of t.edges){let o=t.nodeLookup.get(e.source),r=t.nodeLookup.get(e.target);o&&r&&(0,l.RY)({sourceNode:o,targetNode:r,width:t.width,height:t.height,transform:t.transform})&&n.push(e.id)}return n},[e]),c.X);return t}(t);return(0,i.jsxs)("div",{className:"react-flow__edges",children:[(0,i.jsx)(eX,{defaultColor:e,rfId:n}),k.map(e=>(0,i.jsx)(ts,{id:e,edgesFocusable:x,edgesReconnectable:S,elementsSelectable:C,noPanClassName:r,onReconnect:a,onContextMenu:d,onMouseEnter:u,onMouseMove:g,onMouseLeave:f,onClick:m,reconnectRadius:h,onDoubleClick:y,onReconnectStart:v,onReconnectEnd:w,rfId:n,onError:E,edgeTypes:o,disableKeyboardA11y:b},e))]})}tc.displayName="EdgeRenderer";let tu=(0,s.memo)(tc),tg=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function tf({children:e}){let t=p(tg);return(0,i.jsx)("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:t},children:e})}let tp=e=>e.panZoom?.syncViewport;function tm(e){return e.connection.inProgress?{...e.connection,to:(0,l.m)(e.connection.to,e.transform)}:{...e.connection}}let th=e=>({nodesConnectable:e.nodesConnectable,isValid:e.connection.isValid,inProgress:e.connection.inProgress,width:e.width,height:e.height});function ty({containerStyle:e,style:t,type:n,component:o}){let{nodesConnectable:r,width:s,height:d,isValid:u,inProgress:g}=p(th,c.X);return s&&r&&g?(0,i.jsx)("svg",{style:e,width:s,height:d,className:"react-flow__connectionline react-flow__container",children:(0,i.jsx)("g",{className:(0,a.Z)(["react-flow__connection",(0,l.Zp)(u)]),children:(0,i.jsx)(tv,{style:t,type:n,CustomComponent:o,isValid:u})})}):null}let tv=({style:e,type:t=l.t8.Bezier,CustomComponent:n,isValid:o})=>{let{inProgress:r,from:a,fromNode:s,fromHandle:d,fromPosition:u,to:g,toNode:f,toHandle:m,toPosition:h}=function(e){let t=function(e){if(e){let t=t=>{let n=tm(t);return e(n)};return t}return tm}(void 0);return p(t,c.X)}();if(!r)return;if(n)return(0,i.jsx)(n,{connectionLineType:t,connectionLineStyle:e,fromNode:s,fromHandle:d,fromX:a.x,fromY:a.y,toX:g.x,toY:g.y,fromPosition:u,toPosition:h,connectionStatus:(0,l.Zp)(o),toNode:f,toHandle:m});let y="",v={sourceX:a.x,sourceY:a.y,sourcePosition:u,targetX:g.x,targetY:g.y,targetPosition:h};switch(t){case l.t8.Bezier:[y]=(0,l.OQ)(v);break;case l.t8.SimpleBezier:[y]=eY(v);break;case l.t8.Step:[y]=(0,l.OW)({...v,borderRadius:0});break;case l.t8.SmoothStep:[y]=(0,l.OW)(v);break;default:[y]=(0,l.Hm)(v)}return(0,i.jsx)("path",{d:y,fill:"none",className:"react-flow__connection-path",style:e})};tv.displayName="ConnectionLine";let tw={};function tb(e=tw){(0,s.useRef)(e),m(),(0,s.useEffect)(()=>{},[e])}function tx({nodeTypes:e,edgeTypes:t,onInit:n,onNodeClick:o,onEdgeClick:r,onNodeDoubleClick:a,onEdgeDoubleClick:l,onNodeMouseEnter:d,onNodeMouseMove:c,onNodeMouseLeave:u,onNodeContextMenu:g,onSelectionContextMenu:f,onSelectionStart:h,onSelectionEnd:y,connectionLineType:v,connectionLineStyle:w,connectionLineComponent:b,connectionLineContainerStyle:x,selectionKeyCode:S,selectionOnDrag:C,selectionMode:E,multiSelectionKeyCode:k,panActivationKeyCode:N,zoomActivationKeyCode:M,deleteKeyCode:j,onlyRenderVisibleElements:P,elementsSelectable:_,defaultViewport:R,translateExtent:D,minZoom:I,maxZoom:O,preventScrolling:A,defaultMarkerColor:L,zoomOnScroll:$,zoomOnPinch:B,panOnScroll:z,panOnScrollSpeed:V,panOnScrollMode:Z,zoomOnDoubleClick:H,panOnDrag:F,onPaneClick:X,onPaneMouseEnter:T,onPaneMouseMove:W,onPaneMouseLeave:K,onPaneScroll:Q,onPaneContextMenu:Y,paneClickDistance:U,nodeClickDistance:G,onEdgeContextMenu:J,onEdgeMouseEnter:q,onEdgeMouseMove:ee,onEdgeMouseLeave:et,reconnectRadius:en,onReconnect:er,onReconnectStart:ei,onReconnectEnd:ea,noDragClassName:el,noWheelClassName:es,noPanClassName:ed,disableKeyboardA11y:ec,nodeExtent:eu,rfId:eg,viewport:ef,onViewportChange:ep}){return tb(e),tb(t),m(),(0,s.useRef)(!1),(0,s.useEffect)(()=>{},[]),!function(e){let t=eo(),n=(0,s.useRef)(!1);(0,s.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(n),!function(e){let t=p(tp),n=m();(0,s.useEffect)(()=>{e&&(t?.(e),n.setState({transform:[e.x,e.y,e.zoom]}))},[e,t])}(ef),(0,i.jsx)(eD,{onPaneClick:X,onPaneMouseEnter:T,onPaneMouseMove:W,onPaneMouseLeave:K,onPaneContextMenu:Y,onPaneScroll:Q,paneClickDistance:U,deleteKeyCode:j,selectionKeyCode:S,selectionOnDrag:C,selectionMode:E,onSelectionStart:h,onSelectionEnd:y,multiSelectionKeyCode:k,panActivationKeyCode:N,zoomActivationKeyCode:M,elementsSelectable:_,zoomOnScroll:$,zoomOnPinch:B,zoomOnDoubleClick:H,panOnScroll:z,panOnScrollSpeed:V,panOnScrollMode:Z,panOnDrag:F,defaultViewport:R,translateExtent:D,minZoom:I,maxZoom:O,onSelectionContextMenu:f,preventScrolling:A,noDragClassName:el,noWheelClassName:es,noPanClassName:ed,disableKeyboardA11y:ec,onViewportChange:ep,isControlledViewport:!!ef,children:(0,i.jsxs)(tf,{children:[(0,i.jsx)(tu,{edgeTypes:t,onEdgeClick:r,onEdgeDoubleClick:l,onReconnect:er,onReconnectStart:ei,onReconnectEnd:ea,onlyRenderVisibleElements:P,onEdgeContextMenu:J,onEdgeMouseEnter:q,onEdgeMouseMove:ee,onEdgeMouseLeave:et,reconnectRadius:en,defaultMarkerColor:L,noPanClassName:ed,disableKeyboardA11y:ec,rfId:eg}),(0,i.jsx)(ty,{style:w,type:v,component:b,containerStyle:x}),(0,i.jsx)("div",{className:"react-flow__edgelabel-renderer"}),(0,i.jsx)(eB,{nodeTypes:e,onNodeClick:o,onNodeDoubleClick:a,onNodeMouseEnter:d,onNodeMouseMove:c,onNodeMouseLeave:u,onNodeContextMenu:g,nodeClickDistance:G,onlyRenderVisibleElements:P,noPanClassName:ed,noDragClassName:el,disableKeyboardA11y:ec,nodeExtent:eu,rfId:eg}),(0,i.jsx)("div",{className:"react-flow__viewport-portal"})]})})}tx.displayName="GraphView";let tS=(0,s.memo)(tx),tC=({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,width:r,height:i,fitView:a,nodeOrigin:s,nodeExtent:d}={})=>{let c=new Map,u=new Map,g=new Map,f=new Map,p=o??t??[],m=n??e??[],h=s??[0,0],y=d??l.k5;(0,l.be)(g,f,p),(0,l.yF)(m,c,u,{nodeOrigin:h,nodeExtent:y,elevateNodesOnSelect:!1});let v=[0,0,1];if(a&&r&&i){let e=(0,l.W0)(c,{filter:e=>!!((e.width||e.initialWidth)&&(e.height||e.initialHeight))}),{x:t,y:n,zoom:o}=(0,l.$i)(e,r,i,.5,2,.1);v=[t,n,o]}return{rfId:"1",width:0,height:0,transform:v,nodes:m,nodeLookup:c,parentLookup:u,edges:p,edgeLookup:f,connectionLookup:g,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:void 0!==n,hasDefaultEdges:void 0!==o,panZoom:null,minZoom:.5,maxZoom:2,translateExtent:l.k5,nodeExtent:y,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:l.jD.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:h,nodeDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,fitViewOnInit:!1,fitViewDone:!1,fitViewOnInitOptions:void 0,selectNodesOnDrag:!0,multiSelectionActive:!1,connection:{...l.Ky},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanSpeed:15,connectionRadius:20,onError:l.Ki,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1}},tE=({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,width:r,height:i,fitView:a,nodeOrigin:s,nodeExtent:c})=>(0,d.F)((d,u)=>({...tC({nodes:e,edges:t,width:r,height:i,fitView:a,nodeOrigin:s,nodeExtent:c,defaultNodes:n,defaultEdges:o}),setNodes:e=>{let{nodeLookup:t,parentLookup:n,nodeOrigin:o,elevateNodesOnSelect:r}=u();(0,l.yF)(e,t,n,{nodeOrigin:o,nodeExtent:c,elevateNodesOnSelect:r,checkEquality:!0}),d({nodes:e})},setEdges:e=>{let{connectionLookup:t,edgeLookup:n}=u();(0,l.be)(t,n,e),d({edges:e})},setDefaultNodesAndEdges:(e,t)=>{if(e){let{setNodes:t}=u();t(e),d({hasDefaultNodes:!0})}if(t){let{setEdges:e}=u();e(t),d({hasDefaultEdges:!0})}},updateNodeInternals:(e,t={triggerFitView:!0})=>{let{triggerNodeChanges:n,nodeLookup:o,parentLookup:r,fitViewOnInit:i,fitViewDone:a,fitViewOnInitOptions:s,domNode:c,nodeOrigin:g,nodeExtent:f,debug:p,fitViewSync:m}=u(),{changes:h,updatedInternals:y}=(0,l.B1)(e,o,r,c,g,f);if(y){if((0,l.VV)(o,r,{nodeOrigin:g,nodeExtent:f}),t.triggerFitView){let e=a;!a&&i&&(e=m({...s,nodes:s?.nodes})),d({fitViewDone:e})}else d({});h?.length>0&&(p&&console.log("React Flow: trigger node changes",h),n?.(h))}},updateNodePositions:(e,t=!1)=>{let n=[],o=[];for(let[r,i]of e){let e=!!(i?.expandParent&&i?.parentId&&i?.position),a={id:r,type:"position",position:e?{x:Math.max(0,i.position.x),y:Math.max(0,i.position.y)}:i.position,dragging:t};e&&n.push({id:r,parentId:i.parentId,rect:{...i.internals.positionAbsolute,width:i.measured.width,height:i.measured.height}}),o.push(a)}if(n.length>0){let{nodeLookup:e,parentLookup:t,nodeOrigin:r}=u(),i=(0,l.so)(n,e,t,r);o.push(...i)}u().triggerNodeChanges(o)},triggerNodeChanges:e=>{let{onNodesChange:t,setNodes:n,nodes:o,hasDefaultNodes:r,debug:i}=u();if(e?.length){if(r){let t=X(e,o);n(t)}i&&console.log("React Flow: trigger node changes",e),t?.(e)}},triggerEdgeChanges:e=>{let{onEdgesChange:t,setEdges:n,edges:o,hasDefaultEdges:r,debug:i}=u();if(e?.length){if(r){let t=X(e,o);n(t)}i&&console.log("React Flow: trigger edge changes",e),t?.(e)}},addSelectedNodes:e=>{let{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:i}=u();if(t){let t=e.map(e=>T(e,!0));r(t);return}r(W(o,new Set([...e]),!0)),i(W(n))},addSelectedEdges:e=>{let{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:i}=u();if(t){let t=e.map(e=>T(e,!0));i(t);return}i(W(n,new Set([...e]))),r(W(o,new Set,!0))},unselectNodesAndEdges:({nodes:e,edges:t}={})=>{let{edges:n,nodes:o,nodeLookup:r,triggerNodeChanges:i,triggerEdgeChanges:a}=u(),l=(e||o).map(e=>{let t=r.get(e.id);return t&&(t.selected=!1),T(e.id,!1)}),s=(t||n).map(e=>T(e.id,!1));i(l),a(s)},setMinZoom:e=>{let{panZoom:t,maxZoom:n}=u();t?.setScaleExtent([e,n]),d({minZoom:e})},setMaxZoom:e=>{let{panZoom:t,minZoom:n}=u();t?.setScaleExtent([n,e]),d({maxZoom:e})},setTranslateExtent:e=>{u().panZoom?.setTranslateExtent(e),d({translateExtent:e})},setPaneClickDistance:e=>{u().panZoom?.setClickDistance(e)},resetSelectedElements:()=>{let{edges:e,nodes:t,triggerNodeChanges:n,triggerEdgeChanges:o}=u(),r=t.reduce((e,t)=>t.selected?[...e,T(t.id,!1)]:e,[]),i=e.reduce((e,t)=>t.selected?[...e,T(t.id,!1)]:e,[]);n(r),o(i)},setNodeExtent:e=>{let{nodes:t,nodeLookup:n,parentLookup:o,nodeOrigin:r,elevateNodesOnSelect:i,nodeExtent:a}=u();(e[0][0]!==a[0][0]||e[0][1]!==a[0][1]||e[1][0]!==a[1][0]||e[1][1]!==a[1][1])&&((0,l.yF)(t,n,o,{nodeOrigin:r,nodeExtent:e,elevateNodesOnSelect:i,checkEquality:!1}),d({nodeExtent:e}))},panBy:e=>{let{transform:t,width:n,height:o,panZoom:r,translateExtent:i}=u();return(0,l.hO)({delta:e,panZoom:r,transform:t,translateExtent:i,width:n,height:o})},fitView:e=>{let{panZoom:t,width:n,height:o,minZoom:r,maxZoom:i,nodeLookup:a}=u();if(!t)return Promise.resolve(!1);let s=(0,l.J6)(a,e);return(0,l.Qu)({nodes:s,width:n,height:o,panZoom:t,minZoom:r,maxZoom:i},e)},fitViewSync:e=>{let{panZoom:t,width:n,height:o,minZoom:r,maxZoom:i,nodeLookup:a}=u();if(!t)return!1;let s=(0,l.J6)(a,e);return(0,l.Qu)({nodes:s,width:n,height:o,panZoom:t,minZoom:r,maxZoom:i},e),s.size>0},cancelConnection:()=>{d({connection:{...l.Ky}})},updateConnection:e=>{d({connection:e})},reset:()=>d({...tC()})}),Object.is);function tk({initialNodes:e,initialEdges:t,defaultNodes:n,defaultEdges:o,initialWidth:r,initialHeight:a,fitView:l,nodeOrigin:d,nodeExtent:c,children:u}){let[f]=(0,s.useState)(()=>tE({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,width:r,height:a,fitView:l,nodeOrigin:d,nodeExtent:c}));return(0,i.jsx)(g,{value:f,children:(0,i.jsx)(et,{children:u})})}function tN({children:e,nodes:t,edges:n,defaultNodes:o,defaultEdges:r,width:a,height:l,fitView:d,nodeOrigin:c,nodeExtent:g}){let f=(0,s.useContext)(u);return f?(0,i.jsx)(i.Fragment,{children:e}):(0,i.jsx)(tk,{initialNodes:t,initialEdges:n,defaultNodes:o,defaultEdges:r,initialWidth:a,initialHeight:l,fitView:d,nodeOrigin:c,nodeExtent:g,children:e})}let tM={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};var tj=G(function({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,className:r,nodeTypes:d,edgeTypes:c,onNodeClick:u,onEdgeClick:g,onInit:f,onMove:p,onMoveStart:m,onMoveEnd:h,onConnect:y,onConnectStart:v,onConnectEnd:w,onClickConnectStart:b,onClickConnectEnd:x,onNodeMouseEnter:C,onNodeMouseMove:E,onNodeMouseLeave:N,onNodeContextMenu:M,onNodeDoubleClick:j,onNodeDragStart:P,onNodeDrag:_,onNodeDragStop:O,onNodesDelete:A,onEdgesDelete:L,onDelete:z,onSelectionChange:V,onSelectionDragStart:Z,onSelectionDrag:H,onSelectionDragStop:F,onSelectionContextMenu:X,onSelectionStart:T,onSelectionEnd:W,onBeforeDelete:K,connectionMode:Q,connectionLineType:Y=l.t8.Bezier,connectionLineStyle:U,connectionLineComponent:G,connectionLineContainerStyle:J,deleteKeyCode:q="Backspace",selectionKeyCode:ee="Shift",selectionOnDrag:et=!1,selectionMode:en=l.oW.Full,panActivationKeyCode:eo="Space",multiSelectionKeyCode:er=(0,l.Q5)()?"Meta":"Control",zoomActivationKeyCode:ei=(0,l.Q5)()?"Meta":"Control",snapToGrid:ea,snapGrid:el,onlyRenderVisibleElements:es=!1,selectNodesOnDrag:ed,nodesDraggable:ec,nodesConnectable:eu,nodesFocusable:eg,nodeOrigin:ef=D,edgesFocusable:ep,edgesReconnectable:em,elementsSelectable:eh=!0,defaultViewport:ey=I,minZoom:ev=.5,maxZoom:ew=2,translateExtent:eb=l.k5,preventScrolling:ex=!0,nodeExtent:eS,defaultMarkerColor:eC="#b1b1b7",zoomOnScroll:eE=!0,zoomOnPinch:ek=!0,panOnScroll:eN=!1,panOnScrollSpeed:eM=.5,panOnScrollMode:ej=l.IY.Free,zoomOnDoubleClick:eP=!0,panOnDrag:e_=!0,onPaneClick:eR,onPaneMouseEnter:eD,onPaneMouseMove:eI,onPaneMouseLeave:eO,onPaneScroll:eA,onPaneContextMenu:eL,paneClickDistance:e$=0,nodeClickDistance:eB=0,children:ez,onReconnect:eV,onReconnectStart:eZ,onReconnectEnd:eH,onEdgeContextMenu:eF,onEdgeDoubleClick:eX,onEdgeMouseEnter:eT,onEdgeMouseMove:eW,onEdgeMouseLeave:eK,reconnectRadius:eQ=10,onNodesChange:eY,onEdgesChange:eU,noDragClassName:eG="nodrag",noWheelClassName:eJ="nowheel",noPanClassName:eq="nopan",fitView:e0,fitViewOptions:e1,connectOnClick:e3,attributionPosition:e2,proOptions:e5,defaultEdgeOptions:e4,elevateNodesOnSelect:e6,elevateEdgesOnSelect:e8,disableKeyboardA11y:e7=!1,autoPanOnConnect:e9,autoPanOnNodeDrag:te,autoPanSpeed:tt,connectionRadius:tn,isValidConnection:to,onError:tr,style:ti,id:ta,nodeDragThreshold:tl,viewport:ts,onViewportChange:td,width:tc,height:tu,colorMode:tg="light",debug:tf,...tp},tm){let th=ta||"1",ty=function(e){let[t,n]=(0,s.useState)("system"===e?null:e);return(0,s.useEffect)(()=>{if("system"!==e){n(e);return}let t=B(),o=()=>n(t?.matches?"dark":"light");return o(),t?.addEventListener("change",o),()=>{t?.removeEventListener("change",o)}},[e]),null!==t?t:B()?.matches?"dark":"light"}(tg);return(0,i.jsx)("div",{"data-testid":"rf__wrapper",...tp,style:{...ti,...tM},ref:tm,className:(0,a.Z)(["react-flow",r,ty]),id:ta,children:(0,i.jsxs)(tN,{nodes:e,edges:t,width:tc,height:tu,fitView:e0,nodeOrigin:ef,nodeExtent:eS,children:[(0,i.jsx)(tS,{onInit:f,onNodeClick:u,onEdgeClick:g,onNodeMouseEnter:C,onNodeMouseMove:E,onNodeMouseLeave:N,onNodeContextMenu:M,onNodeDoubleClick:j,nodeTypes:d,edgeTypes:c,connectionLineType:Y,connectionLineStyle:U,connectionLineComponent:G,connectionLineContainerStyle:J,selectionKeyCode:ee,selectionOnDrag:et,selectionMode:en,deleteKeyCode:q,multiSelectionKeyCode:er,panActivationKeyCode:eo,zoomActivationKeyCode:ei,onlyRenderVisibleElements:es,defaultViewport:ey,translateExtent:eb,minZoom:ev,maxZoom:ew,preventScrolling:ex,zoomOnScroll:eE,zoomOnPinch:ek,zoomOnDoubleClick:eP,panOnScroll:eN,panOnScrollSpeed:eM,panOnScrollMode:ej,panOnDrag:e_,onPaneClick:eR,onPaneMouseEnter:eD,onPaneMouseMove:eI,onPaneMouseLeave:eO,onPaneScroll:eA,onPaneContextMenu:eL,paneClickDistance:e$,nodeClickDistance:eB,onSelectionContextMenu:X,onSelectionStart:T,onSelectionEnd:W,onReconnect:eV,onReconnectStart:eZ,onReconnectEnd:eH,onEdgeContextMenu:eF,onEdgeDoubleClick:eX,onEdgeMouseEnter:eT,onEdgeMouseMove:eW,onEdgeMouseLeave:eK,reconnectRadius:eQ,defaultMarkerColor:eC,noDragClassName:eG,noWheelClassName:eJ,noPanClassName:eq,rfId:th,disableKeyboardA11y:e7,nodeExtent:eS,viewport:ts,onViewportChange:td}),(0,i.jsx)($,{nodes:e,edges:t,defaultNodes:n,defaultEdges:o,onConnect:y,onConnectStart:v,onConnectEnd:w,onClickConnectStart:b,onClickConnectEnd:x,nodesDraggable:ec,nodesConnectable:eu,nodesFocusable:eg,edgesFocusable:ep,edgesReconnectable:em,elementsSelectable:eh,elevateNodesOnSelect:e6,elevateEdgesOnSelect:e8,minZoom:ev,maxZoom:ew,nodeExtent:eS,onNodesChange:eY,onEdgesChange:eU,snapToGrid:ea,snapGrid:el,connectionMode:Q,translateExtent:eb,connectOnClick:e3,defaultEdgeOptions:e4,fitView:e0,fitViewOptions:e1,onNodesDelete:A,onEdgesDelete:L,onDelete:z,onNodeDragStart:P,onNodeDrag:_,onNodeDragStop:O,onSelectionDrag:H,onSelectionDragStart:Z,onSelectionDragStop:F,onMove:p,onMoveStart:m,onMoveEnd:h,noPanClassName:eq,nodeOrigin:ef,rfId:th,autoPanOnConnect:e9,autoPanOnNodeDrag:te,autoPanSpeed:tt,onError:tr,connectionRadius:tn,isValidConnection:to,selectNodesOnDrag:ed,nodeDragThreshold:tl,onBeforeDelete:K,paneClickDistance:e$,debug:tf}),(0,i.jsx)(R,{onSelectionChange:V}),ez,(0,i.jsx)(k,{proOptions:e5,position:e2}),(0,i.jsx)(S,{rfId:th,disableKeyboardA11y:e7})]})})});let tP=e=>e.nodes;function t_(){let e=p(tP,c.X);return e}let tR=e=>({x:e.transform[0],y:e.transform[1],zoom:e.transform[2]});function tD(){let e=p(tR,c.X);return e}function tI(e){let[t,n]=(0,s.useState)(e),o=(0,s.useCallback)(e=>n(t=>X(e,t)),[]);return[t,n,o]}function tO(e){let[t,n]=(0,s.useState)(e),o=(0,s.useCallback)(e=>n(t=>X(e,t)),[]);return[t,n,o]}function tA({dimensions:e,lineWidth:t,variant:n,className:o}){return(0,i.jsx)("path",{strokeWidth:t,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`,className:(0,a.Z)(["react-flow__background-pattern",n,o])})}function tL({radius:e,className:t}){return(0,i.jsx)("circle",{cx:e,cy:e,r:e,className:(0,a.Z)(["react-flow__background-pattern","dots",t])})}(o=r||(r={})).Lines="lines",o.Dots="dots",o.Cross="cross";let t$={[r.Dots]:1,[r.Lines]:1,[r.Cross]:6},tB=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function tz({id:e,variant:t=r.Dots,gap:n=20,size:o,lineWidth:l=1,offset:d=0,color:u,bgColor:g,style:f,className:m,patternClassName:h}){let y=(0,s.useRef)(null),{transform:v,patternId:w}=p(tB,c.X),b=o||t$[t],x=t===r.Dots,S=t===r.Cross,C=Array.isArray(n)?n:[n,n],E=[C[0]*v[2]||1,C[1]*v[2]||1],k=b*v[2],N=Array.isArray(d)?d:[d,d],M=S?[k,k]:E,j=[N[0]*v[2]||1+M[0]/2,N[1]*v[2]||1+M[1]/2],P=`${w}${e||""}`;return(0,i.jsxs)("svg",{className:(0,a.Z)(["react-flow__background",m]),style:{...f,...el,"--xy-background-color-props":g,"--xy-background-pattern-color-props":u},ref:y,"data-testid":"rf__background",children:[(0,i.jsx)("pattern",{id:P,x:v[0]%E[0],y:v[1]%E[1],width:E[0],height:E[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${j[0]},-${j[1]})`,children:x?(0,i.jsx)(tL,{radius:k/2,className:h}):(0,i.jsx)(tA,{dimensions:M,lineWidth:l,variant:t,className:h})}),(0,i.jsx)("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${P})`})]})}tz.displayName="Background";let tV=(0,s.memo)(tz);function tZ(){return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:(0,i.jsx)("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function tH(){return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:(0,i.jsx)("path",{d:"M0 0h32v4.2H0z"})})}function tF(){return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:(0,i.jsx)("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function tX(){return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,i.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function tT(){return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,i.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function tW({children:e,className:t,...n}){return(0,i.jsx)("button",{type:"button",className:(0,a.Z)(["react-flow__controls-button",t]),...n,children:e})}let tK=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom});function tQ({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:o=!0,fitViewOptions:r,onZoomIn:l,onZoomOut:s,onFitView:d,onInteractiveChange:u,className:g,children:f,position:h="bottom-left",orientation:y="vertical","aria-label":v="React Flow controls"}){let w=m(),{isInteractive:b,minZoomReached:x,maxZoomReached:S}=p(tK,c.X),{zoomIn:C,zoomOut:k,fitView:N}=eo(),M=()=>{C(),l?.()},j=()=>{k(),s?.()},P=()=>{N(r),d?.()},_=()=>{w.setState({nodesDraggable:!b,nodesConnectable:!b,elementsSelectable:!b}),u?.(!b)};return(0,i.jsxs)(E,{className:(0,a.Z)(["react-flow__controls","horizontal"===y?"horizontal":"vertical",g]),position:h,style:e,"data-testid":"rf__controls","aria-label":v,children:[t&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(tW,{onClick:M,className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:S,children:(0,i.jsx)(tZ,{})}),(0,i.jsx)(tW,{onClick:j,className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:x,children:(0,i.jsx)(tH,{})})]}),n&&(0,i.jsx)(tW,{className:"react-flow__controls-fitview",onClick:P,title:"fit view","aria-label":"fit view",children:(0,i.jsx)(tF,{})}),o&&(0,i.jsx)(tW,{className:"react-flow__controls-interactive",onClick:_,title:"toggle interactivity","aria-label":"toggle interactivity",children:b?(0,i.jsx)(tT,{}):(0,i.jsx)(tX,{})}),f]})}tQ.displayName="Controls";let tY=(0,s.memo)(tQ),tU=(0,s.memo)(function({id:e,x:t,y:n,width:o,height:r,style:l,color:s,strokeColor:d,strokeWidth:c,className:u,borderRadius:g,shapeRendering:f,selected:p,onClick:m}){let{background:h,backgroundColor:y}=l||{};return(0,i.jsx)("rect",{className:(0,a.Z)(["react-flow__minimap-node",{selected:p},u]),x:t,y:n,rx:g,ry:g,width:o,height:r,style:{fill:s||h||y,stroke:d,strokeWidth:c},shapeRendering:f,onClick:m?t=>m(t,e):void 0})}),tG=e=>e.nodes.map(e=>e.id),tJ=e=>e instanceof Function?e:()=>e,tq=(0,s.memo)(function({id:e,nodeColorFunc:t,nodeStrokeColorFunc:n,nodeClassNameFunc:o,nodeBorderRadius:r,nodeStrokeWidth:a,shapeRendering:s,NodeComponent:d,onClick:u}){let{node:g,x:f,y:m,width:h,height:y}=p(t=>{let n=t.nodeLookup.get(e),{x:o,y:r}=n.internals.positionAbsolute,{width:i,height:a}=(0,l.Rf)(n);return{node:n,x:o,y:r,width:i,height:a}},c.X);return g&&!g.hidden&&(0,l.nb)(g)?(0,i.jsx)(d,{x:f,y:m,width:h,height:y,style:g.style,selected:!!g.selected,className:o(g),color:t(g),borderRadius:r,strokeColor:n(g),strokeWidth:a,shapeRendering:s,onClick:u,id:g.id}):null});var t0=(0,s.memo)(function({nodeStrokeColor:e,nodeColor:t,nodeClassName:n="",nodeBorderRadius:o=5,nodeStrokeWidth:r,nodeComponent:a=tU,onClick:l}){let s=p(tG,c.X),d=tJ(t),u=tJ(e),g=tJ(n),f="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return(0,i.jsx)(i.Fragment,{children:s.map(e=>(0,i.jsx)(tq,{id:e,nodeColorFunc:d,nodeStrokeColorFunc:u,nodeClassNameFunc:g,nodeBorderRadius:o,nodeStrokeWidth:r,NodeComponent:a,onClick:l,shapeRendering:f},e))})});let t1=e=>{let t={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:t,boundingRect:e.nodeLookup.size>0?(0,l.oI)((0,l.W0)(e.nodeLookup),t):t,rfId:e.rfId,panZoom:e.panZoom,translateExtent:e.translateExtent,flowWidth:e.width,flowHeight:e.height}};function t3({style:e,className:t,nodeStrokeColor:n,nodeColor:o,nodeClassName:r="",nodeBorderRadius:d=5,nodeStrokeWidth:u,nodeComponent:g,bgColor:f,maskColor:h,maskStrokeColor:y,maskStrokeWidth:v,position:w="bottom-right",onClick:b,onNodeClick:x,pannable:S=!1,zoomable:C=!1,ariaLabel:k="React Flow mini map",inversePan:N,zoomStep:M=10,offsetScale:j=5}){let P=m(),_=(0,s.useRef)(null),{boundingRect:R,viewBB:D,rfId:I,panZoom:O,translateExtent:A,flowWidth:L,flowHeight:$}=p(t1,c.X),B=e?.width??200,z=e?.height??150,V=R.width/B,Z=R.height/z,H=Math.max(V,Z),F=H*B,X=H*z,T=j*H,W=R.x-(F-R.width)/2-T,K=R.y-(X-R.height)/2-T,Q=F+2*T,Y=X+2*T,U=`react-flow__minimap-desc-${I}`,G=(0,s.useRef)(0),J=(0,s.useRef)();G.current=H,(0,s.useEffect)(()=>{if(_.current&&O)return J.current=(0,l.FD)({domNode:_.current,panZoom:O,getTransform:()=>P.getState().transform,getViewScale:()=>G.current}),()=>{J.current?.destroy()}},[O]),(0,s.useEffect)(()=>{J.current?.update({translateExtent:A,width:L,height:$,inversePan:N,pannable:S,zoomStep:M,zoomable:C})},[S,C,N,M,A,L,$]);let q=b?e=>{let[t,n]=J.current?.pointer(e)||[0,0];b(e,{x:t,y:n})}:void 0,ee=x?(0,s.useCallback)((e,t)=>{let n=P.getState().nodeLookup.get(t);x(e,n)},[]):void 0;return(0,i.jsx)(E,{position:w,style:{...e,"--xy-minimap-background-color-props":"string"==typeof f?f:void 0,"--xy-minimap-mask-background-color-props":"string"==typeof h?h:void 0,"--xy-minimap-mask-stroke-color-props":"string"==typeof y?y:void 0,"--xy-minimap-mask-stroke-width-props":"number"==typeof v?v*H:void 0,"--xy-minimap-node-background-color-props":"string"==typeof o?o:void 0,"--xy-minimap-node-stroke-color-props":"string"==typeof n?n:void 0,"--xy-minimap-node-stroke-width-props":"string"==typeof u?u:void 0},className:(0,a.Z)(["react-flow__minimap",t]),"data-testid":"rf__minimap",children:(0,i.jsxs)("svg",{width:B,height:z,viewBox:`${W} ${K} ${Q} ${Y}`,className:"react-flow__minimap-svg",role:"img","aria-labelledby":U,ref:_,onClick:q,children:[k&&(0,i.jsx)("title",{id:U,children:k}),(0,i.jsx)(t0,{onClick:ee,nodeColor:o,nodeStrokeColor:n,nodeBorderRadius:d,nodeClassName:r,nodeStrokeWidth:u,nodeComponent:g}),(0,i.jsx)("path",{className:"react-flow__minimap-mask",d:`M${W-T},${K-T}h${Q+2*T}v${Y+2*T}h${-Q-2*T}z
        M${D.x},${D.y}h${D.width}v${D.height}h${-D.width}z`,fillRule:"evenodd",pointerEvents:"none"})]})})}t3.displayName="MiniMap",(0,s.memo)(t3),(0,s.memo)(function({nodeId:e,position:t,variant:n=l.pB.Handle,className:o,style:r={},children:d,color:c,minWidth:u=10,minHeight:g=10,maxWidth:f=Number.MAX_VALUE,maxHeight:p=Number.MAX_VALUE,keepAspectRatio:h=!1,shouldResize:y,onResizeStart:v,onResize:w,onResizeEnd:b}){let x=ex(),S="string"==typeof e?e:x,C=m(),E=(0,s.useRef)(null),k=n===l.pB.Line?"right":"bottom-right",N=t??k,M=(0,s.useRef)(null);(0,s.useEffect)(()=>{if(E.current&&S)return M.current||(M.current=(0,l.Cz)({domNode:E.current,nodeId:S,getStoreItems:()=>{let{nodeLookup:e,transform:t,snapGrid:n,snapToGrid:o,nodeOrigin:r,domNode:i}=C.getState();return{nodeLookup:e,transform:t,snapGrid:n,snapToGrid:o,nodeOrigin:r,paneDomNode:i}},onChange:(e,t)=>{let{triggerNodeChanges:n,nodeLookup:o,parentLookup:r,nodeOrigin:i}=C.getState(),a=[],s={x:e.x,y:e.y},d=o.get(S);if(d&&d.expandParent&&d.parentId){let t=d.origin??i,n=e.width??d.measured.width,c=e.height??d.measured.height,u={id:d.id,parentId:d.parentId,rect:{width:n,height:c,...(0,l.ZB)({x:e.x??d.position.x,y:e.y??d.position.y},{width:n,height:c},d.parentId,o,t)}},g=(0,l.so)([u],o,r,i);a.push(...g),s.x=e.x?Math.max(t[0]*n,e.x):void 0,s.y=e.y?Math.max(t[1]*c,e.y):void 0}if(void 0!==s.x&&void 0!==s.y){let e={id:S,type:"position",position:{...s}};a.push(e)}if(void 0!==e.width&&void 0!==e.height){let t={id:S,type:"dimensions",resizing:!0,setAttributes:!0,dimensions:{width:e.width,height:e.height}};a.push(t)}for(let e of t){let t={...e,type:"position"};a.push(t)}n(a)},onEnd:()=>{C.getState().triggerNodeChanges([{id:S,type:"dimensions",resizing:!1}])}})),M.current.update({controlPosition:N,boundaries:{minWidth:u,minHeight:g,maxWidth:f,maxHeight:p},keepAspectRatio:h,onResizeStart:v,onResize:w,onResizeEnd:b,shouldResize:y}),()=>{M.current?.destroy()}},[N,u,g,f,p,h,v,w,b,y]);let j=N.split("-"),P=n===l.pB.Line?"borderColor":"backgroundColor",_=c?{...r,[P]:c}:r;return(0,i.jsx)("div",{className:(0,a.Z)(["react-flow__resize-control","nodrag",...j,n,o]),ref:E,style:_,children:d})})}}]);