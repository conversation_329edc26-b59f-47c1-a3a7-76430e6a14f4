"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6734],{91227:function(e,i,n){n.d(i,{Z:function(){return V}});var l=n(85893),t=n(67294),o=n(6459),s=n(91816),a=n(8186),r=n(90092),c=n(38491),d=n(68029),u=n(88329),h=n(73636),p=n(61771),x=n(12519),m=n(90608),g=n(12095),f=n(48899),v=n(34302),b=n(63162),j=n(650),y=n(65095),_=n(45130),w=n(51590),k=n(85699),I=n(17494),F=n(31837),z=n(92562),C=n(31082),S=n(45744),E=n(17597),Z=n(61951),L=n(80153),D=n(47426),A=n(93502),T=n(63166),R=n(92223),O=n(47404);n(71692);var N=n(19399),U=n(72510),B=n(93775),J=n(94124),$=n(5490),G=n(34614),K=n(14437),q=n(57391),P=n(21360),W=n(70010),M=n(73244),Y=n(45443);let H=e=>{let{visible:i,title:n,placeholder:o,onConfirm:s,onCancel:d}=e,{t:u}=(0,F.$G)("common"),[h,p]=(0,t.useState)(""),x=(0,t.useRef)(null);(0,t.useEffect)(()=>{i&&(p(""),setTimeout(()=>{x.current&&x.current.focus()},100))},[i]);let m=()=>{h.trim()&&s(h.trim()),g()},g=()=>{p(""),d()},f=e=>{"Escape"===e.key?g():"Enter"===e.key&&(e.ctrlKey||e.metaKey)&&m()};return(0,l.jsxs)($.u_,{isOpen:i,onClose:g,size:"lg",isCentered:!0,children:[(0,l.jsx)(G.Z,{}),(0,l.jsxs)(K.h,{children:[(0,l.jsx)(q.x,{children:n||u("user_input")}),(0,l.jsx)(P.o,{}),(0,l.jsx)(W.f,{children:(0,l.jsxs)(a.g,{spacing:4,align:"stretch",children:[(0,l.jsx)(M.g,{ref:x,value:h,onChange:e=>p(e.target.value),placeholder:o||u("please_enter_your_requirements"),rows:6,resize:"vertical",onKeyDown:f}),(0,l.jsx)(r.x,{fontSize:"sm",color:"gray.500",children:u("input_hint")})]})}),(0,l.jsxs)(Y.m,{children:[(0,l.jsx)(c.z,{variant:"outline",mr:3,onClick:g,children:u("cancel")}),(0,l.jsx)(c.z,{colorScheme:"blue",onClick:m,isDisabled:!h.trim(),children:u("confirm")})]})]})]})},Q=e=>{let{data:i,setData:n,onDelete:$,showDeleteButton:G=!1,app:K,mode:q,onAIAction:P}=e,W=(0,o.p)(),{openShareModal:M}=(0,C.K)(),Y=(0,t.useRef)(null),{t:Q}=(0,F.$G)("common"),[V,X]=(0,t.useState)((null==i?void 0:i.isLiked)||!1),{user:ee}=(0,Z.a)(),[ei,en]=(0,t.useState)(),[el,et]=(0,t.useState)(!1),[eo,es]=(0,t.useState)(!1),ea=(0,s.S)({base:!0,md:!1}),[er,ec]=(0,t.useState)(),[ed,eu]=(0,t.useState)(!1),[eh,ep]=(0,t.useState)(!1),ex=async()=>{try{let e=await fetch("".concat(E.J,"/ai/updateArtifact"),{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({_id:i._id,app:K,mode:q,data:{folder:1}})});if(e.ok)$(i._id),W({title:"data deleted",status:"success",duration:3e3,isClosable:!0});else throw Error("Failed to delete card")}catch(e){console.error("Error deleting card:",e),W({title:"Error deleting card",status:"error",duration:3e3,isClosable:!0})}},em=async()=>{try{let e=await fetch("".concat(E.J,"/ai/likeArtifact"),{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({_id:i._id,isLiked:!V})});if(e.ok)X(!V),W({title:V?"Removed from favorites":"Added to favorites",status:"success",duration:2e3,isClosable:!0});else throw Error("Failed to update like status")}catch(e){console.error("Error updating like status:",e),W({title:"Error updating like status",status:"error",duration:3e3,isClosable:!0})}},eg=async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if("improve_codes"===e&&!n){eu(!0);return}if(P){ep(!0);try{await P(e,i,n)}catch(e){console.error("AI action error:",e)}finally{ep(!1)}}},ef=(0,t.useCallback)(()=>{if((null==i?void 0:i.type)!="svg")return;let e=(0,N.w)(i.content),n=new Blob([e],{type:"image/svg+xml"}),l=document.createElement("a"),t=URL.createObjectURL(n);l.href=t,l.download="artifact.svg",document.body.appendChild(l),l.click(),document.body.removeChild(l)},[i]);return((0,t.useEffect)(()=>{X(null==i?void 0:i.isLiked)},[i]),(0,t.useEffect)(()=>{"flow"===i.type&&en(i)},[i]),null==i?void 0:i.err)?(0,l.jsxs)(a.g,{spacing:3,children:[(0,l.jsx)(r.x,{children:"exceed_msg_limit"===i.err&&Q("upgrade_to_vip_msg")||i.err}),"exceed_msg_limit"===i.err&&(0,l.jsx)(c.z,{"aria-label":Q("upgrade_to_vip"),colorScheme:"blue",size:"sm",onClick:()=>{window.open("https://app.funblocks.net/#/aiplans","_blank")},children:Q("upgrade_to_vip")})]}):(0,l.jsxs)(d.x,{borderWidth:1,borderRadius:"lg",overflow:"hidden",children:[i&&("imageInsights"===q||[D.IF.graphics,D.IF.onePageSlides,D.IF.infographic,D.IF.mindsnap,D.IF.insightcards].includes(K))&&(0,l.jsx)(z.Z,{app:K,artifact:i,svgRef:Y}),i&&("flow"===i.type||[D.IF.mindmap,D.IF.reading,D.IF.movie,D.IF.feynman,D.IF.bloom,D.IF.solo,D.IF.dok,D.IF.marzano,D.IF.layeredExplanation,D.IF.criticalThinking,D.IF.reflection,D.IF.refineQuestion,D.IF.bias,D.IF.brainstorming,D.IF.mindkit,D.IF.businessmodel,D.IF.startupmentor,D.IF.okr,D.IF.decision,D.IF.planner,D.IF.youtube,D.IF.counselor,D.IF.dreamlens,D.IF.horoscope,D.IF.art,D.IF.photo,D.IF.erase,D.IF.avatar,D.IF.imageEditor].includes(K))&&(0,l.jsx)(d.x,{style:{width:"100%",height:ea?er?void 0:300:600},ref:Y,align:"left",children:(0,l.jsx)(L.Z,{app:K,mode:q,doc:ei,imgGenerated:er,setImgGenerated:ec,content:i.content,mimeContents:i.mimeContents,context:i.context,ai_action:i.action,onDocSaved:e=>{en(e),e&&n(i=>({...i,_id:e._id}))}})}),(K===D.IF.slides||"slides"===i.type)&&(null==i?void 0:i.hid)&&(0,l.jsx)("div",{ref:Y,style:{width:(null==i?void 0:i.hid)?"100%":1,height:(null==i?void 0:i.hid)?540:1},children:(0,l.jsx)("iframe",{className:"nodrag",id:"slides-frame",style:{width:"100%",height:"100%"},src:"https://service.funblocks.net/present.html?theme=sky&hid=".concat(i.hid||""),title:"FunBlocks AI Slides"})}),(null==i?void 0:i.type)==="markdown"&&(0,l.jsx)(d.x,{p:4,textAlign:"left",ref:Y,children:(0,l.jsx)(U.U,{children:i.content})}),(null==i?void 0:i.err)&&(0,l.jsx)("div",{children:i.err}),(0,l.jsxs)(u.k,{justifyContent:"space-between",p:1,bg:"gray.100",width:"100%",alignItems:"center",children:[(0,l.jsxs)(h.U,{spacing:1,fontSize:"sm",color:"gray.500",marginLeft:1,children:[(0,l.jsx)("span",{style:{maxWidth:"300px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:(null==i?void 0:i.promptLabel)||(null==i?void 0:i.title)||""}),(null==ei?void 0:ei.type)==="flow"&&(0,l.jsx)(p.u,{label:Q("to_aiflow_tips"),"aria-label":"Share tooltip",children:(0,l.jsx)(c.z,{"aria-label":Q("to_aiflow_tips"),color:"dodgerblue",size:"sm",onClick:()=>window.open("https://app.funblocks.net/#/aiflow?hid="+ei.hid,"_blank"),children:Q("to_aiflow")})}),(null==i?void 0:i.type)==="slides"&&(0,l.jsx)(x.h,{icon:(0,l.jsx)(A._,{size:24}),"aria-label":"presentation",color:"gray.500",size:"sm",onClick:()=>window.open("https://service.funblocks.net/present.html?theme=sky&hid=".concat(i.hid),"_blank")}),["slides","markdown"].includes(null==i?void 0:i.type)&&(0,l.jsx)(x.h,{icon:(0,l.jsx)(T.I,{size:20}),"aria-label":"edit",color:"gray.500",size:"sm",onClick:()=>{(null==i?void 0:i.type)==="slides"?window.open("https://app.funblocks.net/#/slidesEditor?hid=".concat(i.hid),"_blank"):(null==i?void 0:i.type)==="markdown"&&window.open("https://app.funblocks.net/#/editor?hid=".concat(i.hid),"_blank")}})]}),(0,l.jsxs)(h.U,{spacing:1,children:[P&&["mermaid","svg"].includes(i.type)&&(0,l.jsx)(p.u,{label:Q("ai_actions"),"aria-label":"AI Actions tooltip",children:(0,l.jsxs)(m.v,{children:[(0,l.jsx)(g.j,{as:x.h,icon:eh?(0,l.jsx)(f.$,{size:"sm"}):(0,l.jsx)(B.s,{size:20}),"aria-label":Q("ai_actions"),color:"dodgerblue",size:"sm",variant:"ghost",isLoading:eh,disabled:eh}),(0,l.jsxs)(v.q,{children:[(0,l.jsx)(b.s,{icon:(0,l.jsx)(J.v,{size:16,color:"dodgerblue"}),onClick:()=>eg("fix_codes_bug"),isDisabled:eh,children:Q("fix_codes_bug")}),(0,l.jsx)(b.s,{icon:(0,l.jsx)(B.s,{size:16,color:"dodgerblue"}),onClick:()=>eg("improve_codes"),isDisabled:eh,children:Q("improve_codes")})]})]})}),![D.IF.mindmap].includes(K)&&(0,l.jsx)(p.u,{label:Q(V?"unlike":(null==ee?void 0:ee._id)===(null==i?void 0:i.userId)?"share_to_showcase":"like"),"aria-label":"Like tooltip",children:(0,l.jsx)(x.h,{icon:V?(0,l.jsx)(j.r,{size:20}):(0,l.jsx)(y.I,{size:20}),"aria-label":Q(V?"unlike":"like"),color:V?"red.500":"gray.500",size:"sm",onClick:em})}),(null==i?void 0:i.type)==="svg"&&(0,l.jsx)(p.u,{label:Q("save_svg"),placement:"bottom",children:(0,l.jsx)(x.h,{icon:(0,l.jsx)(_.v,{size:20}),"aria-label":Q("save_svg"),color:"gray.500",size:"sm",onClick:()=>ef()})}),!["slides"].includes(null==i?void 0:i.type)&&(0,l.jsx)(p.u,{label:Q("download"),"aria-label":"Download tooltip",children:(0,l.jsx)(x.h,{icon:"download"===el?(0,l.jsx)(f.$,{size:"sm"}):(0,l.jsx)(w.U,{size:20}),"aria-label":Q("download"),color:"gray.500",size:"sm",onClick:async()=>{et("download");try{await (0,S.gp)(Y,W)}finally{et(null)}},isLoading:"download"===el})}),(null==i?void 0:i.content)&&(0,l.jsx)(p.u,{label:Q(eo?"copied":"copy"),"aria-label":"Share tooltip",children:(0,l.jsx)(x.h,{icon:eo?(0,l.jsx)(R.x,{color:"green",size:20}):(0,l.jsx)(O.T,{size:20}),"aria-label":Q("share"),color:"gray.500",size:"sm",onClick:()=>{navigator.clipboard.writeText(i.content),es(!0),setTimeout(()=>es(!1),2e3)}})}),(0,l.jsx)(p.u,{label:Q("share"),"aria-label":"Share tooltip",children:(0,l.jsx)(x.h,{icon:"share"===el?(0,l.jsx)(f.$,{size:"sm"}):(0,l.jsx)(k.m,{size:20}),"aria-label":Q("share"),color:"gray.500",size:"sm",onClick:async()=>{et("share");try{await (0,S.Bf)(K,null==i?void 0:i._id,null==i?void 0:i.title,Y,M,W)}finally{et(null)}},isLoading:"share"===el})}),G&&(0,l.jsx)(p.u,{label:Q("delete"),"aria-label":"Delete tooltip",children:(0,l.jsx)(x.h,{icon:(0,l.jsx)(I.H,{size:20}),"aria-label":Q("delete"),color:"gray.500",size:"sm",onClick:ex})})]})]}),(0,l.jsx)(H,{visible:ed,title:Q("improve_codes"),placeholder:Q("please_enter_your_requirements"),onConfirm:e=>{eu(!1),eg("improve_codes",e)},onCancel:()=>eu(!1)})]})};var V=Q},86734:function(e,i,n){var l=n(32692),t=n(85893),o=n(67294),s=n(68029),a=n(8186),r=n(38491),c=n(57879),d=n(18357),u=n(16829),h=n(17597),p=n(91227),x=n(47426),m=n(11752),g=n.n(m);function f(){let e=(0,l.Z)(["\n  column-count: 2;\n  column-gap: 16px;\n  \n  @media (max-width: 768px) {\n    column-count: 1;\n  }\n"]);return f=function(){return e},e}function v(){let e=(0,l.Z)(["\n  break-inside: avoid;\n  margin-bottom: 16px;\n"]);return v=function(){return e},e}function b(){let e=(0,l.Z)(["\n  width: 100%;\n  align-items: stretch;\n  gap: 36px;\n"]);return b=function(){return e},e}function j(){let e=(0,l.Z)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n"]);return j=function(){return e},e}function y(){let e=(0,l.Z)(["\n  padding: 5px 12px;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s;\n  \n  &:hover {\n    background-color: #f7fafc;\n    transform: translateY(-1px);\n  }\n"]);return y=function(){return e},e}let _=(0,u.Z)(s.x)(f()),w=(0,u.Z)(s.x)(v()),k=(0,u.Z)(a.g)(b()),I=(0,u.Z)(s.x)(j()),F=(0,u.Z)(s.x)(y()),z=e=>{let{app:i="",mode:n,collection:l,initial_items:u=[]}=e,{basePath:m}=g()().publicRuntimeConfig,[f,v]=(0,o.useState)(u),[b,j]=(0,o.useState)((null==u?void 0:u.length)?1:0),y=i===x.IF.graphics&&10||"collection"===l&&500||!i&&10||20,[z,C]=(0,o.useState)(null==u||!u.length||u.length===y),[S,E]=(0,d.YD)({threshold:0}),Z=async()=>{try{var e,t;let o=i===x.IF.graphics&&"aiinsights"||"",s=await fetch("".concat(h.J,"/ai/").concat("collection"===l?"showcase":l,"_artifacts?app=").concat(i,"&mode=").concat(n||"","&service=").concat(o,"&pageNum=").concat(b,"&pageSize=").concat(y),{credentials:"include"}),a=await s.json();(null==a?void 0:null===(e=a.data)||void 0===e?void 0:e.length)&&(v(e=>[...e,...(null==a?void 0:a.data)||[]]),j(e=>e+1)),C((null==a?void 0:null===(t=a.data)||void 0===t?void 0:t.length)==y)}catch(e){console.error("Error fetching cards:",e)}},L=e=>{v(i=>i.filter(i=>i._id!==e))};return(0,o.useEffect)(()=>{E&&z&&(i===x.IF.graphics||0===b)&&Z()},[E]),(0,t.jsxs)(s.x,{width:"100%",maxWidth:"900px",children:[i===x.IF.graphics?(0,t.jsx)(_,{children:f.map(e=>(0,t.jsx)(w,{children:(0,t.jsx)(p.Z,{data:e,app:i,onDelete:L,showDeleteButton:"my"===l})},e._id))}):"my"===l?(0,t.jsx)(k,{children:f.map(e=>(0,t.jsx)(p.Z,{data:e,app:e.app,mode:e.mode,onDelete:L,showDeleteButton:!0},e._id))}):(0,t.jsx)(I,{children:f.map(e=>{var i;return(0,t.jsx)(F,{children:(0,t.jsx)("a",{href:"".concat(m,"/share/").concat(e.app,"/").concat(e._id,"/").concat(null===(i=e.title)||void 0===i?void 0:i.replaceAll(" ","-")),target:"_blank",children:e.title||"未命名作品"})},e._id)})}),(0,t.jsxs)(a.g,{ref:S,pt:4,pb:4,children:[![l].includes("showcase")&&(0,t.jsx)(r.z,{onClick:Z,isLoading:E&&(i===x.IF.graphics||0===b),children:"Load More"}),[l].includes("showcase")&&(0,t.jsx)(c.r,{color:"dodgerblue",href:"".concat(m,"/collections/").concat(i),isExternal:!0,children:"More"})]})]})};i.Z=z}}]);