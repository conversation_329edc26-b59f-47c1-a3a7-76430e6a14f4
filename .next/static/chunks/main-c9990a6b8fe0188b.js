(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[179],{40037:function(){"trimStart"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),"trimEnd"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),"description"in Symbol.prototype||Object.defineProperty(Symbol.prototype,"description",{configurable:!0,get:function(){var e=/\((.*)\)/.exec(this.toString());return e?e[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(e,t){return t=this.concat.apply([],this),e>1&&t.some(Array.isArray)?t.flat(e-1):t},Array.prototype.flatMap=function(e,t){return this.map(e,t).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(e){if("function"!=typeof e)return this.then(e,e);var t=this.constructor||Promise;return this.then(function(r){return t.resolve(e()).then(function(){return r})},function(r){return t.resolve(e()).then(function(){throw r})})}),Object.fromEntries||(Object.fromEntries=function(e){return Array.from(e).reduce(function(e,t){return e[t[0]]=t[1],e},{})})},98684:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addBasePath=function(e,t){return a.normalizePathTrailingSlash(n.addPathPrefix(e,"/aitools"))};var n=r(95391),a=r(82392);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22725:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addLocale=void 0;var n=r(82392);t.addLocale=function(e){for(var t=arguments.length,a=Array(t>1?t-1:0),o=1;o<t;o++)a[o-1]=arguments[o];return n.normalizePathTrailingSlash(r(77459).addLocale(e,...a))},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38748:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.detectDomainLocale=void 0,t.detectDomainLocale=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return r(48633).detectDomainLocale(...t)},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94119:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hasBasePath=function(e){return n.pathHasPrefix(e,"/aitools")};var n=r(1259);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56007:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'.concat(e.props["data-href"],'"]')))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let r=t[e.type]||[];r.push(e),t[e.type]=r});let r=t.title?t.title[0]:null,o="";if(r){let{children:e}=r.props;o="string"==typeof e?e:Array.isArray(e)?e.join(""):""}o!==document.title&&(document.title=o),["meta","base","link","style","script"].forEach(e=>{(function(e,t){let r=document.getElementsByTagName("head")[0],o=r.querySelector("meta[name=next-head-count]"),l=Number(o.content),i=[];for(let t=0,r=o.previousElementSibling;t<l;t++,r=(null==r?void 0:r.previousElementSibling)||null){var s;(null==r?void 0:null==(s=r.tagName)?void 0:s.toLowerCase())===e&&i.push(r)}let u=t.map(n).filter(e=>{for(let t=0,r=i.length;t<r;t++){let r=i[t];if(a(r,e))return i.splice(t,1),!1}return!0});i.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),u.forEach(e=>r.insertBefore(e,o)),o.content=(l-i.length+u.length).toString()})(e,t[e]||[])})}}},t.isEqualNode=a,t.DOMAttributeNames=void 0;let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function n(e){let{type:t,props:n}=e,a=document.createElement(t);for(let e in n){if(!n.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===n[e])continue;let o=r[e]||e.toLowerCase();"script"===t&&("async"===o||"defer"===o||"noModule"===o)?a[o]=!!n[e]:a.setAttribute(o,n[e])}let{children:o,dangerouslySetInnerHTML:l}=n;return l?a.innerHTML=l.__html||"":o&&(a.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):""),a}function a(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let r=t.getAttribute("nonce");if(r&&!e.getAttribute("nonce")){let n=t.cloneNode(!0);return n.setAttribute("nonce",""),n.nonce=r,r===e.nonce&&e.isEqualNode(n)}}return e.isEqualNode(t)}t.DOMAttributeNames=r,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77339:function(e,t,r){"use strict";let n,a,o,l,i,s,u,c,d,f,p,h;Object.defineProperty(t,"__esModule",{value:!0});let m=r(99402).Z;Object.defineProperty(t,"__esModule",{value:!0}),t.initialize=function(){return V.apply(this,arguments)},t.hydrate=function(e){return es.apply(this,arguments)},t.emitter=t.router=t.version=void 0;var g=r(92612).Z,y=r(31032).Z,v=r(12845).Z;r(99402).Z,r(40037);var _=v(r(67294)),P=v(r(20745)),b=r(8404),S=v(r(35660)),w=r(63462),E=r(95509),x=r(78689),j=r(50466),C=r(88027),O=r(63794),R=r(32207),L=v(r(56007)),M=v(r(95181)),A=v(r(59302)),T=r(48982),I=r(90387),N=r(80676),k=r(59977),D=r(19320),B=r(94119),U=r(21018),H=r(26985),F=r(89334),W=v(r(69777));t.version="13.2.3",t.router=n;let q=S.default();t.emitter=q;let Z=e=>[].slice.call(e),z=!1;self.__next_require__=r;class G extends _.default.Component{componentDidCatch(e,t){this.props.fn(e,t)}componentDidMount(){this.scrollToHash(),n.isSsr&&(a.isFallback||a.nextExport&&(x.isDynamicRoute(n.pathname)||location.search||z)||a.props&&a.props.__N_SSG&&(location.search||z))&&n.replace(n.pathname+"?"+String(j.assign(j.urlQueryToSearchParams(n.query),new URLSearchParams(location.search))),o,{_h:1,shallow:!a.isFallback&&!z}).catch(e=>{if(!e.cancelled)throw e})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:e}=location;if(!(e=e&&e.substring(1)))return;let t=document.getElementById(e);t&&setTimeout(()=>t.scrollIntoView(),0)}render(){return this.props.children}}function V(){return(V=g(function*(){arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=a,h=a.defaultLocale;let e=a.assetPrefix||"";r.p="".concat(e,"/_next/"),C.setConfig({serverRuntimeConfig:{},publicRuntimeConfig:a.runtimeConfig||{}}),o=O.getURL(),B.hasBasePath(o)&&(o=D.removeBasePath(o));{let{normalizeLocalePath:e}=r(94317),{detectDomainLocale:t}=r(48633),{parseRelativeUrl:n}=r(36305),{formatUrl:l}=r(4611);if(a.locales){let r=n(o),i=e(r.pathname,a.locales);i.detectedLocale?(r.pathname=i.pathname,o=l(r)):h=a.locale;let s=t(void 0,window.location.hostname);s&&(h=s.defaultLocale)}}if(a.scriptLoader){let{initScriptLoader:e}=r(20699);e(a.scriptLoader)}l=new M.default(a.buildId,e);let t=e=>{let[t,r]=e;return l.routeLoader.onEntrypoint(t,r)};return window.__NEXT_P&&window.__NEXT_P.map(e=>setTimeout(()=>t(e),0)),window.__NEXT_P=[],window.__NEXT_P.push=t,(s=L.default()).getIsSsr=()=>n.isSsr,i=document.getElementById("__next"),{assetPrefix:e}})).apply(this,arguments)}function X(e,t){return _.default.createElement(e,Object.assign({},t))}function Y(e){var t;let{children:r}=e;return _.default.createElement(G,{fn:e=>K({App:d,err:e}).catch(e=>console.error("Error rendering page: ",e))},_.default.createElement(U.AppRouterContext.Provider,{value:H.adaptForAppRouterInstance(n)},_.default.createElement(F.SearchParamsContext.Provider,{value:H.adaptForSearchParams(n)},_.default.createElement(H.PathnameContextProviderAdapter,{router:n,isAutoExport:null!=(t=self.__NEXT_DATA__.autoExport)&&t},_.default.createElement(w.RouterContext.Provider,{value:I.makePublicRouterInstance(n)},_.default.createElement(b.HeadManagerContext.Provider,{value:s},_.default.createElement(k.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/aitools/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}},r)))))))}let $=e=>t=>{let r=y({},t,{Component:p,err:a.err,router:n});return _.default.createElement(Y,null,X(e,r))};function K(e){let{App:t,err:i}=e;return console.error(i),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),l.loadPage("/_error").then(n=>{let{page:a,styleSheets:o}=n;return(null==u?void 0:u.Component)===a?Promise.resolve().then(()=>m(r(89185))).then(n=>Promise.resolve().then(()=>m(r(46029))).then(r=>(t=r.default,e.App=t,n))).then(e=>({ErrorComponent:e.default,styleSheets:[]})):{ErrorComponent:a,styleSheets:o}}).then(r=>{var l;let{ErrorComponent:s,styleSheets:u}=r,c=$(t),d={Component:s,AppTree:c,router:n,ctx:{err:i,pathname:a.page,query:a.query,asPath:o,AppTree:c}};return Promise.resolve((null==(l=e.props)?void 0:l.err)?e.props:O.loadGetInitialProps(t,d)).then(t=>eo(y({},e,{err:i,Component:s,styleSheets:u,props:t})))})}function J(e){let{callback:t}=e;return _.default.useLayoutEffect(()=>t(),[t]),null}let Q=null,ee=!0;function et(){["beforeRender","afterHydrate","afterRender","routeChange"].forEach(e=>performance.clearMarks(e))}function er(){O.ST&&(performance.mark("afterHydrate"),performance.measure("Next.js-before-hydration","navigationStart","beforeRender"),performance.measure("Next.js-hydration","beforeRender","afterHydrate"),f&&performance.getEntriesByName("Next.js-hydration").forEach(f),et())}function en(){if(!O.ST)return;performance.mark("afterRender");let e=performance.getEntriesByName("routeChange","mark");e.length&&(performance.measure("Next.js-route-change-to-render",e[0].name,"beforeRender"),performance.measure("Next.js-render","beforeRender","afterRender"),f&&(performance.getEntriesByName("Next.js-render").forEach(f),performance.getEntriesByName("Next.js-route-change-to-render").forEach(f)),et(),["Next.js-route-change-to-render","Next.js-render"].forEach(e=>performance.clearMeasures(e)))}function ea(e){let{callbacks:t,children:r}=e;return _.default.useLayoutEffect(()=>t.forEach(e=>e()),[t]),_.default.useEffect(()=>{A.default(f)},[]),r}function eo(e){let t,{App:r,Component:a,props:o,err:l}=e,s="initial"in e?void 0:e.styleSheets;a=a||u.Component,o=o||u.props;let d=y({},o,{Component:a,err:l,router:n});u=d;let f=!1,p=new Promise((e,r)=>{c&&c(),t=()=>{c=null,e()},c=()=>{f=!0,c=null;let e=Error("Cancel rendering route");e.cancelled=!0,r(e)}});function h(){t()}!function(){if(!s)return;let e=Z(document.querySelectorAll("style[data-n-href]")),t=new Set(e.map(e=>e.getAttribute("data-n-href"))),r=document.querySelector("noscript[data-n-css]"),n=null==r?void 0:r.getAttribute("data-n-css");s.forEach(e=>{let{href:r,text:a}=e;if(!t.has(r)){let e=document.createElement("style");e.setAttribute("data-n-href",r),e.setAttribute("media","x"),n&&e.setAttribute("nonce",n),document.head.appendChild(e),e.appendChild(document.createTextNode(a))}})}();let m=_.default.createElement(_.default.Fragment,null,_.default.createElement(J,{callback:function(){if(s&&!f){let e=new Set(s.map(e=>e.href)),t=Z(document.querySelectorAll("style[data-n-href]")),r=t.map(e=>e.getAttribute("data-n-href"));for(let n=0;n<r.length;++n)e.has(r[n])?t[n].removeAttribute("media"):t[n].setAttribute("media","x");let n=document.querySelector("noscript[data-n-css]");n&&s.forEach(e=>{let{href:t}=e,r=document.querySelector('style[data-n-href="'.concat(t,'"]'));r&&(n.parentNode.insertBefore(r,n.nextSibling),n=r)}),Z(document.querySelectorAll("link[data-n-p]")).forEach(e=>{e.parentNode.removeChild(e)})}if(e.scroll){let{x:t,y:r}=e.scroll;E.handleSmoothScroll(()=>{window.scrollTo(t,r)})}}}),_.default.createElement(Y,null,X(r,d),_.default.createElement(R.Portal,{type:"next-route-announcer"},_.default.createElement(T.RouteAnnouncer,null))));return!function(e,t){O.ST&&performance.mark("beforeRender");let r=t(ee?er:en);if(Q){let e=_.default.startTransition;e(()=>{Q.render(r)})}else Q=P.default.hydrateRoot(e,r,{onRecoverableError:W.default}),ee=!1}(i,e=>_.default.createElement(ea,{callbacks:[e,h]},m)),p}function el(e){return ei.apply(this,arguments)}function ei(){return(ei=g(function*(e){if(e.err){yield K(e);return}try{yield eo(e)}catch(r){let t=N.getProperError(r);if(t.cancelled)throw t;yield K(y({},e,{err:t}))}})).apply(this,arguments)}function es(){return(es=g(function*(e){let r=a.err;try{let e=yield l.routeLoader.whenEntrypoint("/_app");if("error"in e)throw e.error;let{component:t,exports:r}=e;d=t,r&&r.reportWebVitals&&(f=e=>{let t,{id:n,name:a,startTime:o,value:l,duration:i,entryType:s,entries:u,attribution:c}=e,d="".concat(Date.now(),"-").concat(Math.floor(Math.random()*(9e12-1))+1e12);u&&u.length&&(t=u[0].startTime);let f={id:n||d,name:a,startTime:o||t,value:null==l?i:l,label:"mark"===s||"measure"===s?"custom":"web-vital"};c&&(f.attribution=c),r.reportWebVitals(f)});let n=yield l.routeLoader.whenEntrypoint(a.page);if("error"in n)throw n.error;p=n.component}catch(e){r=N.getProperError(e)}window.__NEXT_PRELOADREADY&&(yield window.__NEXT_PRELOADREADY(a.dynamicIds)),t.router=n=I.createRouter(a.page,a.query,o,{initialProps:a.props,pageLoader:l,App:d,Component:p,wrapApp:$,err:r,isFallback:Boolean(a.isFallback),subscription:(e,t,r)=>el(Object.assign({},e,{App:t,scroll:r})),locale:a.locale,locales:a.locales,defaultLocale:h,domainLocales:a.domainLocales,isPreview:a.isPreview}),z=yield n._initialMatchesMiddlewarePromise;let i={App:d,initial:!0,Component:p,props:a.props,err:r};(null==e?void 0:e.beforeRender)&&(yield e.beforeRender()),el(i)})).apply(this,arguments)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22870:function(e,t,r){"use strict";var n=r(77339);window.next={version:n.version,get router(){return n.router},emitter:n.emitter},n.initialize({}).then(()=>n.hydrate()).catch(console.error),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82392:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizePathTrailingSlash=void 0;var n=r(86316),a=r(4943);let o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=a.parsePath(e);return"".concat(n.removeTrailingSlash(t)).concat(r).concat(o)};t.normalizePathTrailingSlash=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69777:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){let r=e.digest||t.digest,a="function"==typeof reportError?reportError:e=>{window.console.error(e)};r!==n.NEXT_DYNAMIC_NO_SSR_CODE&&a(e)};var n=r(25958);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95181:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(12845).Z,a=r(98684),o=r(89493),l=n(r(73891)),i=r(22725),s=r(78689),u=r(36305),c=r(86316),d=r(22669);t.default=class{getPageList(){return d.getClientBuildManifest().then(e=>e.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[],window.__MIDDLEWARE_MATCHERS}getDataHref(e){let{asPath:t,href:r,locale:n}=e,{pathname:d,query:f,search:p}=u.parseRelativeUrl(r),{pathname:h}=u.parseRelativeUrl(t),m=c.removeTrailingSlash(d);if("/"!==m[0])throw Error('Route name should start with a "/", got "'.concat(m,'"'));return(e=>{let t=l.default(c.removeTrailingSlash(i.addLocale(e,n)),".json");return a.addBasePath("/_next/data/".concat(this.buildId).concat(t).concat(p),!0)})(e.skipInterpolation?h:s.isDynamicRoute(m)?o.interpolateAs(d,h,f).result:m)}_isSsg(e){return this.promisedSsgManifest.then(t=>t.has(e))}loadPage(e){return this.routeLoader.loadRoute(e).then(e=>{if("component"in e)return{page:e.component,mod:e.exports,styleSheets:e.styles.map(e=>({href:e.href,text:e.content}))};throw e.error})}prefetch(e){return this.routeLoader.prefetch(e)}constructor(e,t){this.routeLoader=d.createRouteLoader(t),this.buildId=e,this.assetPrefix=t,this.promisedSsgManifest=new Promise(e=>{window.__SSG_MANIFEST?e(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{e(window.__SSG_MANIFEST)}})}},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59302:function(e,t,r){"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;let a=["CLS","FCP","FID","INP","LCP","TTFB"];location.href;let o=!1;function l(e){n&&n(e)}var i=e=>{if(n=e,!o)for(let e of(o=!0,a))try{let t;t||(t=r(78018)),t["on".concat(e)](l)}catch(t){console.warn("Failed to track ".concat(e," web-vital"),t)}};t.default=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32207:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Portal=void 0;var n=r(67294),a=r(73935);let o=e=>{let{children:t,type:r}=e,[o,l]=n.useState(null);return n.useEffect(()=>{let e=document.createElement(r);return document.body.appendChild(e),l(e),()=>{document.body.removeChild(e)}},[r]),o?a.createPortal(t,o):null};t.Portal=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19320:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeBasePath=function(e){return(e=e.slice(8)).startsWith("/")||(e="/".concat(e)),e},r(94119),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75776:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeLocale=function(e,t){{let{pathname:r}=n.parsePath(e),a=r.toLowerCase(),o=null==t?void 0:t.toLowerCase();return t&&(a.startsWith("/".concat(o,"/"))||a==="/".concat(o))?"".concat(r.length===t.length+1?"/":"").concat(e.slice(t.length+1)):e}};var n=r(4943);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9311:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cancelIdleCallback=t.requestIdleCallback=void 0;let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)};t.requestIdleCallback=r;let n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};t.cancelIdleCallback=n,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48982:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.RouteAnnouncer=void 0;var n=(0,r(12845).Z)(r(67294)),a=r(90387);let o={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},l=()=>{let{asPath:e}=a.useRouter(),[t,r]=n.default.useState(""),l=n.default.useRef(e);return n.default.useEffect(()=>{if(l.current!==e){if(l.current=e,document.title)r(document.title);else{var t;let n=document.querySelector("h1"),a=null!=(t=null==n?void 0:n.innerText)?t:null==n?void 0:n.textContent;r(a||e)}}},[e]),n.default.createElement("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:o},t)};t.RouteAnnouncer=l,t.default=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22669:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.markAssetError=i,t.isAssetError=function(e){return e&&l in e},t.getClientBuildManifest=c,t.createRouteLoader=function(e){let t=new Map,r=new Map,n=new Map,l=new Map;function c(e){{var t;let n=r.get(e.toString());return n||(document.querySelector('script[src^="'.concat(e,'"]'))?Promise.resolve():(r.set(e.toString(),n=new Promise((r,n)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>n(i(Error("Failed to load script: ".concat(e)))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),n))}}function f(e){let t=n.get(e);return t||n.set(e,t=fetch(e).then(t=>{if(!t.ok)throw Error("Failed to load stylesheet: ".concat(e));return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw i(e)})),t}return{whenEntrypoint:e=>o(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let n=t.get(e);n&&"resolve"in n?r&&(t.set(e,r),n.resolve(r)):(r?t.set(e,r):t.delete(e),l.delete(e))})},loadRoute(r,n){return o(r,l,()=>{let a;return u(d(e,r).then(e=>{let{scripts:n,css:a}=e;return Promise.all([t.has(r)?[]:Promise.all(n.map(c)),Promise.all(a.map(f))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,i(Error("Route did not complete loading: ".concat(r)))).then(e=>{let{entrypoint:t,styles:r}=e,n=Object.assign({styles:r},t);return"error"in t?t:n}).catch(e=>{if(n)throw e;return{error:e}}).finally(()=>null==a?void 0:a())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():d(e,t).then(e=>Promise.all(s?e.scripts.map(e=>{var t,r,n;return t=e.toString(),r="script",new Promise((e,a)=>{let o='\n      link[rel="prefetch"][href^="'.concat(t,'"],\n      link[rel="preload"][href^="').concat(t,'"],\n      script[src^="').concat(t,'"]');if(document.querySelector(o))return e();n=document.createElement("link"),r&&(n.as=r),n.rel="prefetch",n.crossOrigin=void 0,n.onload=e,n.onerror=()=>a(i(Error("Failed to prefetch: ".concat(t)))),n.href=t,document.head.appendChild(n)})}):[])).then(()=>{a.requestIdleCallback(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}},(0,r(12845).Z)(r(73891));var n=r(44991),a=r(9311);function o(e,t,r){let n,a=t.get(e);if(a)return"future"in a?a.future:Promise.resolve(a);let o=new Promise(e=>{n=e});return t.set(e,a={resolve:n,future:o}),r?r().then(e=>(n(e),e)).catch(r=>{throw t.delete(e),r}):o}let l=Symbol("ASSET_LOAD_ERROR");function i(e){return Object.defineProperty(e,l,{})}let s=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(e){return!1}}();function u(e,t,r){return new Promise((n,o)=>{let l=!1;e.then(e=>{l=!0,n(e)}).catch(o),a.requestIdleCallback(()=>setTimeout(()=>{l||o(r)},t))})}function c(){if(self.__BUILD_MANIFEST)return Promise.resolve(self.__BUILD_MANIFEST);let e=new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}});return u(e,3800,i(Error("Failed to load client build manifest")))}function d(e,t){return c().then(r=>{if(!(t in r))throw i(Error("Failed to lookup route: ".concat(t)));let a=r[t].map(t=>e+"/_next/"+encodeURI(t));return{scripts:a.filter(e=>e.endsWith(".js")).map(e=>n.__unsafeCreateTrustedScriptURL(e)),css:a.filter(e=>e.endsWith(".css"))}})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90387:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Router",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"withRouter",{enumerable:!0,get:function(){return s.default}}),t.useRouter=function(){let e=a.default.useContext(l.RouterContext);if(!e)throw Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted");return e},t.createRouter=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.router=new o.default(...t),u.readyCallbacks.forEach(e=>e()),u.readyCallbacks=[],u.router},t.makePublicRouterInstance=function(e){let t={};for(let r of c){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=o.default.events,d.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];return e[r](...n)}}),t},t.default=void 0;var n=r(12845).Z,a=n(r(67294)),o=n(r(76273)),l=r(63462),i=n(r(80676)),s=n(r(38981));let u={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},c=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],d=["push","replace","reload","back","prefetch","beforePopState"];function f(){if(!u.router)throw Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n');return u.router}Object.defineProperty(u,"events",{get:()=>o.default.events}),c.forEach(e=>{Object.defineProperty(u,e,{get(){let t=f();return t[e]}})}),d.forEach(e=>{u[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let a=f();return a[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{u.ready(()=>{o.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let a="on".concat(e.charAt(0).toUpperCase()).concat(e.substring(1));if(u[a])try{u[a](...r)}catch(e){console.error("Error when running the Router event: ".concat(a)),console.error(i.default(e)?"".concat(e.message,"\n").concat(e.stack):e+"")}})})}),t.default=u,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20699:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.handleClientScriptLoad=g,t.initScriptLoader=function(e){e.forEach(g),function(){let e=[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')];e.forEach(e=>{let t=e.id||e.getAttribute("src");p.add(t)})}()},t.default=void 0;var n=r(31032).Z,a=r(12845).Z,o=r(99402).Z,l=r(38774).Z,i=a(r(73935)),s=o(r(67294)),u=r(8404),c=r(56007),d=r(9311);let f=new Map,p=new Set,h=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy"],m=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:a=null,dangerouslySetInnerHTML:o,children:l="",strategy:i="afterInteractive",onError:s}=e,u=r||t;if(u&&p.has(u))return;if(f.has(t)){p.add(u),f.get(t).then(n,s);return}let d=()=>{a&&a(),p.add(u)},m=document.createElement("script"),g=new Promise((e,t)=>{m.addEventListener("load",function(t){e(),n&&n.call(this,t),d()}),m.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});for(let[r,n]of(o?(m.innerHTML=o.__html||"",d()):l?(m.textContent="string"==typeof l?l:Array.isArray(l)?l.join(""):"",d()):t&&(m.src=t,f.set(t,g)),Object.entries(e))){if(void 0===n||h.includes(r))continue;let e=c.DOMAttributeNames[r]||r.toLowerCase();m.setAttribute(e,n)}"worker"===i&&m.setAttribute("type","text/partytown"),m.setAttribute("data-nscript",i),document.body.appendChild(m)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{d.requestIdleCallback(()=>m(e))}):m(e)}function y(e){let{id:t,src:r="",onLoad:a=()=>{},onReady:o=null,strategy:c="afterInteractive",onError:f}=e,h=l(e,["id","src","onLoad","onReady","strategy","onError"]),{updateScripts:g,scripts:y,getIsSsr:v,appDir:_,nonce:P}=s.useContext(u.HeadManagerContext),b=s.useRef(!1);s.useEffect(()=>{let e=t||r;b.current||(o&&e&&p.has(e)&&o(),b.current=!0)},[o,t,r]);let S=s.useRef(!1);if(s.useEffect(()=>{!S.current&&("afterInteractive"===c?m(e):"lazyOnload"===c&&("complete"===document.readyState?d.requestIdleCallback(()=>m(e)):window.addEventListener("load",()=>{d.requestIdleCallback(()=>m(e))})),S.current=!0)},[e,c]),("beforeInteractive"===c||"worker"===c)&&(g?(y[c]=(y[c]||[]).concat([n({id:t,src:r,onLoad:a,onReady:o,onError:f},h)]),g(y)):v&&v()?p.add(t||r):v&&!v()&&m(e)),_){if("beforeInteractive"===c)return r?(i.default.preload(r,h.integrity?{as:"script",integrity:h.integrity}:{as:"script"}),s.default.createElement("script",{nonce:P,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push(".concat(JSON.stringify([r]),")")}})):(h.dangerouslySetInnerHTML&&(h.children=h.dangerouslySetInnerHTML.__html,delete h.dangerouslySetInnerHTML),s.default.createElement("script",{nonce:P,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push(".concat(JSON.stringify([0,n({},h)]),")")}}));"afterInteractive"===c&&r&&i.default.preload(r,h.integrity?{as:"script",integrity:h.integrity}:{as:"script"})}return null}Object.defineProperty(y,"__nextScript",{value:!0}),t.default=y,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44991:function(e,t){"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),t.__unsafeCreateTrustedScriptURL=function(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38981:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(t){return n.default.createElement(e,Object.assign({router:a.useRouter()},t))}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t};var n=(0,r(12845).Z)(r(67294)),a=r(90387);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46029:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=r(92612).Z,o=(0,r(12845).Z)(r(67294)),l=r(63794);function i(e){return s.apply(this,arguments)}function s(){return(s=a(function*(e){let{Component:t,ctx:r}=e,n=yield l.loadGetInitialProps(t,r);return{pageProps:n}})).apply(this,arguments)}class u extends(n=o.default.Component){render(){let{Component:e,pageProps:t}=this.props;return o.default.createElement(e,Object.assign({},t))}}u.origGetInitialProps=i,u.getInitialProps=i,t.default=u,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89185:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=r(12845).Z,o=a(r(67294)),l=a(r(5443));let i={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function s(e){let{res:t,err:r}=e,n=t&&t.statusCode?t.statusCode:r?r.statusCode:404;return{statusCode:n}}let u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block",textAlign:"left"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};class c extends(n=o.default.Component){render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||i[e]||"An unexpected error has occurred";return o.default.createElement("div",{style:u.error},o.default.createElement(l.default,null,o.default.createElement("title",null,e?"".concat(e,": ").concat(r):"Application error: a client-side exception has occurred")),o.default.createElement("div",null,o.default.createElement("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}".concat(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?o.default.createElement("h1",{className:"next-error-h1",style:u.h1},e):null,o.default.createElement("div",{style:u.desc},o.default.createElement("h2",{style:u.h2},this.props.title||e?r:o.default.createElement(o.default.Fragment,null,"Application error: a client-side exception has occurred (see the browser console for more information)"),"."))))}}c.displayName="ErrorPage",c.getInitialProps=s,c.origGetInitialProps=s,t.default=c,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22227:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AmpStateContext=void 0;var n=(0,r(12845).Z)(r(67294));let a=n.default.createContext({});t.AmpStateContext=a},17363:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isInAmpMode=function(){let{ampFirst:e=!1,hybrid:t=!1,hasQuery:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e||t&&r}},21018:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TemplateContext=t.GlobalLayoutRouterContext=t.LayoutRouterContext=t.AppRouterContext=t.CacheStates=void 0;var n,a,o=(0,r(12845).Z)(r(67294));t.CacheStates=n,(a=n||(t.CacheStates=n={})).LAZY_INITIALIZED="LAZYINITIALIZED",a.DATA_FETCH="DATAFETCH",a.READY="READY";let l=o.default.createContext(null);t.AppRouterContext=l;let i=o.default.createContext(null);t.LayoutRouterContext=i;let s=o.default.createContext(null);t.GlobalLayoutRouterContext=s;let u=o.default.createContext(null);t.TemplateContext=u},10489:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.escapeStringRegexp=function(e){return r.test(e)?e.replace(n,"\\$&"):e};let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g},8404:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HeadManagerContext=void 0;var n=(0,r(12845).Z)(r(67294));let a=n.default.createContext({});t.HeadManagerContext=a},5443:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultHead=c,t.default=void 0;var n=r(31032).Z,a=r(12845).Z,o=(0,r(99402).Z)(r(67294)),l=a(r(55188)),i=r(22227),s=r(8404),u=r(17363);function c(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=[o.default.createElement("meta",{charSet:"utf-8"})];return e||t.push(o.default.createElement("meta",{name:"viewport",content:"width=device-width"})),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(35086);let f=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return a=>{let o=!0,l=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){l=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?o=!1:t.add(a.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(a.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=a.props[t],r=n[t]||new Set;("name"!==t||!l)&&r.has(e)?o=!1:(r.add(e),n[t]=r)}}}}return o}}()).reverse().map((e,t)=>{let a=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t=n({},e.props||{});return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:a})})}t.default=function(e){let{children:t}=e,r=o.useContext(i.AmpStateContext),n=o.useContext(s.HeadManagerContext);return o.default.createElement(l.default,{reduceComponentsToState:p,headManager:n,inAmpMode:u.isInAmpMode(r)},t)},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89334:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LayoutSegmentsContext=t.ParamsContext=t.PathnameContext=t.SearchParamsContext=void 0;var n=r(67294);let a=n.createContext(null);t.SearchParamsContext=a;let o=n.createContext(null);t.PathnameContext=o;let l=n.createContext(null);t.ParamsContext=l;let i=n.createContext(null);t.LayoutSegmentsContext=i},48633:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.detectDomainLocale=function(e,t,r){let n;if(e)for(let l of(r&&(r=r.toLowerCase()),e)){var a,o;let e=null==(a=l.domain)?void 0:a.split(":")[0].toLowerCase();if(t===e||r===l.defaultLocale.toLowerCase()||(null==(o=l.locales)?void 0:o.some(e=>e.toLowerCase()===r))){n=l;break}}return n}},94317:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizeLocalePath=function(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}},59977:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ImageConfigContext=void 0;var n=(0,r(12845).Z)(r(67294)),a=r(99309);let o=n.default.createContext(a.imageConfigDefault);t.ImageConfigContext=o},99309:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.imageConfigDefault=t.VALID_LOADERS=void 0,t.VALID_LOADERS=["default","imgix","cloudinary","akamai","custom"],t.imageConfigDefault={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},88887:function(e,t){"use strict";function r(e){return Object.prototype.toString.call(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.getObjectClassLabel=r,t.isPlainObject=function(e){if("[object Object]"!==r(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}},25958:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NEXT_DYNAMIC_NO_SSR_CODE=void 0,t.NEXT_DYNAMIC_NO_SSR_CODE="DYNAMIC_SERVER_USAGE"},35660:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){let e=Object.create(null);return{on(t,r){(e[t]||(e[t]=[])).push(r)},off(t,r){e[t]&&e[t].splice(e[t].indexOf(r)>>>0,1)},emit(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];(e[t]||[]).slice().map(e=>{e(...n)})}}}},78317:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.denormalizePagePath=function(e){let t=a.normalizePathSep(e);return t.startsWith("/index/")&&!n.isDynamicRoute(t)?t.slice(6):"/index"!==t?t:"/"};var n=r(90418),a=r(99892)},99892:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizePathSep=function(e){return e.replace(/\\/g,"/")}},63462:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RouterContext=void 0;var n=(0,r(12845).Z)(r(67294));let a=n.default.createContext(null);t.RouterContext=a},26985:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.adaptForAppRouterInstance=function(e){return{back(){e.back()},forward(){e.forward()},refresh(){e.reload()},push(t){e.push(t)},replace(t){e.replace(t)},prefetch(t){e.prefetch(t)}}},t.adaptForSearchParams=function(e){return e.isReady&&e.query?function(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,e);else void 0!==n&&t.append(r,n);return t}(e.query):new URLSearchParams},t.PathnameContextProviderAdapter=function(e){var{children:t,router:r}=e,n=a(e,["children","router"]);let s=o.useRef(n.isAutoExport),u=o.useMemo(()=>{let e;let t=s.current;if(t&&(s.current=!1),i.isDynamicRoute(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return o.default.createElement(l.PathnameContext.Provider,{value:u},t)};var n=r(99402).Z,a=r(38774).Z,o=n(r(67294)),l=r(89334),i=r(90418)},76273:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.matchesMiddleware=B,t.createKey=X,t.default=void 0;var n=r(92612).Z,a=r(31032).Z,o=r(12845).Z,l=r(99402).Z,i=r(86316),s=r(22669),u=r(20699),c=l(r(80676)),d=r(78317),f=r(94317),p=o(r(35660)),h=r(63794),m=r(78689),g=r(36305);o(r(72431));var y=r(33888),v=r(64095),_=r(4611),P=r(38748),b=r(4943),S=r(22725),w=r(75776),E=r(19320),x=r(98684),j=r(94119),C=r(79423),O=r(20159),R=r(34022),L=r(610),M=r(4634),A=r(29671),T=r(93657),I=r(63297),N=r(89493),k=r(95509);function D(){return Object.assign(Error("Route Cancelled"),{cancelled:!0})}function B(e){return U.apply(this,arguments)}function U(){return(U=n(function*(e){let t=yield Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:r}=b.parsePath(e.asPath),n=j.hasBasePath(r)?E.removeBasePath(r):r,a=x.addBasePath(S.addLocale(n,e.locale));return t.some(e=>RegExp(e.regexp).test(a))})).apply(this,arguments)}function H(e){let t=h.getLocationOrigin();return e.startsWith(t)?e.substring(t.length):e}function F(e,t,r){let[n,a]=I.resolveHref(e,t,!0),o=h.getLocationOrigin(),l=n.startsWith(o),i=a&&a.startsWith(o);n=H(n),a=a?H(a):a;let s=l?n:x.addBasePath(n),u=r?H(I.resolveHref(e,r)):a||n;return{url:s,as:i?u:x.addBasePath(u)}}function W(e,t){let r=i.removeTrailingSlash(d.denormalizePagePath(e));return"/404"===r||"/_error"===r?e:(t.includes(r)||t.some(t=>{if(m.isDynamicRoute(t)&&v.getRouteRegex(t).re.test(r))return e=t,!0}),i.removeTrailingSlash(e))}function q(e){return Z.apply(this,arguments)}function Z(){return(Z=n(function*(e){let t=yield B(e);if(!t||!e.fetchData)return null;try{let t=yield e.fetchData(),r=yield function(e,t,r){let n={basePath:r.router.basePath,i18n:{locales:r.router.locales},trailingSlash:Boolean(!1)},o=t.headers.get("x-nextjs-rewrite"),l=o||t.headers.get("x-nextjs-matched-path"),u=t.headers.get("x-matched-path");if(!u||l||u.includes("__next_data_catchall")||u.includes("/_error")||u.includes("/404")||(l=u),l){if(l.startsWith("/")){let t=g.parseRelativeUrl(l),a=O.getNextPathnameInfo(t.pathname,{nextConfig:n,parseData:!0}),u=i.removeTrailingSlash(a.pathname);return Promise.all([r.router.pageLoader.getPageList(),s.getClientBuildManifest()]).then(n=>{let[l,{__rewrites:i}]=n,s=S.addLocale(a.pathname,a.locale);if(m.isDynamicRoute(s)||!o&&l.includes(f.normalizeLocalePath(E.removeBasePath(s),r.router.locales).pathname)){let r=O.getNextPathnameInfo(g.parseRelativeUrl(e).pathname,{parseData:!0});s=x.addBasePath(r.pathname),t.pathname=s}if(!l.includes(u)){let e=W(u,l);e!==u&&(u=e)}let c=l.includes(u)?u:W(f.normalizeLocalePath(E.removeBasePath(t.pathname),r.router.locales).pathname,l);if(m.isDynamicRoute(c)){let e=y.getRouteMatcher(v.getRouteRegex(c))(s);Object.assign(t.query,e||{})}return{type:"rewrite",parsedAs:t,resolvedHref:c}})}let t=b.parsePath(e),u=R.formatNextPathnameInfo(a({},O.getNextPathnameInfo(t.pathname,{nextConfig:n,parseData:!0}),{defaultLocale:r.router.defaultLocale,buildId:""}));return Promise.resolve({type:"redirect-external",destination:"".concat(u).concat(t.query).concat(t.hash)})}let c=t.headers.get("x-nextjs-redirect");if(c){if(c.startsWith("/")){let e=b.parsePath(c),t=R.formatNextPathnameInfo(a({},O.getNextPathnameInfo(e.pathname,{nextConfig:n,parseData:!0}),{defaultLocale:r.router.defaultLocale,buildId:""}));return Promise.resolve({type:"redirect-internal",newAs:"".concat(t).concat(e.query).concat(e.hash),newUrl:"".concat(t).concat(e.query).concat(e.hash)})}return Promise.resolve({type:"redirect-external",destination:c})}return Promise.resolve({type:"next"})}(t.dataHref,t.response,e);return{dataHref:t.dataHref,json:t.json,response:t.response,text:t.text,cacheKey:t.cacheKey,effect:r}}catch(e){return null}})).apply(this,arguments)}let z=Symbol("SSG_DATA_NOT_FOUND");function G(e){try{return JSON.parse(e)}catch(e){return null}}function V(e){var t;let{dataHref:r,inflightCache:n,isPrefetch:a,hasMiddleware:o,isServerRender:l,parseJSON:i,persistCache:u,isBackground:c,unstable_skipClientCache:d}=e,{href:f}=new URL(r,window.location.href),p=e=>(function e(t,r,n){return fetch(t,{credentials:"same-origin",method:n.method||"GET",headers:Object.assign({},n.headers,{"x-nextjs-data":"1"})}).then(a=>!a.ok&&r>1&&a.status>=500?e(t,r-1,n):a)})(r,l?3:1,{headers:Object.assign({},a?{purpose:"prefetch"}:{},a&&o?{"x-middleware-prefetch":"1"}:{}),method:null!=(t=null==e?void 0:e.method)?t:"GET"}).then(t=>t.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:r,response:t,text:"",json:{},cacheKey:f}:t.text().then(e=>{if(!t.ok){if(o&&[301,302,307,308].includes(t.status))return{dataHref:r,response:t,text:e,json:{},cacheKey:f};if(!o&&404===t.status){var n;if(null==(n=G(e))?void 0:n.notFound)return{dataHref:r,json:{notFound:z},response:t,text:e,cacheKey:f}}let a=Error("Failed to load static props");throw l||s.markAssetError(a),a}return{dataHref:r,json:i?G(e):null,response:t,text:e,cacheKey:f}})).then(e=>(u&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete n[f],e)).catch(e=>{throw d||delete n[f],("Failed to fetch"===e.message||"NetworkError when attempting to fetch resource."===e.message||"Load failed"===e.message)&&s.markAssetError(e),e});return d&&u?p({}).then(e=>(n[f]=Promise.resolve(e),e)):void 0!==n[f]?n[f]:n[f]=p(c?{method:"HEAD"}:{})}function X(){return Math.random().toString(36).slice(2,10)}function Y(e){let{url:t,router:r}=e;if(t===x.addBasePath(S.addLocale(r.asPath,r.locale)))throw Error("Invariant: attempted to hard navigate to the same URL ".concat(t," ").concat(location.href));window.location.href=t}let $=e=>{let{route:t,router:r}=e,n=!1,a=r.clc=()=>{n=!0},o=()=>{if(n){let e=Error('Abort fetching component for route: "'.concat(t,'"'));throw e.cancelled=!0,e}a===r.clc&&(r.clc=null)};return o};class K{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{url:e,as:t}=F(this,e,t),this.change("pushState",e,t,r)}replace(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{url:e,as:t}=F(this,e,t),this.change("replaceState",e,t,r)}_bfl(e,t,r){return n(function*(){})()}change(e,t,r,o,l){var d=this;return n(function*(){var n,p,C,O,R,A,I,k,U;let H,q;if(!M.isLocalURL(t))return Y({url:t,router:d}),!1;let Z=1===o._h;Z||(yield d._bfl(r,void 0,o.locale));let G=Z||o._shouldResolveHref||b.parsePath(t).pathname===b.parsePath(r).pathname,V=a({},d.state),X=!0!==d.isReady;d.isReady=!0;let $=d.isSsr;if(Z||(d.isSsr=!1),Z&&d.clc)return!1;let J=V.locale;{V.locale=!1===o.locale?d.defaultLocale:o.locale||V.locale,void 0===o.locale&&(o.locale=V.locale);let e=g.parseRelativeUrl(j.hasBasePath(r)?E.removeBasePath(r):r),a=f.normalizeLocalePath(e.pathname,d.locales);a.detectedLocale&&(V.locale=a.detectedLocale,e.pathname=x.addBasePath(e.pathname),r=_.formatWithValidation(e),t=x.addBasePath(f.normalizeLocalePath(j.hasBasePath(t)?E.removeBasePath(t):t,d.locales).pathname));let l=!1;(null==(n=d.locales)?void 0:n.includes(V.locale))||(e.pathname=S.addLocale(e.pathname,V.locale),Y({url:_.formatWithValidation(e),router:d}),l=!0);let i=P.detectDomainLocale(d.domainLocales,void 0,V.locale);if(!l&&i&&d.isLocaleDomain&&self.location.hostname!==i.domain){let e=E.removeBasePath(r);Y({url:"http".concat(i.http?"":"s","://").concat(i.domain).concat(x.addBasePath("".concat(V.locale===i.defaultLocale?"":"/".concat(V.locale)).concat("/"===e?"":e)||"/")),router:d}),l=!0}if(l)return new Promise(()=>{})}h.ST&&performance.mark("routeChange");let{shallow:Q=!1,scroll:ee=!0}=o,et={shallow:Q};d._inFlightRoute&&d.clc&&($||K.events.emit("routeChangeError",D(),d._inFlightRoute,et),d.clc(),d.clc=null),r=x.addBasePath(S.addLocale(j.hasBasePath(r)?E.removeBasePath(r):r,o.locale,d.defaultLocale));let er=w.removeLocale(j.hasBasePath(r)?E.removeBasePath(r):r,V.locale);d._inFlightRoute=r;let en=J!==V.locale;if(!Z&&d.onlyAHashChange(er)&&!en){V.asPath=er,K.events.emit("hashChangeStart",r,et),d.changeState(e,t,r,a({},o,{scroll:!1})),ee&&d.scrollToHash(er);try{yield d.set(V,d.components[V.route],null)}catch(e){throw c.default(e)&&e.cancelled&&K.events.emit("routeChangeError",e,er,et),e}return K.events.emit("hashChangeComplete",r,et),!0}let ea=g.parseRelativeUrl(t),{pathname:eo,query:el}=ea;try{[H,{__rewrites:q}]=yield Promise.all([d.pageLoader.getPageList(),s.getClientBuildManifest(),d.pageLoader.getMiddleware()])}catch(e){return Y({url:r,router:d}),!1}d.urlIsNew(er)||en||(e="replaceState");let ei=r;eo=eo?i.removeTrailingSlash(E.removeBasePath(eo)):eo;let es=i.removeTrailingSlash(eo),eu=r.startsWith("/")&&g.parseRelativeUrl(r).pathname,ec=!!(eu&&es!==eu&&(!m.isDynamicRoute(es)||!y.getRouteMatcher(v.getRouteRegex(es))(eu))),ed=!o.shallow&&(yield B({asPath:r,locale:V.locale,router:d}));if(Z&&ed&&(G=!1),G&&"/_error"!==eo&&(o._shouldResolveHref=!0,ea.pathname=W(eo,H),ea.pathname===eo||(eo=ea.pathname,ea.pathname=x.addBasePath(eo),ed||(t=_.formatWithValidation(ea)))),!M.isLocalURL(r))return Y({url:r,router:d}),!1;ei=w.removeLocale(E.removeBasePath(ei),V.locale),es=i.removeTrailingSlash(eo);let ef=!1;if(m.isDynamicRoute(es)){let e=g.parseRelativeUrl(ei),n=e.pathname,a=v.getRouteRegex(es);ef=y.getRouteMatcher(a)(n);let o=es===n,l=o?N.interpolateAs(es,n,el):{};if(ef&&(!o||l.result))o?r=_.formatWithValidation(Object.assign({},e,{pathname:l.result,query:T.omit(el,l.params)})):Object.assign(el,ef);else{let e=Object.keys(a.groups).filter(e=>!el[e]&&!a.groups[e].optional);if(e.length>0&&!ed)throw Error((o?"The provided `href` (".concat(t,") value is missing query values (").concat(e.join(", "),") to be interpolated properly. "):"The provided `as` value (".concat(n,") is incompatible with the `href` value (").concat(es,"). "))+"Read more: https://nextjs.org/docs/messages/".concat(o?"href-interpolation-failed":"incompatible-href-as"))}}Z||K.events.emit("routeChangeStart",r,et);let ep="/404"===d.pathname||"/_error"===d.pathname;try{let n=yield d.getRouteInfo({route:es,pathname:eo,query:el,as:r,resolvedAs:ei,routeProps:et,locale:V.locale,isPreview:V.isPreview,hasMiddleware:ed,unstable_skipClientCache:o.unstable_skipClientCache,isQueryUpdating:Z&&!d.isFallback,isMiddlewareRewrite:ec});if(Z||(yield d._bfl(r,"resolvedAs"in n?n.resolvedAs:void 0,V.locale)),"route"in n&&ed){es=eo=n.route||es,et.shallow||(el=Object.assign({},n.query||{},el));let e=j.hasBasePath(ea.pathname)?E.removeBasePath(ea.pathname):ea.pathname;if(ef&&eo!==e&&Object.keys(ef).forEach(e=>{ef&&el[e]===ef[e]&&delete el[e]}),m.isDynamicRoute(eo)){let e=!et.shallow&&n.resolvedAs?n.resolvedAs:x.addBasePath(S.addLocale(new URL(r,location.href).pathname,V.locale),!0),t=e;j.hasBasePath(t)&&(t=E.removeBasePath(t));{let e=f.normalizeLocalePath(t,d.locales);V.locale=e.detectedLocale||V.locale,t=e.pathname}let a=v.getRouteRegex(eo),o=y.getRouteMatcher(a)(new URL(t,location.href).pathname);o&&Object.assign(el,o)}}if("type"in n){if("redirect-internal"===n.type)return d.change(e,n.newUrl,n.newAs,o);return Y({url:n.destination,router:d}),new Promise(()=>{})}let i=n.Component;if(i&&i.unstable_scriptLoader){let e=[].concat(i.unstable_scriptLoader());e.forEach(e=>{u.handleClientScriptLoad(e.props)})}if((n.__N_SSG||n.__N_SSP)&&n.props){if(n.props.pageProps&&n.props.pageProps.__N_REDIRECT){o.locale=!1;let t=n.props.pageProps.__N_REDIRECT;if(t.startsWith("/")&&!1!==n.props.pageProps.__N_REDIRECT_BASE_PATH){let r=g.parseRelativeUrl(t);r.pathname=W(r.pathname,H);let{url:n,as:a}=F(d,t,t);return d.change(e,n,a,o)}return Y({url:t,router:d}),new Promise(()=>{})}if(V.isPreview=!!n.props.__N_PREVIEW,n.props.notFound===z){let e;try{yield d.fetchComponent("/404"),e="/404"}catch(t){e="/_error"}if(n=yield d.getRouteInfo({route:e,pathname:e,query:el,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:V.locale,isPreview:V.isPreview}),"type"in n)throw Error("Unexpected middleware effect on /404")}}Z&&"/_error"===d.pathname&&(null==(p=self.__NEXT_DATA__.props)?void 0:null==(C=p.pageProps)?void 0:C.statusCode)===500&&(null==(O=n.props)?void 0:O.pageProps)&&(n.props.pageProps.statusCode=500);let s=o.shallow&&V.route===(null!=(R=n.route)?R:es),h=null!=(A=o.scroll)?A:!Z&&!s,_=null!=l?l:h?{x:0,y:0}:null,P=a({},V,{route:es,pathname:eo,query:el,asPath:er,isFallback:!1});if(Z&&ep){if(n=yield d.getRouteInfo({route:d.pathname,pathname:d.pathname,query:el,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:V.locale,isPreview:V.isPreview,isQueryUpdating:Z&&!d.isFallback}),"type"in n)throw Error("Unexpected middleware effect on ".concat(d.pathname));"/_error"===d.pathname&&(null==(I=self.__NEXT_DATA__.props)?void 0:null==(k=I.pageProps)?void 0:k.statusCode)===500&&(null==(U=n.props)?void 0:U.pageProps)&&(n.props.pageProps.statusCode=500);try{yield d.set(P,n,_)}catch(e){throw c.default(e)&&e.cancelled&&K.events.emit("routeChangeError",e,er,et),e}return!0}K.events.emit("beforeHistoryChange",r,et),d.changeState(e,t,r,o);let b=Z&&!_&&!X&&!en&&L.compareRouterStates(P,d.state);if(!b){try{yield d.set(P,n,_)}catch(e){if(e.cancelled)n.error=n.error||e;else throw e}if(n.error)throw Z||K.events.emit("routeChangeError",n.error,er,et),n.error;V.locale&&(document.documentElement.lang=V.locale),Z||K.events.emit("routeChangeComplete",r,et),h&&/#.+$/.test(r)&&d.scrollToHash(r)}return!0}catch(e){if(c.default(e)&&e.cancelled)return!1;throw e}})()}changeState(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};("pushState"!==e||h.getURL()!==r)&&(this._shallow=n.shallow,window.history[e]({url:t,as:r,options:n,__N:!0,key:this._key="pushState"!==e?this._key:X()},"",r))}handleRouteInfoError(e,t,r,a,o,l){var i=this;return n(function*(){if(console.error(e),e.cancelled)throw e;if(s.isAssetError(e)||l)throw K.events.emit("routeChangeError",e,a,o),Y({url:a,router:i}),D();try{let n;let{page:a,styleSheets:o}=yield i.fetchComponent("/_error"),l={props:n,Component:a,styleSheets:o,err:e,error:e};if(!l.props)try{l.props=yield i.getInitialProps(a,{err:e,pathname:t,query:r})}catch(e){console.error("Error in error page `getInitialProps`: ",e),l.props={}}return l}catch(e){return i.handleRouteInfoError(c.default(e)?e:Error(e+""),t,r,a,o,!0)}})()}getRouteInfo(e){let{route:t,pathname:r,query:o,as:l,resolvedAs:s,routeProps:u,locale:d,hasMiddleware:p,isPreview:h,unstable_skipClientCache:m,isQueryUpdating:g,isMiddlewareRewrite:y}=e;var v=this;return n(function*(){let e=t;try{var P,b,S,w;let t=$({route:e,router:v}),c=v.components[e];if(u.shallow&&c&&v.route===e)return c;p&&(c=void 0);let x=!c||"initial"in c?void 0:c,j={dataHref:v.pageLoader.getDataHref({href:_.formatWithValidation({pathname:r,query:o}),skipInterpolation:!0,asPath:s,locale:d}),hasMiddleware:!0,isServerRender:v.isSsr,parseJSON:!0,inflightCache:g?v.sbc:v.sdc,persistCache:!h,isPrefetch:!1,unstable_skipClientCache:m,isBackground:g},O=g&&!y?null:yield q({fetchData:()=>V(j),asPath:s,locale:d,router:v}).catch(e=>{if(g)return null;throw e});if(g&&(O?O.json=self.__NEXT_DATA__.props:O={json:self.__NEXT_DATA__.props}),t(),(null==O?void 0:null==(P=O.effect)?void 0:P.type)==="redirect-internal"||(null==O?void 0:null==(b=O.effect)?void 0:b.type)==="redirect-external")return O.effect;if((null==O?void 0:null==(S=O.effect)?void 0:S.type)==="rewrite"){let t=i.removeTrailingSlash(O.effect.resolvedHref),n=yield v.pageLoader.getPageList();if((!g||n.includes(t))&&(e=t,r=O.effect.resolvedHref,o=a({},o,O.effect.parsedAs.query),s=E.removeBasePath(f.normalizeLocalePath(O.effect.parsedAs.pathname,v.locales).pathname),c=v.components[e],u.shallow&&c&&v.route===e&&!p))return a({},c,{route:e})}if(C.isAPIRoute(e))return Y({url:l,router:v}),new Promise(()=>{});let R=x||(yield v.fetchComponent(e).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP}))),L=null==O?void 0:null==(w=O.response)?void 0:w.headers.get("x-middleware-skip"),M=R.__N_SSG||R.__N_SSP;L&&(null==O?void 0:O.dataHref)&&delete v.sdc[O.dataHref];let{props:A,cacheKey:T}=yield v._getData(n(function*(){if(M){if((null==O?void 0:O.json)&&!L)return{cacheKey:O.cacheKey,props:O.json};let e=(null==O?void 0:O.dataHref)?O.dataHref:v.pageLoader.getDataHref({href:_.formatWithValidation({pathname:r,query:o}),asPath:s,locale:d}),t=yield V({dataHref:e,isServerRender:v.isSsr,parseJSON:!0,inflightCache:L?{}:v.sdc,persistCache:!h,isPrefetch:!1,unstable_skipClientCache:m});return{cacheKey:t.cacheKey,props:t.json||{}}}return{headers:{},props:yield v.getInitialProps(R.Component,{pathname:r,query:o,asPath:l,locale:d,locales:v.locales,defaultLocale:v.defaultLocale})}}));return R.__N_SSP&&j.dataHref&&T&&delete v.sdc[T],v.isPreview||!R.__N_SSG||g||V(Object.assign({},j,{isBackground:!0,persistCache:!1,inflightCache:v.sbc})).catch(()=>{}),A.pageProps=Object.assign({},A.pageProps),R.props=A,R.route=e,R.query=o,R.resolvedAs=s,v.components[e]=R,R}catch(e){return v.handleRouteInfoError(c.getProperError(e),r,o,l,u)}})()}set(e,t,r){return this.state=e,this.sub(t,this.components["/_app"].Component,r)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,r]=this.asPath.split("#"),[n,a]=e.split("#");return!!a&&t===n&&r===a||t===n&&r!==a}scrollToHash(e){let[,t=""]=e.split("#");if(""===t||"top"===t){k.handleSmoothScroll(()=>window.scrollTo(0,0));return}let r=decodeURIComponent(t),n=document.getElementById(r);if(n){k.handleSmoothScroll(()=>n.scrollIntoView());return}let a=document.getElementsByName(r)[0];a&&k.handleSmoothScroll(()=>a.scrollIntoView())}urlIsNew(e){return this.asPath!==e}prefetch(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var o=this;return n(function*(){if(A.isBot(window.navigator.userAgent))return;let n=g.parseRelativeUrl(e),{pathname:l,query:s}=n,u=l;if(!1===r.locale){l=f.normalizeLocalePath(l,o.locales).pathname,n.pathname=l,e=_.formatWithValidation(n);let a=g.parseRelativeUrl(t),i=f.normalizeLocalePath(a.pathname,o.locales);a.pathname=i.pathname,r.locale=i.detectedLocale||o.defaultLocale,t=_.formatWithValidation(a)}let c=yield o.pageLoader.getPageList(),d=t,p=void 0!==r.locale?r.locale||void 0:o.locale,h=yield B({asPath:t,locale:p,router:o});n.pathname=W(n.pathname,c),m.isDynamicRoute(n.pathname)&&(l=n.pathname,n.pathname=l,Object.assign(s,y.getRouteMatcher(v.getRouteRegex(n.pathname))(b.parsePath(t).pathname)||{}),h||(e=_.formatWithValidation(n)));let P=yield q({fetchData:()=>V({dataHref:o.pageLoader.getDataHref({href:_.formatWithValidation({pathname:u,query:s}),skipInterpolation:!0,asPath:d,locale:p}),hasMiddleware:!0,isServerRender:o.isSsr,parseJSON:!0,inflightCache:o.sdc,persistCache:!o.isPreview,isPrefetch:!0}),asPath:t,locale:p,router:o});if((null==P?void 0:P.effect.type)==="rewrite"&&(n.pathname=P.effect.resolvedHref,l=P.effect.resolvedHref,s=a({},s,P.effect.parsedAs.query),d=P.effect.parsedAs.pathname,e=_.formatWithValidation(n)),(null==P?void 0:P.effect.type)==="redirect-external")return;let S=i.removeTrailingSlash(l);yield Promise.all([o.pageLoader._isSsg(S).then(t=>!!t&&V({dataHref:(null==P?void 0:P.json)?null==P?void 0:P.dataHref:o.pageLoader.getDataHref({href:e,asPath:d,locale:p}),isServerRender:!1,parseJSON:!0,inflightCache:o.sdc,persistCache:!o.isPreview,isPrefetch:!0,unstable_skipClientCache:r.unstable_skipClientCache||r.priority&&!0}).then(()=>!1)),o.pageLoader[r.priority?"loadPage":"prefetch"](S)])})()}fetchComponent(e){var t=this;return n(function*(){let r=$({route:e,router:t});try{let n=yield t.pageLoader.loadPage(e);return r(),n}catch(e){throw r(),e}})()}_getData(e){let t=!1,r=()=>{t=!0};return this.clc=r,e().then(e=>{if(r===this.clc&&(this.clc=null),t){let e=Error("Loading initial props cancelled");throw e.cancelled=!0,e}return e})}_getFlightData(e){return V({dataHref:e,isServerRender:!0,parseJSON:!1,inflightCache:this.sdc,persistCache:!1,isPrefetch:!1}).then(e=>{let{text:t}=e;return{data:t}})}getInitialProps(e,t){let{Component:r}=this.components["/_app"],n=this._wrapApp(r);return t.AppTree=n,h.loadGetInitialProps(r,{AppTree:n,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:n,pageLoader:a,App:o,wrapApp:l,Component:s,err:u,subscription:c,isFallback:d,locale:f,locales:p,defaultLocale:y,domainLocales:v,isPreview:b}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=X(),this.onPopState=e=>{let t;let{isFirstPopStateEvent:r}=this;this.isFirstPopStateEvent=!1;let n=e.state;if(!n){let{pathname:e,query:t}=this;this.changeState("replaceState",_.formatWithValidation({pathname:x.addBasePath(e),query:t}),h.getURL());return}if(n.__NA){window.location.reload();return}if(!n.__N||r&&this.locale===n.options.locale&&n.as===this.asPath)return;let{url:a,as:o,options:l,key:i}=n;this._key=i;let{pathname:s}=g.parseRelativeUrl(a);(!this.isSsr||o!==x.addBasePath(this.asPath)||s!==x.addBasePath(this.pathname))&&(!this._bps||this._bps(n))&&this.change("replaceState",a,o,Object.assign({},l,{shallow:l.shallow&&this._shallow,locale:l.locale||this.defaultLocale,_h:0}),t)};let S=i.removeTrailingSlash(e);this.components={},"/_error"!==e&&(this.components[S]={Component:s,initial:!0,props:n,err:u,__N_SSG:n&&n.__N_SSG,__N_SSP:n&&n.__N_SSP}),this.components["/_app"]={Component:o,styleSheets:[]},this.events=K.events,this.pageLoader=a;let w=m.isDynamicRoute(e)&&self.__NEXT_DATA__.autoExport;if(this.basePath="/aitools",this.sub=c,this.clc=null,this._wrapApp=l,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!w&&!self.location.search),this.locales=p,this.defaultLocale=y,this.domainLocales=v,this.isLocaleDomain=!!P.detectDomainLocale(v,self.location.hostname),this.state={route:S,pathname:e,query:t,asPath:w?e:r,isPreview:!!b,locale:f,isFallback:d},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!r.startsWith("//")){let n={locale:f},a=h.getURL();this._initialMatchesMiddlewarePromise=B({router:this,locale:f,asPath:a}).then(o=>(n._shouldResolveHref=r!==e,this.changeState("replaceState",o?a:_.formatWithValidation({pathname:x.addBasePath(e),query:t}),a,n),o))}window.addEventListener("popstate",this.onPopState)}}K.events=p.default(),t.default=K},77459:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addLocale=function(e,t,r,o){return t&&t!==r&&(o||!a.pathHasPrefix(e.toLowerCase(),"/".concat(t.toLowerCase()))&&!a.pathHasPrefix(e.toLowerCase(),"/api"))?n.addPathPrefix(e,"/".concat(t)):e};var n=r(95391),a=r(1259)},95391:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addPathPrefix=function(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:o}=n.parsePath(e);return"".concat(t).concat(r).concat(a).concat(o)};var n=r(4943)},84156:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addPathSuffix=function(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:o}=n.parsePath(e);return"".concat(r).concat(t).concat(a).concat(o)};var n=r(4943)},610:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.compareRouterStates=function(e,t){let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let n=r.length;n--;){let a=r[n];if("query"===a){let r=Object.keys(e.query);if(r.length!==Object.keys(t.query).length)return!1;for(let n=r.length;n--;){let a=r[n];if(!t.query.hasOwnProperty(a)||e.query[a]!==t.query[a])return!1}}else if(!t.hasOwnProperty(a)||e[a]!==t[a])return!1}return!0}},34022:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatNextPathnameInfo=function(e){let t=l.addLocale(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=n.removeTrailingSlash(t)),e.buildId&&(t=o.addPathSuffix(a.addPathPrefix(t,"/_next/data/".concat(e.buildId)),"/"===e.pathname?"index.json":".json")),t=a.addPathPrefix(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:o.addPathSuffix(t,"/"):n.removeTrailingSlash(t)};var n=r(86316),a=r(95391),o=r(84156),l=r(77459)},4611:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatUrl=o,t.formatWithValidation=function(e){return o(e)},t.urlObjectKeys=void 0;var n=(0,r(99402).Z)(r(50466));let a=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",l=e.pathname||"",i=e.hash||"",s=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"[".concat(r,"]"):r),e.port&&(u+=":"+e.port)),s&&"object"==typeof s&&(s=String(n.urlQueryToSearchParams(s)));let c=e.search||s&&"?".concat(s)||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||a.test(o))&&!1!==u?(u="//"+(u||""),l&&"/"!==l[0]&&(l="/"+l)):u||(u=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),l=l.replace(/[?#]/g,encodeURIComponent),c=c.replace("#","%23"),"".concat(o).concat(u).concat(l).concat(c).concat(i)}t.urlObjectKeys=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"]},73891:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r="/"===e?"/index":/^\/index(\/|$)/.test(e)?"/index".concat(e):"".concat(e);return r+t}},20159:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNextPathnameInfo=function(e,t){var r;let{basePath:l,i18n:i,trailingSlash:s}=null!=(r=t.nextConfig)?r:{},u={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):s};if(l&&o.pathHasPrefix(u.pathname,l)&&(u.pathname=a.removePathPrefix(u.pathname,l),u.basePath=l),!0===t.parseData&&u.pathname.startsWith("/_next/data/")&&u.pathname.endsWith(".json")){let e=u.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),t=e[0];u.pathname="index"!==e[1]?"/".concat(e.slice(1).join("/")):"/",u.buildId=t}if(i){let e=n.normalizeLocalePath(u.pathname,i.locales);u.locale=null==e?void 0:e.detectedLocale,u.pathname=(null==e?void 0:e.pathname)||u.pathname}return u};var n=r(94317),a=r(9244),o=r(1259)},95509:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.handleSmoothScroll=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}},90418:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n.getSortedRoutes}}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return a.isDynamicRoute}});var n=r(53907),a=r(78689)},89493:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.interpolateAs=function(e,t,r){let o="",l=a.getRouteRegex(e),i=l.groups,s=(t!==e?n.getRouteMatcher(l)(t):"")||r;o=e;let u=Object.keys(i);return u.every(e=>{let t=s[e]||"",{repeat:r,optional:n}=i[e],a="[".concat(r?"...":"").concat(e,"]");return n&&(a="".concat(t?"":"/","[").concat(a,"]")),r&&!Array.isArray(t)&&(t=[t]),(n||e in s)&&(o=o.replace(a,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(o=""),{params:u,result:o}};var n=r(33888),a=r(64095)},29671:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isBot=function(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}},78689:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isDynamicRoute=function(e){return r.test(e)};let r=/\/\[[^/]+?\](?=\/|$)/},4634:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isLocalURL=function(e){if(!n.isAbsoluteUrl(e))return!0;try{let t=n.getLocationOrigin(),r=new URL(e,t);return r.origin===t&&a.hasBasePath(r.pathname)}catch(e){return!1}};var n=r(63794),a=r(94119)},93657:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.omit=function(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}},4943:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parsePath=function(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}},36305:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseRelativeUrl=function(e,t){let r=new URL(n.getLocationOrigin()),o=t?new URL(t,r):e.startsWith(".")?new URL(window.location.href):r,{pathname:l,searchParams:i,search:s,hash:u,href:c,origin:d}=new URL(e,o);if(d!==r.origin)throw Error("invariant: invalid relative URL, router received ".concat(e));return{pathname:l,query:a.searchParamsToUrlQuery(i),search:s,hash:u,href:c.slice(r.origin.length)}};var n=r(63794),a=r(50466)},1259:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pathHasPrefix=function(e,t){if("string"!=typeof e)return!1;let{pathname:r}=n.parsePath(e);return r===t||r.startsWith(t+"/")};var n=r(4943)},50466:function(e,t){"use strict";function r(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.searchParamsToUrlQuery=function(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t},t.urlQueryToSearchParams=function(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[n,a]=e;Array.isArray(a)?a.forEach(e=>t.append(n,r(e))):t.set(n,r(a))}),t},t.assign=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}},9244:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removePathPrefix=function(e,t){if(n.pathHasPrefix(e,t)){let r=e.slice(t.length);return r.startsWith("/")?r:"/".concat(r)}return e};var n=r(1259)},86316:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeTrailingSlash=function(e){return e.replace(/\/$/,"")||"/"}},63297:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveHref=function(e,t,r){let d;let f="string"==typeof t?t:a.formatWithValidation(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f,m=h.split("?");if((m[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '".concat(f,"' passed to next/router in page: '").concat(e.pathname,"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href."));let t=l.normalizeRepeatedSlashes(h);f=(p?p[0]:"")+t}if(!s.isLocalURL(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=i.normalizePathTrailingSlash(e.pathname);let t="";if(u.isDynamicRoute(e.pathname)&&e.searchParams&&r){let r=n.searchParamsToUrlQuery(e.searchParams),{result:l,params:i}=c.interpolateAs(e.pathname,e.pathname,r);l&&(t=a.formatWithValidation({pathname:l,hash:e.hash,query:o.omit(r,i)}))}let l=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[l,t||l]:l}catch(e){return r?[f]:f}};var n=r(50466),a=r(4611),o=r(93657),l=r(63794),i=r(82392),s=r(4634),u=r(78689),c=r(89493)},33888:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRouteMatcher=function(e){let{re:t,groups:r}=e;return e=>{let a=t.exec(e);if(!a)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},l={};return Object.keys(r).forEach(e=>{let t=r[e],n=a[t.pos];void 0!==n&&(l[e]=~n.indexOf("/")?n.split("/").map(e=>o(e)):t.repeat?[o(n)]:o(n))}),l}};var n=r(63794)},64095:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRouteRegex=s,t.getNamedRouteRegex=function(e){let t=u(e);return n({},s(e),{namedRegex:"^".concat(t.namedParameterizedRoute,"(?:/)?$"),routeKeys:t.routeKeys})},t.getNamedMiddlewareRegex=function(e,t){let{parameterizedRoute:r}=i(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/".concat(n?".*":"","$")};let{namedParameterizedRoute:a}=u(e);return{namedRegex:"^".concat(a).concat(n?"(?:(/.*)?)":"","$")}};var n=r(31032).Z,a=r(10489),o=r(86316);function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function i(e){let t=o.removeTrailingSlash(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{if(!(e.startsWith("[")&&e.endsWith("]")))return"/".concat(a.escapeStringRegexp(e));{let{key:t,optional:a,repeat:o}=l(e.slice(1,-1));return r[t]={pos:n++,repeat:o,optional:a},o?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function s(e){let{parameterizedRoute:t,groups:r}=i(e);return{re:RegExp("^".concat(t,"(?:/)?$")),groups:r}}function u(e){let t,r;let n=o.removeTrailingSlash(e).slice(1).split("/"),i=(t=97,r=1,()=>{let e="";for(let n=0;n<r;n++)e+=String.fromCharCode(t),++t>122&&(r++,t=97);return e}),s={};return{namedParameterizedRoute:n.map(e=>{if(!(e.startsWith("[")&&e.endsWith("]")))return"/".concat(a.escapeStringRegexp(e));{let{key:t,optional:r,repeat:n}=l(e.slice(1,-1)),a=t.replace(/\W/g,""),o=!1;return(0===a.length||a.length>30)&&(o=!0),isNaN(parseInt(a.slice(0,1)))||(o=!0),o&&(a=i()),s[a]=t,n?r?"(?:/(?<".concat(a,">.+?))?"):"/(?<".concat(a,">.+?)"):"/(?<".concat(a,">[^/]+?)")}}).join(""),routeKeys:s}}},53907:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSortedRoutes=function(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()};class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/",t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh("".concat(e).concat(t,"/"))).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh("".concat(e,"[").concat(this.slugName,"]/"))),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'.concat(t,'" and "').concat(t,"[[...").concat(this.optionalRestSlugName,']]").'));r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh("".concat(e,"[...").concat(this.restSlugName,"]/"))),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh("".concat(e,"[[...").concat(this.optionalRestSlugName,"]]/"))),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let a=e[0];if(a.startsWith("[")&&a.endsWith("]")){let r=a.slice(1,-1),l=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),l=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('".concat(r,"')."));if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('".concat(r,"')."));function o(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('".concat(e,"' !== '").concat(r,"')."));t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'.concat(r,'" repeat within a single dynamic path'));if(e.replace(/\W/g,"")===a.replace(/\W/g,""))throw Error('You cannot have the slug names "'.concat(e,'" and "').concat(r,'" differ only by non-word symbols within a single dynamic path'))}),t.push(r)}if(n){if(l){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'.concat(this.restSlugName,']" and "').concat(e[0],'" ).'));o(this.optionalRestSlugName,r),this.optionalRestSlugName=r,a="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'.concat(this.optionalRestSlugName,']]" and "').concat(e[0],'").'));o(this.restSlugName,r),this.restSlugName=r,a="[...]"}}else{if(l)throw Error('Optional route parameters are not yet supported ("'.concat(e[0],'").'));o(this.slugName,r),this.slugName=r,a="[]"}}this.children.has(a)||this.children.set(a,new r),this.children.get(a)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}},88027:function(e,t){"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),t.setConfig=function(e){r=e},t.default=void 0,t.default=()=>r,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55188:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{headManager:t,reduceComponentsToState:r}=e;function i(){if(t&&t.mountedInstances){let a=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(a,e))}}if(a){var s;null==t||null==(s=t.mountedInstances)||s.add(e.children),i()}return o(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=i),()=>{t&&(t._pendingUpdate=i)})),l(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null};var n=(0,r(99402).Z)(r(67294));let a=!1,o=a?()=>{}:n.useLayoutEffect,l=a?()=>{}:n.useEffect},63794:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execOnce=function(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),o=0;o<n;o++)a[o]=arguments[o];return r||(r=!0,t=e(...a)),t}},t.getLocationOrigin=l,t.getURL=function(){let{href:e}=window.location,t=l();return e.substring(t.length)},t.getDisplayName=i,t.isResSent=s,t.normalizeRepeatedSlashes=function(e){let t=e.split("?"),r=t[0];return r.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?".concat(t.slice(1).join("?")):"")},t.loadGetInitialProps=u,t.ST=t.SP=t.isAbsoluteUrl=t.WEB_VITALS=void 0;var n=r(92612).Z;t.WEB_VITALS=["CLS","FCP","FID","INP","LCP","TTFB"];let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>a.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return"".concat(e,"//").concat(t).concat(r?":"+r:"")}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function u(e,t){return c.apply(this,arguments)}function c(){return(c=n(function*(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:yield u(t.Component,t.ctx)}:{};let n=yield e.getInitialProps(t);if(r&&s(r))return n;if(!n){let t='"'.concat(i(e),'.getInitialProps()" should resolve to an object. But found "').concat(n,'" instead.');throw Error(t)}return n})).apply(this,arguments)}t.isAbsoluteUrl=o;let d="undefined"!=typeof performance;t.SP=d;let f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);t.ST=f,t.DecodeError=class extends Error{},t.NormalizeError=class extends Error{},t.PageNotFoundError=class extends Error{constructor(e){super(),this.code="ENOENT",this.message="Cannot find module for page: ".concat(e)}},t.MissingStaticPage=class extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: ".concat(e," ").concat(t)}},t.MiddlewareNotFoundError=class extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}},35086:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.warnOnce=void 0;let r=e=>{};t.warnOnce=r},78018:function(e){var t,r,n,a,o,l,i,s,u,c,d,f,p,h,m,g,y,v,_,P,b,S,w,E,x,j,C,O,R,L,M,A,T,I,N,k,D,B,U,H,F,W,q,Z,z,G;(t={}).d=function(e,r){for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},void 0!==t&&(t.ab="//"),r={},t.r(r),t.d(r,{getCLS:function(){return w},getFCP:function(){return P},getFID:function(){return L},getINP:function(){return W},getLCP:function(){return Z},getTTFB:function(){return G},onCLS:function(){return w},onFCP:function(){return P},onFID:function(){return L},onINP:function(){return W},onLCP:function(){return Z},onTTFB:function(){return G}}),s=-1,u=function(e){addEventListener("pageshow",function(t){t.persisted&&(s=t.timeStamp,e(t))},!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},d=function(){var e=c();return e&&e.activationStart||0},f=function(e,t){var r=c(),n="navigate";return s>=0?n="back-forward-cache":r&&(n=document.prerendering||d()>0?"prerender":r.type.replace(/_/g,"-")),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}},p=function(e,t,r){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var n=new PerformanceObserver(function(e){t(e.getEntries())});return n.observe(Object.assign({type:e,buffered:!0},r||{})),n}}catch(e){}},h=function(e,t){var r=function r(n){"pagehide"!==n.type&&"hidden"!==document.visibilityState||(e(n),t&&(removeEventListener("visibilitychange",r,!0),removeEventListener("pagehide",r,!0)))};addEventListener("visibilitychange",r,!0),addEventListener("pagehide",r,!0)},m=function(e,t,r,n){var a,o;return function(l){var i;t.value>=0&&(l||n)&&((o=t.value-(a||0))||void 0===a)&&(a=t.value,t.delta=o,t.rating=(i=t.value)>r[1]?"poor":i>r[0]?"needs-improvement":"good",e(t))}},g=-1,y=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},v=function(){h(function(e){g=e.timeStamp},!0)},_=function(){return g<0&&(g=y(),v(),u(function(){setTimeout(function(){g=y(),v()},0)})),{get firstHiddenTime(){return g}}},P=function(e,t){t=t||{};var r,n=[1800,3e3],a=_(),o=f("FCP"),l=function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(s&&s.disconnect(),e.startTime<a.firstHiddenTime&&(o.value=e.startTime-d(),o.entries.push(e),r(!0)))})},i=window.performance&&window.performance.getEntriesByName&&window.performance.getEntriesByName("first-contentful-paint")[0],s=i?null:p("paint",l);(i||s)&&(r=m(e,o,n,t.reportAllChanges),i&&l([i]),u(function(a){r=m(e,o=f("FCP"),n,t.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-a.timeStamp,r(!0)})})}))},b=!1,S=-1,w=function(e,t){t=t||{};var r=[.1,.25];b||(P(function(e){S=e.value}),b=!0);var n,a=function(t){S>-1&&e(t)},o=f("CLS",0),l=0,i=[],s=function(e){e.forEach(function(e){if(!e.hadRecentInput){var t=i[0],r=i[i.length-1];l&&e.startTime-r.startTime<1e3&&e.startTime-t.startTime<5e3?(l+=e.value,i.push(e)):(l=e.value,i=[e]),l>o.value&&(o.value=l,o.entries=i,n())}})},c=p("layout-shift",s);c&&(n=m(a,o,r,t.reportAllChanges),h(function(){s(c.takeRecords()),n(!0)}),u(function(){l=0,S=-1,n=m(a,o=f("CLS",0),r,t.reportAllChanges)}))},E={passive:!0,capture:!0},x=new Date,j=function(e,t){n||(n=t,a=e,o=new Date,R(removeEventListener),C())},C=function(){if(a>=0&&a<o-x){var e={entryType:"first-input",name:n.type,target:n.target,cancelable:n.cancelable,startTime:n.timeStamp,processingStart:n.timeStamp+a};l.forEach(function(t){t(e)}),l=[]}},O=function(e){if(e.cancelable){var t,r,n,a=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?(t=function(){j(a,e),n()},r=function(){n()},n=function(){removeEventListener("pointerup",t,E),removeEventListener("pointercancel",r,E)},addEventListener("pointerup",t,E),addEventListener("pointercancel",r,E)):j(a,e)}},R=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,O,E)})},L=function(e,t){t=t||{};var r,o=[100,300],i=_(),s=f("FID"),c=function(e){e.startTime<i.firstHiddenTime&&(s.value=e.processingStart-e.startTime,s.entries.push(e),r(!0))},d=function(e){e.forEach(c)},g=p("first-input",d);r=m(e,s,o,t.reportAllChanges),g&&h(function(){d(g.takeRecords()),g.disconnect()},!0),g&&u(function(){r=m(e,s=f("FID"),o,t.reportAllChanges),l=[],a=-1,n=null,R(addEventListener),l.push(c),C()})},M=0,A=1/0,T=0,I=function(e){e.forEach(function(e){e.interactionId&&(A=Math.min(A,e.interactionId),M=(T=Math.max(T,e.interactionId))?(T-A)/7+1:0)})},N=function(){return i?M:performance.interactionCount||0},k=function(){"interactionCount"in performance||i||(i=p("event",I,{type:"event",buffered:!0,durationThreshold:0}))},D=0,B=function(){return N()-D},U=[],H={},F=function(e){var t=U[U.length-1],r=H[e.interactionId];if(r||U.length<10||e.duration>t.latency){if(r)r.entries.push(e),r.latency=Math.max(r.latency,e.duration);else{var n={id:e.interactionId,latency:e.duration,entries:[e]};H[n.id]=n,U.push(n)}U.sort(function(e,t){return t.latency-e.latency}),U.splice(10).forEach(function(e){delete H[e.id]})}},W=function(e,t){t=t||{};var r=[200,500];k();var n,a=f("INP"),o=function(e){e.forEach(function(e){e.interactionId&&F(e),"first-input"!==e.entryType||U.some(function(t){return t.entries.some(function(t){return e.duration===t.duration&&e.startTime===t.startTime})})||F(e)});var t,r=(t=Math.min(U.length-1,Math.floor(B()/50)),U[t]);r&&r.latency!==a.value&&(a.value=r.latency,a.entries=r.entries,n())},l=p("event",o,{durationThreshold:t.durationThreshold||40});n=m(e,a,r,t.reportAllChanges),l&&(l.observe({type:"first-input",buffered:!0}),h(function(){o(l.takeRecords()),a.value<0&&B()>0&&(a.value=0,a.entries=[]),n(!0)}),u(function(){U=[],D=N(),n=m(e,a=f("INP"),r,t.reportAllChanges)}))},q={},Z=function(e,t){t=t||{};var r,n=[2500,4e3],a=_(),o=f("LCP"),l=function(e){var t=e[e.length-1];if(t){var n=t.startTime-d();n<a.firstHiddenTime&&(o.value=n,o.entries=[t],r())}},i=p("largest-contentful-paint",l);if(i){r=m(e,o,n,t.reportAllChanges);var s=function(){q[o.id]||(l(i.takeRecords()),i.disconnect(),q[o.id]=!0,r(!0))};["keydown","click"].forEach(function(e){addEventListener(e,s,{once:!0,capture:!0})}),h(s,!0),u(function(a){r=m(e,o=f("LCP"),n,t.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-a.timeStamp,q[o.id]=!0,r(!0)})})})}},z=function e(t){document.prerendering?addEventListener("prerenderingchange",function(){return e(t)},!0):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},G=function(e,t){t=t||{};var r=[800,1800],n=f("TTFB"),a=m(e,n,r,t.reportAllChanges);z(function(){var o=c();if(o){if(n.value=Math.max(o.responseStart-d(),0),n.value<0||n.value>performance.now())return;n.entries=[o],a(!0),u(function(){(a=m(e,n=f("TTFB",0),r,t.reportAllChanges))(!0)})}})},e.exports=r},79423:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isAPIRoute=function(e){return"/api"===e||Boolean(null==e?void 0:e.startsWith("/api/"))}},80676:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=a,t.getProperError=function(e){return a(e)?e:Error(n.isPlainObject(e)?JSON.stringify(e):e+"")};var n=r(88887);function a(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}},92612:function(e,t){"use strict";function r(e,t,r,n,a,o,l){try{var i=e[o](l),s=i.value}catch(e){r(e);return}i.done?t(s):Promise.resolve(s).then(n,a)}function n(e){return function(){var t=this,n=arguments;return new Promise(function(a,o){var l=e.apply(t,n);function i(e){r(l,a,o,i,s,"next",e)}function s(e){r(l,a,o,i,s,"throw",e)}i(void 0)})}}Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return n}})},31032:function(e,t){"use strict";function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function n(){return r.apply(this,arguments)}Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return n}})},12845:function(e,t){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return r}})},99402:function(e,t){"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}function n(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var a={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(a,l,i):a[l]=e[l]}return a.default=e,n&&n.set(e,a),a}Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return n}})},38774:function(e,t){"use strict";function r(e,t){if(null==e)return{};var r,n,a={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return r}})},72431:function(){}},function(e){e.O(0,[9774],function(){return e(e.s=22870)}),_N_E=e.O()}]);