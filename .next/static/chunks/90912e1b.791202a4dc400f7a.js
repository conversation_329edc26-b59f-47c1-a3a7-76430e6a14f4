"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9209],{1387:function(e,t,n){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t,n){return t&&a(e.prototype,t),n&&a(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var a=[],o=!0,s=!1;try{for(i=i.call(e);!(o=(n=i.next()).done)&&(a.push(n.value),!t||a.length!==t);o=!0);}catch(e){s=!0,r=e}finally{try{o||null==i.return||i.return()}finally{if(s)throw r}}return a}}(e,t)||u(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function d(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=u(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}n.d(t,{Z:function(){return lE}});var h,p,f,g,v,y,b="undefined"==typeof window?null:window,x=b?b.navigator:null;b&&b.document;var w=r(""),E=r({}),k=r(function(){}),C="undefined"==typeof HTMLElement?"undefined":r(HTMLElement),S=function(e){return e&&e.instanceString&&T(e.instanceString)?e.instanceString():null},D=function(e){return null!=e&&r(e)==w},T=function(e){return null!=e&&r(e)===k},P=function(e){return!N(e)&&(Array.isArray?Array.isArray(e):null!=e&&e instanceof Array)},_=function(e){return null!=e&&r(e)===E&&!P(e)&&e.constructor===Object},M=function(e){return null!=e&&r(e)===r(1)&&!isNaN(e)},B=function(e){if("undefined"!==C)return null!=e&&e instanceof HTMLElement},N=function(e){return A(e)||I(e)},A=function(e){return"collection"===S(e)&&e._private.single},I=function(e){return"collection"===S(e)&&!e._private.single},O=function(e){return"core"===S(e)},R=function(e){return"stylesheet"===S(e)},z=function(e){return null==e||!!(""===e||e.match(/^\s+$/))},L=function(e){return null!=e&&r(e)===E&&T(e.then)},V=function(e,t){t||(t=function(){if(1==arguments.length)return arguments[0];if(0==arguments.length)return"undefined";for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);return e.join("$")});var n=function n(){var r,i=arguments,a=t.apply(this,i),o=n.cache;return(r=o[a])||(r=o[a]=e.apply(this,i)),r};return n.cache={},n},F=V(function(e){return e.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),j=V(function(e){return e.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),q=V(function(e,t){return e+t[0].toUpperCase()+t.substring(1)},function(e,t){return e+"$"+t}),X=function(e){return z(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},Y="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",W="rgb[a]?\\(("+Y+"[%]?)\\s*,\\s*("+Y+"[%]?)\\s*,\\s*("+Y+"[%]?)(?:\\s*,\\s*("+Y+"))?\\)",H="rgb[a]?\\((?:"+Y+"[%]?)\\s*,\\s*(?:"+Y+"[%]?)\\s*,\\s*(?:"+Y+"[%]?)(?:\\s*,\\s*(?:"+Y+"))?\\)",G="hsl[a]?\\(("+Y+")\\s*,\\s*("+Y+"[%])\\s*,\\s*("+Y+"[%])(?:\\s*,\\s*("+Y+"))?\\)",U="hsl[a]?\\((?:"+Y+")\\s*,\\s*(?:"+Y+"[%])\\s*,\\s*(?:"+Y+"[%])(?:\\s*,\\s*(?:"+Y+"))?\\)",K=function(e,t){return e<t?-1:e>t?1:0},Z=null!=Object.assign?Object.assign.bind(Object):function(e){for(var t=arguments,n=1;n<t.length;n++){var r=t[n];if(null!=r)for(var i=Object.keys(r),a=0;a<i.length;a++){var o=i[a];e[o]=r[o]}}return e},$=function(e){if((4===e.length||7===e.length)&&"#"===e[0]){var t,n,r;return 4===e.length?(t=parseInt(e[1]+e[1],16),n=parseInt(e[2]+e[2],16),r=parseInt(e[3]+e[3],16)):(t=parseInt(e[1]+e[2],16),n=parseInt(e[3]+e[4],16),r=parseInt(e[5]+e[6],16)),[t,n,r]}},Q=function(e){function t(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}var n,r,i,a,o,s,l,u,c=RegExp("^"+G+"$").exec(e);if(c){if((r=parseInt(c[1]))<0?r=(360- -1*r%360)%360:r>360&&(r%=360),r/=360,(i=parseFloat(c[2]))<0||i>100||(i/=100,(a=parseFloat(c[3]))<0||a>100)||(a/=100,void 0!==(o=c[4])&&((o=parseFloat(o))<0||o>1)))return;if(0===i)s=l=u=Math.round(255*a);else{var d=a<.5?a*(1+i):a+i-a*i,h=2*a-d;s=Math.round(255*t(h,d,r+1/3)),l=Math.round(255*t(h,d,r)),u=Math.round(255*t(h,d,r-1/3))}n=[s,l,u,o]}return n},J=function(e){var t,n=RegExp("^"+W+"$").exec(e);if(n){t=[];for(var r=[],i=1;i<=3;i++){var a=n[i];if("%"===a[a.length-1]&&(r[i]=!0),a=parseFloat(a),r[i]&&(a=a/100*255),a<0||a>255)return;t.push(Math.floor(a))}var o=r[1]||r[2]||r[3],s=r[1]&&r[2]&&r[3];if(o&&!s)return;var l=n[4];if(void 0!==l){if((l=parseFloat(l))<0||l>1)return;t.push(l)}}return t},ee={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},et=function(e){for(var t=e.map,n=e.keys,r=n.length,i=0;i<r;i++){var a=n[i];if(_(a))throw Error("Tried to set map with object key");i<n.length-1?(null==t[a]&&(t[a]={}),t=t[a]):t[a]=e.value}},en=function(e){for(var t=e.map,n=e.keys,r=n.length,i=0;i<r;i++){var a=n[i];if(_(a))throw Error("Tried to get map with object key");if(null==(t=t[a]))break}return t},er=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},ei="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},ea="object"==typeof ei&&ei&&ei.Object===Object&&ei,eo="object"==typeof self&&self&&self.Object===Object&&self,es=ea||eo||Function("return this")(),el=function(){return es.Date.now()},eu=/\s/,ec=function(e){for(var t=e.length;t--&&eu.test(e.charAt(t)););return t},ed=/^\s+/,eh=es.Symbol,ep=Object.prototype,ef=ep.hasOwnProperty,eg=ep.toString,ev=eh?eh.toStringTag:void 0,ey=function(e){var t=ef.call(e,ev),n=e[ev];try{e[ev]=void 0;var r=!0}catch(e){}var i=eg.call(e);return r&&(t?e[ev]=n:delete e[ev]),i},em=Object.prototype.toString,eb=eh?eh.toStringTag:void 0,ex=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":eb&&eb in Object(e)?ey(e):em.call(e)},ew=function(e){return"symbol"==typeof e||null!=e&&"object"==typeof e&&"[object Symbol]"==ex(e)},eE=0/0,ek=/^[-+]0x[0-9a-f]+$/i,eC=/^0b[01]+$/i,eS=/^0o[0-7]+$/i,eD=parseInt,eT=function(e){if("number"==typeof e)return e;if(ew(e))return eE;if(er(e)){var t,n="function"==typeof e.valueOf?e.valueOf():e;e=er(n)?n+"":n}if("string"!=typeof e)return 0===e?e:+e;e=(t=e)?t.slice(0,ec(t)+1).replace(ed,""):t;var r=eC.test(e);return r||eS.test(e)?eD(e.slice(2),r?2:8):ek.test(e)?eE:+e},eP=Math.max,e_=Math.min,eM=function(e,t,n){var r,i,a,o,s,l,u=0,c=!1,d=!1,h=!0;if("function"!=typeof e)throw TypeError("Expected a function");function p(t){var n=r,a=i;return r=i=void 0,u=t,o=e.apply(a,n)}function f(e){var n=e-l,r=e-u;return void 0===l||n>=t||n<0||d&&r>=a}function g(){var e,n,r,i=el();if(f(i))return v(i);s=setTimeout(g,(e=i-l,n=i-u,r=t-e,d?e_(r,a-n):r))}function v(e){return(s=void 0,h&&r)?p(e):(r=i=void 0,o)}function y(){var e,n=el(),a=f(n);if(r=arguments,i=this,l=n,a){if(void 0===s)return u=e=l,s=setTimeout(g,t),c?p(e):o;if(d)return clearTimeout(s),s=setTimeout(g,t),p(l)}return void 0===s&&(s=setTimeout(g,t)),o}return t=eT(t)||0,er(n)&&(c=!!n.leading,a=(d="maxWait"in n)?eP(eT(n.maxWait)||0,t):a,h="trailing"in n?!!n.trailing:h),y.cancel=function(){void 0!==s&&clearTimeout(s),u=0,r=l=i=s=void 0},y.flush=function(){return void 0===s?o:v(el())},y},eB=b?b.performance:null,eN=eB&&eB.now?function(){return eB.now()}:function(){return Date.now()},eA=function(){if(b){if(b.requestAnimationFrame)return function(e){b.requestAnimationFrame(e)};if(b.mozRequestAnimationFrame)return function(e){b.mozRequestAnimationFrame(e)};if(b.webkitRequestAnimationFrame)return function(e){b.webkitRequestAnimationFrame(e)};if(b.msRequestAnimationFrame)return function(e){b.msRequestAnimationFrame(e)}}return function(e){e&&setTimeout(function(){e(eN())},1e3/60)}}(),eI=function(e){return eA(e)},eO=function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9261,r=n;!(t=e.next()).done;)r=65599*r+t.value|0;return r},eR=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9261;return 65599*t+e|0},ez=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5381;return(t<<5)+t+e|0},eL=function(e){return 2097152*e[0]+e[1]},eV=function(e,t){return[eR(e[0],t[0]),ez(e[1],t[1])]},eF=function(e,t){var n={value:0,done:!1},r=0,i=e.length;return eO({next:function(){return r<i?n.value=e[r++]:n.done=!0,n}},t)},ej=function(e,t){var n={value:0,done:!1},r=0,i=e.length;return eO({next:function(){return r<i?n.value=e.charCodeAt(r++):n.done=!0,n}},t)},eq=function(){return eX(arguments)},eX=function(e){for(var t,n=0;n<e.length;n++){var r=e[n];t=0===n?ej(r):ej(r,t)}return t},eY=!0,eW=null!=console.warn,eH=null!=console.trace,eG=Number.MAX_SAFE_INTEGER||9007199254740991,eU=function(){return!0},eK=function(){return!1},eZ=function(){return 0},e$=function(){},eQ=function(e){throw Error(e)},eJ=function(e){if(void 0===e)return eY;eY=!!e},e0=function(e){eJ()&&(eW?console.warn(e):(console.log(e),eH&&console.trace()))},e1=function(e){return null==e?e:P(e)?e.slice():_(e)?Z({},e):e},e2=function(e,t){for(t=e="";e++<36;t+=51*e&52?(15^e?8^Math.random()*(20^e?16:4):4).toString(16):"-");return t},e5={},e3=function(){return e5},e4=function(e){var t=Object.keys(e);return function(n){for(var r={},i=0;i<t.length;i++){var a=t[i],o=null==n?void 0:n[a];r[a]=void 0===o?e[a]:o}return r}},e9=function(e,t,n){for(var r=e.length-1;r>=0&&(e[r]!==t||(e.splice(r,1),!n));r--);},e6=function(e){e.splice(0,e.length)},e8=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.push(r)}},e7=function(e,t,n){return n&&(t=q(n,t)),e[t]},te=function(e,t,n,r){n&&(t=q(n,t)),e[t]=r},tt=function(){function e(){i(this,e),this._obj={}}return o(e,[{key:"set",value:function(e,t){return this._obj[e]=t,this}},{key:"delete",value:function(e){return this._obj[e]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(e){return void 0!==this._obj[e]}},{key:"get",value:function(e){return this._obj[e]}}]),e}(),tn="undefined"!=typeof Map?Map:tt,tr=function(){function e(t){if(i(this,e),this._obj=Object.create(null),this.size=0,null!=t){var n;n=null!=t.instanceString&&t.instanceString()===this.instanceString()?t.toArray():t;for(var r=0;r<n.length;r++)this.add(n[r])}}return o(e,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(e){var t=this._obj;1!==t[e]&&(t[e]=1,this.size++)}},{key:"delete",value:function(e){var t=this._obj;1===t[e]&&(t[e]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(e){return 1===this._obj[e]}},{key:"toArray",value:function(){var e=this;return Object.keys(this._obj).filter(function(t){return e.has(t)})}},{key:"forEach",value:function(e,t){return this.toArray().forEach(e,t)}}]),e}(),ti=("undefined"==typeof Set?"undefined":r(Set))!=="undefined"?Set:tr,ta=function(e,t){var n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(void 0===e||void 0===t||!O(e)){eQ("An element must have a core reference and parameters set");return}var r=t.group;if(null==r&&(r=t.data&&null!=t.data.source&&null!=t.data.target?"edges":"nodes"),"nodes"!==r&&"edges"!==r){eQ("An element must be of type `nodes` or `edges`; you specified `"+r+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:r,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:void 0===t.selectable||!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:void 0===t.grabbable||!!t.grabbable,pannable:void 0===t.pannable?"edges"===r:!!t.pannable,active:!1,classes:new ti,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(null==i.position.x&&(i.position.x=0),null==i.position.y&&(i.position.y=0),t.renderedPosition){var a=t.renderedPosition,o=e.pan(),s=e.zoom();i.position={x:(a.x-o.x)/s,y:(a.y-o.y)/s}}var l=[];P(t.classes)?l=t.classes:D(t.classes)&&(l=t.classes.split(/\s+/));for(var u=0,c=l.length;u<c;u++){var d=l[u];d&&""!==d&&i.classes.add(d)}this.createEmitter();var h=t.style||t.css;h&&(e0("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h)),(void 0===n||n)&&this.restore()},to=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(t,n,r){_(t)&&!N(t)&&(t=(i=t).roots||i.root,n=i.visit,r=i.directed),r=2!=arguments.length||T(n)?r:n,n=T(n)?n:function(){};for(var i,a,o=this._private.cy,s=t=D(t)?this.filter(t):t,l=[],u=[],c={},d={},h={},p=0,f=this.byGroup(),g=f.nodes,v=f.edges,y=0;y<s.length;y++){var b=s[y],x=b.id();b.isNode()&&(l.unshift(b),e.bfs&&(h[x]=!0,u.push(b)),d[x]=0)}for(;0!==l.length;){var w=function(){var t=e.bfs?l.shift():l.pop(),i=t.id();if(e.dfs){if(h[i])return"continue";h[i]=!0,u.push(t)}var o=d[i],s=c[i],f=null!=s?s.source():null,y=null!=s?s.target():null,b=null==s?void 0:t.same(f)?y[0]:f[0],x=void 0;if(!0===(x=n(t,s,b,p++,o)))return a=t,"break";if(!1===x)return"break";for(var w=t.connectedEdges().filter(function(e){return(!r||e.source().same(t))&&v.has(e)}),E=0;E<w.length;E++){var k=w[E],C=k.connectedNodes().filter(function(e){return!e.same(t)&&g.has(e)}),S=C.id();0===C.length||h[S]||(C=C[0],l.push(C),e.bfs&&(h[S]=!0,u.push(C)),c[S]=k,d[S]=d[i]+1)}}();if("continue"!==w&&"break"===w)break}for(var E=o.collection(),k=0;k<u.length;k++){var C=u[k],S=c[C.id()];null!=S&&E.push(S),E.push(C)}return{path:o.collection(E),found:o.collection(a)}}},ts={breadthFirstSearch:to({bfs:!0}),depthFirstSearch:to({dfs:!0})};ts.bfs=ts.breadthFirstSearch,ts.dfs=ts.depthFirstSearch;var tl=(function(e,t){(function(){var t,n,r,i,a,o,s,l,u,c,d,h,p,f,g;r=Math.floor,c=Math.min,n=function(e,t){return e<t?-1:e>t?1:0},u=function(e,t,i,a,o){var s;if(null==i&&(i=0),null==o&&(o=n),i<0)throw Error("lo must be non-negative");for(null==a&&(a=e.length);i<a;)0>o(t,e[s=r((i+a)/2)])?a=s:i=s+1;return[].splice.apply(e,[i,i-i].concat(t)),t},o=function(e,t,r){return null==r&&(r=n),e.push(t),f(e,0,e.length-1,r)},a=function(e,t){var r,i;return null==t&&(t=n),r=e.pop(),e.length?(i=e[0],e[0]=r,g(e,0,t)):i=r,i},l=function(e,t,r){var i;return null==r&&(r=n),i=e[0],e[0]=t,g(e,0,r),i},s=function(e,t,r){var i;return null==r&&(r=n),e.length&&0>r(e[0],t)&&(t=(i=[e[0],t])[0],e[0]=i[1],g(e,0,r)),t},i=function(e,t){var i,a,o,s,l,u;for(null==t&&(t=n),s=(function(){u=[];for(var t=0,n=r(e.length/2);0<=n?t<n:t>n;0<=n?t++:t--)u.push(t);return u}).apply(this).reverse(),l=[],a=0,o=s.length;a<o;a++)i=s[a],l.push(g(e,i,t));return l},p=function(e,t,r){var i;if(null==r&&(r=n),-1!==(i=e.indexOf(t)))return f(e,0,i,r),g(e,i,r)},d=function(e,t,r){var a,o,l,u;if(null==r&&(r=n),!(a=e.slice(0,t)).length)return a;for(i(a,r),o=0,l=(u=e.slice(t)).length;o<l;o++)s(a,u[o],r);return a.sort(r).reverse()},h=function(e,t,r){var o,s,l,d,h,p,f,g,v;if(null==r&&(r=n),10*t<=e.length){if(!(l=e.slice(0,t).sort(r)).length)return l;for(d=0,s=l[l.length-1],p=(f=e.slice(t)).length;d<p;d++)0>r(o=f[d],s)&&(u(l,o,0,null,r),l.pop(),s=l[l.length-1]);return l}for(i(e,r),v=[],h=0,g=c(t,e.length);0<=g?h<g:h>g;0<=g?++h:--h)v.push(a(e,r));return v},f=function(e,t,r,i){var a,o,s;for(null==i&&(i=n),a=e[r];r>t;){if(o=e[s=r-1>>1],0>i(a,o)){e[r]=o,r=s;continue}break}return e[r]=a},g=function(e,t,r){var i,a,o,s,l;for(null==r&&(r=n),a=e.length,l=t,o=e[t],i=2*t+1;i<a;)(s=i+1)<a&&!(0>r(e[i],e[s]))&&(i=s),e[t]=e[i],i=2*(t=i)+1;return e[t]=o,f(e,l,t,r)},t=function(){function e(e){this.cmp=null!=e?e:n,this.nodes=[]}return e.push=o,e.pop=a,e.replace=l,e.pushpop=s,e.heapify=i,e.updateItem=p,e.nlargest=d,e.nsmallest=h,e.prototype.push=function(e){return o(this.nodes,e,this.cmp)},e.prototype.pop=function(){return a(this.nodes,this.cmp)},e.prototype.peek=function(){return this.nodes[0]},e.prototype.contains=function(e){return -1!==this.nodes.indexOf(e)},e.prototype.replace=function(e){return l(this.nodes,e,this.cmp)},e.prototype.pushpop=function(e){return s(this.nodes,e,this.cmp)},e.prototype.heapify=function(){return i(this.nodes,this.cmp)},e.prototype.updateItem=function(e){return p(this.nodes,e,this.cmp)},e.prototype.clear=function(){return this.nodes=[]},e.prototype.empty=function(){return 0===this.nodes.length},e.prototype.size=function(){return this.nodes.length},e.prototype.clone=function(){var t;return(t=new e).nodes=this.nodes.slice(0),t},e.prototype.toArray=function(){return this.nodes.slice(0)},e.prototype.insert=e.prototype.push,e.prototype.top=e.prototype.peek,e.prototype.front=e.prototype.peek,e.prototype.has=e.prototype.contains,e.prototype.copy=e.prototype.clone,e}(),e.exports=t}).call(ei)}(oX={exports:{}},oX.exports),oX.exports),tu=e4({root:null,weight:function(e){return 1},directed:!1}),tc=e4({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),td=e4({weight:function(e){return 1},directed:!1}),th=e4({weight:function(e){return 1},directed:!1,root:null}),tp=Math.sqrt(2),tf=function(e,t,n){0===n.length&&eQ("Karger-Stein must be run on a connected (sub)graph");for(var r=n[e],i=r[1],a=r[2],o=t[i],s=t[a],l=n,u=l.length-1;u>=0;u--){var c=l[u],d=c[1],h=c[2];(t[d]===o&&t[h]===s||t[d]===s&&t[h]===o)&&l.splice(u,1)}for(var p=0;p<l.length;p++){var f=l[p];f[1]===s?(l[p]=f.slice(),l[p][1]=o):f[2]===s&&(l[p]=f.slice(),l[p][2]=o)}for(var g=0;g<t.length;g++)t[g]===s&&(t[g]=o);return l},tg=function(e,t,n,r){for(;n>r;)t=tf(Math.floor(Math.random()*t.length),e,t),n--;return t},tv=function(e,t,n){return{x:e.x*t+n.x,y:e.y*t+n.y}},ty=function(e,t,n){return{x:(e.x-n.x)/t,y:(e.y-n.y)/t}},tm=function(e){return{x:e[0],y:e[1]}},tb=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=1/0,i=t;i<n;i++){var a=e[i];isFinite(a)&&(r=Math.min(a,r))}return r},tx=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=-1/0,i=t;i<n;i++){var a=e[i];isFinite(a)&&(r=Math.max(a,r))}return r},tw=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=0,i=0,a=t;a<n;a++){var o=e[a];isFinite(o)&&(r+=o,i++)}return r/i},tE=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],a=!(arguments.length>5)||void 0===arguments[5]||arguments[5];r?e=e.slice(t,n):(n<e.length&&e.splice(n,e.length-n),t>0&&e.splice(0,t));for(var o=0,s=e.length-1;s>=0;s--){var l=e[s];a?!isFinite(l)&&(e[s]=-1/0,o++):e.splice(s,1)}i&&e.sort(function(e,t){return e-t});var u=e.length,c=Math.floor(u/2);return u%2!=0?e[c+1+o]:(e[c-1+o]+e[c+o])/2},tk=function(e,t){return Math.atan2(t,e)-Math.PI/2},tC=Math.log2||function(e){return Math.log(e)/Math.log(2)},tS=function(e){return e>0?1:e<0?-1:0},tD=function(e,t){return Math.sqrt(tT(e,t))},tT=function(e,t){var n=t.x-e.x,r=t.y-e.y;return n*n+r*r},tP=function(e){for(var t=e.length,n=0,r=0;r<t;r++)n+=e[r];for(var i=0;i<t;i++)e[i]=e[i]/n;return e},t_=function(e,t,n,r){return(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n},tM=function(e,t,n,r){return{x:t_(e.x,t.x,n.x,r),y:t_(e.y,t.y,n.y,r)}},tB=function(e,t,n,r){var i={x:t.x-e.x,y:t.y-e.y},a=tD(e,t),o={x:i.x/a,y:i.y/a};return n=null==n?0:n,r=null!=r?r:n*a,{x:e.x+o.x*r,y:e.y+o.y*r}},tN=function(e,t,n){return Math.max(e,Math.min(n,t))},tA=function(e){if(null==e)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(null!=e.x1&&null!=e.y1){if(null!=e.x2&&null!=e.y2&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(null!=e.w&&null!=e.h&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},tI=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},tO=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},tR=function(e,t,n){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,n),e.y2=Math.max(e.y2,n),e.h=e.y2-e.y1},tz=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},tL=function(e){var t,n,r,i,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[0];if(1===a.length)t=n=r=i=a[0];else if(2===a.length)t=r=a[0],i=n=a[1];else if(4===a.length){var o=l(a,4);t=o[0],n=o[1],r=o[2],i=o[3]}return e.x1-=i,e.x2+=n,e.y1-=t,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},tV=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},tF=function(e,t){return!(e.x1>t.x2)&&!(t.x1>e.x2)&&!(e.x2<t.x1)&&!(t.x2<e.x1)&&!(e.y2<t.y1)&&!(t.y2<e.y1)&&!(e.y1>t.y2)&&!(t.y1>e.y2)},tj=function(e,t,n){return e.x1<=t&&t<=e.x2&&e.y1<=n&&n<=e.y2},tq=function(e,t){return tj(e,t.x1,t.y1)&&tj(e,t.x2,t.y2)},tX=function(e,t,n,r,i,a,o){var s,l,u=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"auto",c="auto"===u?nt(i,a):u,d=i/2,h=a/2,p=(c=Math.min(c,d,h))!==d,f=c!==h;if(p){var g=n-d+c-o,v=r-h-o,y=n+d-c+o;if((s=t3(e,t,n,r,g,v,y,v,!1)).length>0)return s}if(f){var b=n+d+o,x=r-h+c-o,w=r+h-c+o;if((s=t3(e,t,n,r,b,x,b,w,!1)).length>0)return s}if(p){var E=n-d+c-o,k=r+h+o,C=n+d-c+o;if((s=t3(e,t,n,r,E,k,C,k,!1)).length>0)return s}if(f){var S=n-d-o,D=r-h+c-o,T=r+h-c+o;if((s=t3(e,t,n,r,S,D,S,T,!1)).length>0)return s}var P=n-d+c,_=r-h+c;if((l=t2(e,t,n,r,P,_,c+o)).length>0&&l[0]<=P&&l[1]<=_)return[l[0],l[1]];var M=n+d-c,B=r-h+c;if((l=t2(e,t,n,r,M,B,c+o)).length>0&&l[0]>=M&&l[1]<=B)return[l[0],l[1]];var N=n+d-c,A=r+h-c;if((l=t2(e,t,n,r,N,A,c+o)).length>0&&l[0]>=N&&l[1]>=A)return[l[0],l[1]];var I=n-d+c,O=r+h-c;return(l=t2(e,t,n,r,I,O,c+o)).length>0&&l[0]<=I&&l[1]>=O?[l[0],l[1]]:[]},tY=function(e,t,n,r,i,a,o,s,l){var u={x1:Math.min(n,o,i)-l,x2:Math.max(n,o,i)+l,y1:Math.min(r,s,a)-l,y2:Math.max(r,s,a)+l};return!(e<u.x1)&&!(e>u.x2)&&!(t<u.y1)&&!(t>u.y2)},tW=function(e,t,n,r){var i=t*t-4*e*(n-=r);if(i<0)return[];var a=Math.sqrt(i),o=2*e;return[(-t+a)/o,(-t-a)/o]},tH=function(e,t,n,r,i){var a,o,s,l,u,c,d,h;if(0===e&&(e=1e-5),t/=e,n/=e,r/=e,a=(o=(3*n-t*t)/9)*o*o+(s=(-(27*r)+t*(9*n-2*(t*t)))/54)*s,i[1]=0,d=t/3,a>0){u=(u=s+Math.sqrt(a))<0?-Math.pow(-u,1/3):Math.pow(u,1/3),c=(c=s-Math.sqrt(a))<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-d+u+c,d+=(u+c)/2,i[4]=i[2]=-d,d=Math.sqrt(3)*(-c+u)/2,i[3]=d,i[5]=-d;return}if(i[5]=i[3]=0,0===a){h=s<0?-Math.pow(-s,1/3):Math.pow(s,1/3),i[0]=-d+2*h,i[4]=i[2]=-(h+d);return}l=Math.acos(s/Math.sqrt(l=(o=-o)*o*o)),h=2*Math.sqrt(o),i[0]=-d+h*Math.cos(l/3),i[2]=-d+h*Math.cos((l+2*Math.PI)/3),i[4]=-d+h*Math.cos((l+4*Math.PI)/3)},tG=function(e,t,n,r,i,a,o,s){var l,u=[];tH(1*n*n-4*n*i+2*n*o+4*i*i-4*i*o+o*o+r*r-4*r*a+2*r*s+4*a*a-4*a*s+s*s,9*n*i-3*n*n-3*n*o-6*i*i+3*i*o+9*r*a-3*r*r-3*r*s-6*a*a+3*a*s,3*n*n-6*n*i+n*o-n*e+2*i*i+2*i*e-o*e+3*r*r-6*r*a+r*s-r*t+2*a*a+2*a*t-s*t,1*n*i-n*n+n*e-i*e+r*a-r*r+r*t-a*t,u);for(var c=[],d=0;d<6;d+=2)1e-7>Math.abs(u[d+1])&&u[d]>=0&&u[d]<=1&&c.push(u[d]);c.push(1),c.push(0);for(var h=-1,p=0;p<c.length;p++)l=Math.pow(Math.pow(1-c[p],2)*n+2*(1-c[p])*c[p]*i+c[p]*c[p]*o-e,2)+Math.pow(Math.pow(1-c[p],2)*r+2*(1-c[p])*c[p]*a+c[p]*c[p]*s-t,2),h>=0?l<h&&(h=l):h=l;return h},tU=function(e,t,n,r,i,a){var o=[e-n,t-r],s=[i-n,a-r],l=s[0]*s[0]+s[1]*s[1],u=o[0]*o[0]+o[1]*o[1],c=o[0]*s[0]+o[1]*s[1],d=c*c/l;return c<0?u:d>l?(e-i)*(e-i)+(t-a)*(t-a):u-d},tK=function(e,t,n){for(var r,i,a,o,s=0,l=0;l<n.length/2;l++)if(r=n[2*l],i=n[2*l+1],l+1<n.length/2?(a=n[(l+1)*2],o=n[(l+1)*2+1]):(a=n[(l+1-n.length/2)*2],o=n[(l+1-n.length/2)*2+1]),r==e&&a==e);else{if((!(r>=e)||!(e>=a))&&(!(r<=e)||!(e<=a)))continue;(e-r)/(a-r)*(o-i)+i>t&&s++}return s%2!=0},tZ=function(e,t,n,r,i,a,o,s,l){var u,c=Array(n.length);null!=s[0]?(u=Math.atan(s[1]/s[0]),s[0]<0?u+=Math.PI/2:u=-u-Math.PI/2):u=s;for(var d=Math.cos(-u),h=Math.sin(-u),p=0;p<c.length/2;p++)c[2*p]=a/2*(n[2*p]*d-n[2*p+1]*h),c[2*p+1]=o/2*(n[2*p+1]*d+n[2*p]*h),c[2*p]+=r,c[2*p+1]+=i;return tK(e,t,l>0?tQ(tJ(c,-l)):c)},t$=function(e,t,n,r,i,a,o,s){for(var l=Array(2*n.length),u=0;u<s.length;u++){var c=s[u];if(l[4*u+0]=c.startX,l[4*u+1]=c.startY,l[4*u+2]=c.stopX,l[4*u+3]=c.stopY,Math.pow(c.cx-e,2)+Math.pow(c.cy-t,2)<=Math.pow(c.radius,2))return!0}return tK(e,t,l)},tQ=function(e){for(var t,n,r,i,a,o,s,l,u=Array(e.length/2),c=0;c<e.length/4;c++){t=e[4*c],n=e[4*c+1],r=e[4*c+2],i=e[4*c+3],c<e.length/4-1?(a=e[(c+1)*4],o=e[(c+1)*4+1],s=e[(c+1)*4+2],l=e[(c+1)*4+3]):(a=e[0],o=e[1],s=e[2],l=e[3]);var d=t3(t,n,r,i,a,o,s,l,!0);u[2*c]=d[0],u[2*c+1]=d[1]}return u},tJ=function(e,t){for(var n,r,i,a,o=Array(2*e.length),s=0;s<e.length/2;s++){n=e[2*s],r=e[2*s+1],s<e.length/2-1?(i=e[(s+1)*2],a=e[(s+1)*2+1]):(i=e[0],a=e[1]);var l=a-r,u=-(i-n),c=Math.sqrt(l*l+u*u),d=l/c,h=u/c;o[4*s]=n+d*t,o[4*s+1]=r+h*t,o[4*s+2]=i+d*t,o[4*s+3]=a+h*t}return o},t0=function(e,t,n,r,i,a){var o=n-e,s=r-t,l=Math.sqrt((o/=i)*o+(s/=a)*s),u=l-1;if(u<0)return[];var c=u/l;return[(n-e)*c+e,(r-t)*c+t]},t1=function(e,t,n,r,i,a,o){return e-=i,t-=a,(e/=n/2+o)*e+(t/=r/2+o)*t<=1},t2=function(e,t,n,r,i,a,o){var s=[n-e,r-t],l=[e-i,t-a],u=s[0]*s[0]+s[1]*s[1],c=2*(l[0]*s[0]+l[1]*s[1]),d=c*c-4*u*(l[0]*l[0]+l[1]*l[1]-o*o);if(d<0)return[];var h=(-c+Math.sqrt(d))/(2*u),p=(-c-Math.sqrt(d))/(2*u),f=Math.min(h,p),g=Math.max(h,p),v=[];if(f>=0&&f<=1&&v.push(f),g>=0&&g<=1&&v.push(g),0===v.length)return[];var y=v[0]*s[0]+e,b=v[0]*s[1]+t;return v.length>1?v[0]==v[1]?[y,b]:[y,b,v[1]*s[0]+e,v[1]*s[1]+t]:[y,b]},t5=function(e,t,n){return t<=e&&e<=n||n<=e&&e<=t?e:e<=t&&t<=n||n<=t&&t<=e?t:n},t3=function(e,t,n,r,i,a,o,s,l){var u=e-i,c=n-e,d=o-i,h=t-a,p=r-t,f=s-a,g=d*h-f*u,v=c*h-p*u,y=f*c-d*p;if(0!==y){var b=g/y,x=v/y;return -.001<=b&&b<=1.001&&-.001<=x&&x<=1.001?[e+b*c,t+b*p]:l?[e+b*c,t+b*p]:[]}return 0!==g&&0!==v?[]:t5(e,n,o)===o?[o,s]:t5(e,n,i)===i?[i,a]:t5(i,o,n)===n?[n,r]:[]},t4=function(e,t,n,r,i,a,o,s){var l,u,c,d,h,p,f=[],g=Array(n.length),v=!0;if(null==a&&(v=!1),v){for(var y=0;y<g.length/2;y++)g[2*y]=n[2*y]*a+r,g[2*y+1]=n[2*y+1]*o+i;u=s>0?tQ(tJ(g,-s)):g}else u=n;for(var b=0;b<u.length/2;b++)c=u[2*b],d=u[2*b+1],b<u.length/2-1?(h=u[(b+1)*2],p=u[(b+1)*2+1]):(h=u[0],p=u[1]),0!==(l=t3(e,t,r,i,c,d,h,p)).length&&f.push(l[0],l[1]);return f},t9=function(e,t,n,r,i,a,o,s,l){var u,c=[],d=Array(2*n.length);l.forEach(function(n,a){0===a?(d[d.length-2]=n.startX,d[d.length-1]=n.startY):(d[4*a-2]=n.startX,d[4*a-1]=n.startY),d[4*a]=n.stopX,d[4*a+1]=n.stopY,0!==(u=t2(e,t,r,i,n.cx,n.cy,n.radius)).length&&c.push(u[0],u[1])});for(var h=0;h<d.length/4;h++)0!==(u=t3(e,t,r,i,d[4*h],d[4*h+1],d[4*h+2],d[4*h+3],!1)).length&&c.push(u[0],u[1]);if(c.length>2){for(var p=[c[0],c[1]],f=Math.pow(p[0]-e,2)+Math.pow(p[1]-t,2),g=1;g<c.length/2;g++){var v=Math.pow(c[2*g]-e,2)+Math.pow(c[2*g+1]-t,2);v<=f&&(p[0]=c[2*g],p[1]=c[2*g+1],f=v)}return p}return c},t6=function(e,t,n){var r=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(r[0]*r[0]+r[1]*r[1]),a=(i-n)/i;return a<0&&(a=1e-5),[t[0]+a*r[0],t[1]+a*r[1]]},t8=function(e,t){return t7(ne(e,t))},t7=function(e){for(var t,n,r=e.length/2,i=1/0,a=1/0,o=-1/0,s=-1/0,l=0;l<r;l++)t=e[2*l],n=e[2*l+1],i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);for(var u=2/(o-i),c=2/(s-a),d=0;d<r;d++)t=e[2*d]=e[2*d]*u,n=e[2*d+1]=e[2*d+1]*c,i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);if(a<-1)for(var h=0;h<r;h++)n=e[2*h+1]=e[2*h+1]+(-1-a);return e},ne=function(e,t){var n,r=1/e*2*Math.PI,i=e%2==0?Math.PI/2+r/2:Math.PI/2;i+=t;for(var a=Array(2*e),o=0;o<e;o++)n=o*r+i,a[2*o]=Math.cos(n),a[2*o+1]=Math.sin(-n);return a},nt=function(e,t){return Math.min(e/4,t/4,8)},nn=function(e,t){return Math.min(e/10,t/10,8)},nr=function(){return 8},ni=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},na=e4({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),no=e4({root:null,weight:function(e){return 1},directed:!1,alpha:0}),ns={degreeCentralityNormalized:function(e){e=no(e);var t=this.cy(),n=this.nodes(),r=n.length;if(e.directed){for(var i={},a={},o=0,s=0,l=0;l<r;l++){var u=n[l],c=u.id();e.root=u;var d=this.degreeCentrality(e);o<d.indegree&&(o=d.indegree),s<d.outdegree&&(s=d.outdegree),i[c]=d.indegree,a[c]=d.outdegree}return{indegree:function(e){return 0==o?0:(D(e)&&(e=t.filter(e)),i[e.id()]/o)},outdegree:function(e){return 0===s?0:(D(e)&&(e=t.filter(e)),a[e.id()]/s)}}}for(var h={},p=0,f=0;f<r;f++){var g=n[f];e.root=g;var v=this.degreeCentrality(e);p<v.degree&&(p=v.degree),h[g.id()]=v.degree}return{degree:function(e){return 0===p?0:(D(e)&&(e=t.filter(e)),h[e.id()]/p)}}},degreeCentrality:function(e){e=no(e);var t=this.cy(),n=this,r=e,i=r.root,a=r.weight,o=r.directed,s=r.alpha;if(i=t.collection(i)[0],o){for(var l=i.connectedEdges(),u=l.filter(function(e){return e.target().same(i)&&n.has(e)}),c=l.filter(function(e){return e.source().same(i)&&n.has(e)}),d=u.length,h=c.length,p=0,f=0,g=0;g<u.length;g++)p+=a(u[g]);for(var v=0;v<c.length;v++)f+=a(c[v]);return{indegree:Math.pow(d,1-s)*Math.pow(p,s),outdegree:Math.pow(h,1-s)*Math.pow(f,s)}}for(var y=i.connectedEdges().intersection(n),b=y.length,x=0,w=0;w<y.length;w++)x+=a(y[w]);return{degree:Math.pow(b,1-s)*Math.pow(x,s)}}};ns.dc=ns.degreeCentrality,ns.dcn=ns.degreeCentralityNormalised=ns.degreeCentralityNormalized;var nl=e4({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),nu={closenessCentralityNormalized:function(e){for(var t=nl(e),n=t.harmonic,r=t.weight,i=t.directed,a=this.cy(),o={},s=0,l=this.nodes(),u=this.floydWarshall({weight:r,directed:i}),c=0;c<l.length;c++){for(var d=0,h=l[c],p=0;p<l.length;p++)if(c!==p){var f=u.distance(h,l[p]);n?d+=1/f:d+=f}n||(d=1/d),s<d&&(s=d),o[h.id()]=d}return{closeness:function(e){return 0==s?0:o[e=D(e)?a.filter(e)[0].id():e.id()]/s}}},closenessCentrality:function(e){var t=nl(e),n=t.root,r=t.weight,i=t.directed,a=t.harmonic;n=this.filter(n)[0];for(var o=this.dijkstra({root:n,weight:r,directed:i}),s=0,l=this.nodes(),u=0;u<l.length;u++){var c=l[u];if(!c.same(n)){var d=o.distanceTo(c);a?s+=1/d:s+=d}}return a?s:1/s}};nu.cc=nu.closenessCentrality,nu.ccn=nu.closenessCentralityNormalised=nu.closenessCentralityNormalized;var nc=e4({weight:null,directed:!1}),nd={betweennessCentrality:function(e){for(var t=nc(e),n=t.directed,r=t.weight,i=null!=r,a=this.cy(),o=this.nodes(),s={},l={},u=0,c={set:function(e,t){l[e]=t,t>u&&(u=t)},get:function(e){return l[e]}},d=0;d<o.length;d++){var h=o[d],p=h.id();n?s[p]=h.outgoers().nodes():s[p]=h.openNeighborhood().nodes(),c.set(p,0)}for(var f=0;f<o.length;f++)!function(e){for(var t=o[e].id(),n=[],l={},u={},d={},h=new tl(function(e,t){return d[e]-d[t]}),p=0;p<o.length;p++){var f=o[p].id();l[f]=[],u[f]=0,d[f]=1/0}for(u[t]=1,d[t]=0,h.push(t);!h.empty();){var g=h.pop();if(n.push(g),i)for(var v=0;v<s[g].length;v++){var y=s[g][v],b=a.getElementById(g),x=r(b.edgesTo(y).length>0?b.edgesTo(y)[0]:y.edgesTo(b)[0]);d[y=y.id()]>d[g]+x&&(d[y]=d[g]+x,0>h.nodes.indexOf(y)?h.push(y):h.updateItem(y),u[y]=0,l[y]=[]),d[y]==d[g]+x&&(u[y]=u[y]+u[g],l[y].push(g))}else for(var w=0;w<s[g].length;w++){var E=s[g][w].id();d[E]==1/0&&(h.push(E),d[E]=d[g]+1),d[E]==d[g]+1&&(u[E]=u[E]+u[g],l[E].push(g))}}for(var k={},C=0;C<o.length;C++)k[o[C].id()]=0;for(;n.length>0;){for(var S=n.pop(),D=0;D<l[S].length;D++){var T=l[S][D];k[T]=k[T]+u[T]/u[S]*(1+k[S])}S!=o[e].id()&&c.set(S,c.get(S)+k[S])}}(f);var g={betweenness:function(e){var t=a.collection(e).id();return c.get(t)},betweennessNormalized:function(e){if(0==u)return 0;var t=a.collection(e).id();return c.get(t)/u}};return g.betweennessNormalised=g.betweennessNormalized,g}};nd.bc=nd.betweennessCentrality;var nh=e4({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(e){return 1}]}),np=function(e,t){for(var n=0,r=0;r<t.length;r++)n+=t[r](e);return n},nf=function(e,t,n){for(var r=0;r<t;r++)e[r*t+r]=n},ng=function(e,t){for(var n,r=0;r<t;r++){n=0;for(var i=0;i<t;i++)n+=e[i*t+r];for(var a=0;a<t;a++)e[a*t+r]=e[a*t+r]/n}},nv=function(e,t,n){for(var r=Array(n*n),i=0;i<n;i++){for(var a=0;a<n;a++)r[i*n+a]=0;for(var o=0;o<n;o++)for(var s=0;s<n;s++)r[i*n+s]+=e[i*n+o]*t[o*n+s]}return r},ny=function(e,t,n){for(var r=e.slice(0),i=1;i<n;i++)e=nv(e,r,t);return e},nm=function(e,t,n){for(var r=Array(t*t),i=0;i<t*t;i++)r[i]=Math.pow(e[i],n);return ng(r,t),r},nb=function(e,t,n,r){for(var i=0;i<n;i++)if(Math.round(e[i]*Math.pow(10,r))/Math.pow(10,r)!=Math.round(t[i]*Math.pow(10,r))/Math.pow(10,r))return!1;return!0},nx=function(e,t,n,r){for(var i=[],a=0;a<t;a++){for(var o=[],s=0;s<t;s++)Math.round(1e3*e[a*t+s])/1e3>0&&o.push(n[s]);0!==o.length&&i.push(r.collection(o))}return i},nw=function(e,t){for(var n=0;n<e.length;n++)if(!t[n]||e[n].id()!==t[n].id())return!1;return!0},nE=function(e){for(var t=0;t<e.length;t++)for(var n=0;n<e.length;n++)t!=n&&nw(e[t],e[n])&&e.splice(n,1);return e},nk=function(e){for(var t=this.nodes(),n=this.edges(),r=this.cy(),i=nh(e),a={},o=0;o<t.length;o++)a[t[o].id()]=o;for(var s,l=t.length,u=l*l,c=Array(u),d=0;d<u;d++)c[d]=0;for(var h=0;h<n.length;h++){var p=n[h],f=a[p.source().id()],g=a[p.target().id()],v=np(p,i.attributes);c[f*l+g]+=v,c[g*l+f]+=v}nf(c,l,i.multFactor),ng(c,l);for(var y=!0,b=0;y&&b<i.maxIterations;)y=!1,nb(c=nm(s=ny(c,l,i.expandFactor),l,i.inflateFactor),s,u,4)||(y=!0),b++;return nE(nx(c,l,t,r))},nC=function(e){return e},nS=function(e,t){return Math.abs(t-e)},nD=function(e,t,n){return e+nS(t,n)},nT=function(e,t,n){return e+Math.pow(n-t,2)},nP=function(e){return Math.sqrt(e)},n_=function(e,t,n){return Math.max(e,nS(t,n))},nM=function(e,t,n,r,i){for(var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:nC,o=r,s=0;s<e;s++)o=i(o,t(s),n(s));return a(o)},nB={euclidean:function(e,t,n){return e>=2?nM(e,t,n,0,nT,nP):nM(e,t,n,0,nD)},squaredEuclidean:function(e,t,n){return nM(e,t,n,0,nT)},manhattan:function(e,t,n){return nM(e,t,n,0,nD)},max:function(e,t,n){return nM(e,t,n,-1/0,n_)}};function nN(e,t,n,r,i,a){var o;return(o=T(e)?e:nB[e]||nB.euclidean,0===t&&T(e))?o(i,a):o(t,n,r,i,a)}nB["squared-euclidean"]=nB.squaredEuclidean,nB.squaredeuclidean=nB.squaredEuclidean;var nA=e4({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),nI=function(e){return nA(e)},nO=function(e,t,n,r,i){return nN(e,r.length,"kMedoids"!==i?function(e){return n[e]}:function(e){return r[e](n)},function(e){return r[e](t)},n,t)},nR=function(e,t,n){for(var r=n.length,i=Array(r),a=Array(r),o=Array(t),s=null,l=0;l<r;l++)i[l]=e.min(n[l]).value,a[l]=e.max(n[l]).value;for(var u=0;u<t;u++){s=[];for(var c=0;c<r;c++)s[c]=Math.random()*(a[c]-i[c])+i[c];o[u]=s}return o},nz=function(e,t,n,r,i){for(var a=1/0,o=0,s=0;s<t.length;s++){var l=nO(n,e,t[s],r,i);l<a&&(a=l,o=s)}return o},nL=function(e,t,n){for(var r=[],i=null,a=0;a<t.length;a++)n[(i=t[a]).id()]===e&&r.push(i);return r},nV=function(e,t,n){for(var r=0;r<e.length;r++)for(var i=0;i<e[r].length;i++)if(Math.abs(e[r][i]-t[r][i])>n)return!1;return!0},nF=function(e,t,n){for(var r=0;r<n;r++)if(e===t[r])return!0;return!1},nj=function(e,t){var n=Array(t);if(e.length<50)for(var r=0;r<t;r++){for(var i=e[Math.floor(Math.random()*e.length)];nF(i,n,r);)i=e[Math.floor(Math.random()*e.length)];n[r]=i}else for(var a=0;a<t;a++)n[a]=e[Math.floor(Math.random()*e.length)];return n},nq=function(e,t,n){for(var r=0,i=0;i<t.length;i++)r+=nO("manhattan",t[i],e,n,"kMedoids");return r},nX=function(e,t,n,r,i){for(var a,o,s=0;s<t.length;s++)for(var l=0;l<e.length;l++)r[s][l]=Math.pow(n[s][l],i.m);for(var u=0;u<e.length;u++)for(var c=0;c<i.attributes.length;c++){a=0,o=0;for(var d=0;d<t.length;d++)a+=r[d][u]*i.attributes[c](t[d]),o+=r[d][u];e[u][c]=a/o}},nY=function(e,t,n,r,i){for(var a,o=0;o<e.length;o++)t[o]=e[o].slice();for(var s=2/(i.m-1),l=0;l<n.length;l++)for(var u=0;u<r.length;u++){a=0;for(var c=0;c<n.length;c++)a+=Math.pow(nO(i.distance,r[u],n[l],i.attributes,"cmeans")/nO(i.distance,r[u],n[c],i.attributes,"cmeans"),s);e[u][l]=1/a}},nW=function(e,t,n,r){for(var i,a,o=Array(n.k),s=0;s<o.length;s++)o[s]=[];for(var l=0;l<t.length;l++){i=-1/0,a=-1;for(var u=0;u<t[0].length;u++)t[l][u]>i&&(i=t[l][u],a=u);o[a].push(e[l])}for(var c=0;c<o.length;c++)o[c]=r.collection(o[c]);return o},nH=function(e){var t,n,r,i,a=this.cy(),o=this.nodes(),s=nI(e);r=Array(o.length);for(var l=0;l<o.length;l++)r[l]=Array(s.k);n=Array(o.length);for(var u=0;u<o.length;u++)n[u]=Array(s.k);for(var c=0;c<o.length;c++){for(var d=0,h=0;h<s.k;h++)n[c][h]=Math.random(),d+=n[c][h];for(var p=0;p<s.k;p++)n[c][p]=n[c][p]/d}t=Array(s.k);for(var f=0;f<s.k;f++)t[f]=Array(s.attributes.length);i=Array(o.length);for(var g=0;g<o.length;g++)i[g]=Array(s.k);for(var v=!0,y=0;v&&y<s.maxIterations;)v=!1,nX(t,o,n,i,s),nY(n,r,t,o,s),nV(n,r,s.sensitivityThreshold)||(v=!0),y++;return{clusters:nW(o,n,s,a),degreeOfMembership:n}},nG=e4({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),nU={single:"min",complete:"max"},nK=function(e){var t=nG(e),n=nU[t.linkage];return null!=n&&(t.linkage=n),t},nZ=function(e,t,n,r,i){for(var a,o,s=0,l=1/0,u=i.attributes,c=function(e,t){return nN(i.distance,u.length,function(t){return u[t](e)},function(e){return u[e](t)},e,t)},d=0;d<e.length;d++){var h=e[d].key,p=n[h][r[h]];p<l&&(s=h,l=p)}if("threshold"===i.mode&&l>=i.threshold||"dendrogram"===i.mode&&1===e.length)return!1;var f=t[s],g=t[r[s]];o="dendrogram"===i.mode?{left:f,right:g,key:f.key}:{value:f.value.concat(g.value),key:f.key},e[f.index]=o,e.splice(g.index,1),t[f.key]=o;for(var v=0;v<e.length;v++){var y=e[v];f.key===y.key?a=1/0:"min"===i.linkage?(a=n[f.key][y.key],n[f.key][y.key]>n[g.key][y.key]&&(a=n[g.key][y.key])):"max"===i.linkage?(a=n[f.key][y.key],n[f.key][y.key]<n[g.key][y.key]&&(a=n[g.key][y.key])):a="mean"===i.linkage?(n[f.key][y.key]*f.size+n[g.key][y.key]*g.size)/(f.size+g.size):"dendrogram"===i.mode?c(y.value,f.value):c(y.value[0],f.value[0]),n[f.key][y.key]=n[y.key][f.key]=a}for(var b=0;b<e.length;b++){var x=e[b].key;if(r[x]===f.key||r[x]===g.key){for(var w=x,E=0;E<e.length;E++){var k=e[E].key;n[x][k]<n[x][w]&&(w=k)}r[x]=w}e[b].index=b}return f.key=g.key=f.index=g.index=null,!0},n$=function e(t,n,r){t&&(t.value?n.push(t.value):(t.left&&e(t.left,n),t.right&&e(t.right,n)))},nQ=function e(t,n){if(!t)return"";if(t.left&&t.right){var r=e(t.left,n),i=e(t.right,n),a=n.add({group:"nodes",data:{id:r+","+i}});return n.add({group:"edges",data:{source:r,target:a.id()}}),n.add({group:"edges",data:{source:i,target:a.id()}}),a.id()}if(t.value)return t.value.id()},nJ=function e(t,n,r){if(!t)return[];var i=[],a=[],o=[];return 0===n?(t.left&&n$(t.left,i),t.right&&n$(t.right,a),o=i.concat(a),[r.collection(o)]):1===n?t.value?[r.collection(t.value)]:(t.left&&n$(t.left,i),t.right&&n$(t.right,a),[r.collection(i),r.collection(a)]):t.value?[r.collection(t.value)]:(t.left&&(i=e(t.left,n-1,r)),t.right&&(a=e(t.right,n-1,r)),i.concat(a))},n0=function(e){for(var t,n=this.cy(),r=this.nodes(),i=nK(e),a=i.attributes,o=function(e,t){return nN(i.distance,a.length,function(t){return a[t](e)},function(e){return a[e](t)},e,t)},s=[],l=[],u=[],c=[],d=0;d<r.length;d++){var h={value:"dendrogram"===i.mode?r[d]:[r[d]],key:d,index:d};s[d]=h,c[d]=h,l[d]=[],u[d]=0}for(var p=0;p<s.length;p++)for(var f=0;f<=p;f++){var g=void 0;g="dendrogram"===i.mode?p===f?1/0:o(s[p].value,s[f].value):p===f?1/0:o(s[p].value[0],s[f].value[0]),l[p][f]=g,l[f][p]=g,g<l[p][u[p]]&&(u[p]=f)}for(var v=nZ(s,c,l,u,i);v;)v=nZ(s,c,l,u,i);return"dendrogram"===i.mode?(t=nJ(s[0],i.dendrogramDepth,n),i.addDendrogram&&nQ(s[0],n)):(t=Array(s.length),s.forEach(function(e,r){e.key=e.index=null,t[r]=n.collection(e.value)})),t},n1=e4({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),n2=function(e){var t=e.damping,n=e.preference;.5<=t&&t<1||eQ("Damping must range on [0.5, 1).  Got: ".concat(t));var r=["median","mean","min","max"];return r.some(function(e){return e===n})||M(n)||eQ("Preference must be one of [".concat(r.map(function(e){return"'".concat(e,"'")}).join(", "),"] or a number.  Got: ").concat(n)),n1(e)},n5=function(e,t,n,r){var i=function(e,t){return r[t](e)};return-nN(e,r.length,function(e){return i(t,e)},function(e){return i(n,e)},t,n)},n3=function(e,t,n){for(var r=[],i=0;i<e;i++)t[i*e+i]+n[i*e+i]>0&&r.push(i);return r},n4=function(e,t,n){for(var r=[],i=0;i<e;i++){for(var a=-1,o=-1/0,s=0;s<n.length;s++){var l=n[s];t[i*e+l]>o&&(a=l,o=t[i*e+l])}a>0&&r.push(a)}for(var u=0;u<n.length;u++)r[n[u]]=n[u];return r},n9=function(e,t,n){for(var r=n4(e,t,n),i=0;i<n.length;i++){for(var a=[],o=0;o<r.length;o++)r[o]===n[i]&&a.push(o);for(var s=-1,l=-1/0,u=0;u<a.length;u++){for(var c=0,d=0;d<a.length;d++)c+=t[a[d]*e+a[u]];c>l&&(s=u,l=c)}n[i]=a[s]}return n4(e,t,n)},n6=function(e){for(var t,n,r,i,a,o,s,l,u,c=this.cy(),d=this.nodes(),h=n2(e),p={},f=0;f<d.length;f++)p[d[f].id()]=f;a=Array(i=(r=d.length)*r);for(var g=0;g<i;g++)a[g]=-1/0;for(var v=0;v<r;v++)for(var y=0;y<r;y++)v!==y&&(a[v*r+y]=n5(h.distance,d[v],d[y],h.attributes));t=a,o="median"===(n=h.preference)?tE(t):"mean"===n?tw(t):"min"===n?tb(t):"max"===n?tx(t):n;for(var b=0;b<r;b++)a[b*r+b]=o;s=Array(i);for(var x=0;x<i;x++)s[x]=0;l=Array(i);for(var w=0;w<i;w++)l[w]=0;for(var E=Array(r),k=Array(r),C=Array(r),S=0;S<r;S++)E[S]=0,k[S]=0,C[S]=0;for(var D=Array(r*h.minIterations),T=0;T<D.length;T++)D[T]=0;for(u=0;u<h.maxIterations;u++){for(var P=0;P<r;P++){for(var _=-1/0,M=-1/0,B=-1,N=0,A=0;A<r;A++)E[A]=s[P*r+A],(N=l[P*r+A]+a[P*r+A])>=_?(M=_,_=N,B=A):N>M&&(M=N);for(var I=0;I<r;I++)s[P*r+I]=(1-h.damping)*(a[P*r+I]-_)+h.damping*E[I];s[P*r+B]=(1-h.damping)*(a[P*r+B]-M)+h.damping*E[B]}for(var O=0;O<r;O++){for(var R=0,z=0;z<r;z++)E[z]=l[z*r+O],k[z]=Math.max(0,s[z*r+O]),R+=k[z];R-=k[O],k[O]=s[O*r+O],R+=k[O];for(var L=0;L<r;L++)l[L*r+O]=(1-h.damping)*Math.min(0,R-k[L])+h.damping*E[L];l[O*r+O]=(1-h.damping)*(R-k[O])+h.damping*E[O]}for(var V=0,F=0;F<r;F++){var j=l[F*r+F]+s[F*r+F]>0?1:0;D[u%h.minIterations*r+F]=j,V+=j}if(V>0&&(u>=h.minIterations-1||u==h.maxIterations-1)){for(var q=0,X=0;X<r;X++){C[X]=0;for(var Y=0;Y<h.minIterations;Y++)C[X]+=D[Y*r+X];(0===C[X]||C[X]===h.minIterations)&&q++}if(q===r)break}}for(var W=n3(r,s,l),H=n9(r,a,W),G={},U=0;U<W.length;U++)G[W[U]]=[];for(var K=0;K<d.length;K++){var Z=H[p[d[K].id()]];null!=Z&&G[Z].push(d[K])}for(var $=Array(W.length),Q=0;Q<W.length;Q++)$[Q]=c.collection(G[W[Q]]);return $},n8=e4({root:void 0,directed:!1}),n7=function(){var e=this,t={},n=0,r=0,i=[],a=[],o={},s=function(n,r){for(var o=a.length-1,s=[],l=e.spawn();a[o].x!=n||a[o].y!=r;)s.push(a.pop().edge),o--;s.push(a.pop().edge),s.forEach(function(n){var r=n.connectedNodes().intersection(e);l.merge(n),r.forEach(function(n){var r=n.id(),i=n.connectedEdges().intersection(e);l.merge(n),t[r].cutVertex?l.merge(i.filter(function(e){return e.isLoop()})):l.merge(i)})}),i.push(l)},l=function l(u,c,d){u===d&&(r+=1),t[c]={id:n,low:n++,cutVertex:!1};var h,p,f,g,v=e.getElementById(c).connectedEdges().intersection(e);0===v.size()?i.push(e.spawn(e.getElementById(c))):v.forEach(function(e){h=e.source().id(),p=e.target().id(),(f=h===c?p:h)!==d&&(o[g=e.id()]||(o[g]=!0,a.push({x:c,y:f,edge:e})),f in t?t[c].low=Math.min(t[c].low,t[f].id):(l(u,f,c),t[c].low=Math.min(t[c].low,t[f].low),t[c].id<=t[f].low&&(t[c].cutVertex=!0,s(c,f))))})};e.forEach(function(e){if(e.isNode()){var n=e.id();n in t||(r=0,l(n,n),t[n].cutVertex=r>1)}});var u=Object.keys(t).filter(function(e){return t[e].cutVertex}).map(function(t){return e.getElementById(t)});return{cut:e.spawn(u),components:i}},re=function(){var e=this,t={},n=0,r=[],i=[],a=e.spawn(e),o=function o(s){if(i.push(s),t[s]={index:n,low:n++,explored:!1},e.getElementById(s).connectedEdges().intersection(e).forEach(function(e){var n=e.target().id();n===s||(n in t||o(n),t[n].explored||(t[s].low=Math.min(t[s].low,t[n].low)))}),t[s].index===t[s].low){for(var l=e.spawn();;){var u=i.pop();if(l.merge(e.getElementById(u)),t[u].low=t[s].index,t[u].explored=!0,u===s)break}var c=l.edgesWith(l),d=l.merge(c);r.push(d),a=a.difference(d)}};return e.forEach(function(e){if(e.isNode()){var n=e.id();n in t||o(n)}}),{cut:a,components:r}},rt={};[ts,{dijkstra:function(e){if(!_(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var n=tu(e),r=n.root,i=n.weight,a=n.directed,o=this,s=D(r)?this.filter(r)[0]:r[0],l={},u={},c={},d=this.byGroup(),h=d.nodes,p=d.edges;p.unmergeBy(function(e){return e.isLoop()});for(var f=function(e){return l[e.id()]},g=new tl(function(e,t){return f(e)-f(t)}),v=0;v<h.length;v++){var y=h[v];l[y.id()]=y.same(s)?0:1/0,g.push(y)}for(;g.size()>0;){var b=g.pop(),x=f(b);if(c[b.id()]=x,x!==1/0)for(var w=b.neighborhood().intersect(h),E=0;E<w.length;E++){var k=w[E],C=k.id(),S=function(e,t){for(var n,r=(a?e.edgesTo(t):e.edgesWith(t)).intersect(p),o=1/0,s=0;s<r.length;s++){var l=r[s],u=i(l);(u<o||!n)&&(o=u,n=l)}return{edge:n,dist:o}}(b,k),T=x+S.dist;T<f(k)&&(l[k.id()]=T,g.updateItem(k),u[C]={node:b,edge:S.edge})}}return{distanceTo:function(e){return c[(D(e)?h.filter(e)[0]:e[0]).id()]},pathTo:function(e){var t=D(e)?h.filter(e)[0]:e[0],n=[],r=t,i=r.id();if(t.length>0)for(n.unshift(t);u[i];){var a=u[i];n.unshift(a.edge),n.unshift(a.node),i=(r=a.node).id()}return o.spawn(n)}}}},{kruskal:function(e){e=e||function(e){return 1};for(var t=this.byGroup(),n=t.nodes,r=t.edges,i=n.length,a=Array(i),o=function(e){for(var t=0;t<a.length;t++)if(a[t].has(e))return t},s=0;s<i;s++)a[s]=this.spawn(n[s]);for(var l=r.sort(function(t,n){return e(t)-e(n)}),u=0;u<l.length;u++){var c=l[u],d=c.source()[0],h=c.target()[0],p=o(d),f=o(h),g=a[p],v=a[f];p!==f&&(n.merge(c),g.merge(v),a.splice(f,1))}return n}},{aStar:function(e){var t,n,r=this.cy(),i=tc(e),a=i.root,o=i.goal,s=i.heuristic,l=i.directed,u=i.weight;a=r.collection(a)[0],o=r.collection(o)[0];var c=a.id(),d=o.id(),h={},p={},f={},g=new tl(function(e,t){return p[e.id()]-p[t.id()]}),v=new ti,y={},b={},x=function(e,t){g.push(e),v.add(t)};x(a,c),h[c]=0,p[c]=s(a);for(var w=0;g.size()>0;){if(n=(t=g.pop()).id(),v.delete(n),w++,n===d){for(var E=[],k=o,C=d,S=b[C];E.unshift(k),null!=S&&E.unshift(S),null!=(k=y[C]);)S=b[C=k.id()];return{found:!0,distance:h[n],path:this.spawn(E),steps:w}}f[n]=!0;for(var D=t._private.edges,T=0;T<D.length;T++){var P=D[T];if(this.hasElementWithId(P.id())&&(!l||P.data("source")===n)){var _=P.source(),M=P.target(),B=_.id()!==n?_:M,N=B.id();if(this.hasElementWithId(N)&&!f[N]){var A=h[n]+u(P);if(!v.has(N)){h[N]=A,p[N]=A+s(B),x(B,N),y[N]=t,b[N]=P;continue}A<h[N]&&(h[N]=A,p[N]=A+s(B),y[N]=t,b[N]=P)}}}}return{found:!1,distance:void 0,path:void 0,steps:w}}},{floydWarshall:function(e){for(var t=this.cy(),n=td(e),r=n.weight,i=n.directed,a=this.byGroup(),o=a.nodes,s=a.edges,l=o.length,u=l*l,c=function(e){return o.indexOf(e)},d=function(e){return o[e]},h=Array(u),p=0;p<u;p++){var f=p%l;(p-f)/l===f?h[p]=0:h[p]=1/0}for(var g=Array(u),v=Array(u),y=0;y<s.length;y++){var b=s[y],x=b.source()[0],w=b.target()[0];if(x!==w){var E=c(x),k=c(w),C=E*l+k,S=r(b);if(h[C]>S&&(h[C]=S,g[C]=k,v[C]=b),!i){var T=k*l+E;!i&&h[T]>S&&(h[T]=S,g[T]=E,v[T]=b)}}}for(var P=0;P<l;P++)for(var _=0;_<l;_++)for(var M=_*l+P,B=0;B<l;B++){var N=_*l+B,A=P*l+B;h[M]+h[A]<h[N]&&(h[N]=h[M]+h[A],g[N]=g[M])}var I=function(e){return c((D(e)?t.filter(e):e)[0])};return{distance:function(e,t){return h[I(e)*l+I(t)]},path:function(e,n){var r,i=I(e),a=I(n),o=d(i);if(i===a)return o.collection();if(null==g[i*l+a])return t.collection();var s=t.collection(),u=i;for(s.merge(o);i!==a;)u=i,i=g[i*l+a],r=v[u*l+i],s.merge(r),s.merge(d(i));return s}}}},{bellmanFord:function(e){var t=this,n=th(e),r=n.weight,i=n.directed,a=n.root,o=this,s=this.cy(),l=this.byGroup(),u=l.edges,c=l.nodes,d=c.length,h=new tn,p=!1,f=[];a=s.collection(a)[0],u.unmergeBy(function(e){return e.isLoop()});for(var g=u.length,v=function(e){var t=h.get(e.id());return t||(t={},h.set(e.id(),t)),t},y=function(e){return(D(e)?s.$(e):e)[0]},b=0;b<d;b++){var x=c[b],w=v(x);x.same(a)?w.dist=0:w.dist=1/0,w.pred=null,w.edge=null}for(var E=!1,k=function(e,t,n,r,i,a){var o=r.dist+a;o<i.dist&&!n.same(r.edge)&&(i.dist=o,i.pred=e,i.edge=n,E=!0)},C=1;C<d;C++){E=!1;for(var S=0;S<g;S++){var T=u[S],P=T.source(),_=T.target(),M=r(T),B=v(P),N=v(_);k(P,_,T,B,N,M),i||k(_,P,T,N,B,M)}if(!E)break}if(E)for(var A=[],I=0;I<g;I++){var O=u[I],R=O.source(),z=O.target(),L=r(O),V=v(R).dist,F=v(z).dist;if(V+L<F||!i&&F+L<V){if(p||(e0("Graph contains a negative weight cycle for Bellman-Ford"),p=!0),!1!==e.findNegativeWeightCycles){var j=[];V+L<F&&j.push(R),!i&&F+L<V&&j.push(z);for(var q=j.length,X=0;X<q;X++){var Y=j[X],W=[Y];W.push(v(Y).edge);for(var H=v(Y).pred;-1===W.indexOf(H);)W.push(H),W.push(v(H).edge),H=v(H).pred;for(var G=(W=W.slice(W.indexOf(H)))[0].id(),U=0,K=2;K<W.length;K+=2)W[K].id()<G&&(G=W[K].id(),U=K);(W=W.slice(U).concat(W.slice(0,U))).push(W[0]);var Z=W.map(function(e){return e.id()}).join(",");-1===A.indexOf(Z)&&(f.push(o.spawn(W)),A.push(Z))}}else break}}return{distanceTo:function(e){return v(y(e)).dist},pathTo:function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=y(e),i=[],s=r;;){if(null==s)return t.spawn();var l=v(s),u=l.edge,c=l.pred;if(i.unshift(s[0]),s.same(n)&&i.length>0)break;null!=u&&i.unshift(u),s=c}return o.spawn(i)},hasNegativeWeightCycle:p,negativeWeightCycles:f}}},{kargerStein:function(){var e=this,t=this.byGroup(),n=t.nodes,r=t.edges;r.unmergeBy(function(e){return e.isLoop()});var i=n.length,a=r.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),s=Math.floor(i/tp);if(i<2){eQ("At least 2 nodes are required for Karger-Stein algorithm");return}for(var l=[],u=0;u<a;u++){var c=r[u];l.push([u,n.indexOf(c.source()),n.indexOf(c.target())])}for(var d=1/0,h=[],p=Array(i),f=Array(i),g=Array(i),v=function(e,t){for(var n=0;n<i;n++)t[n]=e[n]},y=0;y<=o;y++){for(var b=0;b<i;b++)f[b]=b;var x=tg(f,l.slice(),i,s),w=x.slice();v(f,g);var E=tg(f,x,s,2),k=tg(g,w,s,2);E.length<=k.length&&E.length<d?(d=E.length,h=E,v(f,p)):k.length<=E.length&&k.length<d&&(d=k.length,h=k,v(g,p))}for(var C=this.spawn(h.map(function(e){return r[e[0]]})),S=this.spawn(),D=this.spawn(),T=p[0],P=0;P<p.length;P++){var _=p[P],M=n[P];_===T?S.merge(M):D.merge(M)}var B=function(t){var n=e.spawn();return t.forEach(function(t){n.merge(t),t.connectedEdges().forEach(function(t){e.contains(t)&&!C.contains(t)&&n.merge(t)})}),n};return{cut:C,components:[B(S),B(D)],partition1:S,partition2:D}}},{pageRank:function(e){for(var t,n=na(e),r=n.dampingFactor,i=n.precision,a=n.iterations,o=n.weight,s=this._private.cy,l=this.byGroup(),u=l.nodes,c=l.edges,d=u.length,h=c.length,p=Array(d*d),f=Array(d),g=(1-r)/d,v=0;v<d;v++){for(var y=0;y<d;y++)p[v*d+y]=0;f[v]=0}for(var b=0;b<h;b++){var x=c[b],w=x.data("source"),E=x.data("target");if(w!==E){var k=u.indexOfId(w),C=u.indexOfId(E),S=o(x),D=C*d+k;p[D]+=S,f[k]+=S}}for(var T=1/d+g,P=0;P<d;P++)if(0===f[P])for(var _=0;_<d;_++)p[_*d+P]=T;else for(var M=0;M<d;M++){var B=M*d+P;p[B]=p[B]/f[P]+g}for(var N=Array(d),A=Array(d),I=0;I<d;I++)N[I]=1;for(var O=0;O<a;O++){for(var R=0;R<d;R++)A[R]=0;for(var z=0;z<d;z++)for(var L=0;L<d;L++){var V=z*d+L;A[z]+=p[V]*N[L]}tP(A),t=N,N=A,A=t;for(var F=0,j=0;j<d;j++){var q=t[j]-N[j];F+=q*q}if(F<i)break}return{rank:function(e){return e=s.collection(e)[0],N[u.indexOf(e)]}}}},ns,nu,nd,{markovClustering:nk,mcl:nk},{kMeans:function(e){var t,n=this.cy(),i=this.nodes(),a=null,o=nI(e),s=Array(o.k),l={};o.testMode?"number"==typeof o.testCentroids?(o.testCentroids,t=nR(i,o.k,o.attributes)):t="object"===r(o.testCentroids)?o.testCentroids:nR(i,o.k,o.attributes):t=nR(i,o.k,o.attributes);for(var u=!0,c=0;u&&c<o.maxIterations;){for(var d=0;d<i.length;d++)l[(a=i[d]).id()]=nz(a,t,o.distance,o.attributes,"kMeans");u=!1;for(var h=0;h<o.k;h++){var p=nL(h,i,l);if(0!==p.length){for(var f=o.attributes.length,g=t[h],v=Array(f),y=Array(f),b=0;b<f;b++){y[b]=0;for(var x,w=0;w<p.length;w++)a=p[w],y[b]+=o.attributes[b](a);v[b]=y[b]/p.length,x=v[b],Math.abs(g[b]-x)<=o.sensitivityThreshold||(u=!0)}t[h]=v,s[h]=n.collection(p)}}c++}return s},kMedoids:function(e){var t,n,i=this.cy(),a=this.nodes(),o=null,s=nI(e),l=Array(s.k),u={},c=Array(s.k);s.testMode?"number"==typeof s.testCentroids||(t="object"===r(s.testCentroids)?s.testCentroids:nj(a,s.k)):t=nj(a,s.k);for(var d=!0,h=0;d&&h<s.maxIterations;){for(var p=0;p<a.length;p++)u[(o=a[p]).id()]=nz(o,t,s.distance,s.attributes,"kMedoids");d=!1;for(var f=0;f<t.length;f++){var g=nL(f,a,u);if(0!==g.length){c[f]=nq(t[f],g,s.attributes);for(var v=0;v<g.length;v++)(n=nq(g[v],g,s.attributes))<c[f]&&(c[f]=n,t[f]=g[v],d=!0);l[f]=i.collection(g)}}h++}return l},fuzzyCMeans:nH,fcm:nH},{hierarchicalClustering:n0,hca:n0},{affinityPropagation:n6,ap:n6},{hierholzer:function(e){if(!_(e)){var t,n,r,i=arguments;e={root:i[0],directed:i[1]}}var a=n8(e),o=a.root,s=a.directed,l=!1;o&&(r=D(o)?this.filter(o)[0].id():o[0].id());var u={},c={};s?this.forEach(function(e){var r=e.id();if(e.isNode()){var i=e.indegree(!0),a=e.outdegree(!0),o=i-a,s=a-i;1==o?t?l=!0:t=r:1==s?n?l=!0:n=r:(s>1||o>1)&&(l=!0),u[r]=[],e.outgoers().forEach(function(e){e.isEdge()&&u[r].push(e.id())})}else c[r]=[void 0,e.target().id()]}):this.forEach(function(e){var r=e.id();e.isNode()?(e.degree(!0)%2&&(t?n?l=!0:n=r:t=r),u[r]=[],e.connectedEdges().forEach(function(e){return u[r].push(e.id())})):c[r]=[e.source().id(),e.target().id()]});var d={found:!1,trail:void 0};if(l)return d;if(n&&t){if(s){if(r&&n!=r)return d;r=n}else{if(r&&n!=r&&t!=r)return d;r||(r=n)}}else r||(r=this[0].id());var h=function(e){for(var t,n,r,i=e,a=[e];u[i].length;)n=c[t=u[i].shift()][0],i!=(r=c[t][1])?(u[r]=u[r].filter(function(e){return e!=t}),i=r):s||i==n||(u[n]=u[n].filter(function(e){return e!=t}),i=n),a.unshift(t),a.unshift(i);return a},p=[],f=[];for(f=h(r);1!=f.length;)0==u[f[0]].length?(p.unshift(this.getElementById(f.shift())),p.unshift(this.getElementById(f.shift()))):f=h(f.shift()).concat(f);for(var g in p.unshift(this.getElementById(f.shift())),u)if(u[g].length)return d;return d.found=!0,d.trail=this.spawn(p,!0),d}},{hopcroftTarjanBiconnected:n7,htbc:n7,htb:n7,hopcroftTarjanBiconnectedComponents:n7},{tarjanStronglyConnected:re,tsc:re,tscc:re,tarjanStronglyConnectedComponents:re}].forEach(function(e){Z(rt,e)});var rn=function e(t){if(!(this instanceof e))return new e(t);this.id="Thenable/1.0.7",this.state=0,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},"function"==typeof t&&t.call(this,this.fulfill.bind(this),this.reject.bind(this))};rn.prototype={fulfill:function(e){return rr(this,1,"fulfillValue",e)},reject:function(e){return rr(this,2,"rejectReason",e)},then:function(e,t){var n=new rn;return this.onFulfilled.push(ro(e,n,"fulfill")),this.onRejected.push(ro(t,n,"reject")),ri(this),n.proxy}};var rr=function(e,t,n,r){return 0===e.state&&(e.state=t,e[n]=r,ri(e)),e},ri=function(e){1===e.state?ra(e,"onFulfilled",e.fulfillValue):2===e.state&&ra(e,"onRejected",e.rejectReason)},ra=function(e,t,n){if(0!==e[t].length){var r=e[t];e[t]=[];var i=function(){for(var e=0;e<r.length;e++)r[e](n)};"function"==typeof setImmediate?setImmediate(i):setTimeout(i,0)}},ro=function(e,t,n){return function(r){if("function"!=typeof e)t[n].call(t,r);else{var i;try{i=e(r)}catch(e){t.reject(e);return}rs(t,i)}}},rs=function e(t,n){if(t===n||t.proxy===n){t.reject(TypeError("cannot resolve promise with itself"));return}if("object"===r(n)&&null!==n||"function"==typeof n)try{i=n.then}catch(e){t.reject(e);return}if("function"==typeof i){var i,a=!1;try{i.call(n,function(r){a||(a=!0,r===n?t.reject(TypeError("circular thenable chain")):e(t,r))},function(e){a||(a=!0,t.reject(e))})}catch(e){a||t.reject(e)}return}t.fulfill(n)};rn.all=function(e){return new rn(function(t,n){for(var r=Array(e.length),i=0,a=function(n,a){r[n]=a,++i===e.length&&t(r)},o=0;o<e.length;o++)!function(t){var r=e[t];null!=r&&null!=r.then?r.then(function(e){a(t,e)},function(e){n(e)}):a(t,r)}(o)})},rn.resolve=function(e){return new rn(function(t,n){t(e)})},rn.reject=function(e){return new rn(function(t,n){n(e)})};var rl="undefined"!=typeof Promise?Promise:rn,ru=function(e,t,n){var r=O(e),i=this._private=Z({duration:1e3},t,n);if(i.target=e,i.style=i.style||i.css,i.started=!1,i.playing=!1,i.hooked=!1,i.applying=!1,i.progress=0,i.completes=[],i.frames=[],i.complete&&T(i.complete)&&i.completes.push(i.complete),!r){var a=e.position();i.startPosition=i.startPosition||{x:a.x,y:a.y},i.startStyle=i.startStyle||e.cy().style().getAnimationStartStyle(e,i.style)}if(r){var o=e.pan();i.startPan={x:o.x,y:o.y},i.startZoom=e.zoom()}this.length=1,this[0]=this},rc=ru.prototype;Z(rc,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t=e.target._private.animation;(e.queue?t.queue:t.current).push(this),N(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return 1===e.progress&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return void 0===e?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,n=t.playing;return void 0===e?t.progress:(n&&this.pause(),t.progress=e,t.started=!1,n&&this.play(),this)},completed:function(){return 1===this._private.progress},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var n=function(t,n){var r=e[t];null!=r&&(e[t]=e[n],e[n]=r)};if(n("zoom","startZoom"),n("pan","startPan"),n("position","startPosition"),e.style)for(var r=0;r<e.style.length;r++){var i=e.style[r],a=i.name,o=e.startStyle[a];e.startStyle[a]=i,e.style[r]=o}return t&&this.play(),this},promise:function(e){var t,n=this._private;return t="frame"===e?n.frames:n.completes,new rl(function(e,n){t.push(function(){e()})})}}),rc.complete=rc.completed,rc.run=rc.play,rc.running=rc.playing;var rd=Array.isArray,rh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,rp=/^\w*$/,rf=function(e,t){if(rd(e))return!1;var n=typeof e;return!!("number"==n||"symbol"==n||"boolean"==n||null==e||ew(e))||rp.test(e)||!rh.test(e)||null!=t&&e in Object(t)},rg=function(e){if(!er(e))return!1;var t=ex(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},rv=es["__core-js_shared__"],ry=(oY=/[^.]+$/.exec(rv&&rv.keys&&rv.keys.IE_PROTO||""))?"Symbol(src)_1."+oY:"",rm=Function.prototype.toString,rb=function(e){if(null!=e){try{return rm.call(e)}catch(e){}try{return e+""}catch(e){}}return""},rx=/^\[object .+?Constructor\]$/,rw=Object.prototype,rE=Function.prototype.toString,rk=rw.hasOwnProperty,rC=RegExp("^"+rE.call(rk).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),rS=function(e,t){var n,r=null==e?void 0:e[t];return er(n=r)&&(!ry||!(ry in n))&&(rg(n)?rC:rx).test(rb(n))?r:void 0},rD=rS(Object,"create"),rT=Object.prototype.hasOwnProperty,rP=Object.prototype.hasOwnProperty;function r_(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}r_.prototype.clear=function(){this.__data__=rD?rD(null):{},this.size=0},r_.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},r_.prototype.get=function(e){var t=this.__data__;if(rD){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return rT.call(t,e)?t[e]:void 0},r_.prototype.has=function(e){var t=this.__data__;return rD?void 0!==t[e]:rP.call(t,e)},r_.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=rD&&void 0===t?"__lodash_hash_undefined__":t,this};var rM=function(e,t){return e===t||e!=e&&t!=t},rB=function(e,t){for(var n=e.length;n--;)if(rM(e[n][0],t))return n;return -1},rN=Array.prototype.splice;function rA(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}rA.prototype.clear=function(){this.__data__=[],this.size=0},rA.prototype.delete=function(e){var t=this.__data__,n=rB(t,e);return!(n<0)&&(n==t.length-1?t.pop():rN.call(t,n,1),--this.size,!0)},rA.prototype.get=function(e){var t=this.__data__,n=rB(t,e);return n<0?void 0:t[n][1]},rA.prototype.has=function(e){return rB(this.__data__,e)>-1},rA.prototype.set=function(e,t){var n=this.__data__,r=rB(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};var rI=rS(es,"Map"),rO=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e},rR=function(e,t){var n=e.__data__;return rO(t)?n["string"==typeof t?"string":"hash"]:n.map};function rz(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function rL(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o)||a,o};return n.cache=new(rL.Cache||rz),n}rz.prototype.clear=function(){this.size=0,this.__data__={hash:new r_,map:new(rI||rA),string:new r_}},rz.prototype.delete=function(e){var t=rR(this,e).delete(e);return this.size-=t?1:0,t},rz.prototype.get=function(e){return rR(this,e).get(e)},rz.prototype.has=function(e){return rR(this,e).has(e)},rz.prototype.set=function(e,t){var n=rR(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},rL.Cache=rz;var rV=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,rF=/\\(\\)?/g,rj=(oH=(oW=rL(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(rV,function(e,n,r,i){t.push(r?i.replace(rF,"$1"):n||e)}),t},function(e){return 500===oH.size&&oH.clear(),e})).cache,oW),rq=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i},rX=1/0,rY=eh?eh.prototype:void 0,rW=rY?rY.toString:void 0,rH=function e(t){if("string"==typeof t)return t;if(rd(t))return rq(t,e)+"";if(ew(t))return rW?rW.call(t):"";var n=t+"";return"0"==n&&1/t==-rX?"-0":n},rG=function(e){return null==e?"":rH(e)},rU=function(e,t){return rd(e)?e:rf(e,t)?[e]:rj(rG(e))},rK=1/0,rZ=function(e){if("string"==typeof e||ew(e))return e;var t=e+"";return"0"==t&&1/e==-rK?"-0":t},r$=function(e,t){t=rU(t,e);for(var n=0,r=t.length;null!=e&&n<r;)e=e[rZ(t[n++])];return n&&n==r?e:void 0},rQ=function(e,t,n){var r=null==e?void 0:r$(e,t);return void 0===r?n:r},rJ=function(){try{var e=rS(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),r0=function(e,t,n){"__proto__"==t&&rJ?rJ(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},r1=Object.prototype.hasOwnProperty,r2=function(e,t,n){var r=e[t];r1.call(e,t)&&rM(r,n)&&(void 0!==n||t in e)||r0(e,t,n)},r5=/^(?:0|[1-9]\d*)$/,r3=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&r5.test(e))&&e>-1&&e%1==0&&e<t},r4=function(e,t,n,r){if(!er(e))return e;t=rU(t,e);for(var i=-1,a=t.length,o=a-1,s=e;null!=s&&++i<a;){var l=rZ(t[i]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)break;if(i!=o){var c=s[l];void 0===(u=r?r(c,l,s):void 0)&&(u=er(c)?c:r3(t[i+1])?[]:{})}r2(s,l,u),s=s[l]}return e},r9=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t},r6={};[{animated:function(){return function(){var e=void 0!==this.length;if(!(this._private.cy||this).styleEnabled())return!1;var t=(e?this:[this])[0];if(t)return t._private.animation.current.length>0}},clearQueue:function(){return function(){var e=void 0!==this.length?this:[this];if(!(this._private.cy||this).styleEnabled())return this;for(var t=0;t<e.length;t++)e[t]._private.animation.queue=[];return this}},delay:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animate({delay:e,duration:e,complete:t}):this}},delayAnimation:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animation({delay:e,duration:e,complete:t}):this}},animation:function(){return function(e,t){var n=void 0!==this.length,r=n?this:[this],i=this._private.cy||this,a=!n,o=!a;if(!i.styleEnabled())return this;var s=i.style();if(0===Object.keys(e=Z({},e,t)).length)return new ru(r[0],e);switch(void 0===e.duration&&(e.duration=400),e.duration){case"slow":e.duration=600;break;case"fast":e.duration=200}if(o&&(e.style=s.getPropsList(e.style||e.css),e.css=void 0),o&&null!=e.renderedPosition){var l=e.renderedPosition,u=i.pan(),c=i.zoom();e.position=ty(l,c,u)}if(a&&null!=e.panBy){var d=e.panBy,h=i.pan();e.pan={x:h.x+d.x,y:h.y+d.y}}var p=e.center||e.centre;if(a&&null!=p){var f=i.getCenterPan(p.eles,e.zoom);null!=f&&(e.pan=f)}if(a&&null!=e.fit){var g=e.fit,v=i.getFitViewport(g.eles||g.boundingBox,g.padding);null!=v&&(e.pan=v.pan,e.zoom=v.zoom)}if(a&&_(e.zoom)){var y=i.getZoomedViewport(e.zoom);null!=y?(y.zoomed&&(e.zoom=y.zoom),y.panned&&(e.pan=y.pan)):e.zoom=null}return new ru(r[0],e)}},animate:function(){return function(e,t){var n=void 0!==this.length?this:[this];if(!(this._private.cy||this).styleEnabled())return this;t&&(e=Z({},e,t));for(var r=0;r<n.length;r++){var i=n[r],a=i.animated()&&(void 0===e.queue||e.queue);i.animation(e,a?{queue:!0}:void 0).play()}return this}},stop:function(){return function(e,t){var n=void 0!==this.length?this:[this],r=this._private.cy||this;if(!r.styleEnabled())return this;for(var i=0;i<n.length;i++){for(var a=n[i]._private,o=a.animation.current,s=0;s<o.length;s++){var l=o[s]._private;t&&(l.duration=0)}e&&(a.animation.queue=[]),t||(a.animation.current=[])}return r.notify("draw"),this}}},{data:function(e){return e=Z({},{field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(e){},beforeSet:function(e,t){},onSet:function(e){},canSet:function(e){return!0}},e),function(t,n){var r,i=e,a=void 0!==this.length,o=a?this:[this],l=a?this[0]:this;if(D(t)){var u=-1!==t.indexOf(".")&&(rd(t)?rq(t,rZ):ew(t)?[t]:r9(rj(rG(t))));if(i.allowGetting&&void 0===n)return l&&(i.beforeGet(l),p=u&&void 0===l._private[i.field][t]?rQ(l._private[i.field],u):l._private[i.field][t]),p;if(i.allowSetting&&void 0!==n&&!i.immutableKeys[t]){var c=s({},t,n);i.beforeSet(this,c);for(var d=0,h=o.length;d<h;d++){var p,f,g=o[d];i.canSet(g)&&(u&&void 0===l._private[i.field][t]?null==(f=g._private[i.field])||r4(f,u,n):g._private[i.field][t]=n)}i.updateStyle&&this.updateStyle(),i.onSet(this),i.settingTriggersEvent&&this[i.triggerFnName](i.settingEvent)}}else if(i.allowSetting&&_(t)){var v,y,b=Object.keys(t);i.beforeSet(this,t);for(var x=0;x<b.length;x++)if(y=t[v=b[x]],!i.immutableKeys[v])for(var w=0;w<o.length;w++){var E=o[w];i.canSet(E)&&(E._private[i.field][v]=y)}i.updateStyle&&this.updateStyle(),i.onSet(this),i.settingTriggersEvent&&this[i.triggerFnName](i.settingEvent)}else if(i.allowBinding&&T(t))this.on(i.bindingEvent,t);else if(i.allowGetting&&void 0===t)return l&&(i.beforeGet(l),r=l._private[i.field]),r;return this}},removeData:function(e){return e=Z({},{field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}},e),function(t){var n=e,r=void 0!==this.length?this:[this];if(D(t)){for(var i=t.split(/\s+/),a=i.length,o=0;o<a;o++){var s=i[o];if(!z(s)&&!n.immutableKeys[s])for(var l=0,u=r.length;l<u;l++)r[l]._private[n.field][s]=void 0}n.triggerEvent&&this[n.triggerFnName](n.event)}else if(void 0===t){for(var c=0,d=r.length;c<d;c++)for(var h=r[c]._private[n.field],p=Object.keys(h),f=0;f<p.length;f++){var g=p[f];n.immutableKeys[g]||(h[g]=void 0)}n.triggerEvent&&this[n.triggerFnName](n.event)}return this}}},{eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(e,t){var n=this,r=Array.prototype.slice.call(arguments,0);return new rl(function(e,t){var i=r.concat([function(t){n.off.apply(n,a),e(t)}]),a=i.concat([]);n.on.apply(n,i)})}}}].forEach(function(e){Z(r6,e)});var r8={animate:r6.animate(),animation:r6.animation(),animated:r6.animated(),clearQueue:r6.clearQueue(),delay:r6.delay(),delayAnimation:r6.delayAnimation(),stop:r6.stop()},r7={classes:function(e){if(void 0===e){var t=[];return this[0]._private.classes.forEach(function(e){return t.push(e)}),t}P(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],r=new ti(e),i=0;i<this.length;i++){for(var a=this[i],o=a._private,s=o.classes,l=!1,u=0;u<e.length;u++){var c=e[u];if(!s.has(c)){l=!0;break}}l||(l=s.size!==e.length),l&&(o.classes=r,n.push(a))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),this},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return null!=t&&t._private.classes.has(e)},toggleClass:function(e,t){P(e)||(e=e.match(/\S+/g)||[]);for(var n=void 0===t,r=[],i=0,a=this.length;i<a;i++)for(var o=this[i],s=o._private.classes,l=!1,u=0;u<e.length;u++){var c=e[u],d=s.has(c),h=!1;t||n&&!d?(s.add(c),h=!0):(!t||n&&d)&&(s.delete(c),h=!0),!l&&h&&(r.push(o),l=!0)}return r.length>0&&this.spawn(r).updateStyle().emit("class"),this},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var n=this;if(null==t)t=250;else if(0===t)return n;return n.addClass(e),setTimeout(function(){n.removeClass(e)},t),n}};r7.className=r7.classNames=r7.classes;var ie={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:"\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'",number:Y,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};ie.variable="(?:[\\w-.]|(?:\\\\"+ie.metaChar+"))+",ie.className="(?:[\\w-]|(?:\\\\"+ie.metaChar+"))+",ie.value=ie.string+"|"+ie.number,ie.id=ie.variable,function(){var e,t,n;for(n=0,e=ie.comparatorOp.split("|");n<e.length;n++)t=e[n],ie.comparatorOp+="|@"+t;for(n=0,e=ie.comparatorOp.split("|");n<e.length;n++)(t=e[n]).indexOf("!")>=0||"="===t||(ie.comparatorOp+="|\\!"+t)}();var it=function(){return{checks:[]}},ir={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},ii=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(e,t){return -1*K(e.selector,t.selector)}),ia=function(){for(var e,t={},n=0;n<ii.length;n++)t[(e=ii[n]).selector]=e.matches;return t}(),io="("+ii.map(function(e){return e.selector}).join("|")+")",is=function(e){return e.replace(RegExp("\\\\("+ie.metaChar+")","g"),function(e,t){return t})},il=function(e,t,n){e[e.length-1]=n},iu=[{name:"group",query:!0,regex:"("+ie.group+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:ir.GROUP,value:"*"===r?r:r+"s"})}},{name:"state",query:!0,regex:io,populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:ir.STATE,value:r})}},{name:"id",query:!0,regex:"\\#("+ie.id+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:ir.ID,value:is(r)})}},{name:"className",query:!0,regex:"\\.("+ie.className+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:ir.CLASS,value:is(r)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+ie.variable+")\\s*\\]",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:ir.DATA_EXIST,field:is(r)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+ie.variable+")\\s*("+ie.comparatorOp+")\\s*("+ie.value+")\\s*\\]",populate:function(e,t,n){var r=l(n,3),i=r[0],a=r[1],o=r[2];o=null!=RegExp("^"+ie.string+"$").exec(o)?o.substring(1,o.length-1):parseFloat(o),t.checks.push({type:ir.DATA_COMPARE,field:is(i),operator:a,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+ie.boolOp+")\\s*("+ie.variable+")\\s*\\]",populate:function(e,t,n){var r=l(n,2),i=r[0],a=r[1];t.checks.push({type:ir.DATA_BOOL,field:is(a),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+ie.meta+")\\s*("+ie.comparatorOp+")\\s*("+ie.number+")\\s*\\]\\]",populate:function(e,t,n){var r=l(n,3),i=r[0],a=r[1],o=r[2];t.checks.push({type:ir.META_COMPARE,field:is(i),operator:a,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:ie.separator,populate:function(e,t){var n=e.currentSubject,r=e.edgeCount,i=e.compoundCount,a=e[e.length-1];return null!=n&&(a.subject=n,e.currentSubject=null),a.edgeCount=r,a.compoundCount=i,e.edgeCount=0,e.compoundCount=0,e[e.length++]=it()}},{name:"directedEdge",separator:!0,regex:ie.directedEdge,populate:function(e,t){if(null==e.currentSubject){var n=it(),r=it();return n.checks.push({type:ir.DIRECTED_EDGE,source:t,target:r}),il(e,t,n),e.edgeCount++,r}var i=it(),a=it();return i.checks.push({type:ir.NODE_SOURCE,source:t,target:a}),il(e,t,i),e.edgeCount++,a}},{name:"undirectedEdge",separator:!0,regex:ie.undirectedEdge,populate:function(e,t){if(null==e.currentSubject){var n=it(),r=it();return n.checks.push({type:ir.UNDIRECTED_EDGE,nodes:[t,r]}),il(e,t,n),e.edgeCount++,r}var i=it(),a=it();return i.checks.push({type:ir.NODE_NEIGHBOR,node:t,neighbor:a}),il(e,t,i),a}},{name:"child",separator:!0,regex:ie.child,populate:function(e,t){if(null==e.currentSubject){var n=it(),r=it(),i=e[e.length-1];return n.checks.push({type:ir.CHILD,parent:i,child:r}),il(e,t,n),e.compoundCount++,r}if(e.currentSubject===t){var a=it(),o=e[e.length-1],s=it(),l=it(),u=it(),c=it();return a.checks.push({type:ir.COMPOUND_SPLIT,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:ir.TRUE}],c.checks.push({type:ir.TRUE}),s.checks.push({type:ir.PARENT,parent:c,child:u}),il(e,o,a),e.currentSubject=l,e.compoundCount++,u}var d=it(),h=it(),p=[{type:ir.PARENT,parent:d,child:h}];return d.checks=t.checks,t.checks=p,e.compoundCount++,h}},{name:"descendant",separator:!0,regex:ie.descendant,populate:function(e,t){if(null==e.currentSubject){var n=it(),r=it(),i=e[e.length-1];return n.checks.push({type:ir.DESCENDANT,ancestor:i,descendant:r}),il(e,t,n),e.compoundCount++,r}if(e.currentSubject===t){var a=it(),o=e[e.length-1],s=it(),l=it(),u=it(),c=it();return a.checks.push({type:ir.COMPOUND_SPLIT,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:ir.TRUE}],c.checks.push({type:ir.TRUE}),s.checks.push({type:ir.ANCESTOR,ancestor:c,descendant:u}),il(e,o,a),e.currentSubject=l,e.compoundCount++,u}var d=it(),h=it(),p=[{type:ir.ANCESTOR,ancestor:d,descendant:h}];return d.checks=t.checks,t.checks=p,e.compoundCount++,h}},{name:"subject",modifier:!0,regex:ie.subject,populate:function(e,t){if(null!=e.currentSubject&&e.currentSubject!==t)return e0("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var n=e[e.length-1].checks[0],r=null==n?null:n.type;r===ir.DIRECTED_EDGE?n.type=ir.NODE_TARGET:r===ir.UNDIRECTED_EDGE&&(n.type=ir.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];iu.forEach(function(e){return e.regexObj=RegExp("^"+e.regex)});var ic=function(e){for(var t,n,r,i=0;i<iu.length;i++){var a=iu[i],o=a.name,s=e.match(a.regexObj);if(null!=s){n=s,t=a,r=o;var l=s[0];e=e.substring(l.length);break}}return{expr:t,match:n,name:r,remaining:e}},id=function(e){var t=e.match(/^\s+/);if(t){var n=t[0];e=e.substring(n.length)}return e},ih=function(e,t,n){var r,i,a,o=D(e),s=M(e),l=D(n),u=!1,c=!1,d=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),c=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),u=!0),(o||l||u)&&(i=o||s?""+e:"",a=""+n),u&&(e=i=i.toLowerCase(),n=a=a.toLowerCase()),t){case"*=":r=i.indexOf(a)>=0;break;case"$=":r=i.indexOf(a,i.length-a.length)>=0;break;case"^=":r=0===i.indexOf(a);break;case"=":r=e===n;break;case">":d=!0,r=e>n;break;case">=":d=!0,r=e>=n;break;case"<":d=!0,r=e<n;break;case"<=":d=!0,r=e<=n;break;default:r=!1}return c&&(null!=e||!d)&&(r=!r),r},ip=function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return void 0===e}},ig=function(e,t){return e.data(t)},iv=[],iy=function(e,t){return e.checks.every(function(e){return iv[e.type](e,t)})};iv[ir.GROUP]=function(e,t){var n=e.value;return"*"===n||n===t.group()},iv[ir.STATE]=function(e,t){return ia[e.value](t)},iv[ir.ID]=function(e,t){var n=e.value;return t.id()===n},iv[ir.CLASS]=function(e,t){var n=e.value;return t.hasClass(n)},iv[ir.META_COMPARE]=function(e,t){var n=e.field,r=e.operator,i=e.value;return ih(t[n](),r,i)},iv[ir.DATA_COMPARE]=function(e,t){var n=e.field,r=e.operator,i=e.value;return ih(ig(t,n),r,i)},iv[ir.DATA_BOOL]=function(e,t){var n=e.field,r=e.operator;return ip(ig(t,n),r)},iv[ir.DATA_EXIST]=function(e,t){var n=e.field;return e.operator,void 0!==ig(t,n)},iv[ir.UNDIRECTED_EDGE]=function(e,t){var n=e.nodes[0],r=e.nodes[1],i=t.source(),a=t.target();return iy(n,i)&&iy(r,a)||iy(r,i)&&iy(n,a)},iv[ir.NODE_NEIGHBOR]=function(e,t){return iy(e.node,t)&&t.neighborhood().some(function(t){return t.isNode()&&iy(e.neighbor,t)})},iv[ir.DIRECTED_EDGE]=function(e,t){return iy(e.source,t.source())&&iy(e.target,t.target())},iv[ir.NODE_SOURCE]=function(e,t){return iy(e.source,t)&&t.outgoers().some(function(t){return t.isNode()&&iy(e.target,t)})},iv[ir.NODE_TARGET]=function(e,t){return iy(e.target,t)&&t.incomers().some(function(t){return t.isNode()&&iy(e.source,t)})},iv[ir.CHILD]=function(e,t){return iy(e.child,t)&&iy(e.parent,t.parent())},iv[ir.PARENT]=function(e,t){return iy(e.parent,t)&&t.children().some(function(t){return iy(e.child,t)})},iv[ir.DESCENDANT]=function(e,t){return iy(e.descendant,t)&&t.ancestors().some(function(t){return iy(e.ancestor,t)})},iv[ir.ANCESTOR]=function(e,t){return iy(e.ancestor,t)&&t.descendants().some(function(t){return iy(e.descendant,t)})},iv[ir.COMPOUND_SPLIT]=function(e,t){return iy(e.subject,t)&&iy(e.left,t)&&iy(e.right,t)},iv[ir.TRUE]=function(){return!0},iv[ir.COLLECTION]=function(e,t){return e.value.has(t)},iv[ir.FILTER]=function(e,t){return(0,e.value)(t)};var im=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,null==e||D(e)&&e.match(/^\s*$/)||(N(e)?this.addQuery({checks:[{type:ir.COLLECTION,value:e.collection()}]}):T(e)?this.addQuery({checks:[{type:ir.FILTER,value:e}]}):D(e)?this.parse(e)||(this.invalid=!0):eQ("A selector must be created from a string; found "))},ib=im.prototype;[{parse:function(e){var t=this,n=t.inputText=e,r=t[0]=it();for(t.length=1,n=id(n);;){var i=ic(n);if(null==i.expr)return e0("The selector `"+e+"`is invalid"),!1;var a=i.match.slice(1),o=i.expr.populate(t,r,a);if(!1===o)return!1;if(null!=o&&(r=o),(n=i.remaining).match(/^\s*$/))break}var s=t[t.length-1];null!=t.currentSubject&&(s.subject=t.currentSubject),s.edgeCount=t.edgeCount,s.compoundCount=t.compoundCount;for(var l=0;l<t.length;l++){var u=t[l];if(u.compoundCount>0&&u.edgeCount>0)return e0("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(u.edgeCount>1)return e0("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;1===u.edgeCount&&e0("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},toString:function(){if(null!=this.toStringCache)return this.toStringCache;for(var e=function(e){return null==e?"":e},t=function(t){return D(t)?'"'+t+'"':e(t)},n=function(e){return" "+e+" "},r=function(r,a){var o=r.type,s=r.value;switch(o){case ir.GROUP:var l=e(s);return l.substring(0,l.length-1);case ir.DATA_COMPARE:return"["+r.field+n(e(r.operator))+t(s)+"]";case ir.DATA_BOOL:var u=r.operator,c=r.field;return"["+e(u)+c+"]";case ir.DATA_EXIST:return"["+r.field+"]";case ir.META_COMPARE:var d=r.operator;return"[["+r.field+n(e(d))+t(s)+"]]";case ir.STATE:return s;case ir.ID:return"#"+s;case ir.CLASS:return"."+s;case ir.PARENT:case ir.CHILD:return i(r.parent,a)+n(">")+i(r.child,a);case ir.ANCESTOR:case ir.DESCENDANT:return i(r.ancestor,a)+" "+i(r.descendant,a);case ir.COMPOUND_SPLIT:var h=i(r.left,a),p=i(r.subject,a),f=i(r.right,a);return h+(h.length>0?" ":"")+p+f;case ir.TRUE:return""}},i=function(e,t){return e.checks.reduce(function(n,i,a){return n+(t===e&&0===a?"$":"")+r(i,t)},"")},a="",o=0;o<this.length;o++){var s=this[o];a+=i(s,s.subject),this.length>1&&o<this.length-1&&(a+=", ")}return this.toStringCache=a,a}},{matches:function(e){for(var t=0;t<this.length;t++)if(iy(this[t],e))return!0;return!1},filter:function(e){var t=this;if(1===t.length&&1===t[0].checks.length&&t[0].checks[0].type===ir.ID)return e.getElementById(t[0].checks[0].value).collection();var n=function(e){for(var n=0;n<t.length;n++)if(iy(t[n],e))return!0;return!1};return null==t.text()&&(n=function(){return!0}),e.filter(n)}}].forEach(function(e){return Z(ib,e)}),ib.text=function(){return this.inputText},ib.size=function(){return this.length},ib.eq=function(e){return this[e]},ib.sameText=function(e){return!this.invalid&&!e.invalid&&this.text()===e.text()},ib.addQuery=function(e){this[this.length++]=e},ib.selector=ib.toString;var ix={allAre:function(e){var t=new im(e);return this.every(function(e){return t.matches(e)})},is:function(e){var t=new im(e);return this.some(function(e){return t.matches(e)})},some:function(e,t){for(var n=0;n<this.length;n++)if(t?e.apply(t,[this[n],n,this]):e(this[n],n,this))return!0;return!1},every:function(e,t){for(var n=0;n<this.length;n++)if(!(t?e.apply(t,[this[n],n,this]):e(this[n],n,this)))return!1;return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length;return t===e.length&&(1===t?this[0]===e[0]:this.every(function(t){return e.hasElementWithId(t.id())}))},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(e){return t.hasElementWithId(e.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(e){return t.hasElementWithId(e.id())})}};ix.allAreNeighbours=ix.allAreNeighbors,ix.has=ix.contains,ix.equal=ix.equals=ix.same;var iw=function(e,t){return function(n,r,i,a){if(null==n?o="":N(n)&&1===n.length&&(o=n.id()),1!==this.length||!o)return e.call(this,n,r,i,a);var o,s=this[0]._private,l=s.traversalCache=s.traversalCache||{},u=l[t]=l[t]||[],c=ej(o);return u[c]||(u[c]=e.call(this,n,r,i,a))}},iE={parent:function(e){var t=[];if(1===this.length){var n=this[0]._private.parent;if(n)return n}for(var r=0;r<this.length;r++){var i=this[r]._private.parent;i&&t.push(i)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],n=this.parent();n.nonempty();){for(var r=0;r<n.length;r++){var i=n[r];t.push(i)}n=n.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,n=0;n<this.length;n++){var r=this[n].parents();t=(t=t||r).intersect(r)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(e){return e.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(e){return e.isChild()}).filter(e)},children:iw(function(e){for(var t=[],n=0;n<this.length;n++)for(var r=this[n]._private.children,i=0;i<r.length;i++)t.push(r[i]);return this.spawn(t,!0).filter(e)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&0!==e._private.children.length},isChildless:function(){var e=this[0];if(e)return e.isNode()&&0===e._private.children.length},isChild:function(){var e=this[0];if(e)return e.isNode()&&null!=e._private.parent},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&null==e._private.parent},descendants:function(e){var t=[];return!function e(n){for(var r=0;r<n.length;r++){var i=n[r];t.push(i),i.children().nonempty()&&e(i.children())}}(this.children()),this.spawn(t,!0).filter(e)}};function ik(e,t,n,r){for(var i=[],a=new ti,o=e.cy().hasCompoundNodes(),s=0;s<e.length;s++){var l=e[s];n?i.push(l):o&&r(i,a,l)}for(;i.length>0;){var u=i.shift();t(u),a.add(u.id()),o&&r(i,a,u)}return e}function iC(e,t,n){if(n.isParent())for(var r=n._private.children,i=0;i<r.length;i++){var a=r[i];t.has(a.id())||e.push(a)}}function iS(e,t,n){if(n.isChild()){var r=n._private.parent;t.has(r.id())||e.push(r)}}function iD(e,t,n){iS(e,t,n),iC(e,t,n)}iE.forEachDown=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return ik(this,e,t,iC)},iE.forEachUp=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return ik(this,e,t,iS)},iE.forEachUpAndDown=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return ik(this,e,t,iD)},iE.ancestors=iE.parents,(oU=oK={data:r6.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:r6.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:r6.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:r6.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:r6.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:r6.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}}).attr=oU.data,oU.removeAttr=oU.removeData;var iT={};function iP(e){return function(t){if(void 0===t&&(t=!0),0!==this.length&&!(!this.isNode()||this.removed())){for(var n=0,r=this[0],i=r._private.edges,a=0;a<i.length;a++){var o=i[a];!t&&o.isLoop()||(n+=e(r,o))}return n}}}function i_(e,t){return function(n){for(var r,i=this.nodes(),a=0;a<i.length;a++){var o=i[a][e](n);void 0!==o&&(void 0===r||t(o,r))&&(r=o)}return r}}Z(iT,{degree:iP(function(e,t){return t.source().same(t.target())?2:1}),indegree:iP(function(e,t){return t.target().same(e)?1:0}),outdegree:iP(function(e,t){return t.source().same(e)?1:0})}),Z(iT,{minDegree:i_("degree",function(e,t){return e<t}),maxDegree:i_("degree",function(e,t){return e>t}),minIndegree:i_("indegree",function(e,t){return e<t}),maxIndegree:i_("indegree",function(e,t){return e>t}),minOutdegree:i_("outdegree",function(e,t){return e<t}),maxOutdegree:i_("outdegree",function(e,t){return e>t})}),Z(iT,{totalDegree:function(e){for(var t=0,n=this.nodes(),r=0;r<n.length;r++)t+=n[r].degree(e);return t}});var iM=function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r];if(!i.locked()){var a=i._private.position,o={x:null!=t.x?t.x-a.x:0,y:null!=t.y?t.y-a.y:0};i.isParent()&&!(0===o.x&&0===o.y)&&i.children().shift(o,n),i.dirtyBoundingBoxCache()}}},iB={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){iM(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};(oZ=o$={position:r6.data(iB),silentPosition:r6.data(Z({},iB,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){iM(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(_(e))t?this.silentPosition(e):this.position(e);else if(T(e)){var n=this.cy();n.startBatch();for(var r=0;r<this.length;r++){var i=this[r],a=void 0;(a=e(i,r))&&(t?i.silentPosition(a):i.position(a))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,n){var r;if(_(e)?(r={x:M(e.x)?e.x:0,y:M(e.y)?e.y:0},n=t):D(e)&&M(t)&&((r={x:0,y:0})[e]=t),null!=r){var i=this.cy();i.startBatch();for(var a=0;a<this.length;a++){var o=this[a];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var s=o.position(),l={x:s.x+r.x,y:s.y+r.y};n?o.silentPosition(l):o.position(l)}}i.endBatch()}return this},silentShift:function(e,t){return _(e)?this.shift(e,!0):D(e)&&M(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var n=this[0],r=this.cy(),i=r.zoom(),a=r.pan(),o=_(e)?e:void 0,s=void 0!==o||void 0!==t&&D(e);if(n&&n.isNode()){if(!s)return(o=tv(n.position(),i,a),void 0===e)?o:o[e];for(var l=0;l<this.length;l++){var u=this[l];void 0!==t?u.position(e,(t-a[e])/i):void 0!==o&&u.position(ty(o,i,a))}}else if(!s)return;return this},relativePosition:function(e,t){var n=this[0],r=this.cy(),i=_(e)?e:void 0,a=void 0!==i||void 0!==t&&D(e),o=r.hasCompoundNodes();if(n&&n.isNode()){if(a)for(var s=0;s<this.length;s++){var l=this[s],u=o?l.parent():null,c=u&&u.length>0;c&&(u=u[0]);var d=c?u.position():{x:0,y:0};void 0!==t?l.position(e,t+d[e]):void 0!==i&&l.position({x:i.x+d.x,y:i.y+d.y})}else{var h=n.position(),p=o?n.parent():null,f=p&&p.length>0;f&&(p=p[0]);var g=f?p.position():{x:0,y:0};return(i={x:h.x-g.x,y:h.y-g.y},void 0===e)?i:i[e]}}else if(!a)return;return this}}).modelPosition=oZ.point=oZ.position,oZ.modelPositions=oZ.points=oZ.positions,oZ.renderedPoint=oZ.renderedPosition,oZ.relativePoint=oZ.relativePosition,oQ=oJ={},oJ.renderedBoundingBox=function(e){var t=this.boundingBox(e),n=this.cy(),r=n.zoom(),i=n.pan(),a=t.x1*r+i.x,o=t.x2*r+i.x,s=t.y1*r+i.y,l=t.y2*r+i.y;return{x1:a,x2:o,y1:s,y2:l,w:o-a,h:l-s}},oJ.dirtyCompoundBoundsCache=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();return t.styleEnabled()&&t.hasCompoundNodes()&&this.forEachUp(function(t){if(t.isParent()){var n=t._private;n.compoundBoundsClean=!1,n.bbCache=null,e||t.emitAndNotify("bounds")}}),this},oJ.updateCompoundBounds=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();if(!t.styleEnabled()||!t.hasCompoundNodes()||!e&&t.batching())return this;for(var n=0;n<this.length;n++){var r=this[n],i=r._private;i.compoundBoundsClean&&!e||(function(e){if(e.isParent()){var t=e._private,n=e.children(),r="include"===e.pstyle("compound-sizing-wrt-labels").value,i={width:{val:e.pstyle("min-width").pfValue,left:e.pstyle("min-width-bias-left"),right:e.pstyle("min-width-bias-right")},height:{val:e.pstyle("min-height").pfValue,top:e.pstyle("min-height-bias-top"),bottom:e.pstyle("min-height-bias-bottom")}},a=n.boundingBox({includeLabels:r,includeOverlays:!1,useCache:!1}),o=t.position;(0===a.w||0===a.h)&&((a={w:e.pstyle("width").pfValue,h:e.pstyle("height").pfValue}).x1=o.x-a.w/2,a.x2=o.x+a.w/2,a.y1=o.y-a.h/2,a.y2=o.y+a.h/2);var s=i.width.left.value;"px"===i.width.left.units&&i.width.val>0&&(s=100*s/i.width.val);var l=i.width.right.value;"px"===i.width.right.units&&i.width.val>0&&(l=100*l/i.width.val);var u=i.height.top.value;"px"===i.height.top.units&&i.height.val>0&&(u=100*u/i.height.val);var c=i.height.bottom.value;"px"===i.height.bottom.units&&i.height.val>0&&(c=100*c/i.height.val);var d=y(i.width.val-a.w,s,l),h=d.biasDiff,p=d.biasComplementDiff,f=y(i.height.val-a.h,u,c),g=f.biasDiff,v=f.biasComplementDiff;t.autoPadding=function(e,t,n,r){if("%"===n.units)switch(r){case"width":return e>0?n.pfValue*e:0;case"height":return t>0?n.pfValue*t:0;case"average":return e>0&&t>0?n.pfValue*(e+t)/2:0;case"min":return e>0&&t>0?e>t?n.pfValue*t:n.pfValue*e:0;case"max":return e>0&&t>0?e>t?n.pfValue*e:n.pfValue*t:0;default:return 0}else if("px"===n.units)return n.pfValue;else return 0}(a.w,a.h,e.pstyle("padding"),e.pstyle("padding-relative-to").value),t.autoWidth=Math.max(a.w,i.width.val),o.x=(-h+a.x1+a.x2+p)/2,t.autoHeight=Math.max(a.h,i.height.val),o.y=(-g+a.y1+a.y2+v)/2}function y(e,t,n){var r=0,i=0,a=t+n;return e>0&&a>0&&(r=t/a*e,i=n/a*e),{biasDiff:r,biasComplementDiff:i}}}(r),t.batching()||(i.compoundBoundsClean=!0))}return this};var iN=function(e){return e===1/0||e===-1/0?0:e},iA=function(e,t,n,r,i){r-t!=0&&i-n!=0&&null!=t&&null!=n&&null!=r&&null!=i&&(e.x1=t<e.x1?t:e.x1,e.x2=r>e.x2?r:e.x2,e.y1=n<e.y1?n:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},iI=function(e,t){return null==t?e:iA(e,t.x1,t.y1,t.x2,t.y2)},iO=function(e,t,n){return e7(e,t,n)},iR=function(e,t,n){if(!t.cy().headless()){var r,i,a=t._private,o=a.rstyle,s=o.arrowWidth/2;if("none"!==t.pstyle(n+"-arrow-shape").value){"source"===n?(r=o.srcX,i=o.srcY):"target"===n?(r=o.tgtX,i=o.tgtY):(r=o.midX,i=o.midY);var l=a.arrowBounds=a.arrowBounds||{},u=l[n]=l[n]||{};u.x1=r-s,u.y1=i-s,u.x2=r+s,u.y2=i+s,u.w=u.x2-u.x1,u.h=u.y2-u.y1,tz(u,1),iA(e,u.x1,u.y1,u.x2,u.y2)}}},iz=function(e,t,n){if(!t.cy().headless()){a=n?n+"-":"";var r=t._private,i=r.rstyle;if(t.pstyle(a+"label").strValue){var a,o,s,l,u,c=t.pstyle("text-halign"),d=t.pstyle("text-valign"),h=iO(i,"labelWidth",n),p=iO(i,"labelHeight",n),f=iO(i,"labelX",n),g=iO(i,"labelY",n),v=t.pstyle(a+"text-margin-x").pfValue,y=t.pstyle(a+"text-margin-y").pfValue,b=t.isEdge(),x=t.pstyle(a+"text-rotation"),w=t.pstyle("text-outline-width").pfValue,E=t.pstyle("text-border-width").pfValue/2,k=t.pstyle("text-background-padding").pfValue,C=h/2,S=p/2;if(b)o=f-C,s=f+C,l=g-S,u=g+S;else{switch(c.value){case"left":o=f-h,s=f;break;case"center":o=f-C,s=f+C;break;case"right":o=f,s=f+h}switch(d.value){case"top":l=g-p,u=g;break;case"center":l=g-S,u=g+S;break;case"bottom":l=g,u=g+p}}var D=v-Math.max(w,E)-k-2,T=v+Math.max(w,E)+k+2,P=y-Math.max(w,E)-k-2,_=y+Math.max(w,E)+k+2;o+=D,s+=T,l+=P,u+=_;var M=n||"main",B=r.labelBounds,N=B[M]=B[M]||{};N.x1=o,N.y1=l,N.x2=s,N.y2=u,N.w=s-o,N.h=u-l,N.leftPad=D,N.rightPad=T,N.topPad=P,N.botPad=_;var A=b&&"autorotate"===x.strValue,I=null!=x.pfValue&&0!==x.pfValue;if(A||I){var O=A?iO(r.rstyle,"labelAngle",n):x.pfValue,R=Math.cos(O),z=Math.sin(O),L=(o+s)/2,V=(l+u)/2;if(!b){switch(c.value){case"left":L=s;break;case"right":L=o}switch(d.value){case"top":V=u;break;case"bottom":V=l}}var F=function(e,t){return{x:(e-=L)*R-(t-=V)*z+L,y:e*z+t*R+V}},j=F(o,l),q=F(o,u),X=F(s,l),Y=F(s,u);o=Math.min(j.x,q.x,X.x,Y.x),s=Math.max(j.x,q.x,X.x,Y.x),l=Math.min(j.y,q.y,X.y,Y.y),u=Math.max(j.y,q.y,X.y,Y.y)}var W=M+"Rot",H=B[W]=B[W]||{};H.x1=o,H.y1=l,H.x2=s,H.y2=u,H.w=s-o,H.h=u-l,iA(e,o,l,s,u),iA(r.labelBounds.all,o,l,s,u)}return e}},iL=function(e,t){if(!t.cy().headless()){var n=t.pstyle("outline-opacity").value,r=t.pstyle("outline-width").value;if(n>0&&r>0){var i=t.pstyle("outline-offset").value,a=t.pstyle("shape").value,o=r+i,s=(e.w+2*o)/e.w,l=(e.h+2*o)/e.h,u=0;["diamond","pentagon","round-triangle"].includes(a)?(s=(e.w+2.4*o)/e.w,u=-o/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(a)?s=(e.w+2.4*o)/e.w:"star"===a?(s=(e.w+2.8*o)/e.w,l=(e.h+2.6*o)/e.h,u=-o/3.8):"triangle"===a?(s=(e.w+2.8*o)/e.w,l=(e.h+2.4*o)/e.h,u=-o/1.4):"vee"===a&&(s=(e.w+4.4*o)/e.w,l=(e.h+3.8*o)/e.h,u=-(.5*o));var c=e.h*l-e.h,d=e.w*s-e.w;if(tL(e,[Math.ceil(c/2),Math.ceil(d/2)]),0!==u){var h,p=(h=u,{x1:e.x1+0,x2:e.x2+0,y1:e.y1+h,y2:e.y2+h,w:e.w,h:e.h});tO(e,p)}}}},iV=function(e,t){var n=e._private.cy,r=n.styleEnabled(),i=n.headless(),a=tA(),o=e._private,s=e.isNode(),l=e.isEdge(),u=o.rstyle,c=s&&r?e.pstyle("bounds-expansion").pfValue:[0],d=function(e){return"none"!==e.pstyle("display").value},h=!r||d(e)&&(!l||d(e.source())&&d(e.target()));if(h){var p=0;r&&t.includeOverlays&&0!==e.pstyle("overlay-opacity").value&&(p=e.pstyle("overlay-padding").value);var f=0;r&&t.includeUnderlays&&0!==e.pstyle("underlay-opacity").value&&(f=e.pstyle("underlay-padding").value);var g=Math.max(p,f),v=0;if(r&&(v=e.pstyle("width").pfValue/2),s&&t.includeNodes){var y=e.position();S=y.x,D=y.y;var b=e.outerWidth()/2,x=e.outerHeight()/2;w=S-b,E=S+b,iA(a,w,k=D-x,E,C=D+x),r&&t.includeOutlines&&iL(a,e)}else if(l&&t.includeEdges){if(r&&!i){var w,E,k,C,S,D,T,P=e.pstyle("curve-style").strValue;if(w=Math.min(u.srcX,u.midX,u.tgtX),E=Math.max(u.srcX,u.midX,u.tgtX),k=Math.min(u.srcY,u.midY,u.tgtY),C=Math.max(u.srcY,u.midY,u.tgtY),w-=v,E+=v,iA(a,w,k-=v,E,C+=v),"haystack"===P){var _=u.haystackPts;if(_&&2===_.length){if(w=_[0].x,k=_[0].y,E=_[1].x,C=_[1].y,w>E){var M=w;w=E,E=M}if(k>C){var B=k;k=C,C=B}iA(a,w-v,k-v,E+v,C+v)}}else if("bezier"===P||"unbundled-bezier"===P||P.endsWith("segments")||P.endsWith("taxi")){switch(P){case"bezier":case"unbundled-bezier":T=u.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":T=u.linePts}if(null!=T)for(var N=0;N<T.length;N++){var A=T[N];w=A.x-v,E=A.x+v,iA(a,w,k=A.y-v,E,C=A.y+v)}}}else{var I=e.source().position(),O=e.target().position();if(w=I.x,E=O.x,k=I.y,C=O.y,w>E){var R=w;w=E,E=R}if(k>C){var z=k;k=C,C=z}w-=v,E+=v,iA(a,w,k-=v,E,C+=v)}}if(r&&t.includeEdges&&l&&(iR(a,e,"mid-source"),iR(a,e,"mid-target"),iR(a,e,"source"),iR(a,e,"target")),r&&"yes"===e.pstyle("ghost").value){var L=e.pstyle("ghost-offset-x").pfValue,V=e.pstyle("ghost-offset-y").pfValue;iA(a,a.x1+L,a.y1+V,a.x2+L,a.y2+V)}var F=o.bodyBounds=o.bodyBounds||{};tV(F,a),tL(F,c),tz(F,1),r&&(w=a.x1,E=a.x2,k=a.y1,C=a.y2,iA(a,w-g,k-g,E+g,C+g));var j=o.overlayBounds=o.overlayBounds||{};tV(j,a),tL(j,c),tz(j,1);var q=o.labelBounds=o.labelBounds||{};null!=q.all?tI(q.all):q.all=tA(),r&&t.includeLabels&&(t.includeMainLabels&&iz(a,e,null),l&&(t.includeSourceLabels&&iz(a,e,"source"),t.includeTargetLabels&&iz(a,e,"target")))}return a.x1=iN(a.x1),a.y1=iN(a.y1),a.x2=iN(a.x2),a.y2=iN(a.y2),a.w=iN(a.x2-a.x1),a.h=iN(a.y2-a.y1),a.w>0&&a.h>0&&h&&(tL(a,c),tz(a,1)),a},iF=function(e){var t=0,n=function(e){return(e?1:0)<<t++};return 0+n(e.incudeNodes)+n(e.includeEdges)+n(e.includeLabels)+n(e.includeMainLabels)+n(e.includeSourceLabels)+n(e.includeTargetLabels)+n(e.includeOverlays)+n(e.includeOutlines)},ij=function(e){if(!e.isEdge())return 0;var t=e.source().position(),n=e.target().position(),r=function(e){return Math.round(e)};return eF([r(t.x),r(t.y),r(n.x),r(n.y)])},iq=function(e,t){var n,r=e._private,i=e.isEdge(),a=null==t?iY:iF(t),o=ij(e),s=r.bbCachePosKey===o,l=t.useCache&&s,u=function(e){return null==e._private.bbCache||e._private.styleDirty};if(!l||u(e)||i&&(u(e.source())||u(e.target()))?(s||e.recalculateRenderedStyle(l),n=iV(e,iX),r.bbCache=n,r.bbCachePosKey=o):n=r.bbCache,a!==iY){var c=e.isNode();n=tA(),(t.includeNodes&&c||t.includeEdges&&!c)&&(t.includeOverlays?iI(n,r.overlayBounds):iI(n,r.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!i||t.includeSourceLabels&&t.includeTargetLabels)?iI(n,r.labelBounds.all):(t.includeMainLabels&&iI(n,r.labelBounds.mainRot),t.includeSourceLabels&&iI(n,r.labelBounds.sourceRot),t.includeTargetLabels&&iI(n,r.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},iX={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},iY=iF(iX),iW=e4(iX);oJ.boundingBox=function(e){var t;if(1!==this.length||null==this[0]._private.bbCache||this[0]._private.styleDirty||void 0!==e&&void 0!==e.useCache&&!0!==e.useCache){t=tA();var n=iW(e=e||iX);if(this.cy().styleEnabled())for(var r=0;r<this.length;r++){var i=this[r],a=i._private,o=ij(i),s=a.bbCachePosKey===o,l=n.useCache&&s&&!a.styleDirty;i.recalculateRenderedStyle(l)}this.updateCompoundBounds(!e.useCache);for(var u=0;u<this.length;u++)iI(t,iq(this[u],n))}else e=void 0===e?iX:iW(e),t=iq(this[0],e);return t.x1=iN(t.x1),t.y1=iN(t.y1),t.x2=iN(t.x2),t.y2=iN(t.y2),t.w=iN(t.x2-t.x1),t.h=iN(t.y2-t.y1),t},oJ.dirtyBoundingBoxCache=function(){for(var e=0;e<this.length;e++){var t=this[e]._private;t.bbCache=null,t.bbCachePosKey=null,t.bodyBounds=null,t.overlayBounds=null,t.labelBounds.all=null,t.labelBounds.source=null,t.labelBounds.target=null,t.labelBounds.main=null,t.labelBounds.sourceRot=null,t.labelBounds.targetRot=null,t.labelBounds.mainRot=null,t.arrowBounds.source=null,t.arrowBounds.target=null,t.arrowBounds["mid-source"]=null,t.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this},oJ.boundingBoxAt=function(e){var t,n=this.nodes(),r=this.cy(),i=r.hasCompoundNodes(),a=r.collection();if(i&&(a=n.filter(function(e){return e.isParent()}),n=n.not(a)),_(e)){var o=e;e=function(){return o}}r.startBatch(),n.forEach(function(t,n){return t._private.bbAtOldPos=e(t,n)}).silentPositions(e),i&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0));var s={x1:(t=this.boundingBox({useCache:!1})).x1,x2:t.x2,w:t.w,y1:t.y1,y2:t.y2,h:t.h};return n.silentPositions(function(e){return e._private.bbAtOldPos}),i&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0)),r.endBatch(),s},oQ.boundingbox=oQ.bb=oQ.boundingBox,oQ.renderedBoundingbox=oQ.renderedBoundingBox,o0=o1={};var iH=function(e){e.uppercaseName=X(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=X(e.outerName),o0[e.name]=function(){var t=this[0],n=t._private,r=n.cy._private.styleEnabled;if(t){if(!r)return 1;if(t.isParent())return t.updateCompoundBounds(),n[e.autoName]||0;var i=t.pstyle(e.name);return"label"===i.strValue?(t.recalculateRenderedStyle(),n.rstyle[e.labelName]||0):i.pfValue}},o0["outer"+e.uppercaseName]=function(){var t=this[0],n=t._private.cy._private.styleEnabled;if(t)return n?t[e.name]()+t.pstyle("border-width").pfValue+2*t.padding():1},o0["rendered"+e.uppercaseName]=function(){var t=this[0];if(t)return t[e.name]()*this.cy().zoom()},o0["rendered"+e.uppercaseOuterName]=function(){var t=this[0];if(t)return t[e.outerName]()*this.cy().zoom()}};iH({name:"width"}),iH({name:"height"}),o1.padding=function(){var e=this[0],t=e._private;return e.isParent()&&(e.updateCompoundBounds(),void 0!==t.autoPadding)?t.autoPadding:e.pstyle("padding").pfValue},o1.paddedHeight=function(){var e=this[0];return e.height()+2*e.padding()},o1.paddedWidth=function(){var e=this[0];return e.width()+2*e.padding()};var iG=function(e,t){if(e.isEdge())return t(e)},iU=function(e,t){if(e.isEdge()){var n=e.cy();return tv(t(e),n.zoom(),n.pan())}},iK=function(e,t){if(e.isEdge()){var n=e.cy(),r=n.pan(),i=n.zoom();return t(e).map(function(e){return tv(e,i,r)})}},iZ={controlPoints:{get:function(e){return e.renderer().getControlPoints(e)},mult:!0},segmentPoints:{get:function(e){return e.renderer().getSegmentPoints(e)},mult:!0},sourceEndpoint:{get:function(e){return e.renderer().getSourceEndpoint(e)}},targetEndpoint:{get:function(e){return e.renderer().getTargetEndpoint(e)}},midpoint:{get:function(e){return e.renderer().getEdgeMidpoint(e)}}},i$=Z({},o$,oJ,o1,Object.keys(iZ).reduce(function(e,t){var n=iZ[t],r="rendered"+t[0].toUpperCase()+t.substr(1);return e[t]=function(){return iG(this,n.get)},n.mult?e[r]=function(){return iK(this,n.get)}:e[r]=function(){return iU(this,n.get)},e},{})),iQ=function(e,t){this.recycle(e,t)};function iJ(){return!1}function i0(){return!0}iQ.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=iJ,null!=e&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?i0:iJ):null!=e&&e.type?t=e:this.type=e,null!=t&&(this.originalEvent=t.originalEvent,this.type=null!=t.type?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),null!=this.cy&&null!=this.position&&null==this.renderedPosition){var n=this.position,r=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:n.x*r+i.x,y:n.y*r+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=i0;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=i0;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=i0,this.stopPropagation()},isDefaultPrevented:iJ,isPropagationStopped:iJ,isImmediatePropagationStopped:iJ};var i1=/^([^.]+)(\.(?:[^.]+))?$/,i2={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},i5=Object.keys(i2),i3={};function i4(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i3,t=arguments.length>1?arguments[1]:void 0,n=0;n<i5.length;n++){var r=i5[n];this[r]=e[r]||i2[r]}this.context=t||this.context,this.listeners=[],this.emitting=0}var i9=i4.prototype,i6=function(e,t,n,r,i,a,o){T(r)&&(i=r,r=null),o&&(a=null==a?o:Z({},a,o));for(var s=P(n)?n:n.split(/\s+/),l=0;l<s.length;l++){var u=s[l];if(!z(u)){var c=u.match(i1);if(c&&!1===t(e,u,c[1],c[2]?c[2]:null,r,i,a))break}}},i8=function(e,t){return e.addEventFields(e.context,t),new iQ(t.type,t)},i7=function(e,t,n){if("event"===S(n)){t(e,n);return}if(_(n)){t(e,i8(e,n));return}for(var r=P(n)?n:n.split(/\s+/),i=0;i<r.length;i++){var a=r[i];if(!z(a)){var o=a.match(i1);if(o){var s=i8(e,{type:o[1],namespace:o[2]?o[2]:null,target:e.context});t(e,s)}}}};i9.on=i9.addListener=function(e,t,n,r,i){return i6(this,function(e,t,n,r,i,a,o){T(a)&&e.listeners.push({event:t,callback:a,type:n,namespace:r,qualifier:i,conf:o})},e,t,n,r,i),this},i9.one=function(e,t,n,r){return this.on(e,t,n,r,{one:!0})},i9.removeListener=i9.off=function(e,t,n,r){var i=this;0!==this.emitting&&(this.listeners=this.listeners.slice());for(var a=this.listeners,o=function(o){var s=a[o];i6(i,function(t,n,r,i,l,u){if((s.type===r||"*"===e)&&(!i&&".*"!==s.namespace||s.namespace===i)&&(!l||t.qualifierCompare(s.qualifier,l))&&(!u||s.callback===u))return a.splice(o,1),!1},e,t,n,r)},s=a.length-1;s>=0;s--)o(s);return this},i9.removeAllListeners=function(){return this.removeListener("*")},i9.emit=i9.trigger=function(e,t,n){var r=this.listeners,i=r.length;return this.emitting++,P(t)||(t=[t]),i7(this,function(e,a){null!=n&&(i=(r=[{event:a.event,type:a.type,namespace:a.namespace,callback:n}]).length);for(var o=0;o<i;o++)!function(n){var i=r[n];if(i.type===a.type&&(!i.namespace||i.namespace===a.namespace||".*"===i.namespace)&&e.eventMatches(e.context,i,a)){var o=[a];null!=t&&e8(o,t),e.beforeEmit(e.context,i,a),i.conf&&i.conf.one&&(e.listeners=e.listeners.filter(function(e){return e!==i}));var s=e.callbackContext(e.context,i,a),l=i.callback.apply(s,o);e.afterEmit(e.context,i,a),!1===l&&(a.stopPropagation(),a.preventDefault())}}(o);e.bubble(e.context)&&!a.isPropagationStopped()&&e.parent(e.context).emit(a,t)},e),this.emitting--,this};var ae={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&A(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},at=function(e){return D(e)?new im(e):e},an={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],n=t._private;n.emitter||(n.emitter=new i4(ae,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,n){for(var r=at(t),i=0;i<this.length;i++)this[i].emitter().on(e,r,n);return this},removeListener:function(e,t,n){for(var r=at(t),i=0;i<this.length;i++)this[i].emitter().removeListener(e,r,n);return this},removeAllListeners:function(){for(var e=0;e<this.length;e++)this[e].emitter().removeAllListeners();return this},one:function(e,t,n){for(var r=at(t),i=0;i<this.length;i++)this[i].emitter().one(e,r,n);return this},once:function(e,t,n){for(var r=at(t),i=0;i<this.length;i++)this[i].emitter().on(e,r,n,{once:!0,onceCollection:this})},emit:function(e,t){for(var n=0;n<this.length;n++)this[n].emitter().emit(e,t);return this},emitAndNotify:function(e,t){if(0!==this.length)return this.cy().notify(e,this),this.emit(e,t),this}};r6.eventAliasesOn(an);var ar={nodes:function(e){return this.filter(function(e){return e.isNode()}).filter(e)},edges:function(e){return this.filter(function(e){return e.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),n=0;n<this.length;n++){var r=this[n];r.isNode()?e.push(r):t.push(r)}return{nodes:e,edges:t}},filter:function(e,t){if(void 0===e)return this;if(D(e)||N(e))return new im(e).filter(this);if(T(e)){for(var n=this.spawn(),r=0;r<this.length;r++){var i=this[r];(t?e.apply(t,[i,r,this]):e(i,r,this))&&n.push(i)}return n}return this.spawn()},not:function(e){if(!e)return this;D(e)&&(e=this.filter(e));for(var t=this.spawn(),n=0;n<this.length;n++){var r=this[n];e.has(r)||t.push(r)}return t},absoluteComplement:function(){return this.cy().mutableElements().not(this)},intersect:function(e){if(D(e))return this.filter(e);for(var t=this.spawn(),n=this.length<e.length,r=n?this:e,i=n?e:this,a=0;a<r.length;a++){var o=r[a];i.has(o)&&t.push(o)}return t},xor:function(e){var t=this._private.cy;D(e)&&(e=t.$(e));var n=this.spawn(),r=e,i=function(e,t){for(var r=0;r<e.length;r++){var i=e[r],a=i._private.data.id;t.hasElementWithId(a)||n.push(i)}};return i(this,r),i(r,this),n},diff:function(e){var t=this._private.cy;D(e)&&(e=t.$(e));var n=this.spawn(),r=this.spawn(),i=this.spawn(),a=e,o=function(e,t,n){for(var r=0;r<e.length;r++){var a=e[r],o=a._private.data.id;t.hasElementWithId(o)?i.merge(a):n.push(a)}};return o(this,a,n),o(a,this,r),{left:n,right:r,both:i}},add:function(e){var t=this._private.cy;if(!e)return this;if(D(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=this.spawnSelf(),i=0;i<e.length;i++){var a=e[i];this.has(a)||r.push(a)}return r},merge:function(e){var t=this._private,n=t.cy;if(!e)return this;if(e&&D(e)){var r=e;e=n.mutableElements().filter(r)}for(var i=t.map,a=0;a<e.length;a++){var o=e[a],s=o._private.data.id;if(!i.has(s)){var l=this.length++;this[l]=o,i.set(s,{ele:o,index:l})}}return this},unmergeAt:function(e){var t=this[e].id(),n=this._private.map;this[e]=void 0,n.delete(t);var r=e===this.length-1;if(this.length>1&&!r){var i=this.length-1,a=this[i],o=a._private.data.id;this[i]=void 0,this[e]=a,n.set(o,{ele:a,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,n=e._private.data.id,r=t.map.get(n);if(!r)return this;var i=r.index;return this.unmergeAt(i),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&D(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=0;r<e.length;r++)this.unmergeOne(e[r]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--)e(this[t])&&this.unmergeAt(t);return this},map:function(e,t){for(var n=[],r=0;r<this.length;r++){var i=this[r],a=t?e.apply(t,[i,r,this]):e(i,r,this);n.push(a)}return n},reduce:function(e,t){for(var n=t,r=0;r<this.length;r++)n=e(n,this[r],r,this);return n},max:function(e,t){for(var n,r=-1/0,i=0;i<this.length;i++){var a=this[i],o=t?e.apply(t,[a,i,this]):e(a,i,this);o>r&&(r=o,n=a)}return{value:r,ele:n}},min:function(e,t){for(var n,r=1/0,i=0;i<this.length;i++){var a=this[i],o=t?e.apply(t,[a,i,this]):e(a,i,this);o<r&&(r=o,n=a)}return{value:r,ele:n}}},ai=ar;ai.u=ai["|"]=ai["+"]=ai.union=ai.or=ai.add,ai["\\"]=ai["!"]=ai["-"]=ai.difference=ai.relativeComplement=ai.subtract=ai.not,ai.n=ai["&"]=ai["."]=ai.and=ai.intersection=ai.intersect,ai["^"]=ai["(+)"]=ai["(-)"]=ai.symmetricDifference=ai.symdiff=ai.xor,ai.fnFilter=ai.filterFn=ai.stdFilter=ai.filter,ai.complement=ai.abscomp=ai.absoluteComplement;var aa=function(e,t){var n=e.cy().hasCompoundNodes();function r(e){var t=e.pstyle("z-compound-depth");return"auto"===t.value?n?e.zDepth():0:"bottom"===t.value?-1:"top"===t.value?eG:0}var i=r(e)-r(t);if(0!==i)return i;function a(e){return"auto"===e.pstyle("z-index-compare").value&&e.isNode()?1:0}var o=a(e)-a(t);if(0!==o)return o;var s=e.pstyle("z-index").value-t.pstyle("z-index").value;return 0!==s?s:e.poolIndex()-t.poolIndex()},ao={forEach:function(e,t){if(T(e))for(var n=this.length,r=0;r<n;r++){var i=this[r];if(!1===(t?e.apply(t,[i,r,this]):e(i,r,this)))break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var n=[],r=this.length;null==t&&(t=r),null==e&&(e=0),e<0&&(e=r+e),t<0&&(t=r+t);for(var i=e;i>=0&&i<t&&i<r;i++)n.push(this[i]);return this.spawn(n)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return 0===this.length},nonempty:function(){return!this.empty()},sort:function(e){if(!T(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(aa)},zDepth:function(){var e=this[0];if(e){var t=e._private;if("nodes"===t.group){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:eG-1}var r=t.source,i=t.target;return Math.max(r.zDepth(),i.zDepth(),0)}}};ao.each=ao.forEach,oG="undefined",("undefined"==typeof Symbol?"undefined":r(Symbol))!=oG&&r(Symbol.iterator)!=oG&&(ao[Symbol.iterator]=function(){var e=this,t={value:void 0,done:!1},n=0,r=this.length;return s({next:function(){return n<r?t.value=e[n++]:(t.value=void 0,t.done=!0),t}},Symbol.iterator,function(){return this})});var as=e4({nodeDimensionsIncludeLabels:!1}),al={layoutDimensions:function(e){if(e=as(e),this.takesUpSpace()){if(e.nodeDimensionsIncludeLabels){var t,n=this.boundingBox();t={w:n.w,h:n.h}}else t={w:this.outerWidth(),h:this.outerHeight()}}else t={w:0,h:0};return(0===t.w||0===t.h)&&(t.w=t.h=1),t},layoutPositions:function(e,t,n){var r=this.nodes().filter(function(e){return!e.isParent()}),i=this.cy(),a=t.eles,o=function(e){return e.id()},s=V(n,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var l=function(e,t,n){var r={x:t.x1+t.w/2,y:t.y1+t.h/2},i={x:(n.x-r.x)*e,y:(n.y-r.y)*e};return{x:r.x+i.x,y:r.y+i.y}},u=t.spacingFactor&&1!==t.spacingFactor,c=function(){if(!u)return null;for(var e=tA(),t=0;t<r.length;t++){var n=s(r[t],t);tR(e,n.x,n.y)}return e}(),d=V(function(e,n){var r=s(e,n);return u&&(r=l(Math.abs(t.spacingFactor),c,r)),null!=t.transform&&(r=t.transform(e,r)),r},o);if(t.animate){for(var h=0;h<r.length;h++){var p=r[h],f=d(p,h);if(null==t.animateFilter||t.animateFilter(p,h)){var g=p.animation({position:f,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(g)}else p.position(f)}if(t.fit){var v=i.animation({fit:{boundingBox:a.boundingBoxAt(d),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(v)}else if(void 0!==t.zoom&&void 0!==t.pan){var y=i.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(y)}e.animations.forEach(function(e){return e.play()}),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),rl.all(e.animations.map(function(e){return e.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else r.positions(d),t.fit&&i.fit(t.eles,t.padding),null!=t.zoom&&i.zoom(t.zoom),t.pan&&i.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){return this.cy().makeLayout(Z({},e,{eles:this}))}};function au(e,t,n){var r,i=n._private,a=i.styleCache=i.styleCache||[];return null!=(r=a[e])?r:r=a[e]=t(n)}function ac(e,t){return e=ej(e),function(n){return au(e,t,n)}}function ad(e,t){e=ej(e);var n=function(e){return t.call(e)};return function(){var t=this[0];if(t)return au(e,n,t)}}al.createLayout=al.makeLayout=al.layout;var ah={recalculateRenderedStyle:function(e){var t=this.cy(),n=t.renderer(),r=t.styleEnabled();return n&&r&&n.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e,t=this.cy(),n=function(e){return e._private.styleCache=null};return t.hasCompoundNodes()?((e=this.spawnSelf().merge(this.descendants()).merge(this.parents())).merge(e.connectedEdges()),e.forEach(n)):this.forEach(function(e){n(e),e.connectedEdges().forEach(n)}),this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching())return t._private.batchStyleEles.merge(this),this;var n=t.hasCompoundNodes(),r=this;e=!!e||void 0===e,n&&(r=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var i=r;return e?i.emitAndNotify("style"):i.emit("style"),r.forEach(function(e){return e._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var n=this[t];n._private.styleDirty&&(n._private.styleDirty=!1,e.style().apply(n))}},parsedStyle:function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=this[0],r=n.cy();if(r.styleEnabled()&&n){this.cleanStyle();var i=n._private.style[e];return null!=i?i:t?r.style().getDefaultProperty(e):null}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var n=t.pstyle(e);return void 0!==n.pfValue?n.pfValue:n.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled()&&t)return t.pstyle(e).units},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=this[0];if(n)return t.style().getRenderedStyle(n,e)},style:function(e,t){var n=this.cy();if(!n.styleEnabled())return this;var r=n.style();if(_(e))r.applyBypass(this,e,!1),this.emitAndNotify("style");else if(D(e)){if(void 0===t){var i=this[0];return i?r.getStylePropertyValue(i,e):void 0}r.applyBypass(this,e,t,!1),this.emitAndNotify("style")}else if(void 0===e){var a=this[0];return a?r.getRawStyle(a):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=t.style();if(void 0===e)for(var r=0;r<this.length;r++){var i=this[r];n.removeAllBypasses(i,!1)}else{e=e.split(/\s+/);for(var a=0;a<this.length;a++){var o=this[a];n.removeBypasses(o,e,!1)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),n=this[0];if(n){var r=n._private,i=n.pstyle("opacity").value;if(!t)return i;var a=r.data.parent?n.parents():null;if(a)for(var o=0;o<a.length;o++)i*=a[o].pstyle("opacity").value;return i}},transparent:function(){if(!this.cy().styleEnabled())return!1;var e=this[0],t=e.cy().hasCompoundNodes();if(e)return t?0===e.effectiveOpacity():0===e.pstyle("opacity").value},backgrounding:function(){return!!this.cy().styleEnabled()&&!!this[0]._private.backgrounding}};function ap(e,t){var n=e._private.data.parent?e.parents():null;if(n){for(var r=0;r<n.length;r++)if(!t(n[r]))return!1}return!0}function af(e){var t=e.ok,n=e.edgeOkViaNode||e.ok,r=e.parentOk||e.ok;return function(){var e=this.cy();if(!e.styleEnabled())return!0;var i=this[0],a=e.hasCompoundNodes();if(i){var o=i._private;if(!t(i))return!1;if(i.isNode())return!a||ap(i,r);var s=o.source,l=o.target;return n(s)&&(!a||ap(s,n))&&(s===l||n(l)&&(!a||ap(l,n)))}}}var ag=ac("eleTakesUpSpace",function(e){return"element"===e.pstyle("display").value&&0!==e.width()&&(!e.isNode()||0!==e.height())});ah.takesUpSpace=ad("takesUpSpace",af({ok:ag}));var av=ac("eleInteractive",function(e){return"yes"===e.pstyle("events").value&&"visible"===e.pstyle("visibility").value&&ag(e)}),ay=ac("parentInteractive",function(e){return"visible"===e.pstyle("visibility").value&&ag(e)});ah.interactive=ad("interactive",af({ok:av,parentOk:ay,edgeOkViaNode:ag})),ah.noninteractive=function(){var e=this[0];if(e)return!e.interactive()};var am=ac("eleVisible",function(e){return"visible"===e.pstyle("visibility").value&&0!==e.pstyle("opacity").pfValue&&ag(e)});ah.visible=ad("visible",af({ok:am,edgeOkViaNode:ag})),ah.hidden=function(){var e=this[0];if(e)return!e.visible()},ah.isBundledBezier=ad("isBundledBezier",function(){return!!this.cy().styleEnabled()&&!this.removed()&&"bezier"===this.pstyle("curve-style").value&&this.takesUpSpace()}),ah.bypass=ah.css=ah.style,ah.renderedCss=ah.renderedStyle,ah.removeBypass=ah.removeCss=ah.removeStyle,ah.pstyle=ah.parsedStyle;var ab={};function ax(e){return function(){var t=arguments,n=[];if(2===t.length){var r=t[0],i=t[1];this.on(e.event,r,i)}else if(1===t.length&&T(t[0])){var a=t[0];this.on(e.event,a)}else if(0===t.length||1===t.length&&P(t[0])){for(var o=1===t.length?t[0]:null,s=0;s<this.length;s++){var l=this[s],u=!e.ableField||l._private[e.ableField],c=l._private[e.field]!=e.value;if(e.overrideAble){var d=e.overrideAble(l);if(void 0!==d&&(u=d,!d))return this}u&&(l._private[e.field]=e.value,c&&n.push(l))}var h=this.spawn(n);h.updateStyle(),h.emit(e.event),o&&h.emit(o)}return this}}function aw(e){ab[e.field]=function(){var t=this[0];if(t){if(e.overrideField){var n=e.overrideField(t);if(void 0!==n)return n}return t._private[e.field]}},ab[e.on]=ax({event:e.on,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!0}),ab[e.off]=ax({event:e.off,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!1})}aw({field:"locked",overrideField:function(e){return!!e.cy().autolock()||void 0},on:"lock",off:"unlock"}),aw({field:"grabbable",overrideField:function(e){return!(e.cy().autoungrabify()||e.pannable())&&void 0},on:"grabify",off:"ungrabify"}),aw({field:"selected",ableField:"selectable",overrideAble:function(e){return!e.cy().autounselectify()&&void 0},on:"select",off:"unselect"}),aw({field:"selectable",overrideField:function(e){return!e.cy().autounselectify()&&void 0},on:"selectify",off:"unselectify"}),ab.deselect=ab.unselect,ab.grabbed=function(){var e=this[0];if(e)return e._private.grabbed},aw({field:"active",on:"activate",off:"unactivate"}),aw({field:"pannable",on:"panify",off:"unpanify"}),ab.inactive=function(){var e=this[0];if(e)return!e._private.active};var aE={},ak=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r];if(i.isNode()){for(var a=!1,o=i.connectedEdges(),s=0;s<o.length;s++){var l=o[s],u=l.source(),c=l.target();if(e.noIncomingEdges&&c===i&&u!==i||e.noOutgoingEdges&&u===i&&c!==i){a=!0;break}}a||n.push(i)}}return this.spawn(n,!0).filter(t)}},aC=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r];if(i.isNode())for(var a=i.connectedEdges(),o=0;o<a.length;o++){var s=a[o],l=s.source(),u=s.target();e.outgoing&&l===i?(n.push(s),n.push(u)):e.incoming&&u===i&&(n.push(s),n.push(l))}}return this.spawn(n,!0).filter(t)}},aS=function(e){return function(t){for(var n=this,r=[],i={};;){var a=e.outgoing?n.outgoers():n.incomers();if(0===a.length)break;for(var o=!1,s=0;s<a.length;s++){var l=a[s],u=l.id();i[u]||(i[u]=!0,r.push(l),o=!0)}if(!o)break;n=a}return this.spawn(r,!0).filter(t)}};function aD(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r]._private[e.attr];i&&n.push(i)}return this.spawn(n,!0).filter(t)}}function aT(e){return function(t){var n=[],r=this._private.cy,i=e||{};D(t)&&(t=r.$(t));for(var a=0;a<t.length;a++)for(var o=t[a]._private.edges,s=0;s<o.length;s++){var l=o[s],u=l._private.data,c=this.hasElementWithId(u.source)&&t.hasElementWithId(u.target),d=t.hasElementWithId(u.source)&&this.hasElementWithId(u.target);(c||d)&&(!i.thisIsSrc&&!i.thisIsTgt||(!i.thisIsSrc||c)&&(!i.thisIsTgt||d))&&n.push(l)}return this.spawn(n,!0)}}function aP(e){return e=Z({},{codirected:!1},e),function(t){for(var n=[],r=this.edges(),i=e,a=0;a<r.length;a++)for(var o=r[a]._private,s=o.source,l=s._private.data.id,u=o.data.target,c=s._private.edges,d=0;d<c.length;d++){var h=c[d],p=h._private.data,f=p.target,g=p.source,v=f===u&&g===l,y=l===f&&u===g;(i.codirected&&v||!i.codirected&&(v||y))&&n.push(h)}return this.spawn(n,!0).filter(t)}}aE.clearTraversalCache=function(){for(var e=0;e<this.length;e++)this[e]._private.traversalCache=null},Z(aE,{roots:ak({noIncomingEdges:!0}),leaves:ak({noOutgoingEdges:!0}),outgoers:iw(aC({outgoing:!0}),"outgoers"),successors:aS({outgoing:!0}),incomers:iw(aC({incoming:!0}),"incomers"),predecessors:aS({incoming:!0})}),Z(aE,{neighborhood:iw(function(e){for(var t=[],n=this.nodes(),r=0;r<n.length;r++)for(var i=n[r],a=i.connectedEdges(),o=0;o<a.length;o++){var s=a[o],l=s.source(),u=s.target(),c=i===l?u:l;c.length>0&&t.push(c[0]),t.push(s[0])}return this.spawn(t,!0).filter(e)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),aE.neighbourhood=aE.neighborhood,aE.closedNeighbourhood=aE.closedNeighborhood,aE.openNeighbourhood=aE.openNeighborhood,Z(aE,{source:iw(function(e){var t,n=this[0];return n&&(t=n._private.source||n.cy().collection()),t&&e?t.filter(e):t},"source"),target:iw(function(e){var t,n=this[0];return n&&(t=n._private.target||n.cy().collection()),t&&e?t.filter(e):t},"target"),sources:aD({attr:"source"}),targets:aD({attr:"target"})}),Z(aE,{edgesWith:iw(aT(),"edgesWith"),edgesTo:iw(aT({thisIsSrc:!0}),"edgesTo")}),Z(aE,{connectedEdges:iw(function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];if(r.isNode())for(var i=r._private.edges,a=0;a<i.length;a++){var o=i[a];t.push(o)}}return this.spawn(t,!0).filter(e)},"connectedEdges"),connectedNodes:iw(function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];r.isEdge()&&(t.push(r.source()[0]),t.push(r.target()[0]))}return this.spawn(t,!0).filter(e)},"connectedNodes"),parallelEdges:iw(aP(),"parallelEdges"),codirectedEdges:iw(aP({codirected:!0}),"codirectedEdges")}),Z(aE,{components:function(e){var t=this,n=t.cy(),r=n.collection(),i=null==e?t.nodes():e.nodes(),a=[];null!=e&&i.empty()&&(i=e.sources());var o=function(e,t){r.merge(e),i.unmerge(e),t.merge(e)};if(i.empty())return t.spawn();do!function(){var e=n.collection();a.push(e);var r=i[0];o(r,e),t.bfs({directed:!1,roots:r,visit:function(t){return o(t,e)}}),e.forEach(function(n){n.connectedEdges().forEach(function(n){t.has(n)&&e.has(n.source())&&e.has(n.target())&&e.merge(n)})})}();while(i.length>0);return a},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}}),aE.componentsOf=aE.components;var a_=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(void 0===e){eQ("A collection must have a reference to the core");return}var i=new tn,a=!1;if(t){if(t.length>0&&_(t[0])&&!A(t[0])){a=!0;for(var o=[],s=new ti,l=0,u=t.length;l<u;l++){var c=t[l];null==c.data&&(c.data={});var d=c.data;if(null==d.id)d.id=e2();else if(e.hasElementWithId(d.id)||s.has(d.id))continue;var h=new ta(e,c,!1);o.push(h),s.add(d.id)}t=o}}else t=[];this.length=0;for(var p=0,f=t.length;p<f;p++){var g=t[p][0];if(null!=g){var v=g._private.data.id;(!n||!i.has(v))&&(n&&i.set(v,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return null==this.lazyMap&&this.rebuildMap(),this.lazyMap},set map(m){this.lazyMap=m},rebuildMap:function(){for(var e=this.lazyMap=new tn,t=this.eles,n=0;n<t.length;n++){var r=t[n];e.set(r.id(),{index:n,ele:r})}}},n&&(this._private.map=i),a&&!r&&this.restore()},aM=ta.prototype=a_.prototype=Object.create(Array.prototype);aM.instanceString=function(){return"collection"},aM.spawn=function(e,t){return new a_(this.cy(),e,t)},aM.spawnSelf=function(){return this.spawn(this)},aM.cy=function(){return this._private.cy},aM.renderer=function(){return this._private.cy.renderer()},aM.element=function(){return this[0]},aM.collection=function(){return I(this)?this:new a_(this._private.cy,[this])},aM.unique=function(){return new a_(this._private.cy,this,!0)},aM.hasElementWithId=function(e){return e=""+e,this._private.map.has(e)},aM.getElementById=function(e){e=""+e;var t=this._private.cy,n=this._private.map.get(e);return n?n.ele:new a_(t)},aM.$id=aM.getElementById,aM.poolIndex=function(){var e=this._private.cy._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index},aM.indexOf=function(e){var t=e[0]._private.data.id;return this._private.map.get(t).index},aM.indexOfId=function(e){return e=""+e,this._private.map.get(e).index},aM.json=function(e){var t=this.element(),n=this.cy();if(null==t&&e)return this;if(null!=t){var r=t._private;if(_(e)){if(n.startBatch(),e.data){t.data(e.data);var i=r.data;if(t.isEdge()){var a=!1,o={},s=e.data.source,l=e.data.target;null!=s&&s!=i.source&&(o.source=""+s,a=!0),null!=l&&l!=i.target&&(o.target=""+l,a=!0),a&&(t=t.move(o))}else{var u="parent"in e.data,c=e.data.parent;u&&(null!=c||null!=i.parent)&&c!=i.parent&&(void 0===c&&(c=null),null!=c&&(c=""+c),t=t.move({parent:c}))}}e.position&&t.position(e.position);var d=function(n,i,a){var o=e[n];null!=o&&o!==r[n]&&(o?t[i]():t[a]())};return d("removed","remove","restore"),d("selected","select","unselect"),d("selectable","selectify","unselectify"),d("locked","lock","unlock"),d("grabbable","grabify","ungrabify"),d("pannable","panify","unpanify"),null!=e.classes&&t.classes(e.classes),n.endBatch(),this}if(void 0===e){var h={data:e1(r.data),position:e1(r.position),group:r.group,removed:r.removed,selected:r.selected,selectable:r.selectable,locked:r.locked,grabbable:r.grabbable,pannable:r.pannable,classes:null};h.classes="";var p=0;return r.classes.forEach(function(e){return h.classes+=0==p++?e:" "+e}),h}}},aM.jsons=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t].json();e.push(n)}return e},aM.clone=function(){for(var e=this.cy(),t=[],n=0;n<this.length;n++){var r=this[n].json(),i=new ta(e,r,!1);t.push(i)}return new a_(e,t)},aM.copy=aM.clone,aM.restore=function(){for(var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=this.cy(),r=n._private,i=[],a=[],o=0,s=this.length;o<s;o++){var l=this[o];(!t||l.removed())&&(l.isNode()?i.push(l):a.push(l))}I=i.concat(a);var u=function(){I.splice(O,1),O--};for(O=0;O<I.length;O++){var c=I[O],d=c._private,h=d.data;if(c.clearTraversalCache(),t||d.removed){if(void 0===h.id)h.id=e2();else if(M(h.id))h.id=""+h.id;else if(z(h.id)||!D(h.id)){eQ("Can not create element with invalid string ID `"+h.id+"`"),u();continue}else if(n.hasElementWithId(h.id)){eQ("Can not create second element with ID `"+h.id+"`"),u();continue}}var p=h.id;if(c.isNode()){var f=d.position;null==f.x&&(f.x=0),null==f.y&&(f.y=0)}if(c.isEdge()){for(var g=c,v=["source","target"],y=v.length,b=!1,x=0;x<y;x++){var w=v[x],E=h[w];M(E)&&(E=h[w]=""+h[w]),null==E||""===E?(eQ("Can not create edge `"+p+"` with unspecified "+w),b=!0):n.hasElementWithId(E)||(eQ("Can not create edge `"+p+"` with nonexistant "+w+" `"+E+"`"),b=!0)}if(b){u();continue}var k=n.getElementById(h.source),C=n.getElementById(h.target);k.same(C)?k._private.edges.push(g):(k._private.edges.push(g),C._private.edges.push(g)),g._private.source=k,g._private.target=C}d.map=new tn,d.map.set(p,{ele:c,index:0}),d.removed=!1,t&&n.addToPool(c)}for(var S=0;S<i.length;S++){var T=i[S],P=T._private.data;M(P.parent)&&(P.parent=""+P.parent);var _=P.parent;if(null!=_||T._private.parent){var B=T._private.parent?n.collection().merge(T._private.parent):n.getElementById(_);if(B.empty())P.parent=void 0;else if(B[0].removed())e0("Node added with missing parent, reference to parent removed"),P.parent=void 0,T._private.parent=null;else{for(var N=!1,A=B;!A.empty();){if(T.same(A)){N=!0,P.parent=void 0;break}A=A.parent()}N||(B[0]._private.children.push(T),T._private.parent=B[0],r.hasCompoundNodes=!0)}}}if(I.length>0){for(var I,O,R=I.length===this.length?this:new a_(n,I),L=0;L<R.length;L++){var V=R[L];V.isNode()||(V.parallelEdges().clearTraversalCache(),V.source().clearTraversalCache(),V.target().clearTraversalCache())}(r.hasCompoundNodes?n.collection().merge(R).merge(R.connectedNodes()).merge(R.parent()):R).dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(e),e?R.emitAndNotify("add"):t&&R.emit("add")}return this},aM.removed=function(){var e=this[0];return e&&e._private.removed},aM.inside=function(){var e=this[0];return e&&!e._private.removed},aM.remove=function(){for(var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=[],r={},i=this._private.cy,a=0,o=this.length;a<o;a++)(function e(i){var a=r[i.id()];t&&i.removed()||a||(r[i.id()]=!0,i.isNode()?(n.push(i),function(t){for(var n=t._private.edges,r=0;r<n.length;r++)e(n[r])}(i),function(t){for(var n=t._private.children,r=0;r<n.length;r++)e(n[r])}(i)):n.unshift(i))})(this[a]);function s(e,t){e9(e._private.edges,t),e.clearTraversalCache()}var l=[];l.ids={},this.dirtyCompoundBoundsCache(),t&&i.removeFromPool(n);for(var u=0;u<n.length;u++){var c=n[u];if(c.isEdge()){var d=c.source()[0],h=c.target()[0];s(d,c),s(h,c);for(var p=c.parallelEdges(),f=0;f<p.length;f++){var g=p[f];(function(e){e.clearTraversalCache()})(g),g.isBundledBezier()&&g.dirtyBoundingBoxCache()}}else{var v=c.parent();0!==v.length&&function(e,t){t=t[0];var n=(e=e[0])._private.children,r=e.id();e9(n,t),t._private.parent=null,l.ids[r]||(l.ids[r]=!0,l.push(e))}(v,c)}t&&(c._private.removed=!0)}var y=i._private.elements;i._private.hasCompoundNodes=!1;for(var b=0;b<y.length;b++)if(y[b].isParent()){i._private.hasCompoundNodes=!0;break}var x=new a_(this.cy(),n);x.size()>0&&(e?x.emitAndNotify("remove"):t&&x.emit("remove"));for(var w=0;w<l.length;w++){var E=l[w];t&&E.removed()||E.updateStyle()}return x},aM.move=function(e){var t=this._private.cy,n=this,r=function(e){return null==e?e:""+e};if(void 0!==e.source||void 0!==e.target){var i=r(e.source),a=r(e.target),o=null!=i&&t.hasElementWithId(i),s=null!=a&&t.hasElementWithId(a);(o||s)&&(t.batch(function(){n.remove(!1,!1),n.emitAndNotify("moveout");for(var e=0;e<n.length;e++){var t=n[e],r=t._private.data;t.isEdge()&&(o&&(r.source=i),s&&(r.target=a))}n.restore(!1,!1)}),n.emitAndNotify("move"))}else if(void 0!==e.parent){var l=r(e.parent);if(null===l||t.hasElementWithId(l)){var u=null===l?void 0:l;t.batch(function(){var e=n.remove(!1,!1);e.emitAndNotify("moveout");for(var t=0;t<n.length;t++){var r=n[t],i=r._private.data;r.isNode()&&(i.parent=u)}e.restore(!1,!1)}),n.emitAndNotify("move")}}return this},[rt,r8,r7,ix,iE,oK,iT,i$,an,ar,{isNode:function(){return"nodes"===this.group()},isEdge:function(){return"edges"===this.group()},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},ao,al,ah,ab,aE].forEach(function(e){Z(aM,e)});/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */var aB=function(){function e(e){return-e.tension*e.x-e.friction*e.v}function t(t,n,r){var i={x:t.x+r.dx*n,v:t.v+r.dv*n,tension:t.tension,friction:t.friction};return{dx:i.v,dv:e(i)}}return function n(r,i,a){var o,s,l,u,c,d,h,p,f,g={x:-1,v:0,tension:null,friction:null},v=[0],y=0;for(r=parseFloat(r)||500,i=parseFloat(i)||20,a=a||null,g.tension=r,g.friction=i,f=(p=null!==a)?(y=n(r,i))/a*.016:.016;s={dx:(o=last_state||g).v,dv:e(o)},l=t(o,.5*f,s),u=t(o,.5*f,l),c=t(o,f,u),d=1/6*(s.dx+2*(l.dx+u.dx)+c.dx),h=1/6*(s.dv+2*(l.dv+u.dv)+c.dv),o.x=o.x+d*f,o.v=o.v+h*f,v.push(1+o.x),y+=16,Math.abs(o.x)>1e-4&&Math.abs(o.v)>1e-4;);return p?function(e){return v[e*(v.length-1)|0]}:y}}(),aN=function(e,t,n,r){var i=/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */function(e,t,n,r){var i="undefined"!=typeof Float32Array;if(4!=arguments.length)return!1;for(var a=0;a<4;++a)if("number"!=typeof arguments[a]||isNaN(arguments[a])||!isFinite(arguments[a]))return!1;e=Math.min(e,1),n=Math.min(n,1),e=Math.max(e,0),n=Math.max(n,0);var o=i?new Float32Array(11):Array(11);function s(e,t,n){return(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e}function l(e,t,n){return 3*(1-3*n+3*t)*e*e+2*(3*n-6*t)*e+3*t}var u=!1,c=function(i){return(u||(u=!0,(e!==t||n!==r)&&function(){for(var t=0;t<11;++t)o[t]=s(.1*t,e,n)}()),e===t&&n===r)?i:0===i?0:1===i?1:s(function(t){for(var r=0,i=1;10!==i&&o[i]<=t;++i)r+=.1;var a=r+(t-o[--i])/(o[i+1]-o[i])*.1,u=l(a,e,n);return u>=.001?function(t,r){for(var i=0;i<4;++i){var a=l(r,e,n);if(0===a)break;var o=s(r,e,n)-t;r-=o/a}return r}(t,a):0===u?a:function(t,r,i){var a,o,l=0;do(a=s(o=r+(i-r)/2,e,n)-t)>0?i=o:r=o;while(Math.abs(a)>1e-7&&++l<10);return o}(t,r,r+.1)}(i),t,r)};c.getControlPoints=function(){return[{x:e,y:t},{x:n,y:r}]};var d="generateBezier("+[e,t,n,r]+")";return c.toString=function(){return d},c}(e,t,n,r);return function(e,t,n){return e+(t-e)*i(n)}},aA={linear:function(e,t,n){return e+(t-e)*n},ease:aN(.25,.1,.25,1),"ease-in":aN(.42,0,1,1),"ease-out":aN(0,0,.58,1),"ease-in-out":aN(.42,0,.58,1),"ease-in-sine":aN(.47,0,.745,.715),"ease-out-sine":aN(.39,.575,.565,1),"ease-in-out-sine":aN(.445,.05,.55,.95),"ease-in-quad":aN(.55,.085,.68,.53),"ease-out-quad":aN(.25,.46,.45,.94),"ease-in-out-quad":aN(.455,.03,.515,.955),"ease-in-cubic":aN(.55,.055,.675,.19),"ease-out-cubic":aN(.215,.61,.355,1),"ease-in-out-cubic":aN(.645,.045,.355,1),"ease-in-quart":aN(.895,.03,.685,.22),"ease-out-quart":aN(.165,.84,.44,1),"ease-in-out-quart":aN(.77,0,.175,1),"ease-in-quint":aN(.755,.05,.855,.06),"ease-out-quint":aN(.23,1,.32,1),"ease-in-out-quint":aN(.86,0,.07,1),"ease-in-expo":aN(.95,.05,.795,.035),"ease-out-expo":aN(.19,1,.22,1),"ease-in-out-expo":aN(1,0,0,1),"ease-in-circ":aN(.6,.04,.98,.335),"ease-out-circ":aN(.075,.82,.165,1),"ease-in-out-circ":aN(.785,.135,.15,.86),spring:function(e,t,n){if(0===n)return aA.linear;var r=aB(e,t,n);return function(e,t,n){return e+(t-e)*r(n)}},"cubic-bezier":aN};function aI(e,t,n,r,i){if(1===r||t===n)return n;var a=i(t,n,r);return null==e||((e.roundValue||e.color)&&(a=Math.round(a)),void 0!==e.min&&(a=Math.max(a,e.min)),void 0!==e.max&&(a=Math.min(a,e.max))),a}function aO(e,t){return null==e.pfValue&&null==e.value?e:null!=e.pfValue&&(null==t||"%"!==t.type.units)?e.pfValue:e.value}function aR(e,t,n,r,i){var a=null!=i?i.type:null;n<0?n=0:n>1&&(n=1);var o=aO(e,i),s=aO(t,i);if(M(o)&&M(s))return aI(a,o,s,n,r);if(P(o)&&P(s)){for(var l=[],u=0;u<s.length;u++){var c=o[u],d=s[u];if(null!=c&&null!=d){var h=aI(a,c,d,n,r);l.push(h)}else l.push(d)}return l}}function az(e,t){return null!=e&&null!=t&&(!!(M(e)&&M(t))||!!e&&!!t)}function aL(e,t){var n=t._private.aniEles,r=[];function i(t,n){var i=t._private,a=i.animation.current,o=i.animation.queue,s=!1;if(0===a.length){var l=o.shift();l&&a.push(l)}for(var u=function(e){for(var t=e.length-1;t>=0;t--)(0,e[t])();e.splice(0,e.length)},c=a.length-1;c>=0;c--){var d=a[c],h=d._private;if(h.stopped){a.splice(c,1),h.hooked=!1,h.playing=!1,h.started=!1,u(h.frames);continue}(h.playing||h.applying)&&(h.playing&&h.applying&&(h.applying=!1),h.started||function(e,t,n,r){var i=t._private;i.started=!0,i.startTime=n-i.progress*i.duration}(0,d,e),function(e,t,n,r){var i,a,o,s,l=!r,u=e._private,c=t._private,d=c.easing,h=c.startTime,p=(r?e:e.cy()).style();c.easingImpl||(null==d?c.easingImpl=aA.linear:(i=D(d)?p.parse("transition-timing-function",d).value:d,D(i)?(a=i,o=[]):(a=i[1],o=i.slice(2).map(function(e){return+e})),o.length>0?("spring"===a&&o.push(c.duration),c.easingImpl=aA[a].apply(null,o)):c.easingImpl=aA[a]));var f=c.easingImpl;if(s=0===c.duration?1:(n-h)/c.duration,c.applying&&(s=c.progress),s<0?s=0:s>1&&(s=1),null==c.delay){var g=c.startPosition,v=c.position;if(v&&l&&!e.locked()){var y={};az(g.x,v.x)&&(y.x=aR(g.x,v.x,s,f)),az(g.y,v.y)&&(y.y=aR(g.y,v.y,s,f)),e.position(y)}var b=c.startPan,x=c.pan,w=u.pan,E=null!=x&&r;E&&(az(b.x,x.x)&&(w.x=aR(b.x,x.x,s,f)),az(b.y,x.y)&&(w.y=aR(b.y,x.y,s,f)),e.emit("pan"));var k=c.startZoom,C=c.zoom,S=null!=C&&r;S&&(az(k,C)&&(u.zoom=tN(u.minZoom,aR(k,C,s,f),u.maxZoom)),e.emit("zoom")),(E||S)&&e.emit("viewport");var T=c.style;if(T&&T.length>0&&l){for(var P=0;P<T.length;P++){var _=T[P],M=_.name,B=c.startStyle[M],N=p.properties[B.name],A=aR(B,_,s,f,N);p.overrideBypass(e,M,A)}e.emit("style")}}c.progress=s}(t,d,e,n),h.applying&&(h.applying=!1),u(h.frames),null!=h.step&&h.step(e),d.completed()&&(a.splice(c,1),h.hooked=!1,h.playing=!1,h.started=!1,u(h.completes)),s=!0)}return n||0!==a.length||0!==o.length||r.push(t),s}for(var a=!1,o=0;o<n.length;o++){var s=i(n[o]);a=a||s}var l=i(t,!0);(a||l)&&(n.length>0?t.notify("draw",n):t.notify("draw")),n.unmerge(r),t.emit("step")}var aV={animate:r6.animate(),animation:r6.animation(),animated:r6.animated(),clearQueue:r6.clearQueue(),delay:r6.delay(),delayAnimation:r6.delayAnimation(),stop:r6.stop(),addToAnimationPool:function(e){this.styleEnabled()&&this._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,e.styleEnabled()){var t=e.renderer();t&&t.beforeRender?t.beforeRender(function(t,n){aL(n,e)},t.beforeRenderPriorities.animations):function t(){e._private.animationsRunning&&eI(function(n){aL(n,e),t()})}()}}},aF={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&A(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e}},aj=function(e){return D(e)?new im(e):e},aq={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new i4(aF,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,n){return this.emitter().on(e,aj(t),n),this},removeListener:function(e,t,n){return this.emitter().removeListener(e,aj(t),n),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,n){return this.emitter().one(e,aj(t),n),this},once:function(e,t,n){return this.emitter().one(e,aj(t),n),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};r6.eventAliasesOn(aq);var aX={png:function(e){return e=e||{},this._private.renderer.png(e)},jpg:function(e){var t=this._private.renderer;return(e=e||{}).bg=e.bg||"#fff",t.jpg(e)}};aX.jpeg=aX.jpg;var aY={layout:function(e){if(null==e){eQ("Layout options must be specified to make a layout");return}if(null==e.name){eQ("A `name` must be specified to make a layout");return}var t,n=e.name,r=this.extension("layout",n);if(null==r){eQ("No such layout `"+n+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}return t=D(e.eles)?this.$(e.eles):null!=e.eles?e.eles:this.$(),new r(Z({},e,{cy:this,eles:t}))}};aY.createLayout=aY.makeLayout=aY.layout;var aW=e4({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1}),aH={renderTo:function(e,t,n,r){return this._private.renderer.renderTo(e,t,n,r),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,n=t.extension("renderer",e.name);if(null==n){eQ("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}void 0!==e.wheelSensitivity&&e0("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var r=aW(e);r.cy=t,t._private.renderer=new n(r),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(e){var t=e._private;t.rscratch={},t.rstyle={},t.animation.current=[],t.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};aH.invalidateDimensions=aH.resize;var aG={collection:function(e,t){return D(e)?this.$(e):N(e)?e.collection():P(e)?(t||(t={}),new a_(this,e,t.unique,t.removed)):new a_(this)},nodes:function(e){var t=this.$(function(e){return e.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(e){return e.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};aG.elements=aG.filter=aG.$;var aU={};aU.apply=function(e){for(var t=this._private.cy.collection(),n=0;n<e.length;n++){var r=e[n],i=this.getContextMeta(r);if(!i.empty){var a=this.getContextStyle(i),o=this.applyContextStyle(i,a,r);r._private.appliedInitStyle?this.updateTransitions(r,o.diffProps):r._private.appliedInitStyle=!0,this.updateStyleHints(r)&&t.push(r)}}return t},aU.getPropertiesDiff=function(e,t){var n=this,r=n._private.propDiffs=n._private.propDiffs||{},i=e+"-"+t,a=r[i];if(a)return a;for(var o=[],s={},l=0;l<n.length;l++){var u=n[l],c="t"===e[l],d="t"===t[l],h=c!==d,p=u.mappedProperties.length>0;if(h||d&&p){var f=void 0;h&&p?f=u.properties:h?f=u.properties:p&&(f=u.mappedProperties);for(var g=0;g<f.length;g++){for(var v=f[g],y=v.name,b=!1,x=l+1;x<n.length;x++){var w=n[x];if("t"===t[x]&&(b=null!=w.properties[v.name]))break}s[y]||b||(s[y]=!0,o.push(y))}}}return r[i]=o,o},aU.getContextMeta=function(e){for(var t,n="",r=e._private.styleCxtKey||"",i=0;i<this.length;i++){var a=this[i];a.selector&&a.selector.matches(e)?n+="t":n+="f"}return t=this.getPropertiesDiff(r,n),e._private.styleCxtKey=n,{key:n,diffPropNames:t,empty:0===t.length}},aU.getContextStyle=function(e){var t=e.key,n=this._private.contextStyles=this._private.contextStyles||{};if(n[t])return n[t];for(var r={_private:{key:t}},i=0;i<this.length;i++){var a=this[i];if("t"===t[i])for(var o=0;o<a.properties.length;o++){var s=a.properties[o];r[s.name]=s}}return n[t]=r,r},aU.applyContextStyle=function(e,t,n){for(var r=e.diffPropNames,i={},a=this.types,o=0;o<r.length;o++){var s=r[o],l=t[s],u=n.pstyle(s);if(!l){if(!u)continue;l=u.bypass?{name:s,deleteBypassed:!0}:{name:s,delete:!0}}if(u!==l){if(l.mapped===a.fn&&null!=u&&null!=u.mapping&&u.mapping.value===l.value){var c=u.mapping;if((c.fnValue=l.value(n))===c.prevFnValue)continue}var d=i[s]={prev:u};this.applyParsedProperty(n,l),d.next=n.pstyle(s),d.next&&d.next.bypass&&(d.next=d.next.bypassed)}}return{diffProps:i}},aU.updateStyleHints=function(e){var t=e._private,n=this,r=n.propertyGroupNames,i=n.propertyGroupKeys,a=function(e,t,r){return n.getPropertiesHash(e,t,r)},o=t.styleKey;if(e.removed())return!1;var s="nodes"===t.group,l=e._private.style;r=Object.keys(l);for(var u=0;u<i.length;u++){var c=i[u];t.styleKeys[c]=[9261,5381]}for(var d=function(e,n){return t.styleKeys[n][0]=eR(e,t.styleKeys[n][0])},h=function(e,n){return t.styleKeys[n][1]=ez(e,t.styleKeys[n][1])},p=function(e,t){for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);d(r,t),h(r,t)}},f=0;f<r.length;f++){var g,v,y=r[f],b=l[y];if(null!=b){var x=this.properties[y],w=x.type,E=x.groupKey,k=void 0;null!=x.hashOverride?k=x.hashOverride(e,b):null!=b.pfValue&&(k=b.pfValue);var C=null==x.enums?b.value:null,S=null!=k,D=null!=C,T=S||D,P=b.units;w.number&&T&&!w.multiple?(d(v=-128<(g=S?k:C)&&g<128&&Math.floor(g)!==g?2e9-(1024*g|0):g,E),h(v,E),S||null==P||p(P,E)):p(b.strValue,E)}}for(var _=[9261,5381],M=0;M<i.length;M++){var B=i[M],N=t.styleKeys[B];_[0]=eR(N[0],_[0]),_[1]=ez(N[1],_[1])}t.styleKey=2097152*_[0]+_[1];var A=t.styleKeys;t.labelDimsKey=eL(A.labelDimensions);var I=a(e,["label"],A.labelDimensions);if(t.labelKey=eL(I),t.labelStyleKey=eL(eV(A.commonLabel,I)),!s){var O=a(e,["source-label"],A.labelDimensions);t.sourceLabelKey=eL(O),t.sourceLabelStyleKey=eL(eV(A.commonLabel,O));var R=a(e,["target-label"],A.labelDimensions);t.targetLabelKey=eL(R),t.targetLabelStyleKey=eL(eV(A.commonLabel,R))}if(s){var z=t.styleKeys,L=z.nodeBody,V=z.nodeBorder,F=z.nodeOutline,j=z.backgroundImage,q=z.compound,X=z.pie,Y=[L,V,F,j,q,X].filter(function(e){return null!=e}).reduce(eV,[9261,5381]);t.nodeKey=eL(Y),t.hasPie=null!=X&&9261!==X[0]&&5381!==X[1]}return o!==t.styleKey},aU.clearStyleHints=function(e){var t=e._private;t.styleCxtKey="",t.styleKeys={},t.styleKey=null,t.labelKey=null,t.labelStyleKey=null,t.sourceLabelKey=null,t.sourceLabelStyleKey=null,t.targetLabelKey=null,t.targetLabelStyleKey=null,t.nodeKey=null,t.hasPie=null},aU.applyParsedProperty=function(e,t){var n=this,r=t,i=e._private.style,a=n.types,o=n.properties[r.name].type,s=r.bypass,l=i[r.name],u=l&&l.bypass,c=e._private,d="mapping",h=function(e){return null==e?null:null!=e.pfValue?e.pfValue:e.value},p=function(){var t=h(l),i=h(r);n.checkTriggers(e,r.name,t,i)};if("curve-style"===t.name&&e.isEdge()&&("bezier"!==t.value&&e.isLoop()||"haystack"===t.value&&(e.source().isParent()||e.target().isParent()))&&(r=t=this.parse(t.name,"bezier",s)),r.delete)return i[r.name]=void 0,p(),!0;if(r.deleteBypassed)return l?!!l.bypass&&(l.bypassed=void 0,p(),!0):(p(),!0);if(r.deleteBypass)return l?!!l.bypass&&(i[r.name]=l.bypassed,p(),!0):(p(),!0);var f=function(){e0("Do not assign mappings to elements without corresponding data (i.e. ele `"+e.id()+"` has no mapping for property `"+r.name+"` with data field `"+r.field+"`); try a `["+r.field+"]` selector to limit scope to elements with `"+r.field+"` defined")};switch(r.mapped){case a.mapData:for(var g,v,y=r.field.split("."),b=c.data,x=0;x<y.length&&b;x++)b=b[y[x]];if(null==b)return f(),!1;if(!M(b))return e0("Do not use continuous mappers without specifying numeric data (i.e. `"+r.field+": "+b+"` for `"+e.id()+"` is non-numeric)"),!1;var w=r.fieldMax-r.fieldMin;if((v=0===w?0:(b-r.fieldMin)/w)<0?v=0:v>1&&(v=1),o.color){var E=r.valueMin[0],k=r.valueMax[0],C=r.valueMin[1],S=r.valueMax[1],D=r.valueMin[2],T=r.valueMax[2],P=null==r.valueMin[3]?1:r.valueMin[3],_=[Math.round(E+(k-E)*v),Math.round(C+(S-C)*v),Math.round(D+(T-D)*v),Math.round(P+((null==r.valueMax[3]?1:r.valueMax[3])-P)*v)];g={bypass:r.bypass,name:r.name,value:_,strValue:"rgb("+_[0]+", "+_[1]+", "+_[2]+")"}}else{if(!o.number)return!1;var B=r.valueMin+(r.valueMax-r.valueMin)*v;g=this.parse(r.name,B,r.bypass,d)}if(!g)return f(),!1;g.mapping=r,r=g;break;case a.data:for(var N=r.field.split("."),A=c.data,I=0;I<N.length&&A;I++)A=A[N[I]];if(null!=A&&(g=this.parse(r.name,A,r.bypass,d)),!g)return f(),!1;g.mapping=r,r=g;break;case a.fn:var O=r.value,R=null!=r.fnValue?r.fnValue:O(e);if(r.prevFnValue=R,null==R)return e0("Custom function mappers may not return null (i.e. `"+r.name+"` for ele `"+e.id()+"` is null)"),!1;if(!(g=this.parse(r.name,R,r.bypass,d)))return e0("Custom function mappers may not return invalid values for the property type (i.e. `"+r.name+"` for ele `"+e.id()+"` is invalid)"),!1;g.mapping=e1(r),r=g;break;case void 0:break;default:return!1}return s?(u?r.bypassed=l.bypassed:r.bypassed=l,i[r.name]=r):u?l.bypassed=r:i[r.name]=r,p(),!0},aU.cleanElements=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(this.clearStyleHints(r),r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache(),t)for(var i=r._private.style,a=Object.keys(i),o=0;o<a.length;o++){var s=a[o],l=i[s];null!=l&&(l.bypass?l.bypassed=null:i[s]=null)}else r._private.style={}}},aU.update=function(){this._private.cy.mutableElements().updateStyle()},aU.updateTransitions=function(e,t){var n=this,r=e._private,i=e.pstyle("transition-property").value,a=e.pstyle("transition-duration").pfValue,o=e.pstyle("transition-delay").pfValue;if(i.length>0&&a>0){for(var s={},l=!1,u=0;u<i.length;u++){var c=i[u],d=e.pstyle(c),h=t[c];if(h){var p=h.prev,f=null!=h.next?h.next:d,g=!1,v=void 0;p&&(M(p.pfValue)&&M(f.pfValue)?(g=f.pfValue-p.pfValue,v=p.pfValue+1e-6*g):M(p.value)&&M(f.value)?(g=f.value-p.value,v=p.value+1e-6*g):P(p.value)&&P(f.value)&&(g=p.value[0]!==f.value[0]||p.value[1]!==f.value[1]||p.value[2]!==f.value[2],v=p.strValue),g&&(s[c]=f.strValue,this.applyBypass(e,c,v),l=!0))}}if(!l)return;r.transitioning=!0,new rl(function(t){o>0?e.delayAnimation(o).play().promise().then(t):t()}).then(function(){return e.animation({style:s,duration:a,easing:e.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){n.removeBypasses(e,i),e.emitAndNotify("style"),r.transitioning=!1})}else r.transitioning&&(this.removeBypasses(e,i),e.emitAndNotify("style"),r.transitioning=!1)},aU.checkTrigger=function(e,t,n,r,i,a){var o=this.properties[t],s=i(o);null!=s&&s(n,r)&&a(o)},aU.checkZOrderTrigger=function(e,t,n,r){var i=this;this.checkTrigger(e,t,n,r,function(e){return e.triggersZOrder},function(){i._private.cy.notify("zorder",e)})},aU.checkBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,function(e){return e.triggersBounds},function(i){e.dirtyCompoundBoundsCache(),e.dirtyBoundingBoxCache(),i.triggersBoundsOfParallelBeziers&&"curve-style"===t&&("bezier"===n||"bezier"===r)&&e.parallelEdges().forEach(function(e){e.dirtyBoundingBoxCache()}),i.triggersBoundsOfConnectedEdges&&"display"===t&&("none"===n||"none"===r)&&e.connectedEdges().forEach(function(e){e.dirtyBoundingBoxCache()})})},aU.checkTriggers=function(e,t,n,r){e.dirtyStyleCache(),this.checkZOrderTrigger(e,t,n,r),this.checkBoundsTrigger(e,t,n,r)};var aK={};aK.applyBypass=function(e,t,n,r){var i=[];if("*"===t||"**"===t){if(void 0!==n)for(var a=0;a<this.properties.length;a++){var o=this.properties[a].name,s=this.parse(o,n,!0);s&&i.push(s)}}else if(D(t)){var l=this.parse(t,n,!0);l&&i.push(l)}else{if(!_(t))return!1;r=n;for(var u=Object.keys(t),c=0;c<u.length;c++){var d=u[c],h=t[d];if(void 0===h&&(h=t[j(d)]),void 0!==h){var p=this.parse(d,h,!0);p&&i.push(p)}}}if(0===i.length)return!1;for(var f=!1,g=0;g<e.length;g++){for(var v=e[g],y={},b=void 0,x=0;x<i.length;x++){var w=i[x];if(r){var E=v.pstyle(w.name);b=y[w.name]={prev:E}}f=this.applyParsedProperty(v,e1(w))||f,r&&(b.next=v.pstyle(w.name))}f&&this.updateStyleHints(v),r&&this.updateTransitions(v,y,!0)}return f},aK.overrideBypass=function(e,t,n){t=F(t);for(var r=0;r<e.length;r++){var i=e[r],a=i._private.style[t],o=this.properties[t].type,s=o.color,l=o.mutiple,u=a?null!=a.pfValue?a.pfValue:a.value:null;a&&a.bypass?(a.value=n,null!=a.pfValue&&(a.pfValue=n),s?a.strValue="rgb("+n.join(",")+")":l?a.strValue=n.join(" "):a.strValue=""+n,this.updateStyleHints(i)):this.applyBypass(i,t,n),this.checkTriggers(i,t,u,n)}},aK.removeAllBypasses=function(e,t){return this.removeBypasses(e,this.propertyNames,t)},aK.removeBypasses=function(e,t,n){for(var r=0;r<e.length;r++){for(var i=e[r],a={},o=0;o<t.length;o++){var s=t[o],l=this.properties[s],u=i.pstyle(l.name);if(u&&u.bypass){var c=this.parse(s,"",!0),d=a[l.name]={prev:u};this.applyParsedProperty(i,c),d.next=i.pstyle(l.name)}}this.updateStyleHints(i),n&&this.updateTransitions(i,a,!0)}};var aZ={};aZ.getEmSizeInPixels=function(){var e=this.containerCss("font-size");return null!=e?parseFloat(e):1},aZ.containerCss=function(e){var t=this._private.cy,n=t.container(),r=t.window();if(r&&n&&r.getComputedStyle)return r.getComputedStyle(n).getPropertyValue(e)};var a$={};a$.getRenderedStyle=function(e,t){return t?this.getStylePropertyValue(e,t,!0):this.getRawStyle(e,!0)},a$.getRawStyle=function(e,t){if(e=e[0]){for(var n={},r=0;r<this.properties.length;r++){var i=this.properties[r],a=this.getStylePropertyValue(e,i.name,t);null!=a&&(n[i.name]=a,n[j(i.name)]=a)}return n}},a$.getIndexedStyle=function(e,t,n,r){var i=e.pstyle(t)[n][r];return null!=i?i:e.cy().style().getDefaultProperty(t)[n][0]},a$.getStylePropertyValue=function(e,t,n){if(e=e[0]){var r=this.properties[t];r.alias&&(r=r.pointsTo);var i=r.type,a=e.pstyle(r.name);if(a){var o=a.value,s=a.units,l=a.strValue;if(n&&i.number&&null!=o&&M(o)){var u=e.cy().zoom(),c=function(e){return e*u},d=function(e,t){return c(e)+t},h=P(o);return(h?s.every(function(e){return null!=e}):null!=s)?h?o.map(function(e,t){return d(e,s[t])}).join(" "):d(o,s):h?o.map(function(e){return D(e)?e:""+c(e)}).join(" "):""+c(o)}if(null!=l)return l}return null}},a$.getAnimationStartStyle=function(e,t){for(var n={},r=0;r<t.length;r++){var i=t[r].name,a=e.pstyle(i);void 0!==a&&(a=_(a)?this.parse(i,a.strValue):this.parse(i,a)),a&&(n[i]=a)}return n},a$.getPropsList=function(e){var t=[],n=this.properties;if(e)for(var r=Object.keys(e),i=0;i<r.length;i++){var a=r[i],o=e[a],s=n[a]||n[F(a)],l=this.parse(s.name,o);l&&t.push(l)}return t},a$.getNonDefaultPropertiesHash=function(e,t,n){var r,i,a,o,s,l,u=n.slice();for(s=0;s<t.length;s++)if(r=t[s],null!=(i=e.pstyle(r,!1))){if(null!=i.pfValue)u[0]=eR(o,u[0]),u[1]=ez(o,u[1]);else for(l=0,a=i.strValue;l<a.length;l++)o=a.charCodeAt(l),u[0]=eR(o,u[0]),u[1]=ez(o,u[1])}return u},a$.getPropertiesHash=a$.getNonDefaultPropertiesHash;var aQ={};aQ.appendFromJson=function(e){for(var t=0;t<e.length;t++){var n=e[t],r=n.selector,i=n.style||n.css,a=Object.keys(i);this.selector(r);for(var o=0;o<a.length;o++){var s=a[o],l=i[s];this.css(s,l)}}return this},aQ.fromJson=function(e){return this.resetToDefault(),this.appendFromJson(e),this},aQ.json=function(){for(var e=[],t=this.defaultLength;t<this.length;t++){for(var n=this[t],r=n.selector,i=n.properties,a={},o=0;o<i.length;o++){var s=i[o];a[s.name]=s.strValue}e.push({selector:r?r.toString():"core",style:a})}return e};var aJ={};aJ.appendFromString=function(e){var t,n,r,i=""+e;function a(){i=i.length>t.length?i.substr(t.length):""}function o(){n=n.length>r.length?n.substr(r.length):""}for(i=i.replace(/[/][*](\s|.)+?[*][/]/g,"");!i.match(/^\s*$/);){var s=i.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!s){e0("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+i);break}t=s[0];var l=s[1];if("core"!==l&&new im(l).invalid){e0("Skipping parsing of block: Invalid selector found in string stylesheet: "+l),a();continue}var u=s[2],c=!1;n=u;for(var d=[];!n.match(/^\s*$/);){var h=n.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!h){e0("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+u),c=!0;break}r=h[0];var p=h[1],f=h[2];if(!this.properties[p]){e0("Skipping property: Invalid property name in: "+r),o();continue}if(!this.parse(p,f)){e0("Skipping property: Invalid property definition in: "+r),o();continue}d.push({name:p,val:f}),o()}if(c){a();break}this.selector(l);for(var g=0;g<d.length;g++){var v=d[g];this.css(v.name,v.val)}a()}return this},aJ.fromString=function(e){return this.resetToDefault(),this.appendFromString(e),this};var a0={};!function(){var e=function(e){return"^"+e+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},t=function(e){var t=Y+"|\\w+|"+H+"|"+U+"|\\#[0-9a-fA-F]{3}|\\#[0-9a-fA-F]{6}";return"^"+e+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+Y+")\\s*\\,\\s*("+Y+")\\s*,\\s*("+t+")\\s*\\,\\s*("+t+")\\)$"},n=["^url\\s*\\(\\s*['\"]?(.+?)['\"]?\\s*\\)$","^(none)$","^(.+)$"];a0.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:e("data")},layoutData:{mapping:!0,regex:e("layoutData")},scratch:{mapping:!0,regex:e("scratch")},mapData:{mapping:!0,regex:t("mapData")},mapLayoutData:{mapping:!0,regex:t("mapLayoutData")},mapScratch:{mapping:!0,regex:t("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:n,singleRegexMatchValue:!0},urls:{regexes:n,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(e,t){switch(e.length){case 2:return"deg"!==t[0]&&"rad"!==t[0]&&"deg"!==t[1]&&"rad"!==t[1];case 1:return D(e[0])||"deg"===t[0]||"rad"===t[0];default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+Y+")\\s*,\\s*("+Y+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+Y+")\\s*,\\s*("+Y+")\\s*,\\s*("+Y+")\\s*,\\s*("+Y+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(e){var t=e.length;return 1===t||2===t||4===t}}};var r={zeroNonZero:function(e,t){return(null==e||null==t)&&e!==t||0==e&&0!=t||0!=e&&0==t},any:function(e,t){return e!=t},emptyNonEmpty:function(e,t){var n=z(e),r=z(t);return n&&!r||!n&&r}},i=a0.types,a=[{name:"label",type:i.text,triggersBounds:r.any,triggersZOrder:r.emptyNonEmpty},{name:"text-rotation",type:i.textRotation,triggersBounds:r.any},{name:"text-margin-x",type:i.bidirectionalSize,triggersBounds:r.any},{name:"text-margin-y",type:i.bidirectionalSize,triggersBounds:r.any}],o=[{name:"source-label",type:i.text,triggersBounds:r.any},{name:"source-text-rotation",type:i.textRotation,triggersBounds:r.any},{name:"source-text-margin-x",type:i.bidirectionalSize,triggersBounds:r.any},{name:"source-text-margin-y",type:i.bidirectionalSize,triggersBounds:r.any},{name:"source-text-offset",type:i.size,triggersBounds:r.any}],s=[{name:"target-label",type:i.text,triggersBounds:r.any},{name:"target-text-rotation",type:i.textRotation,triggersBounds:r.any},{name:"target-text-margin-x",type:i.bidirectionalSize,triggersBounds:r.any},{name:"target-text-margin-y",type:i.bidirectionalSize,triggersBounds:r.any},{name:"target-text-offset",type:i.size,triggersBounds:r.any}],l=[{name:"font-family",type:i.fontFamily,triggersBounds:r.any},{name:"font-style",type:i.fontStyle,triggersBounds:r.any},{name:"font-weight",type:i.fontWeight,triggersBounds:r.any},{name:"font-size",type:i.size,triggersBounds:r.any},{name:"text-transform",type:i.textTransform,triggersBounds:r.any},{name:"text-wrap",type:i.textWrap,triggersBounds:r.any},{name:"text-overflow-wrap",type:i.textOverflowWrap,triggersBounds:r.any},{name:"text-max-width",type:i.size,triggersBounds:r.any},{name:"text-outline-width",type:i.size,triggersBounds:r.any},{name:"line-height",type:i.positiveNumber,triggersBounds:r.any}],u=[{name:"text-valign",type:i.valign,triggersBounds:r.any},{name:"text-halign",type:i.halign,triggersBounds:r.any},{name:"color",type:i.color},{name:"text-outline-color",type:i.color},{name:"text-outline-opacity",type:i.zeroOneNumber},{name:"text-background-color",type:i.color},{name:"text-background-opacity",type:i.zeroOneNumber},{name:"text-background-padding",type:i.size,triggersBounds:r.any},{name:"text-border-opacity",type:i.zeroOneNumber},{name:"text-border-color",type:i.color},{name:"text-border-width",type:i.size,triggersBounds:r.any},{name:"text-border-style",type:i.borderStyle,triggersBounds:r.any},{name:"text-background-shape",type:i.textBackgroundShape,triggersBounds:r.any},{name:"text-justification",type:i.justification}],c=[{name:"events",type:i.bool,triggersZOrder:r.any},{name:"text-events",type:i.bool,triggersZOrder:r.any}],d=[{name:"display",type:i.display,triggersZOrder:r.any,triggersBounds:r.any,triggersBoundsOfConnectedEdges:!0},{name:"visibility",type:i.visibility,triggersZOrder:r.any},{name:"opacity",type:i.zeroOneNumber,triggersZOrder:r.zeroNonZero},{name:"text-opacity",type:i.zeroOneNumber},{name:"min-zoomed-font-size",type:i.size},{name:"z-compound-depth",type:i.zCompoundDepth,triggersZOrder:r.any},{name:"z-index-compare",type:i.zIndexCompare,triggersZOrder:r.any},{name:"z-index",type:i.number,triggersZOrder:r.any}],h=[{name:"overlay-padding",type:i.size,triggersBounds:r.any},{name:"overlay-color",type:i.color},{name:"overlay-opacity",type:i.zeroOneNumber,triggersBounds:r.zeroNonZero},{name:"overlay-shape",type:i.overlayShape,triggersBounds:r.any},{name:"overlay-corner-radius",type:i.cornerRadius}],p=[{name:"underlay-padding",type:i.size,triggersBounds:r.any},{name:"underlay-color",type:i.color},{name:"underlay-opacity",type:i.zeroOneNumber,triggersBounds:r.zeroNonZero},{name:"underlay-shape",type:i.overlayShape,triggersBounds:r.any},{name:"underlay-corner-radius",type:i.cornerRadius}],f=[{name:"transition-property",type:i.propList},{name:"transition-duration",type:i.time},{name:"transition-delay",type:i.time},{name:"transition-timing-function",type:i.easing}],g=function(e,t){return"label"===t.value?-e.poolIndex():t.pfValue},v=[{name:"height",type:i.nodeSize,triggersBounds:r.any,hashOverride:g},{name:"width",type:i.nodeSize,triggersBounds:r.any,hashOverride:g},{name:"shape",type:i.nodeShape,triggersBounds:r.any},{name:"shape-polygon-points",type:i.polygonPointList,triggersBounds:r.any},{name:"corner-radius",type:i.cornerRadius},{name:"background-color",type:i.color},{name:"background-fill",type:i.fill},{name:"background-opacity",type:i.zeroOneNumber},{name:"background-blacken",type:i.nOneOneNumber},{name:"background-gradient-stop-colors",type:i.colors},{name:"background-gradient-stop-positions",type:i.percentages},{name:"background-gradient-direction",type:i.gradientDirection},{name:"padding",type:i.sizeMaybePercent,triggersBounds:r.any},{name:"padding-relative-to",type:i.paddingRelativeTo,triggersBounds:r.any},{name:"bounds-expansion",type:i.boundsExpansion,triggersBounds:r.any}],y=[{name:"border-color",type:i.color},{name:"border-opacity",type:i.zeroOneNumber},{name:"border-width",type:i.size,triggersBounds:r.any},{name:"border-style",type:i.borderStyle},{name:"border-cap",type:i.lineCap},{name:"border-join",type:i.lineJoin},{name:"border-dash-pattern",type:i.numbers},{name:"border-dash-offset",type:i.number},{name:"border-position",type:i.linePosition}],b=[{name:"outline-color",type:i.color},{name:"outline-opacity",type:i.zeroOneNumber},{name:"outline-width",type:i.size,triggersBounds:r.any},{name:"outline-style",type:i.borderStyle},{name:"outline-offset",type:i.size,triggersBounds:r.any}],x=[{name:"background-image",type:i.urls},{name:"background-image-crossorigin",type:i.bgCrossOrigin},{name:"background-image-opacity",type:i.zeroOneNumbers},{name:"background-image-containment",type:i.bgContainment},{name:"background-image-smoothing",type:i.bools},{name:"background-position-x",type:i.bgPos},{name:"background-position-y",type:i.bgPos},{name:"background-width-relative-to",type:i.bgRelativeTo},{name:"background-height-relative-to",type:i.bgRelativeTo},{name:"background-repeat",type:i.bgRepeat},{name:"background-fit",type:i.bgFit},{name:"background-clip",type:i.bgClip},{name:"background-width",type:i.bgWH},{name:"background-height",type:i.bgWH},{name:"background-offset-x",type:i.bgPos},{name:"background-offset-y",type:i.bgPos}],w=[{name:"position",type:i.position,triggersBounds:r.any},{name:"compound-sizing-wrt-labels",type:i.compoundIncludeLabels,triggersBounds:r.any},{name:"min-width",type:i.size,triggersBounds:r.any},{name:"min-width-bias-left",type:i.sizeMaybePercent,triggersBounds:r.any},{name:"min-width-bias-right",type:i.sizeMaybePercent,triggersBounds:r.any},{name:"min-height",type:i.size,triggersBounds:r.any},{name:"min-height-bias-top",type:i.sizeMaybePercent,triggersBounds:r.any},{name:"min-height-bias-bottom",type:i.sizeMaybePercent,triggersBounds:r.any}],E=[{name:"line-style",type:i.lineStyle},{name:"line-color",type:i.color},{name:"line-fill",type:i.fill},{name:"line-cap",type:i.lineCap},{name:"line-opacity",type:i.zeroOneNumber},{name:"line-dash-pattern",type:i.numbers},{name:"line-dash-offset",type:i.number},{name:"line-outline-width",type:i.size},{name:"line-outline-color",type:i.color},{name:"line-gradient-stop-colors",type:i.colors},{name:"line-gradient-stop-positions",type:i.percentages},{name:"curve-style",type:i.curveStyle,triggersBounds:r.any,triggersBoundsOfParallelBeziers:!0},{name:"haystack-radius",type:i.zeroOneNumber,triggersBounds:r.any},{name:"source-endpoint",type:i.edgeEndpoint,triggersBounds:r.any},{name:"target-endpoint",type:i.edgeEndpoint,triggersBounds:r.any},{name:"control-point-step-size",type:i.size,triggersBounds:r.any},{name:"control-point-distances",type:i.bidirectionalSizes,triggersBounds:r.any},{name:"control-point-weights",type:i.numbers,triggersBounds:r.any},{name:"segment-distances",type:i.bidirectionalSizes,triggersBounds:r.any},{name:"segment-weights",type:i.numbers,triggersBounds:r.any},{name:"segment-radii",type:i.numbers,triggersBounds:r.any},{name:"radius-type",type:i.radiusType,triggersBounds:r.any},{name:"taxi-turn",type:i.bidirectionalSizeMaybePercent,triggersBounds:r.any},{name:"taxi-turn-min-distance",type:i.size,triggersBounds:r.any},{name:"taxi-direction",type:i.axisDirection,triggersBounds:r.any},{name:"taxi-radius",type:i.number,triggersBounds:r.any},{name:"edge-distances",type:i.edgeDistances,triggersBounds:r.any},{name:"arrow-scale",type:i.positiveNumber,triggersBounds:r.any},{name:"loop-direction",type:i.angle,triggersBounds:r.any},{name:"loop-sweep",type:i.angle,triggersBounds:r.any},{name:"source-distance-from-node",type:i.size,triggersBounds:r.any},{name:"target-distance-from-node",type:i.size,triggersBounds:r.any}],k=[{name:"ghost",type:i.bool,triggersBounds:r.any},{name:"ghost-offset-x",type:i.bidirectionalSize,triggersBounds:r.any},{name:"ghost-offset-y",type:i.bidirectionalSize,triggersBounds:r.any},{name:"ghost-opacity",type:i.zeroOneNumber}],C=[{name:"selection-box-color",type:i.color},{name:"selection-box-opacity",type:i.zeroOneNumber},{name:"selection-box-border-color",type:i.color},{name:"selection-box-border-width",type:i.size},{name:"active-bg-color",type:i.color},{name:"active-bg-opacity",type:i.zeroOneNumber},{name:"active-bg-size",type:i.size},{name:"outside-texture-bg-color",type:i.color},{name:"outside-texture-bg-opacity",type:i.zeroOneNumber}],S=[];a0.pieBackgroundN=16,S.push({name:"pie-size",type:i.sizeMaybePercent});for(var T=1;T<=a0.pieBackgroundN;T++)S.push({name:"pie-"+T+"-background-color",type:i.color}),S.push({name:"pie-"+T+"-background-size",type:i.percent}),S.push({name:"pie-"+T+"-background-opacity",type:i.zeroOneNumber});var P=[],_=a0.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:i.arrowShape,triggersBounds:r.any},{name:"arrow-color",type:i.color},{name:"arrow-fill",type:i.arrowFill},{name:"arrow-width",type:i.arrowWidth}].forEach(function(e){_.forEach(function(t){var n=t+"-"+e.name,r=e.type,i=e.triggersBounds;P.push({name:n,type:r,triggersBounds:i})})},{});var M=a0.properties=[].concat(c,f,d,h,p,k,u,l,a,o,s,v,y,b,x,S,w,E,P,C),B=a0.propertyGroups={behavior:c,transition:f,visibility:d,overlay:h,underlay:p,ghost:k,commonLabel:u,labelDimensions:l,mainLabel:a,sourceLabel:o,targetLabel:s,nodeBody:v,nodeBorder:y,nodeOutline:b,backgroundImage:x,pie:S,compound:w,edgeLine:E,edgeArrow:P,core:C},N=a0.propertyGroupNames={};(a0.propertyGroupKeys=Object.keys(B)).forEach(function(e){N[e]=B[e].map(function(e){return e.name}),B[e].forEach(function(t){return t.groupKey=e})});var A=a0.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];a0.propertyNames=M.map(function(e){return e.name});for(var I=0;I<M.length;I++){var O=M[I];M[O.name]=O}for(var R=0;R<A.length;R++){var L=A[R],V=M[L.pointsTo],F={name:L.name,alias:!0,pointsTo:V};M.push(F),M[L.name]=F}}(),a0.getDefaultProperty=function(e){return this.getDefaultProperties()[e]},a0.getDefaultProperties=function(){var e=this._private;if(null!=e.defaultProperties)return e.defaultProperties;for(var t=Z({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(e,t){for(var n=1;n<=a0.pieBackgroundN;n++){var r=t.name.replace("{{i}}",n),i=t.value;e[r]=i}return e},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(e,t){return a0.arrowPrefixes.forEach(function(n){var r=n+"-"+t.name,i=t.value;e[r]=i}),e},{})),n={},r=0;r<this.properties.length;r++){var i=this.properties[r];if(!i.pointsTo){var a=i.name,o=t[a],s=this.parse(a,o);n[a]=s}}return e.defaultProperties=n,e.defaultProperties},a0.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var a1={};a1.parse=function(e,t,n,r){var i,a=this;if(T(t))return a.parseImplWarn(e,t,n,r);var o=eq(e,""+t,n?"t":"f","mapping"===r||!0===r||!1===r||null==r?"dontcare":r),s=a.propCache=a.propCache||[];return(i=s[o])||(i=s[o]=a.parseImplWarn(e,t,n,r)),(n||"mapping"===r)&&(i=e1(i))&&(i.value=e1(i.value)),i},a1.parseImplWarn=function(e,t,n,r){var i=this.parseImpl(e,t,n,r);return i||null==t||e0("The style property `".concat(e,": ").concat(t,"` is invalid")),i&&("width"===i.name||"height"===i.name)&&"label"===t&&e0("The style value of `label` is deprecated for `"+i.name+"`"),i},a1.parseImpl=function(e,t,n,r){e=F(e);var i=this.properties[e],a=t,o=this.types;if(!i||void 0===t)return null;i.alias&&(e=(i=i.pointsTo).name);var s=D(t);s&&(t=t.trim());var l=i.type;if(!l)return null;if(n&&(""===t||null===t))return{name:e,value:t,bypass:!0,deleteBypass:!0};if(T(t))return{name:e,value:t,strValue:"fn",mapped:o.fn,bypass:n};if(!s||r||t.length<7||"a"!==t[1]);else if(t.length>=7&&"d"===t[0]&&(f=RegExp(o.data.regex).exec(t)))return!n&&{name:e,value:f,strValue:""+t,mapped:o.data,field:f[1],bypass:n};else if(t.length>=10&&"m"===t[0]&&(g=RegExp(o.mapData.regex).exec(t))){if(n||l.multiple)return!1;var u=o.mapData;if(!(l.color||l.number))return!1;var c=this.parse(e,g[4]);if(!c||c.mapped)return!1;var d=this.parse(e,g[5]);if(!d||d.mapped)return!1;if(c.pfValue===d.pfValue||c.strValue===d.strValue)return e0("`"+e+": "+t+"` is not a valid mapper because the output range is zero; converting to `"+e+": "+c.strValue+"`"),this.parse(e,c.strValue);if(l.color){var h=c.value,p=d.value;if(h[0]===p[0]&&h[1]===p[1]&&h[2]===p[2]&&(h[3]===p[3]||(null==h[3]||1===h[3])&&(null==p[3]||1===p[3])))return!1}return{name:e,value:g,strValue:""+t,mapped:u,field:g[1],fieldMin:parseFloat(g[2]),fieldMax:parseFloat(g[3]),valueMin:c.value,valueMax:d.value,bypass:n}}if(l.multiple&&"multiple"!==r){if(v=s?t.split(/\s+/):P(t)?t:[t],l.evenMultiple&&v.length%2!=0)return null;for(var f,g,v,y=[],b=[],x=[],w="",E=!1,k=0;k<v.length;k++){var C=this.parse(e,v[k],n,"multiple");E=E||D(C.value),y.push(C.value),x.push(null!=C.pfValue?C.pfValue:C.value),b.push(C.units),w+=(k>0?" ":"")+C.strValue}return l.validate&&!l.validate(y,b)?null:l.singleEnum&&E?1===y.length&&D(y[0])?{name:e,value:y[0],strValue:y[0],bypass:n}:null:{name:e,value:y,pfValue:x,strValue:w,bypass:n,units:b}}var S=function(){for(var r=0;r<l.enums.length;r++)if(l.enums[r]===t)return{name:e,value:t,strValue:""+t,bypass:n};return null};if(l.number){var _,B,N="px";if(l.units&&(B=l.units),l.implicitUnits&&(N=l.implicitUnits),!l.unitless){if(s){var A="px|em"+(l.allowPercent?"|\\%":"");B&&(A=B);var I=t.match("^("+Y+")("+A+")?$");I&&(t=I[1],B=I[2]||N)}else(!B||l.implicitUnits)&&(B=N)}if(isNaN(t=parseFloat(t))&&void 0===l.enums)return null;if(isNaN(t)&&void 0!==l.enums)return t=a,S();if(l.integer&&!(M(_=t)&&Math.floor(_)===_)||void 0!==l.min&&(t<l.min||l.strictMin&&t===l.min)||void 0!==l.max&&(t>l.max||l.strictMax&&t===l.max))return null;var O={name:e,value:t,strValue:""+t+(B||""),units:B,bypass:n};return l.unitless||"px"!==B&&"em"!==B?O.pfValue=t:O.pfValue="px"!==B&&B?this.getEmSizeInPixels()*t:t,("ms"===B||"s"===B)&&(O.pfValue="ms"===B?t:1e3*t),("deg"===B||"rad"===B)&&(O.pfValue="rad"===B?t:Math.PI*t/180),"%"===B&&(O.pfValue=t/100),O}if(l.propList){var R=[],z=""+t;if("none"===z);else{for(var L=z.split(/\s*,\s*|\s+/),V=0;V<L.length;V++){var j=L[V].trim();this.properties[j]?R.push(j):e0("`"+j+"` is not a valid property name")}if(0===R.length)return null}return{name:e,value:R,strValue:0===R.length?"none":R.join(" "),bypass:n}}if(l.color){var q,X=(P(q=t)?q:null)||ee[q.toLowerCase()]||$(q)||J(q)||Q(q);return X?{name:e,value:X,pfValue:X,strValue:"rgb("+X[0]+","+X[1]+","+X[2]+")",bypass:n}:null}if(l.regex||l.regexes){if(l.enums){var W=S();if(W)return W}for(var H=l.regexes?l.regexes:[l.regex],G=0;G<H.length;G++){var U=RegExp(H[G]).exec(t);if(U)return{name:e,value:l.singleRegexMatchValue?U[1]:U,strValue:""+t,bypass:n}}return null}return l.string?{name:e,value:""+t,strValue:""+t,bypass:n}:l.enums?S():null};var a2=function e(t){if(!(this instanceof e))return new e(t);if(!O(t)){eQ("A style must have a core reference");return}this._private={cy:t,coreStyle:{}},this.length=0,this.resetToDefault()},a5=a2.prototype;a5.instanceString=function(){return"style"},a5.clear=function(){for(var e=this._private,t=e.cy.elements(),n=0;n<this.length;n++)this[n]=void 0;return this.length=0,e.contextStyles={},e.propDiffs={},this.cleanElements(t,!0),t.forEach(function(e){var t=e[0]._private;t.styleDirty=!0,t.appliedInitStyle=!1}),this},a5.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},a5.core=function(e){return this._private.coreStyle[e]||this.getDefaultProperty(e)},a5.selector=function(e){var t="core"===e?null:new im(e),n=this.length++;return this[n]={selector:t,properties:[],mappedProperties:[],index:n},this},a5.css=function(){var e=arguments;if(1===e.length)for(var t=e[0],n=0;n<this.properties.length;n++){var r=this.properties[n],i=t[r.name];void 0===i&&(i=t[j(r.name)]),void 0!==i&&this.cssRule(r.name,i)}else 2===e.length&&this.cssRule(e[0],e[1]);return this},a5.style=a5.css,a5.cssRule=function(e,t){var n=this.parse(e,t);if(n){var r=this.length-1;this[r].properties.push(n),this[r].properties[n.name]=n,n.name.match(/pie-(\d+)-background-size/)&&n.value&&(this._private.hasPie=!0),n.mapped&&this[r].mappedProperties.push(n),this[r].selector||(this._private.coreStyle[n.name]=n)}return this},a5.append=function(e){return R(e)?e.appendToStyle(this):P(e)?this.appendFromJson(e):D(e)&&this.appendFromString(e),this},a2.fromJson=function(e,t){var n=new a2(e);return n.fromJson(t),n},a2.fromString=function(e,t){return new a2(e).fromString(t)},[aU,aK,aZ,a$,aQ,aJ,a0,a1].forEach(function(e){Z(a5,e)}),a2.types=a5.types,a2.properties=a5.properties,a2.propertyGroups=a5.propertyGroups,a2.propertyGroupNames=a5.propertyGroupNames,a2.propertyGroupKeys=a5.propertyGroupKeys;var a3={autolock:function(e){return void 0===e?this._private.autolock:(this._private.autolock=!!e,this)},autoungrabify:function(e){return void 0===e?this._private.autoungrabify:(this._private.autoungrabify=!!e,this)},autounselectify:function(e){return void 0===e?this._private.autounselectify:(this._private.autounselectify=!!e,this)},selectionType:function(e){var t=this._private;return(null==t.selectionType&&(t.selectionType="single"),void 0===e)?t.selectionType:(("additive"===e||"single"===e)&&(t.selectionType=e),this)},panningEnabled:function(e){return void 0===e?this._private.panningEnabled:(this._private.panningEnabled=!!e,this)},userPanningEnabled:function(e){return void 0===e?this._private.userPanningEnabled:(this._private.userPanningEnabled=!!e,this)},zoomingEnabled:function(e){return void 0===e?this._private.zoomingEnabled:(this._private.zoomingEnabled=!!e,this)},userZoomingEnabled:function(e){return void 0===e?this._private.userZoomingEnabled:(this._private.userZoomingEnabled=!!e,this)},boxSelectionEnabled:function(e){return void 0===e?this._private.boxSelectionEnabled:(this._private.boxSelectionEnabled=!!e,this)},pan:function(){var e,t,n,r,i,a=arguments,o=this._private.pan;switch(a.length){case 0:return o;case 1:if(D(a[0]))return o[e=a[0]];if(_(a[0])){if(!this._private.panningEnabled)return this;r=(n=a[0]).x,i=n.y,M(r)&&(o.x=r),M(i)&&(o.y=i),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;e=a[0],t=a[1],("x"===e||"y"===e)&&M(t)&&(o[e]=t),this.emit("pan viewport")}return this.notify("viewport"),this},panBy:function(e,t){var n,r,i,a=arguments,o=this._private.pan;if(!this._private.panningEnabled)return this;switch(a.length){case 1:_(e)&&(r=(n=a[0]).x,i=n.y,M(r)&&(o.x+=r),M(i)&&(o.y+=i),this.emit("pan viewport"));break;case 2:("x"===e||"y"===e)&&M(t)&&(o[e]+=t),this.emit("pan viewport")}return this.notify("viewport"),this},fit:function(e,t){var n=this.getFitViewport(e,t);if(n){var r=this._private;r.zoom=n.zoom,r.pan=n.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(M(e)&&void 0===t&&(t=e,e=void 0),this._private.panningEnabled&&this._private.zoomingEnabled){if(D(e)){var n,r,i,a=e;e=this.$(a)}else if(_(n=e)&&M(n.x1)&&M(n.x2)&&M(n.y1)&&M(n.y2)){var o=e;(r={x1:o.x1,y1:o.y1,x2:o.x2,y2:o.y2}).w=r.x2-r.x1,r.h=r.y2-r.y1}else N(e)||(e=this.mutableElements());if(!(N(e)&&e.empty())){r=r||e.boundingBox();var s=this.width(),l=this.height();if(t=M(t)?t:0,!isNaN(s)&&!isNaN(l)&&s>0&&l>0&&!isNaN(r.w)&&!isNaN(r.h)&&r.w>0&&r.h>0){i=(i=(i=Math.min((s-2*t)/r.w,(l-2*t)/r.h))>this._private.maxZoom?this._private.maxZoom:i)<this._private.minZoom?this._private.minZoom:i;var u={x:(s-i*(r.x1+r.x2))/2,y:(l-i*(r.y1+r.y2))/2};return{zoom:i,pan:u}}}}},zoomRange:function(e,t){var n=this._private;if(null==t){var r=e;e=r.min,t=r.max}return M(e)&&M(t)&&e<=t?(n.minZoom=e,n.maxZoom=t):M(e)&&void 0===t&&e<=n.maxZoom?n.minZoom=e:M(t)&&void 0===e&&t>=n.minZoom&&(n.maxZoom=t),this},minZoom:function(e){return void 0===e?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return void 0===e?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t,n,r=this._private,i=r.pan,a=r.zoom,o=!1;if(r.zoomingEnabled||(o=!0),M(e)?n=e:_(e)&&(n=e.level,null!=e.position?t=tv(e.position,a,i):null!=e.renderedPosition&&(t=e.renderedPosition),null==t||r.panningEnabled||(o=!0)),n=(n=n>r.maxZoom?r.maxZoom:n)<r.minZoom?r.minZoom:n,o||!M(n)||n===a||null!=t&&(!M(t.x)||!M(t.y)))return null;if(null==t)return{zoomed:!0,panned:!1,zoom:n,pan:i};var s=n,l={x:-s/a*(t.x-i.x)+t.x,y:-s/a*(t.y-i.y)+t.y};return{zoomed:!0,panned:!0,zoom:s,pan:l}},zoom:function(e){if(void 0===e)return this._private.zoom;var t=this.getZoomedViewport(e),n=this._private;return null!=t&&t.zoomed&&(n.zoom=t.zoom,t.panned&&(n.pan.x=t.pan.x,n.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport")),this},viewport:function(e){var t=this._private,n=!0,r=!0,i=[],a=!1,o=!1;if(!e||(M(e.zoom)||(n=!1),_(e.pan)||(r=!1),!n&&!r))return this;if(n){var s=e.zoom;s<t.minZoom||s>t.maxZoom||!t.zoomingEnabled?a=!0:(t.zoom=s,i.push("zoom"))}if(r&&(!a||!e.cancelOnFailedZoom)&&t.panningEnabled){var l=e.pan;M(l.x)&&(t.pan.x=l.x,o=!1),M(l.y)&&(t.pan.y=l.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(D(e)){var n=e;e=this.mutableElements().filter(n)}else N(e)||(e=this.mutableElements());if(0!==e.length){var r=e.boundingBox(),i=this.width(),a=this.height();return{x:(i-(t=void 0===t?this._private.zoom:t)*(r.x1+r.x2))/2,y:(a-t*(r.y1+r.y2))/2}}}},reset:function(){return this._private.panningEnabled&&this._private.zoomingEnabled&&this.viewport({pan:{x:0,y:0},zoom:1}),this},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e,t,n=this._private,r=n.container;return n.sizeCache=n.sizeCache||(r?(e=this.window().getComputedStyle(r),t=function(t){return parseFloat(e.getPropertyValue(t))},{width:r.clientWidth-t("padding-left")-t("padding-right"),height:r.clientHeight-t("padding-top")-t("padding-bottom")}):{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,n=this.renderedExtent(),r={x1:(n.x1-e.x)/t,x2:(n.x2-e.x)/t,y1:(n.y1-e.y)/t,y2:(n.y2-e.y)/t};return r.w=r.x2-r.x1,r.h=r.y2-r.y1,r},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){return e?(this._private.multiClickDebounceTime=e,this):this._private.multiClickDebounceTime}};a3.centre=a3.center,a3.autolockNodes=a3.autolock,a3.autoungrabifyNodes=a3.autoungrabify;var a4={data:r6.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:r6.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:r6.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:r6.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};a4.attr=a4.data,a4.removeAttr=a4.removeData;var a9=function(e){var t=this,n=(e=Z({},e)).container;n&&!B(n)&&B(n[0])&&(n=n[0]);var r=n?n._cyreg:null;(r=r||{}).cy&&(r.cy.destroy(),r={});var i=r.readies=r.readies||[];n&&(n._cyreg=r),r.cy=t;var a=void 0!==b&&void 0!==n&&!e.headless,o=e;o.layout=Z({name:a?"grid":"null"},o.layout),o.renderer=Z({name:a?"canvas":"null"},o.renderer);var s=function(e,t,n){return void 0!==t?t:void 0!==n?n:e},l=this._private={container:n,ready:!1,options:o,elements:new a_(this),listeners:[],aniEles:new a_(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:s(!0,o.zoomingEnabled),userZoomingEnabled:s(!0,o.userZoomingEnabled),panningEnabled:s(!0,o.panningEnabled),userPanningEnabled:s(!0,o.userPanningEnabled),boxSelectionEnabled:s(!0,o.boxSelectionEnabled),autolock:s(!1,o.autolock,o.autolockNodes),autoungrabify:s(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:s(!1,o.autounselectify),styleEnabled:void 0===o.styleEnabled?a:o.styleEnabled,zoom:M(o.zoom)?o.zoom:1,pan:{x:_(o.pan)&&M(o.pan.x)?o.pan.x:0,y:_(o.pan)&&M(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:s(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom}),l.styleEnabled&&t.setStyle([]);var u=Z({},o,o.renderer);t.initRenderer(u);var c=function(e,n,r){t.notifications(!1);var i=t.mutableElements();i.length>0&&i.remove(),null!=e&&(_(e)||P(e))&&t.add(e),t.one("layoutready",function(e){t.notifications(!0),t.emit(e),t.one("load",n),t.emitAndNotify("load")}).one("layoutstop",function(){t.one("done",r),t.emit("done")});var a=Z({},t._private.options.layout);a.eles=t.elements(),t.layout(a).run()};!function(e,t){if(e.some(L))return rl.all(e).then(t);t(e)}([o.style,o.elements],function(e){var n=e[0],a=e[1];l.styleEnabled&&t.style().append(n),c(a,function(){t.startAnimationLoop(),l.ready=!0,T(o.ready)&&t.on("ready",o.ready);for(var e=0;e<i.length;e++){var n=i[e];t.on("ready",n)}r&&(r.readies=[]),t.emit("ready")},o.done)})},a6=a9.prototype;Z(a6,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){if(null==this._private.container)return b;var e=this._private.container.ownerDocument;return void 0===e||null==e?b:e.defaultView||b},mount:function(e){if(null!=e){var t=this._private,n=t.options;return!B(e)&&B(e[0])&&(e=e[0]),this.stopAnimationLoop(),this.destroyRenderer(),t.container=e,t.styleEnabled=!0,this.invalidateSize(),this.initRenderer(Z({},n,n.renderer,{name:"null"===n.renderer.name?"canvas":n.renderer.name})),this.startAnimationLoop(),this.style(n.style),this.emit("mount"),this}},unmount:function(){return this.stopAnimationLoop(),this.destroyRenderer(),this.initRenderer({name:"null"}),this.emit("unmount"),this},options:function(){return e1(this._private.options)},json:function(e){var t=this,n=t._private,r=t.mutableElements();if(_(e)){if(t.startBatch(),e.elements){var i={},a=function(e,n){for(var r=[],a=[],o=0;o<e.length;o++){var s=e[o];if(!s.data.id){e0("cy.json() cannot handle elements without an ID attribute");continue}var l=""+s.data.id,u=t.getElementById(l);i[l]=!0,0!==u.length?a.push({ele:u,json:s}):(n&&(s.group=n),r.push(s))}t.add(r);for(var c=0;c<a.length;c++){var d=a[c],h=d.ele,p=d.json;h.json(p)}};if(P(e.elements))a(e.elements);else for(var o=["nodes","edges"],s=0;s<o.length;s++){var l=o[s],u=e.elements[l];P(u)&&a(u,l)}var c=t.collection();r.filter(function(e){return!i[e.id()]}).forEach(function(e){e.isParent()?c.merge(e):e.remove()}),c.forEach(function(e){return e.children().move({parent:null})}),c.forEach(function(e){return t.getElementById(e.id()).remove()})}e.style&&t.style(e.style),null!=e.zoom&&e.zoom!==n.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x!==n.pan.x||e.pan.y!==n.pan.y)&&t.pan(e.pan),e.data&&t.data(e.data);for(var d=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],h=0;h<d.length;h++){var p=d[h];null!=e[p]&&t[p](e[p])}return t.endBatch(),this}var f={};e?f.elements=this.elements().map(function(e){return e.json()}):(f.elements={},r.forEach(function(e){var t=e.group();f.elements[t]||(f.elements[t]=[]),f.elements[t].push(e.json())})),this._private.styleEnabled&&(f.style=t.style().json()),f.data=e1(t.data());var g=n.options;return f.zoomingEnabled=n.zoomingEnabled,f.userZoomingEnabled=n.userZoomingEnabled,f.zoom=n.zoom,f.minZoom=n.minZoom,f.maxZoom=n.maxZoom,f.panningEnabled=n.panningEnabled,f.userPanningEnabled=n.userPanningEnabled,f.pan=e1(n.pan),f.boxSelectionEnabled=n.boxSelectionEnabled,f.renderer=e1(g.renderer),f.hideEdgesOnViewport=g.hideEdgesOnViewport,f.textureOnViewport=g.textureOnViewport,f.wheelSensitivity=g.wheelSensitivity,f.motionBlur=g.motionBlur,f.multiClickDebounceTime=g.multiClickDebounceTime,f}}),a6.$id=a6.getElementById,[{add:function(e){var t;if(N(e)){if(e._private.cy===this)t=e.restore();else{for(var n=[],r=0;r<e.length;r++){var i=e[r];n.push(i.json())}t=new a_(this,n)}}else if(P(e))t=new a_(this,e);else if(_(e)&&(P(e.nodes)||P(e.edges))){for(var a=[],o=["nodes","edges"],s=0,l=o.length;s<l;s++){var u=o[s],c=e[u];if(P(c))for(var d=0,h=c.length;d<h;d++){var p=Z({group:u},c[d]);a.push(p)}}t=new a_(this,a)}else t=new ta(this,e).collection();return t},remove:function(e){if(N(e));else if(D(e)){var t=e;e=this.$(t)}return e.remove()}},aV,aq,aX,aY,{notify:function(e,t){var n=this._private;if(this.batching()){n.batchNotifications=n.batchNotifications||{};var r=n.batchNotifications[e]=n.batchNotifications[e]||this.collection();null!=t&&r.merge(t);return}if(n.notificationsEnabled){var i=this.renderer();!this.destroyed()&&i&&i.notify(e,t)}},notifications:function(e){var t=this._private;return void 0===e?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return null==e.batchCount&&(e.batchCount=0),0===e.batchCount&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(0===e.batchCount)return this;if(e.batchCount--,0===e.batchCount){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach(function(n){var r=e.batchNotifications[n];r.empty()?t.notify(n):t.notify(n,r)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var n=Object.keys(e),r=0;r<n.length;r++){var i=n[r],a=e[i];t.getElementById(i).data(a)}})}},aH,aG,{style:function(e){return e&&this.setStyle(e).update(),this._private.style},setStyle:function(e){var t=this._private;return R(e)?t.style=e.generateStyle(this):P(e)?t.style=a2.fromJson(this,e):D(e)?t.style=a2.fromString(this,e):t.style=a2(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},a3,a4].forEach(function(e){Z(a6,e)});var a8={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},a7={maximal:!1,acyclic:!1},oe=function(e){return e.scratch("breadthfirst")},ot=function(e,t){return e.scratch("breadthfirst",t)};function on(e){this.options=Z({},a8,a7,e)}on.prototype.run=function(){var e,t=this.options,n=t.cy,r=t.eles,i=r.nodes().filter(function(e){return!e.isParent()}),a=t.directed,o=t.acyclic||t.maximal||t.maximalAdjustments>0,s=tA(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()});if(N(t.roots))e=t.roots;else if(P(t.roots)){for(var l=[],u=0;u<t.roots.length;u++){var c=t.roots[u],d=n.getElementById(c);l.push(d)}e=n.collection(l)}else if(D(t.roots))e=n.$(t.roots);else if(a)e=i.roots();else{var h=r.components();e=n.collection();for(var p=0;p<h.length;p++)!function(t){var n=h[t],r=n.maxDegree(!1),i=n.filter(function(e){return e.degree(!1)===r});e=e.add(i)}(p)}var f=[],g={},v=function(e,t){null==f[t]&&(f[t]=[]);var n=f[t].length;f[t].push(e),ot(e,{index:n,depth:t})},y=function(e,t){var n=oe(e),r=n.depth,i=n.index;f[r][i]=null,v(e,t)};r.bfs({roots:e,directed:t.directed,visit:function(e,t,n,r,i){var a=e[0],o=a.id();v(a,i),g[o]=!0}});for(var b=[],x=0;x<i.length;x++){var w=i[x];g[w.id()]||b.push(w)}var E=function(e){for(var t=f[e],n=0;n<t.length;n++){var r=t[n];if(null==r){t.splice(n,1),n--;continue}ot(r,{depth:e,index:n})}},k=function(){for(var e=0;e<f.length;e++)E(e)};if(a&&o){var C=[],S={},T=function(e){return C.push(e)};for(i.forEach(function(e){return C.push(e)});C.length>0;){var _=C.shift(),M=function(e,n){for(var i=oe(e),a=e.incomers().filter(function(e){return e.isNode()&&r.has(e)}),o=-1,s=e.id(),l=0;l<a.length;l++)o=Math.max(o,oe(a[l]).depth);if(i.depth<=o){if(!t.acyclic&&n[s])return null;var u=o+1;return y(e,u),n[s]=u,!0}return!1}(_,S);if(M)_.outgoers().filter(function(e){return e.isNode()&&r.has(e)}).forEach(T);else if(null===M){e0("Detected double maximal shift for node `"+_.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}k();var B=0;if(t.avoidOverlap)for(var A=0;A<i.length;A++){var I=i[A].layoutDimensions(t);B=Math.max(B,I.w,I.h)}var O={},R=function(e){if(O[e.id()])return O[e.id()];for(var t=oe(e).depth,n=e.neighborhood(),r=0,a=0,o=0;o<n.length;o++){var s=n[o];if(!(s.isEdge()||s.isParent())&&i.has(s)){var l=oe(s);if(null!=l){var u=l.index,c=l.depth;if(null!=u&&null!=c){var d=f[c].length;c<t&&(r+=u/d,a++)}}}}return r/=a=Math.max(1,a),0===a&&(r=0),O[e.id()]=r,r},z=function(e,t){var n=R(e)-R(t);return 0===n?K(e.id(),t.id()):n};void 0!==t.depthSort&&(z=t.depthSort);for(var L=0;L<f.length;L++)f[L].sort(z),E(L);for(var V=[],F=0;F<b.length;F++)V.push(b[F]);f.unshift(V),k();for(var j=0,q=0;q<f.length;q++)j=Math.max(f[q].length,j);var X={x:s.x1+s.w/2,y:s.x1+s.h/2},Y=f.reduce(function(e,t){return Math.max(e,t.length)},0);return r.nodes().layoutPositions(this,t,function(e){var n=oe(e),r=n.depth,i=n.index,a=f[r].length,o=Math.max(s.w/((t.grid?Y:a)+1),B),l=Math.max(s.h/(f.length+1),B),u=Math.min(s.w/2/f.length,s.h/2/f.length);if(u=Math.max(u,B),!t.circle)return{x:X.x+(i+1-(a+1)/2)*o,y:(r+1)*l};var c=u*r+u-(f.length>0&&f[0].length<=3?u/2:0),d=2*Math.PI/f[r].length*i;return 0===r&&1===f[0].length&&(c=1),{x:X.x+c*Math.cos(d),y:X.y+c*Math.sin(d)}}),this};var or={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function oi(e){this.options=Z({},or,e)}oi.prototype.run=function(){var e,t=this.options,n=t.cy,r=t.eles,i=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,a=r.nodes().not(":parent");t.sort&&(a=a.sort(t.sort));for(var o=tA(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),s={x:o.x1+o.w/2,y:o.y1+o.h/2},l=(void 0===t.sweep?2*Math.PI-2*Math.PI/a.length:t.sweep)/Math.max(1,a.length-1),u=0,c=0;c<a.length;c++){var d=a[c].layoutDimensions(t);u=Math.max(u,d.w,d.h)}if(e=M(t.radius)?t.radius:a.length<=1?0:Math.min(o.h,o.w)/2-u,a.length>1&&t.avoidOverlap){var h=Math.cos(l)-1,p=Math.sin(l)-0;e=Math.max(Math.sqrt((u*=1.75)*u/(h*h+p*p)),e)}return r.nodes().layoutPositions(this,t,function(n,r){var a=t.startAngle+r*l*(i?1:-1),o=e*Math.cos(a),u=e*Math.sin(a);return{x:s.x+o,y:s.y+u}}),this};var oa={fit:!0,padding:30,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function oo(e){this.options=Z({},oa,e)}oo.prototype.run=function(){for(var e=this.options,t=void 0!==e.counterclockwise?!e.counterclockwise:e.clockwise,n=e.cy,r=e.eles,i=r.nodes().not(":parent"),a=tA(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),o={x:a.x1+a.w/2,y:a.y1+a.h/2},s=[],l=0,u=0;u<i.length;u++){var c=i[u],d=void 0;d=e.concentric(c),s.push({value:d,node:c}),c._private.scratch.concentric=d}i.updateStyle();for(var h=0;h<i.length;h++){var p=i[h].layoutDimensions(e);l=Math.max(l,p.w,p.h)}s.sort(function(e,t){return t.value-e.value});for(var f=e.levelWidth(i),g=[[]],v=g[0],y=0;y<s.length;y++){var b=s[y];v.length>0&&Math.abs(v[0].value-b.value)>=f&&(v=[],g.push(v)),v.push(b)}var x=l+e.minNodeSpacing;if(!e.avoidOverlap){var w=g.length>0&&g[0].length>1,E=(Math.min(a.w,a.h)/2-x)/(g.length+w?1:0);x=Math.min(x,E)}for(var k=0,C=0;C<g.length;C++){var S=g[C],D=void 0===e.sweep?2*Math.PI-2*Math.PI/S.length:e.sweep,T=S.dTheta=D/Math.max(1,S.length-1);if(S.length>1&&e.avoidOverlap){var P=Math.cos(T)-1,_=Math.sin(T)-0;k=Math.max(Math.sqrt(x*x/(P*P+_*_)),k)}S.r=k,k+=x}if(e.equidistant){for(var M=0,B=0,N=0;N<g.length;N++)M=Math.max(M,g[N].r-B);B=0;for(var A=0;A<g.length;A++){var I=g[A];0===A&&(B=I.r),I.r=B,B+=M}}for(var O={},R=0;R<g.length;R++)for(var z=g[R],L=z.dTheta,V=z.r,F=0;F<z.length;F++){var j=z[F],q=e.startAngle+(t?1:-1)*L*F,X={x:o.x+V*Math.cos(q),y:o.y+V*Math.sin(q)};O[j.node.id()]=X}return r.nodes().layoutPositions(this,e,function(e){return O[e.id()]}),this};var os={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function ol(e){this.options=Z({},os,e),this.options.layout=this;var t=this.options.eles.nodes(),n=this.options.eles.edges().filter(function(e){var n=e.source().data("id"),r=e.target().data("id"),i=t.some(function(e){return e.data("id")===n}),a=t.some(function(e){return e.data("id")===r});return!i||!a});this.options.eles=this.options.eles.not(n)}ol.prototype.run=function(){var e=this.options,t=e.cy,n=this;n.stopped=!1,(!0===e.animate||!1===e.animate)&&n.emit({type:"layoutstart",layout:n}),o2=!0===e.debug;var r=ou(t,n,e);o2&&o5(r),e.randomize&&oh(r);var i=eN(),a=function(){of(r,t,e),!0===e.fit&&t.fit(e.padding)},o=function(t){return!n.stopped&&!(t>=e.numIter)&&(og(r,e),r.temperature=r.temperature*e.coolingFactor,!(r.temperature<e.minTemp))},s=function(){if(!0===e.animate||!1===e.animate)a(),n.one("layoutstop",e.stop),n.emit({type:"layoutstop",layout:n});else{var t=e.eles.nodes(),i=op(r,e,t);t.layoutPositions(n,e,i)}},l=0,u=!0;if(!0===e.animate)!function t(){for(var n=0;u&&n<e.refresh;)u=o(l),l++,n++;u?(eN()-i>=e.animationThreshold&&a(),eI(t)):(oT(r,e),s())}();else{for(;u;)u=o(l),l++;oT(r,e),s()}return this},ol.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},ol.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var ou=function(e,t,n){for(var r=n.eles.edges(),i=n.eles.nodes(),a=tA(n.boundingBox?n.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:r.size(),temperature:n.initialTemp,clientWidth:a.w,clientHeight:a.h,boundingBox:a},s=n.eles.components(),l={},u=0;u<s.length;u++)for(var c=s[u],d=0;d<c.length;d++){var h=c[d];l[h.id()]=u}for(var u=0;u<o.nodeSize;u++){var p=i[u],f=p.layoutDimensions(n),g={};g.isLocked=p.locked(),g.id=p.data("id"),g.parentId=p.data("parent"),g.cmptId=l[p.id()],g.children=[],g.positionX=p.position("x"),g.positionY=p.position("y"),g.offsetX=0,g.offsetY=0,g.height=f.w,g.width=f.h,g.maxX=g.positionX+g.width/2,g.minX=g.positionX-g.width/2,g.maxY=g.positionY+g.height/2,g.minY=g.positionY-g.height/2,g.padLeft=parseFloat(p.style("padding")),g.padRight=parseFloat(p.style("padding")),g.padTop=parseFloat(p.style("padding")),g.padBottom=parseFloat(p.style("padding")),g.nodeRepulsion=T(n.nodeRepulsion)?n.nodeRepulsion(p):n.nodeRepulsion,o.layoutNodes.push(g),o.idToIndex[g.id]=u}for(var v=[],y=0,b=-1,x=[],u=0;u<o.nodeSize;u++){var p=o.layoutNodes[u],w=p.parentId;null!=w?o.layoutNodes[o.idToIndex[w]].children.push(p.id):(v[++b]=p.id,x.push(p.id))}for(o.graphSet.push(x);y<=b;){var E=v[y++],k=o.idToIndex[E],h=o.layoutNodes[k],C=h.children;if(C.length>0){o.graphSet.push(C);for(var u=0;u<C.length;u++)v[++b]=C[u]}}for(var u=0;u<o.graphSet.length;u++)for(var S=o.graphSet[u],d=0;d<S.length;d++){var D=o.idToIndex[S[d]];o.indexToGraph[D]=u}for(var u=0;u<o.edgeSize;u++){var P=r[u],_={};_.id=P.data("id"),_.sourceId=P.data("source"),_.targetId=P.data("target");var M=T(n.idealEdgeLength)?n.idealEdgeLength(P):n.idealEdgeLength,B=T(n.edgeElasticity)?n.edgeElasticity(P):n.edgeElasticity,N=o.idToIndex[_.sourceId],A=o.idToIndex[_.targetId];if(o.indexToGraph[N]!=o.indexToGraph[A]){for(var I=oc(_.sourceId,_.targetId,o),O=o.graphSet[I],R=0,g=o.layoutNodes[N];-1===O.indexOf(g.id);)g=o.layoutNodes[o.idToIndex[g.parentId]],R++;for(g=o.layoutNodes[A];-1===O.indexOf(g.id);)g=o.layoutNodes[o.idToIndex[g.parentId]],R++;M*=R*n.nestingFactor}_.idealLength=M,_.elasticity=B,o.layoutEdges.push(_)}return o},oc=function(e,t,n){var r=od(e,t,0,n);return 2>r.count?0:r.graph},od=function e(t,n,r,i){var a=i.graphSet[r];if(-1<a.indexOf(t)&&-1<a.indexOf(n))return{count:2,graph:r};for(var o=0,s=0;s<a.length;s++){var l=a[s],u=i.idToIndex[l],c=i.layoutNodes[u].children;if(0!==c.length){var d=e(t,n,i.indexToGraph[i.idToIndex[c[0]]],i);if(0!==d.count){if(1!==d.count)return d;if(2==++o)break}}}return{count:o,graph:r}},oh=function(e,t){for(var n=e.clientWidth,r=e.clientHeight,i=0;i<e.nodeSize;i++){var a=e.layoutNodes[i];0!==a.children.length||a.isLocked||(a.positionX=Math.random()*n,a.positionY=Math.random()*r)}},op=function(e,t,n){var r=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(n.forEach(function(t){var n=e.layoutNodes[e.idToIndex[t.data("id")]];i.x1=Math.min(i.x1,n.positionX),i.x2=Math.max(i.x2,n.positionX),i.y1=Math.min(i.y1,n.positionY),i.y2=Math.max(i.y2,n.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(n,a){var o=e.layoutNodes[e.idToIndex[n.data("id")]];if(!t.boundingBox)return{x:o.positionX,y:o.positionY};var s=(o.positionX-i.x1)/i.w,l=(o.positionY-i.y1)/i.h;return{x:r.x1+s*r.w,y:r.y1+l*r.h}}},of=function(e,t,n){var r=n.layout,i=n.eles.nodes(),a=op(e,n,i);i.positions(a),!0!==e.ready&&(e.ready=!0,r.one("layoutready",n.ready),r.emit({type:"layoutready",layout:this}))},og=function(e,t,n){ov(e,t),ow(e),oE(e,t),ok(e),oC(e)},ov=function(e,t){for(var n=0;n<e.graphSet.length;n++)for(var r=e.graphSet[n],i=r.length,a=0;a<i;a++)for(var o=e.layoutNodes[e.idToIndex[r[a]]],s=a+1;s<i;s++)om(o,e.layoutNodes[e.idToIndex[r[s]]],e,t)},oy=function(e){return-e+2*e*Math.random()},om=function(e,t,n,r){if(e.cmptId===t.cmptId||n.isCompound){var i=t.positionX-e.positionX,a=t.positionY-e.positionY;0===i&&0===a&&(i=oy(1),a=oy(1));var o=ob(e,t,i,a);if(o>0)var s=r.nodeOverlap*o,l=Math.sqrt(i*i+a*a),u=s*i/l,c=s*a/l;else var d=ox(e,i,a),h=ox(t,-1*i,-1*a),p=h.x-d.x,f=h.y-d.y,g=p*p+f*f,l=Math.sqrt(g),s=(e.nodeRepulsion+t.nodeRepulsion)/g,u=s*p/l,c=s*f/l;e.isLocked||(e.offsetX-=u,e.offsetY-=c),t.isLocked||(t.offsetX+=u,t.offsetY+=c)}},ob=function(e,t,n,r){if(n>0)var i=e.maxX-t.minX;else var i=t.maxX-e.minX;if(r>0)var a=e.maxY-t.minY;else var a=t.maxY-e.minY;return i>=0&&a>=0?Math.sqrt(i*i+a*a):0},ox=function(e,t,n){var r=e.positionX,i=e.positionY,a=e.height||1,o=e.width||1,s=n/t,l=a/o,u={};return 0===t&&0<n||0===t&&0>n?(u.x=r,u.y=i+a/2,u):0<t&&-1*l<=s&&s<=l?(u.x=r+o/2,u.y=i+o*n/2/t,u):0>t&&-1*l<=s&&s<=l?(u.x=r-o/2,u.y=i-o*n/2/t,u):0<n&&(s<=-1*l||s>=l)?(u.x=r+a*t/2/n,u.y=i+a/2,u):(0>n&&(s<=-1*l||s>=l)&&(u.x=r-a*t/2/n,u.y=i-a/2),u)},ow=function(e,t){for(var n=0;n<e.edgeSize;n++){var r=e.layoutEdges[n],i=e.idToIndex[r.sourceId],a=e.layoutNodes[i],o=e.idToIndex[r.targetId],s=e.layoutNodes[o],l=s.positionX-a.positionX,u=s.positionY-a.positionY;if(0!==l||0!==u){var c=ox(a,l,u),d=ox(s,-1*l,-1*u),h=d.x-c.x,p=d.y-c.y,f=Math.sqrt(h*h+p*p),g=Math.pow(r.idealLength-f,2)/r.elasticity;if(0!==f)var v=g*h/f,y=g*p/f;else var v=0,y=0;a.isLocked||(a.offsetX+=v,a.offsetY+=y),s.isLocked||(s.offsetX-=v,s.offsetY-=y)}}},oE=function(e,t){if(0!==t.gravity)for(var n=0;n<e.graphSet.length;n++){var r=e.graphSet[n],i=r.length;if(0===n)var a=e.clientHeight/2,o=e.clientWidth/2;else var s=e.layoutNodes[e.idToIndex[r[0]]],l=e.layoutNodes[e.idToIndex[s.parentId]],a=l.positionX,o=l.positionY;for(var u=0;u<i;u++){var c=e.layoutNodes[e.idToIndex[r[u]]];if(!c.isLocked){var d=a-c.positionX,h=o-c.positionY,p=Math.sqrt(d*d+h*h);if(p>1){var f=t.gravity*d/p,g=t.gravity*h/p;c.offsetX+=f,c.offsetY+=g}}}}},ok=function(e,t){var n=[],r=0,i=-1;for(n.push.apply(n,e.graphSet[0]),i+=e.graphSet[0].length;r<=i;){var a=n[r++],o=e.idToIndex[a],s=e.layoutNodes[o],l=s.children;if(0<l.length&&!s.isLocked){for(var u=s.offsetX,c=s.offsetY,d=0;d<l.length;d++){var h=e.layoutNodes[e.idToIndex[l[d]]];h.offsetX+=u,h.offsetY+=c,n[++i]=l[d]}s.offsetX=0,s.offsetY=0}}},oC=function(e,t){for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];0<r.children.length&&(r.maxX=void 0,r.minX=void 0,r.maxY=void 0,r.minY=void 0)}for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];if(!(0<r.children.length)&&!r.isLocked){var i=oS(r.offsetX,r.offsetY,e.temperature);r.positionX+=i.x,r.positionY+=i.y,r.offsetX=0,r.offsetY=0,r.minX=r.positionX-r.width,r.maxX=r.positionX+r.width,r.minY=r.positionY-r.height,r.maxY=r.positionY+r.height,oD(r,e)}}for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];0<r.children.length&&!r.isLocked&&(r.positionX=(r.maxX+r.minX)/2,r.positionY=(r.maxY+r.minY)/2,r.width=r.maxX-r.minX,r.height=r.maxY-r.minY)}},oS=function(e,t,n){var r=Math.sqrt(e*e+t*t);if(r>n)var i={x:n*e/r,y:n*t/r};else var i={x:e,y:t};return i},oD=function e(t,n){var r=t.parentId;if(null!=r){var i=n.layoutNodes[n.idToIndex[r]],a=!1;if((null==i.maxX||t.maxX+i.padRight>i.maxX)&&(i.maxX=t.maxX+i.padRight,a=!0),(null==i.minX||t.minX-i.padLeft<i.minX)&&(i.minX=t.minX-i.padLeft,a=!0),(null==i.maxY||t.maxY+i.padBottom>i.maxY)&&(i.maxY=t.maxY+i.padBottom,a=!0),(null==i.minY||t.minY-i.padTop<i.minY)&&(i.minY=t.minY-i.padTop,a=!0),a)return e(i,n)}},oT=function(e,t){for(var n=e.layoutNodes,r=[],i=0;i<n.length;i++){var a=n[i],o=a.cmptId;(r[o]=r[o]||[]).push(a)}for(var s=0,i=0;i<r.length;i++){var l=r[i];if(l){l.x1=1/0,l.x2=-1/0,l.y1=1/0,l.y2=-1/0;for(var u=0;u<l.length;u++){var c=l[u];l.x1=Math.min(l.x1,c.positionX-c.width/2),l.x2=Math.max(l.x2,c.positionX+c.width/2),l.y1=Math.min(l.y1,c.positionY-c.height/2),l.y2=Math.max(l.y2,c.positionY+c.height/2)}l.w=l.x2-l.x1,l.h=l.y2-l.y1,s+=l.w*l.h}}r.sort(function(e,t){return t.w*t.h-e.w*e.h});for(var d=0,h=0,p=0,f=0,g=Math.sqrt(s)*e.clientWidth/e.clientHeight,i=0;i<r.length;i++){var l=r[i];if(l){for(var u=0;u<l.length;u++){var c=l[u];c.isLocked||(c.positionX+=d-l.x1,c.positionY+=h-l.y1)}d+=l.w+t.componentSpacing,p+=l.w+t.componentSpacing,f=Math.max(f,l.h),p>g&&(h+=f+t.componentSpacing,d=0,p=0,f=0)}}},oP={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function o_(e){this.options=Z({},oP,e)}o_.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=n.nodes().not(":parent");e.sort&&(r=r.sort(e.sort));var i=tA(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});if(0===i.h||0===i.w)n.nodes().layoutPositions(this,e,function(e){return{x:i.x1,y:i.y1}});else{var a=r.size(),o=Math.sqrt(a*i.h/i.w),s=Math.round(o),l=Math.round(i.w/i.h*o),u=function(e){if(null==e)return Math.min(s,l);Math.min(s,l)==s?s=e:l=e},c=function(e){if(null==e)return Math.max(s,l);Math.max(s,l)==s?s=e:l=e},d=e.rows,h=null!=e.cols?e.cols:e.columns;if(null!=d&&null!=h)s=d,l=h;else if(null!=d&&null==h)l=Math.ceil(a/(s=d));else if(null==d&&null!=h)s=Math.ceil(a/(l=h));else if(l*s>a){var p=u(),f=c();(p-1)*f>=a?u(p-1):(f-1)*p>=a&&c(f-1)}else for(;l*s<a;){var g=u(),v=c();(v+1)*g>=a?c(v+1):u(g+1)}var y=i.w/l,b=i.h/s;if(e.condense&&(y=0,b=0),e.avoidOverlap)for(var x=0;x<r.length;x++){var w=r[x],E=w._private.position;(null==E.x||null==E.y)&&(E.x=0,E.y=0);var k=w.layoutDimensions(e),C=e.avoidOverlapPadding,S=k.w+C,D=k.h+C;y=Math.max(y,S),b=Math.max(b,D)}for(var T={},P=function(e,t){return!!T["c-"+e+"-"+t]},_=function(e,t){T["c-"+e+"-"+t]=!0},M=0,B=0,N=function(){++B>=l&&(B=0,M++)},A={},I=0;I<r.length;I++){var O=r[I],R=e.position(O);if(R&&(void 0!==R.row||void 0!==R.col)){var z={row:R.row,col:R.col};if(void 0===z.col)for(z.col=0;P(z.row,z.col);)z.col++;else if(void 0===z.row)for(z.row=0;P(z.row,z.col);)z.row++;A[O.id()]=z,_(z.row,z.col)}}r.layoutPositions(this,e,function(e,t){if(e.locked()||e.isParent())return!1;var n,r,a=A[e.id()];if(a)n=a.col*y+y/2+i.x1,r=a.row*b+b/2+i.y1;else{for(;P(M,B);)N();n=B*y+y/2+i.x1,r=M*b+b/2+i.y1,_(M,B),N()}return{x:n,y:r}})}return this};var oM={ready:function(){},stop:function(){}};function oB(e){this.options=Z({},oM,e)}oB.prototype.run=function(){var e=this.options,t=e.eles;return e.cy,this.emit("layoutstart"),t.nodes().positions(function(){return{x:0,y:0}}),this.one("layoutready",e.ready),this.emit("layoutready"),this.one("layoutstop",e.stop),this.emit("layoutstop"),this},oB.prototype.stop=function(){return this};var oN={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function oA(e){this.options=Z({},oN,e)}oA.prototype.run=function(){var e=this.options,t=e.eles.nodes(),n=T(e.positions);return t.layoutPositions(this,e,function(t,r){var i=function(t){if(null==e.positions){var r;return{x:(r=t.position()).x,y:r.y}}if(n)return e.positions(t);var i=e.positions[t._private.data.id];return null==i?null:i}(t);return!t.locked()&&null!=i&&i}),this};var oI={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function oO(e){this.options=Z({},oI,e)}oO.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=tA(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});return n.nodes().layoutPositions(this,e,function(e,t){return{x:r.x1+Math.round(Math.random()*r.w),y:r.y1+Math.round(Math.random()*r.h)}}),this};var oR=[{name:"breadthfirst",impl:on},{name:"circle",impl:oi},{name:"concentric",impl:oo},{name:"cose",impl:ol},{name:"grid",impl:o_},{name:"null",impl:oB},{name:"preset",impl:oA},{name:"random",impl:oO}];function oz(e){this.options=e,this.notifications=0}var oL=function(){},oV=function(){throw Error("A headless instance can not render images")};oz.prototype={recalculateRenderedStyle:oL,notify:function(){this.notifications++},init:oL,isHeadless:function(){return!0},png:oV,jpg:oV};var oF={};oF.arrowShapeWidth=.3,oF.registerArrowShapes=function(){var e=this.arrowShapes={},t=this,n=function(e,t,n,r,i,a,o){var s=i.x-n/2-o,l=i.x+n/2+o,u=i.y-n/2-o,c=i.y+n/2+o;return s<=e&&e<=l&&u<=t&&t<=c},r=function(e,t,n,r,i){return{x:(e*Math.cos(r)-t*Math.sin(r))*n+i.x,y:(e*Math.sin(r)+t*Math.cos(r))*n+i.y}},i=function(e,t,n,i){for(var a=[],o=0;o<e.length;o+=2){var s=e[o],l=e[o+1];a.push(r(s,l,t,n,i))}return a},a=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r.x,r.y)}return t},o=function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").pfValue*2},s=function(r,s){D(s)&&(s=e[s]),e[r]=Z({name:r,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(e,t,n,r,o,s){return tK(e,t,a(i(this.points,n+2*s,r,o)))},roughCollide:n,draw:function(e,n,r,a){var o=i(this.points,n,r,a);t.arrowShapeImpl("polygon")(e,o)},spacing:function(e){return 0},gap:o},s)};s("none",{collide:eK,roughCollide:eK,draw:e$,spacing:eZ,gap:eZ}),s("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),s("arrow","triangle"),s("triangle-backcurve",{points:e.triangle.points,controlPoint:[0,-.15],roughCollide:n,draw:function(e,n,a,o,s){var l=i(this.points,n,a,o),u=this.controlPoint,c=r(u[0],u[1],n,a,o);t.arrowShapeImpl(this.name)(e,l,c)},gap:function(e){return .8*o(e)}}),s("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(e,t,n,r,o,s,l){var u=a(i(this.points,n+2*l,r,o)),c=a(i(this.pointsTee,n+2*l,r,o));return tK(e,t,u)||tK(e,t,c)},draw:function(e,n,r,a,o){var s=i(this.points,n,r,a),l=i(this.pointsTee,n,r,a);t.arrowShapeImpl(this.name)(e,s,l)}}),s("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(e,t,n,r,o,s,l){var u=Math.pow(o.x-e,2)+Math.pow(o.y-t,2)<=Math.pow((n+2*l)*this.radius,2);return tK(e,t,a(i(this.points,n+2*l,r,o)))||u},draw:function(e,n,r,a,o){var s=i(this.pointsTr,n,r,a);t.arrowShapeImpl(this.name)(e,s,a.x,a.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(e,t){var n=this.baseCrossLinePts.slice(),r=t/e;return n[3]=n[3]-r,n[5]=n[5]-r,n},collide:function(e,t,n,r,o,s,l){var u=a(i(this.points,n+2*l,r,o)),c=a(i(this.crossLinePts(n,s),n+2*l,r,o));return tK(e,t,u)||tK(e,t,c)},draw:function(e,n,r,a,o){var s=i(this.points,n,r,a),l=i(this.crossLinePts(n,o),n,r,a);t.arrowShapeImpl(this.name)(e,s,l)}}),s("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(e){return .525*o(e)}}),s("circle",{radius:.15,collide:function(e,t,n,r,i,a,o){return Math.pow(i.x-e,2)+Math.pow(i.y-t,2)<=Math.pow((n+2*o)*this.radius,2)},draw:function(e,n,r,i,a){t.arrowShapeImpl(this.name)(e,i.x,i.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(e){return 1},gap:function(e){return 1}}),s("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),s("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}}),s("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(e){return .95*e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}})};var oj={};oj.projectIntoViewport=function(e,t){var n=this.cy,r=this.findContainerClientCoords(),i=r[0],a=r[1],o=r[4],s=n.pan(),l=n.zoom();return[((e-i)/o-s.x)/l,((t-a)/o-s.y)/l]},oj.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var e=this.container,t=e.getBoundingClientRect(),n=this.cy.window().getComputedStyle(e),r=function(e){return parseFloat(n.getPropertyValue(e))},i={left:r("padding-left"),right:r("padding-right"),top:r("padding-top"),bottom:r("padding-bottom")},a={left:r("border-left-width"),right:r("border-right-width"),top:r("border-top-width"),bottom:r("border-bottom-width")},o=e.clientWidth,s=e.clientHeight,l=i.left+i.right,u=i.top+i.bottom,c=a.left+a.right,d=t.width/(o+c),h=t.left+i.left+a.left,p=t.top+i.top+a.top;return this.containerBB=[h,p,o-l,s-u,d]},oj.invalidateContainerClientCoordsCache=function(){this.containerBB=null},oj.findNearestElement=function(e,t,n,r){return this.findNearestElements(e,t,n,r)[0]},oj.findNearestElements=function(e,t,n,r){var i,a,o=this,s=this,l=s.getCachedZSortedEles(),u=[],c=s.cy.zoom(),d=s.cy.hasCompoundNodes(),h=(r?24:8)/c,p=(r?8:2)/c,f=(r?8:2)/c,g=1/0;function v(e,t){if(e.isNode()){if(a)return;a=e,u.push(e)}if(e.isEdge()&&(null==t||t<g)){if(i){if(i.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value&&i.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value){for(var n=0;n<u.length;n++)if(u[n].isEdge()){u[n]=e,i=e,g=null!=t?t:g;break}}}else u.push(e),i=e,g=null!=t?t:g}}function y(n){var r=n.outerWidth()+2*p,i=n.outerHeight()+2*p,a=r/2,l=i/2,u=n.position(),c="auto"===n.pstyle("corner-radius").value?"auto":n.pstyle("corner-radius").pfValue,d=n._private.rscratch;if(u.x-a<=e&&e<=u.x+a&&u.y-l<=t&&t<=u.y+l&&s.nodeShapes[o.getNodeShape(n)].checkPoint(e,t,0,r,i,u.x,u.y,c,d))return v(n,0),!0}function b(n,r){var i,a=n._private;i=r?r+"-":"",n.boundingBox();var o=a.labelBounds[r||"main"],s=n.pstyle(i+"label").value;if("yes"===n.pstyle("text-events").strValue&&s){var l=e7(a.rscratch,"labelX",r),u=e7(a.rscratch,"labelY",r),c=e7(a.rscratch,"labelAngle",r),d=n.pstyle(i+"text-margin-x").pfValue,h=n.pstyle(i+"text-margin-y").pfValue,p=o.x1-f-d,g=o.x2+f-d,y=o.y1-f-h,b=o.y2+f-h;if(c){var x=Math.cos(c),w=Math.sin(c),E=function(e,t){return{x:(e-=l)*x-(t-=u)*w+l,y:e*w+t*x+u}},k=E(p,y),C=E(p,b),S=E(g,y),D=E(g,b);if(tK(e,t,[k.x+d,k.y+h,S.x+d,S.y+h,D.x+d,D.y+h,C.x+d,C.y+h]))return v(n),!0}else if(tj(o,e,t))return v(n),!0}}n&&(l=l.interactive);for(var x=l.length-1;x>=0;x--){var w=l[x];w.isNode()?y(w)||b(w):function(n){var r,i=n._private,a=i.rscratch,l=n.pstyle("width").pfValue,c=n.pstyle("arrow-scale").value,p=l/2+h,f=p*p,g=2*p,b=i.source,x=i.target;if("segments"===a.edgeType||"straight"===a.edgeType||"haystack"===a.edgeType){for(var w,E,k,C,S=a.allpts,D=0;D+3<S.length;D+=2)if(w=S[D],E=S[D+1],k=S[D+2],C=S[D+3],Math.min(w,k)-g<=e&&e<=Math.max(w,k)+g&&Math.min(E,C)-g<=t&&t<=Math.max(E,C)+g&&f>(r=tU(e,t,S[D],S[D+1],S[D+2],S[D+3])))return v(n,r),!0}else if("bezier"===a.edgeType||"multibezier"===a.edgeType||"self"===a.edgeType||"compound"===a.edgeType){for(var S=a.allpts,D=0;D+5<a.allpts.length;D+=4)if(tY(e,t,S[D],S[D+1],S[D+2],S[D+3],S[D+4],S[D+5],g)&&f>(r=tG(e,t,S[D],S[D+1],S[D+2],S[D+3],S[D+4],S[D+5])))return v(n,r),!0}for(var b=b||i.source,x=x||i.target,T=o.getArrowWidth(l,c),P=[{name:"source",x:a.arrowStartX,y:a.arrowStartY,angle:a.srcArrowAngle},{name:"target",x:a.arrowEndX,y:a.arrowEndY,angle:a.tgtArrowAngle},{name:"mid-source",x:a.midX,y:a.midY,angle:a.midsrcArrowAngle},{name:"mid-target",x:a.midX,y:a.midY,angle:a.midtgtArrowAngle}],D=0;D<P.length;D++){var _=P[D],M=s.arrowShapes[n.pstyle(_.name+"-arrow-shape").value],B=n.pstyle("width").pfValue;if(M.roughCollide(e,t,T,_.angle,{x:_.x,y:_.y},B,h)&&M.collide(e,t,T,_.angle,{x:_.x,y:_.y},B,h))return v(n),!0}d&&u.length>0&&(y(b),y(x))}(w)||b(w)||b(w,"source")||b(w,"target")}return u},oj.getAllInBox=function(e,t,n,r){var i=this.getCachedZSortedEles().interactive,a=[],o=Math.min(e,n),s=Math.max(e,n),l=Math.min(t,r),u=Math.max(t,r);e=o,n=s,t=l,r=u;for(var c=tA({x1:e,y1:t,x2:n,y2:r}),d=0;d<i.length;d++){var h=i[d];if(h.isNode()){var p=h.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});tF(c,p)&&!tq(p,c)&&a.push(h)}else{var f=h._private,g=f.rscratch;if(null!=g.startX&&null!=g.startY&&!tj(c,g.startX,g.startY)||null!=g.endX&&null!=g.endY&&!tj(c,g.endX,g.endY))continue;if("bezier"===g.edgeType||"multibezier"===g.edgeType||"self"===g.edgeType||"compound"===g.edgeType||"segments"===g.edgeType||"haystack"===g.edgeType){for(var v,y=f.rstyle.bezierPts||f.rstyle.linePts||f.rstyle.haystackPts,b=!0,x=0;x<y.length;x++)if(!tj(c,(v=y[x]).x,v.y)){b=!1;break}b&&a.push(h)}else("haystack"===g.edgeType||"straight"===g.edgeType)&&a.push(h)}}return a};var oq={};oq.calculateArrowAngles=function(e){var t=e._private.rscratch,n="haystack"===t.edgeType,r="bezier"===t.edgeType,i="multibezier"===t.edgeType,a="segments"===t.edgeType,o="compound"===t.edgeType,s="self"===t.edgeType;if(n?(y=t.haystackPts[0],b=t.haystackPts[1],x=t.haystackPts[2],w=t.haystackPts[3]):(y=t.arrowStartX,b=t.arrowStartY,x=t.arrowEndX,w=t.arrowEndY),d=t.midX,h=t.midY,a)g=y-t.segpts[0],v=b-t.segpts[1];else if(i||o||s||r){var l=t.allpts,u=t_(l[0],l[2],l[4],.1),c=t_(l[1],l[3],l[5],.1);g=y-u,v=b-c}else g=y-d,v=b-h;t.srcArrowAngle=tk(g,v);var d=t.midX,h=t.midY;if(n&&(d=(y+x)/2,h=(b+w)/2),g=x-y,v=w-b,a){var l=t.allpts;if(l.length/2%2==0){var p=l.length/2,f=p-2;g=l[p]-l[f],v=l[p+1]-l[f+1]}else if(t.isRound)g=t.midVector[1],v=-t.midVector[0];else{var p=l.length/2-1,f=p-2;g=l[p]-l[f],v=l[p+1]-l[f+1]}}else if(i||o||s){var g,v,y,b,x,w,d,h,E,k,C,S,l=t.allpts;if(t.ctrlpts.length/2%2==0){var D=l.length/2-1,T=D+2,P=T+2;E=t_(l[D],l[T],l[P],0),k=t_(l[D+1],l[T+1],l[P+1],0),C=t_(l[D],l[T],l[P],1e-4),S=t_(l[D+1],l[T+1],l[P+1],1e-4)}else{var T=l.length/2-1,D=T-2,P=T+2;E=t_(l[D],l[T],l[P],.4999),k=t_(l[D+1],l[T+1],l[P+1],.4999),C=t_(l[D],l[T],l[P],.5),S=t_(l[D+1],l[T+1],l[P+1],.5)}g=C-E,v=S-k}if(t.midtgtArrowAngle=tk(g,v),t.midDispX=g,t.midDispY=v,g*=-1,v*=-1,a){var l=t.allpts;if(l.length/2%2==0);else if(!t.isRound){var p=l.length/2-1,_=p+2;g=-(l[_]-l[p]),v=-(l[_+1]-l[p+1])}}if(t.midsrcArrowAngle=tk(g,v),a)g=x-t.segpts[t.segpts.length-2],v=w-t.segpts[t.segpts.length-1];else if(i||o||s||r){var l=t.allpts,M=l.length,u=t_(l[M-6],l[M-4],l[M-2],.9),c=t_(l[M-5],l[M-3],l[M-1],.9);g=x-u,v=w-c}else g=x-d,v=w-h;t.tgtArrowAngle=tk(g,v)},oq.getArrowWidth=oq.getArrowHeight=function(e,t){var n=this.arrowWidthCache=this.arrowWidthCache||{},r=n[e+", "+t];return r||(r=Math.max(Math.pow(13.37*e,.9),29)*t,n[e+", "+t]=r),r};var oX,oY,oW,oH,oG,oU,oK,oZ,o$,oQ,oJ,o0,o1,o2,o5,o3,o4,o9,o6,o8,o7,se,st,sn,sr,si,sa,so={},ss={},sl=function(e,t,n){n.x=t.x-e.x,n.y=t.y-e.y,n.len=Math.sqrt(n.x*n.x+n.y*n.y),n.nx=n.x/n.len,n.ny=n.y/n.len,n.ang=Math.atan2(n.ny,n.nx)},su=function(e,t){t.x=-1*e.x,t.y=-1*e.y,t.nx=-1*e.nx,t.ny=-1*e.ny,t.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},sc=function(e,t,n,r,i){if(e!==v?sl(t,e,so):su(ss,so),sl(t,n,ss),o9=so.nx*ss.ny-so.ny*ss.nx,o6=so.nx*ss.nx- -(so.ny*ss.ny),1e-6>Math.abs(se=Math.asin(Math.max(-1,Math.min(1,o9))))){o3=t.x,o4=t.y,sn=si=0;return}o8=1,o7=!1,o6<0?se<0?se=Math.PI+se:(se=Math.PI-se,o8=-1,o7=!0):se>0&&(o8=-1,o7=!0),si=void 0!==t.radius?t.radius:r,st=se/2,sa=Math.min(so.len/2,ss.len/2),sn=i?(sr=Math.abs(Math.cos(st)*si/Math.sin(st)))>sa?Math.abs((sr=sa)*Math.sin(st)/Math.cos(st)):si:Math.abs((sr=Math.min(sa,si))*Math.sin(st)/Math.cos(st)),f=t.x+ss.nx*sr,g=t.y+ss.ny*sr,o3=f-ss.ny*sn*o8,o4=g+ss.nx*sn*o8,h=t.x+so.nx*sr,p=t.y+so.ny*sr,v=t};function sd(e,t){0===t.radius?e.lineTo(t.cx,t.cy):e.arc(t.cx,t.cy,t.radius,t.startAngle,t.endAngle,t.counterClockwise)}function sh(e,t,n,r){var i=!(arguments.length>4)||void 0===arguments[4]||arguments[4];return 0===r||0===t.radius?{cx:t.x,cy:t.y,radius:0,startX:t.x,startY:t.y,stopX:t.x,stopY:t.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(sc(e,t,n,r,i),{cx:o3,cy:o4,radius:sn,startX:h,startY:p,stopX:f,stopY:g,startAngle:so.ang+Math.PI/2*o8,endAngle:ss.ang-Math.PI/2*o8,counterClockwise:o7})}var sp={};function sf(e){var t=[];if(null!=e){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1];t.push({x:r,y:i})}return t}}sp.findMidptPtsEtc=function(e,t){var n,r=t.posPts,i=t.intersectionPts,a=t.vectorNormInverse,o=e.pstyle("source-endpoint"),s=e.pstyle("target-endpoint"),u=null!=o.units&&null!=s.units;switch(e.pstyle("edge-distances").value){case"node-position":n=r;break;case"intersection":n=i;break;case"endpoints":if(u){var c,d,h,p=l(this.manualEndptToPx(e.source()[0],o),2),f=p[0],g=p[1],v=l(this.manualEndptToPx(e.target()[0],s),2),y=v[0],b=v[1];c=b-g,h=Math.sqrt((d=y-f)*d+c*c),a={x:-c/h,y:d/h},n={x1:f,y1:g,x2:y,y2:b}}else e0("Edge ".concat(e.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),n=i}return{midptPts:n,vectorNormInverse:a}},sp.findHaystackPoints=function(e){for(var t=0;t<e.length;t++){var n=e[t],r=n._private,i=r.rscratch;if(!i.haystack){var a=2*Math.random()*Math.PI;i.source={x:Math.cos(a),y:Math.sin(a)},a=2*Math.random()*Math.PI,i.target={x:Math.cos(a),y:Math.sin(a)}}var o=r.source,s=r.target,l=o.position(),u=s.position(),c=o.width(),d=s.width(),h=o.height(),p=s.height(),f=n.pstyle("haystack-radius").value/2;i.haystackPts=i.allpts=[i.source.x*c*f+l.x,i.source.y*h*f+l.y,i.target.x*d*f+u.x,i.target.y*p*f+u.y],i.midX=(i.allpts[0]+i.allpts[2])/2,i.midY=(i.allpts[1]+i.allpts[3])/2,i.edgeType="haystack",i.haystack=!0,this.storeEdgeProjections(n),this.calculateArrowAngles(n),this.recalculateEdgeLabelProjections(n),this.calculateLabelAngles(n)}},sp.findSegmentsPoints=function(e,t){var n=e._private.rscratch,r=e.pstyle("segment-weights"),i=e.pstyle("segment-distances"),a=e.pstyle("segment-radii"),o=e.pstyle("radius-type"),s=Math.min(r.pfValue.length,i.pfValue.length),l=a.pfValue[a.pfValue.length-1],u=o.pfValue[o.pfValue.length-1];n.edgeType="segments",n.segpts=[],n.radii=[],n.isArcRadius=[];for(var c=0;c<s;c++){var d=r.pfValue[c],h=i.pfValue[c],p=1-d,f=this.findMidptPtsEtc(e,t),g=f.midptPts,v=f.vectorNormInverse,y={x:g.x1*p+g.x2*d,y:g.y1*p+g.y2*d};n.segpts.push(y.x+v.x*h,y.y+v.y*h),n.radii.push(void 0!==a.pfValue[c]?a.pfValue[c]:l),n.isArcRadius.push((void 0!==o.pfValue[c]?o.pfValue[c]:u)==="arc-radius")}},sp.findLoopPoints=function(e,t,n,r){var i=e._private.rscratch,a=t.dirCounts,o=t.srcPos,s=e.pstyle("control-point-distances"),l=s?s.pfValue[0]:void 0,u=e.pstyle("loop-direction").pfValue,c=e.pstyle("loop-sweep").pfValue,d=e.pstyle("control-point-step-size").pfValue;i.edgeType="self";var h=n,p=d;r&&(h=0,p=l);var f=u-Math.PI/2,g=f-c/2,v=f+c/2,y=String(u+"_"+c);h=void 0===a[y]?a[y]=0:++a[y],i.ctrlpts=[o.x+1.4*Math.cos(g)*p*(h/3+1),o.y+1.4*Math.sin(g)*p*(h/3+1),o.x+1.4*Math.cos(v)*p*(h/3+1),o.y+1.4*Math.sin(v)*p*(h/3+1)]},sp.findCompoundLoopPoints=function(e,t,n,r){var i=e._private.rscratch;i.edgeType="compound";var a=t.srcPos,o=t.tgtPos,s=t.srcW,l=t.srcH,u=t.tgtW,c=t.tgtH,d=e.pstyle("control-point-step-size").pfValue,h=e.pstyle("control-point-distances"),p=h?h.pfValue[0]:void 0,f=n,g=d;r&&(f=0,g=p);var v={x:a.x-s/2,y:a.y-l/2},y={x:o.x-u/2,y:o.y-c/2},b={x:Math.min(v.x,y.x),y:Math.min(v.y,y.y)};i.ctrlpts=[b.x,b.y-1.7995514309304248*g*(f/3+1)*Math.max(.5,Math.log(.01*s)),b.x-1.7995514309304248*g*(f/3+1)*Math.max(.5,Math.log(.01*u)),b.y]},sp.findStraightEdgePoints=function(e){e._private.rscratch.edgeType="straight"},sp.findBezierPoints=function(e,t,n,r,i){var a=e._private.rscratch,o=e.pstyle("control-point-step-size").pfValue,s=e.pstyle("control-point-distances"),l=e.pstyle("control-point-weights"),u=s&&l?Math.min(s.value.length,l.value.length):1,c=s?s.pfValue[0]:void 0,d=l.value[0];a.edgeType=r?"multibezier":"bezier",a.ctrlpts=[];for(var h=0;h<u;h++){var p=(.5-t.eles.length/2+n)*o*(i?-1:1),f=void 0,g=tS(p);r&&(c=s?s.pfValue[h]:o,d=l.value[h]);var v=void 0!==(f=r?c:void 0!==c?g*c:void 0)?f:p,y=1-d,b=d,x=this.findMidptPtsEtc(e,t),w=x.midptPts,E=x.vectorNormInverse,k={x:w.x1*y+w.x2*b,y:w.y1*y+w.y2*b};a.ctrlpts.push(k.x+E.x*v,k.y+E.y*v)}},sp.findTaxiPoints=function(e,t){var n,r=e._private.rscratch;r.edgeType="segments";var i="vertical",a="horizontal",o="leftward",s="rightward",l="downward",u="upward",c=t.posPts,d=t.srcW,h=t.srcH,p=t.tgtW,f=t.tgtH,g="node-position"!==e.pstyle("edge-distances").value,v=e.pstyle("taxi-direction").value,y=v,b=e.pstyle("taxi-turn"),x="%"===b.units,w=b.pfValue,E=e.pstyle("taxi-turn-min-distance").pfValue,k=c.x2-c.x1,C=c.y2-c.y1,S=function(e,t){return e>0?Math.max(e-t,0):Math.min(e+t,0)},D=S(k,g?(d+p)/2:0),T=S(C,g?(h+f)/2:0),P=!1;"auto"===y?v=Math.abs(D)>Math.abs(T)?a:i:y===u||y===l?(v=i,P=!0):(y===o||y===s)&&(v=a,P=!0);var _=v===i,M=_?T:D,B=_?C:k,N=tS(B),A=!1;!(P&&(x||w<0))&&(y===l&&B<0||y===u&&B>0||y===o&&B>0||y===s&&B<0)&&(N*=-1,M=N*Math.abs(M),A=!0);var I=function(e){return Math.abs(e)<E||Math.abs(e)>=Math.abs(M)},O=I(n=x?(w<0?1+w:w)*M:(w<0?M:0)+w*N),R=I(Math.abs(M)-Math.abs(n));if((O||R)&&!A){if(_){if(Math.abs(B)<=h/2){var z=(c.x1+c.x2)/2,L=c.y1,V=c.y2;r.segpts=[z,L,z,V]}else if(Math.abs(k)<=p/2){var F=(c.y1+c.y2)/2,j=c.x1,q=c.x2;r.segpts=[j,F,q,F]}else r.segpts=[c.x1,c.y2]}else if(Math.abs(B)<=d/2){var X=(c.y1+c.y2)/2,Y=c.x1,W=c.x2;r.segpts=[Y,X,W,X]}else if(Math.abs(C)<=f/2){var H=(c.x1+c.x2)/2,G=c.y1,U=c.y2;r.segpts=[H,G,H,U]}else r.segpts=[c.x2,c.y1]}else if(_){var K=c.y1+n+(g?h/2*N:0),Z=c.x1,$=c.x2;r.segpts=[Z,K,$,K]}else{var Q=c.x1+n+(g?d/2*N:0),J=c.y1,ee=c.y2;r.segpts=[Q,J,Q,ee]}if(r.isRound){var et=e.pstyle("taxi-radius").value,en="arc-radius"===e.pstyle("radius-type").value[0];r.radii=Array(r.segpts.length/2).fill(et),r.isArcRadius=Array(r.segpts.length/2).fill(en)}},sp.tryToCorrectInvalidPoints=function(e,t){var n=e._private.rscratch;if("bezier"===n.edgeType){var r=t.srcPos,i=t.tgtPos,a=t.srcW,o=t.srcH,s=t.tgtW,l=t.tgtH,u=t.srcShape,c=t.tgtShape,d=t.srcCornerRadius,h=t.tgtCornerRadius,p=t.srcRs,f=t.tgtRs,g=!M(n.startX)||!M(n.startY),v=!M(n.arrowStartX)||!M(n.arrowStartY),y=!M(n.endX)||!M(n.endY),b=!M(n.arrowEndX)||!M(n.arrowEndY),x=3*(this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth),w=tD({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.startX,y:n.startY}),E=w<x,k=tD({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.endX,y:n.endY}),C=k<x,S=!1;if(g||v||E){S=!0;var D={x:n.ctrlpts[0]-r.x,y:n.ctrlpts[1]-r.y},T=Math.sqrt(D.x*D.x+D.y*D.y),P={x:D.x/T,y:D.y/T},_=Math.max(a,o),B={x:n.ctrlpts[0]+2*P.x*_,y:n.ctrlpts[1]+2*P.y*_},N=u.intersectLine(r.x,r.y,a,o,B.x,B.y,0,d,p);E?(n.ctrlpts[0]=n.ctrlpts[0]+P.x*(x-w),n.ctrlpts[1]=n.ctrlpts[1]+P.y*(x-w)):(n.ctrlpts[0]=N[0]+P.x*x,n.ctrlpts[1]=N[1]+P.y*x)}if(y||b||C){S=!0;var A={x:n.ctrlpts[0]-i.x,y:n.ctrlpts[1]-i.y},I=Math.sqrt(A.x*A.x+A.y*A.y),O={x:A.x/I,y:A.y/I},R=Math.max(a,o),z={x:n.ctrlpts[0]+2*O.x*R,y:n.ctrlpts[1]+2*O.y*R},L=c.intersectLine(i.x,i.y,s,l,z.x,z.y,0,h,f);C?(n.ctrlpts[0]=n.ctrlpts[0]+O.x*(x-k),n.ctrlpts[1]=n.ctrlpts[1]+O.y*(x-k)):(n.ctrlpts[0]=L[0]+O.x*x,n.ctrlpts[1]=L[1]+O.y*x)}S&&this.findEndpoints(e)}},sp.storeAllpts=function(e){var t=e._private.rscratch;if("multibezier"===t.edgeType||"bezier"===t.edgeType||"self"===t.edgeType||"compound"===t.edgeType){t.allpts=[],t.allpts.push(t.startX,t.startY);for(var n,r=0;r+1<t.ctrlpts.length;r+=2)t.allpts.push(t.ctrlpts[r],t.ctrlpts[r+1]),r+3<t.ctrlpts.length&&t.allpts.push((t.ctrlpts[r]+t.ctrlpts[r+2])/2,(t.ctrlpts[r+1]+t.ctrlpts[r+3])/2);(t.allpts.push(t.endX,t.endY),t.ctrlpts.length/2%2==0)?(n=t.allpts.length/2-1,t.midX=t.allpts[n],t.midY=t.allpts[n+1]):(n=t.allpts.length/2-3,t.midX=t_(t.allpts[n],t.allpts[n+2],t.allpts[n+4],.5),t.midY=t_(t.allpts[n+1],t.allpts[n+3],t.allpts[n+5],.5))}else if("straight"===t.edgeType)t.allpts=[t.startX,t.startY,t.endX,t.endY],t.midX=(t.startX+t.endX+t.arrowStartX+t.arrowEndX)/4,t.midY=(t.startY+t.endY+t.arrowStartY+t.arrowEndY)/4;else if("segments"===t.edgeType){if(t.allpts=[],t.allpts.push(t.startX,t.startY),t.allpts.push.apply(t.allpts,t.segpts),t.allpts.push(t.endX,t.endY),t.isRound){t.roundCorners=[];for(var i=2;i+3<t.allpts.length;i+=2){var a=t.radii[i/2-1],o=t.isArcRadius[i/2-1];t.roundCorners.push(sh({x:t.allpts[i-2],y:t.allpts[i-1]},{x:t.allpts[i],y:t.allpts[i+1],radius:a},{x:t.allpts[i+2],y:t.allpts[i+3]},a,o))}}if(t.segpts.length%4==0){var s=t.segpts.length/2,l=s-2;t.midX=(t.segpts[l]+t.segpts[s])/2,t.midY=(t.segpts[l+1]+t.segpts[s+1])/2}else{var u=t.segpts.length/2-1;if(t.isRound){var c={x:t.segpts[u],y:t.segpts[u+1]},d=t.roundCorners[u/2],h=[c.x-d.cx,c.y-d.cy],p=d.radius/Math.sqrt(Math.pow(h[0],2)+Math.pow(h[1],2));h=h.map(function(e){return e*p}),t.midX=d.cx+h[0],t.midY=d.cy+h[1],t.midVector=h}else t.midX=t.segpts[u],t.midY=t.segpts[u+1]}}},sp.checkForInvalidEdgeWarning=function(e){var t=e[0]._private.rscratch;t.nodesOverlap||M(t.startX)&&M(t.startY)&&M(t.endX)&&M(t.endY)?t.loggedErr=!1:t.loggedErr||(t.loggedErr=!0,e0("Edge `"+e.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))},sp.findEdgeControlPoints=function(e){var t=this;if(e&&0!==e.length){for(var n=this,r=n.cy.hasCompoundNodes(),i={map:new tn,get:function(e){var t=this.map.get(e[0]);return null!=t?t.get(e[1]):null},set:function(e,t){var n=this.map.get(e[0]);null==n&&(n=new tn,this.map.set(e[0],n)),n.set(e[1],t)}},a=[],o=[],s=0;s<e.length;s++){var l=e[s],u=l._private,c=l.pstyle("curve-style").value;if(!l.removed()&&l.takesUpSpace()){if("haystack"===c){o.push(l);continue}var d="unbundled-bezier"===c||c.endsWith("segments")||"straight"===c||"straight-triangle"===c||c.endsWith("taxi"),h="unbundled-bezier"===c||"bezier"===c,p=u.source,f=u.target,g=[p.poolIndex(),f.poolIndex()].sort(),v=i.get(g);null==v&&(v={eles:[]},i.set(g,v),a.push(g)),v.eles.push(l),d&&(v.hasUnbundled=!0),h&&(v.hasBezier=!0)}}for(var y=0;y<a.length;y++)!function(e){var o=a[e],s=i.get(o),l=void 0;if(!s.hasUnbundled){var u=s.eles[0].parallelEdges().filter(function(e){return e.isBundledBezier()});e6(s.eles),u.forEach(function(e){return s.eles.push(e)}),s.eles.sort(function(e,t){return e.poolIndex()-t.poolIndex()})}var c=s.eles[0],d=c.source(),h=c.target();if(d.poolIndex()>h.poolIndex()){var p=d;d=h,h=p}var f=s.srcPos=d.position(),g=s.tgtPos=h.position(),v=s.srcW=d.outerWidth(),y=s.srcH=d.outerHeight(),b=s.tgtW=h.outerWidth(),x=s.tgtH=h.outerHeight(),w=s.srcShape=n.nodeShapes[t.getNodeShape(d)],E=s.tgtShape=n.nodeShapes[t.getNodeShape(h)],k=s.srcCornerRadius="auto"===d.pstyle("corner-radius").value?"auto":d.pstyle("corner-radius").pfValue,C=s.tgtCornerRadius="auto"===h.pstyle("corner-radius").value?"auto":h.pstyle("corner-radius").pfValue,S=s.tgtRs=h._private.rscratch,D=s.srcRs=d._private.rscratch;s.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var T=0;T<s.eles.length;T++){var P=s.eles[T],_=P[0]._private.rscratch,B=P.pstyle("curve-style").value,N="unbundled-bezier"===B||B.endsWith("segments")||B.endsWith("taxi"),A=!d.same(P.source());if(!s.calculatedIntersection&&d!==h&&(s.hasBezier||s.hasUnbundled)){s.calculatedIntersection=!0;var I=w.intersectLine(f.x,f.y,v,y,g.x,g.y,0,k,D),O=s.srcIntn=I,R=E.intersectLine(g.x,g.y,b,x,f.x,f.y,0,C,S),z=s.tgtIntn=R,L=s.intersectionPts={x1:I[0],x2:R[0],y1:I[1],y2:R[1]},V=s.posPts={x1:f.x,x2:g.x,y1:f.y,y2:g.y},F=R[1]-I[1],j=R[0]-I[0],q=Math.sqrt(j*j+F*F),X=s.vector={x:j,y:F},Y=s.vectorNorm={x:X.x/q,y:X.y/q},W={x:-Y.y,y:Y.x};s.nodesOverlap=!M(q)||E.checkPoint(I[0],I[1],0,b,x,g.x,g.y,C,S)||w.checkPoint(R[0],R[1],0,v,y,f.x,f.y,k,D),s.vectorNormInverse=W,l={nodesOverlap:s.nodesOverlap,dirCounts:s.dirCounts,calculatedIntersection:!0,hasBezier:s.hasBezier,hasUnbundled:s.hasUnbundled,eles:s.eles,srcPos:g,srcRs:S,tgtPos:f,tgtRs:D,srcW:b,srcH:x,tgtW:v,tgtH:y,srcIntn:z,tgtIntn:O,srcShape:E,tgtShape:w,posPts:{x1:V.x2,y1:V.y2,x2:V.x1,y2:V.y1},intersectionPts:{x1:L.x2,y1:L.y2,x2:L.x1,y2:L.y1},vector:{x:-X.x,y:-X.y},vectorNorm:{x:-Y.x,y:-Y.y},vectorNormInverse:{x:-W.x,y:-W.y}}}var H=A?l:s;_.nodesOverlap=H.nodesOverlap,_.srcIntn=H.srcIntn,_.tgtIntn=H.tgtIntn,_.isRound=B.startsWith("round"),r&&(d.isParent()||d.isChild()||h.isParent()||h.isChild())&&(d.parents().anySame(h)||h.parents().anySame(d)||d.same(h)&&d.isParent())?t.findCompoundLoopPoints(P,H,T,N):d===h?t.findLoopPoints(P,H,T,N):B.endsWith("segments")?t.findSegmentsPoints(P,H):B.endsWith("taxi")?t.findTaxiPoints(P,H):"straight"!==B&&(N||s.eles.length%2!=1||T!==Math.floor(s.eles.length/2))?t.findBezierPoints(P,H,T,N,A):t.findStraightEdgePoints(P),t.findEndpoints(P),t.tryToCorrectInvalidPoints(P,H),t.checkForInvalidEdgeWarning(P),t.storeAllpts(P),t.storeEdgeProjections(P),t.calculateArrowAngles(P),t.recalculateEdgeLabelProjections(P),t.calculateLabelAngles(P)}}(y);this.findHaystackPoints(o)}},sp.getSegmentPoints=function(e){var t=e[0]._private.rscratch;if(this.recalculateRenderedStyle(e),"segments"===t.edgeType)return sf(t.segpts)},sp.getControlPoints=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);var n=t.edgeType;if("bezier"===n||"multibezier"===n||"self"===n||"compound"===n)return sf(t.ctrlpts)},sp.getEdgeMidpoint=function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),{x:t.midX,y:t.midY}};var sg={};sg.manualEndptToPx=function(e,t){var n=e.position(),r=e.outerWidth(),i=e.outerHeight(),a=e._private.rscratch;if(2===t.value.length){var o=[t.pfValue[0],t.pfValue[1]];return"%"===t.units[0]&&(o[0]=o[0]*r),"%"===t.units[1]&&(o[1]=o[1]*i),o[0]+=n.x,o[1]+=n.y,o}var s=t.pfValue[0];s=-Math.PI/2+s;var l=2*Math.max(r,i),u=[n.x+Math.cos(s)*l,n.y+Math.sin(s)*l];return this.nodeShapes[this.getNodeShape(e)].intersectLine(n.x,n.y,r,i,u[0],u[1],0,"auto"===e.pstyle("corner-radius").value?"auto":e.pstyle("corner-radius").pfValue,a)},sg.findEndpoints=function(e){var t,n,r,i,a,o=e.source()[0],s=e.target()[0],l=o.position(),u=s.position(),c=e.pstyle("target-arrow-shape").value,d=e.pstyle("source-arrow-shape").value,h=e.pstyle("target-distance-from-node").pfValue,p=e.pstyle("source-distance-from-node").pfValue,f=o._private.rscratch,g=s._private.rscratch,v=e.pstyle("curve-style").value,y=e._private.rscratch,b=y.edgeType,x="self"===b||"compound"===b,w="bezier"===b||"multibezier"===b||x,E="bezier"!==b,k="straight"===b||"segments"===b,C="segments"===b,S=x||"taxi"===v,D=e.pstyle("source-endpoint"),T=S?"outside-to-node":D.value,P="auto"===o.pstyle("corner-radius").value?"auto":o.pstyle("corner-radius").pfValue,_=e.pstyle("target-endpoint"),B=S?"outside-to-node":_.value,N="auto"===s.pstyle("corner-radius").value?"auto":s.pstyle("corner-radius").pfValue;if(y.srcManEndpt=D,y.tgtManEndpt=_,w){var A=[y.ctrlpts[0],y.ctrlpts[1]];n=E?[y.ctrlpts[y.ctrlpts.length-2],y.ctrlpts[y.ctrlpts.length-1]]:A,r=A}else if(k){var I=C?y.segpts.slice(0,2):[u.x,u.y];n=C?y.segpts.slice(y.segpts.length-2):[l.x,l.y],r=I}if("inside-to-node"===B)t=[u.x,u.y];else if(_.units)t=this.manualEndptToPx(s,_);else if("outside-to-line"===B)t=y.tgtIntn;else if("outside-to-node"===B||"outside-to-node-or-label"===B?i=n:("outside-to-line"===B||"outside-to-line-or-label"===B)&&(i=[l.x,l.y]),t=this.nodeShapes[this.getNodeShape(s)].intersectLine(u.x,u.y,s.outerWidth(),s.outerHeight(),i[0],i[1],0,N,g),"outside-to-node-or-label"===B||"outside-to-line-or-label"===B){var O=s._private.rscratch,R=O.labelWidth,z=O.labelHeight,L=O.labelX,V=O.labelY,F=R/2,j=z/2,q=s.pstyle("text-valign").value;"top"===q?V-=j:"bottom"===q&&(V+=j);var X=s.pstyle("text-halign").value;"left"===X?L-=F:"right"===X&&(L+=F);var Y=t4(i[0],i[1],[L-F,V-j,L+F,V-j,L+F,V+j,L-F,V+j],u.x,u.y);if(Y.length>0){var W=tT(l,tm(t)),H=tT(l,tm(Y)),G=W;H<W&&(t=Y,G=H),Y.length>2&&tT(l,{x:Y[2],y:Y[3]})<G&&(t=[Y[2],Y[3]])}}var U=t6(t,n,this.arrowShapes[c].spacing(e)+h),K=t6(t,n,this.arrowShapes[c].gap(e)+h);if(y.endX=K[0],y.endY=K[1],y.arrowEndX=U[0],y.arrowEndY=U[1],"inside-to-node"===T)t=[l.x,l.y];else if(D.units)t=this.manualEndptToPx(o,D);else if("outside-to-line"===T)t=y.srcIntn;else if("outside-to-node"===T||"outside-to-node-or-label"===T?a=r:("outside-to-line"===T||"outside-to-line-or-label"===T)&&(a=[u.x,u.y]),t=this.nodeShapes[this.getNodeShape(o)].intersectLine(l.x,l.y,o.outerWidth(),o.outerHeight(),a[0],a[1],0,P,f),"outside-to-node-or-label"===T||"outside-to-line-or-label"===T){var Z=o._private.rscratch,$=Z.labelWidth,Q=Z.labelHeight,J=Z.labelX,ee=Z.labelY,et=$/2,en=Q/2,er=o.pstyle("text-valign").value;"top"===er?ee-=en:"bottom"===er&&(ee+=en);var ei=o.pstyle("text-halign").value;"left"===ei?J-=et:"right"===ei&&(J+=et);var ea=t4(a[0],a[1],[J-et,ee-en,J+et,ee-en,J+et,ee+en,J-et,ee+en],l.x,l.y);if(ea.length>0){var eo=tT(u,tm(t)),es=tT(u,tm(ea)),el=eo;es<eo&&(t=[ea[0],ea[1]],el=es),ea.length>2&&tT(u,{x:ea[2],y:ea[3]})<el&&(t=[ea[2],ea[3]])}}var eu=t6(t,r,this.arrowShapes[d].spacing(e)+p),ec=t6(t,r,this.arrowShapes[d].gap(e)+p);y.startX=ec[0],y.startY=ec[1],y.arrowStartX=eu[0],y.arrowStartY=eu[1],(w||E||k)&&(M(y.startX)&&M(y.startY)&&M(y.endX)&&M(y.endY)?y.badLine=!1:y.badLine=!0)},sg.getSourceEndpoint=function(e){var t=e[0]._private.rscratch;return(this.recalculateRenderedStyle(e),"haystack"===t.edgeType)?{x:t.haystackPts[0],y:t.haystackPts[1]}:{x:t.arrowStartX,y:t.arrowStartY}},sg.getTargetEndpoint=function(e){var t=e[0]._private.rscratch;return(this.recalculateRenderedStyle(e),"haystack"===t.edgeType)?{x:t.haystackPts[2],y:t.haystackPts[3]}:{x:t.arrowEndX,y:t.arrowEndY}};var sv={};sv.storeEdgeProjections=function(e){var t=e._private,n=t.rscratch,r=n.edgeType;if(t.rstyle.bezierPts=null,t.rstyle.linePts=null,t.rstyle.haystackPts=null,"multibezier"===r||"bezier"===r||"self"===r||"compound"===r){t.rstyle.bezierPts=[];for(var i=0;i+5<n.allpts.length;i+=4)!function(e,t,n){for(var r=function(e,t,n,r){return t_(e,t,n,r)},i=t._private.rstyle.bezierPts,a=0;a<e.bezierProjPcts.length;a++){var o=e.bezierProjPcts[a];i.push({x:r(n[0],n[2],n[4],o),y:r(n[1],n[3],n[5],o)})}}(this,e,n.allpts.slice(i,i+6))}else if("segments"===r)for(var a=t.rstyle.linePts=[],i=0;i+1<n.allpts.length;i+=2)a.push({x:n.allpts[i],y:n.allpts[i+1]});else if("haystack"===r){var o=n.haystackPts;t.rstyle.haystackPts=[{x:o[0],y:o[1]},{x:o[2],y:o[3]}]}t.rstyle.arrowWidth=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth},sv.recalculateEdgeProjections=function(e){this.findEdgeControlPoints(e)};var sy={};sy.recalculateNodeLabelProjection=function(e){if(!z(e.pstyle("label").strValue)){var t,n,r=e._private,i=e.width(),a=e.height(),o=e.padding(),s=e.position(),l=e.pstyle("text-halign").strValue,u=e.pstyle("text-valign").strValue,c=r.rscratch,d=r.rstyle;switch(l){case"left":t=s.x-i/2-o;break;case"right":t=s.x+i/2+o;break;default:t=s.x}switch(u){case"top":n=s.y-a/2-o;break;case"bottom":n=s.y+a/2+o;break;default:n=s.y}c.labelX=t,c.labelY=n,d.labelX=t,d.labelY=n,this.calculateLabelAngles(e),this.applyLabelDimensions(e)}};var sm=function(e,t){var n=Math.atan(t/e);return 0===e&&n<0&&(n*=-1),n},sb=function(e,t){return sm(t.x-e.x,t.y-e.y)},sx=function(e,t,n,r){var i=tN(0,r-.001,1),a=tN(0,r+.001,1);return sb(tM(e,t,n,i),tM(e,t,n,a))};sy.recalculateEdgeLabelProjections=function(e){var t,n=e._private,r=n.rscratch,i=this,a={mid:e.pstyle("label").strValue,source:e.pstyle("source-label").strValue,target:e.pstyle("target-label").strValue};if(a.mid||a.source||a.target){t={x:r.midX,y:r.midY};var o=function(e,t,r){te(n.rscratch,e,t,r),te(n.rstyle,e,t,r)};o("labelX",null,t.x),o("labelY",null,t.y),o("labelAutoAngle",null,sm(r.midDispX,r.midDispY));var s=function e(){if(e.cache)return e.cache;for(var t=[],a=0;a+5<r.allpts.length;a+=4){var o={x:r.allpts[a],y:r.allpts[a+1]},s={x:r.allpts[a+2],y:r.allpts[a+3]},l={x:r.allpts[a+4],y:r.allpts[a+5]};t.push({p0:o,p1:s,p2:l,startDist:0,length:0,segments:[]})}var u=n.rstyle.bezierPts,c=i.bezierProjPcts.length;function d(e,t,n,r,i){var a=tD(t,n),o=e.segments[e.segments.length-1],s={p0:t,p1:n,t0:r,t1:i,startDist:o?o.startDist+o.length:0,length:a};e.segments.push(s),e.length+=a}for(var h=0;h<t.length;h++){var p=t[h],f=t[h-1];f&&(p.startDist=f.startDist+f.length),d(p,p.p0,u[h*c],0,i.bezierProjPcts[0]);for(var g=0;g<c-1;g++)d(p,u[h*c+g],u[h*c+g+1],i.bezierProjPcts[g],i.bezierProjPcts[g+1]);d(p,u[h*c+c-1],p.p2,i.bezierProjPcts[c-1],1)}return e.cache=t},l=function(n){var i="source"===n;if(a[n]){var l=e.pstyle(n+"-text-offset").pfValue;switch(r.edgeType){case"self":case"compound":case"bezier":case"multibezier":for(var u,c,d=s(),h=0,p=0,f=0;f<d.length;f++){for(var g=d[i?f:d.length-1-f],v=0;v<g.segments.length;v++){var y=g.segments[i?v:g.segments.length-1-v],b=f===d.length-1&&v===g.segments.length-1;if(h=p,(p+=y.length)>=l||b){c={cp:g,segment:y};break}}if(c)break}var x=c.cp,w=c.segment,E=(l-h)/w.length,k=w.t1-w.t0,C=i?w.t0+k*E:w.t1-k*E;C=tN(0,C,1),t=tM(x.p0,x.p1,x.p2,C),u=sx(x.p0,x.p1,x.p2,C);break;case"straight":case"segments":case"haystack":for(var S,D,T,P,_=0,M=r.allpts.length,B=0;B+3<M&&(i?(S={x:r.allpts[B],y:r.allpts[B+1]},D={x:r.allpts[B+2],y:r.allpts[B+3]}):(S={x:r.allpts[M-2-B],y:r.allpts[M-1-B]},D={x:r.allpts[M-4-B],y:r.allpts[M-3-B]}),T=tD(S,D),P=_,!((_+=T)>=l));B+=2);var N=(l-P)/T;t=tB(S,D,N=tN(0,N,1)),u=sb(S,D)}o("labelX",n,t.x),o("labelY",n,t.y),o("labelAutoAngle",n,u)}};l("source"),l("target"),this.applyLabelDimensions(e)}},sy.applyLabelDimensions=function(e){this.applyPrefixedLabelDimensions(e),e.isEdge()&&(this.applyPrefixedLabelDimensions(e,"source"),this.applyPrefixedLabelDimensions(e,"target"))},sy.applyPrefixedLabelDimensions=function(e,t){var n=e._private,r=this.getLabelText(e,t),i=this.calculateLabelDimensions(e,r),a=e.pstyle("line-height").pfValue,o=e.pstyle("text-wrap").strValue,s=e7(n.rscratch,"labelWrapCachedLines",t)||[],l="wrap"!==o?1:Math.max(s.length,1),u=i.height/l,c=i.width,d=i.height+(l-1)*(a-1)*u;te(n.rstyle,"labelWidth",t,c),te(n.rscratch,"labelWidth",t,c),te(n.rstyle,"labelHeight",t,d),te(n.rscratch,"labelHeight",t,d),te(n.rscratch,"labelLineHeight",t,u*a)},sy.getLabelText=function(e,t){var n=e._private,r=e.pstyle((t?t+"-":"")+"label").strValue,i=e.pstyle("text-transform").value,a=function(e,r){return r?(te(n.rscratch,e,t,r),r):e7(n.rscratch,e,t)};if(!r)return"";"none"==i||("uppercase"==i?r=r.toUpperCase():"lowercase"==i&&(r=r.toLowerCase()));var o=e.pstyle("text-wrap").value;if("wrap"===o){var s=a("labelKey");if(null!=s&&a("labelWrapKey")===s)return a("labelWrapCachedText");for(var l=r.split("\n"),u=e.pstyle("text-max-width").pfValue,c="anywhere"===e.pstyle("text-overflow-wrap").value,h=[],p=/[\s\u200b]+|$/g,f=0;f<l.length;f++){var g=l[f],v=this.calculateLabelDimensions(e,g).width;if(c&&(g=g.split("").join("​")),v>u){var y,b=g.matchAll(p),x="",w=0,E=d(b);try{for(E.s();!(y=E.n()).done;){var k=y.value,C=k[0],S=g.substring(w,k.index);w=k.index+C.length;var D=0===x.length?S:x+S+C;this.calculateLabelDimensions(e,D).width<=u?x+=S+C:(x&&h.push(x),x=S+C)}}catch(e){E.e(e)}finally{E.f()}x.match(/^[\s\u200b]+$/)||h.push(x)}else h.push(g)}a("labelWrapCachedLines",h),r=a("labelWrapCachedText",h.join("\n")),a("labelWrapKey",s)}else if("ellipsis"===o){var T=e.pstyle("text-max-width").pfValue,P="",_=!1;if(this.calculateLabelDimensions(e,r).width<T)return r;for(var M=0;M<r.length&&!(this.calculateLabelDimensions(e,P+r[M]+"…").width>T);M++)P+=r[M],M===r.length-1&&(_=!0);return _||(P+="…"),P}return r},sy.getLabelJustification=function(e){var t=e.pstyle("text-justification").strValue,n=e.pstyle("text-halign").strValue;if("auto"!==t)return t;if(!e.isNode())return"center";switch(n){case"left":return"right";case"right":return"left";default:return"center"}},sy.calculateLabelDimensions=function(e,t){var n=this,r=n.cy.window().document,i=ej(t,e._private.labelDimsKey),a=n.labelDimCache||(n.labelDimCache=[]),o=a[i];if(null!=o)return o;var s=e.pstyle("font-style").strValue,l=e.pstyle("font-size").pfValue,u=e.pstyle("font-family").strValue,c=e.pstyle("font-weight").strValue,d=this.labelCalcCanvas,h=this.labelCalcCanvasContext;if(!d){d=this.labelCalcCanvas=r.createElement("canvas"),h=this.labelCalcCanvasContext=d.getContext("2d");var p=d.style;p.position="absolute",p.left="-9999px",p.top="-9999px",p.zIndex="-1",p.visibility="hidden",p.pointerEvents="none"}h.font="".concat(s," ").concat(c," ").concat(l,"px ").concat(u);for(var f=0,g=0,v=t.split("\n"),y=0;y<v.length;y++){var b=v[y];f=Math.max(Math.ceil(h.measureText(b).width),f),g+=l}return f+=0,g+=0,a[i]={width:f,height:g}},sy.calculateLabelAngle=function(e,t){var n=e._private.rscratch,r=e.isEdge(),i=e.pstyle((t?t+"-":"")+"text-rotation"),a=i.strValue;return"none"===a?0:r&&"autorotate"===a?n.labelAutoAngle:"autorotate"===a?0:i.pfValue},sy.calculateLabelAngles=function(e){var t=e.isEdge(),n=e._private.rscratch;n.labelAngle=this.calculateLabelAngle(e),t&&(n.sourceLabelAngle=this.calculateLabelAngle(e,"source"),n.targetLabelAngle=this.calculateLabelAngle(e,"target"))};var sw={},sE=!1;sw.getNodeShape=function(e){var t=e.pstyle("shape").value;if("cutrectangle"===t&&(28>e.width()||28>e.height()))return sE||(e0("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),sE=!0),"rectangle";if(e.isParent())return"rectangle"===t||"roundrectangle"===t||"round-rectangle"===t||"cutrectangle"===t||"cut-rectangle"===t||"barrel"===t?t:"rectangle";if("polygon"===t){var n=e.pstyle("shape-polygon-points").value;return this.nodeShapes.makePolygon(n).name}return t};var sk={};sk.registerCalculationListeners=function(){var e=this.cy,t=e.collection(),n=this,r=function(e){var n=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(t.merge(e),n)for(var r=0;r<e.length;r++){var i=e[r]._private.rstyle;i.clean=!1,i.cleanConnected=!1}};n.binder(e).on("bounds.* dirty.*",function(e){r(e.target)}).on("style.* background.*",function(e){r(e.target,!1)});var i=function(i){if(i){var a=n.onUpdateEleCalcsFns;t.cleanStyle();for(var o=0;o<t.length;o++){var s=t[o],l=s._private.rstyle;s.isNode()&&!l.cleanConnected&&(r(s.connectedEdges()),l.cleanConnected=!0)}if(a)for(var u=0;u<a.length;u++)(0,a[u])(i,t);n.recalculateRenderedStyle(t),t=e.collection()}};n.flushRenderedStyleQueue=function(){i(!0)},n.beforeRender(i,n.beforeRenderPriorities.eleCalcs)},sk.onUpdateEleCalcs=function(e){(this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[]).push(e)},sk.recalculateRenderedStyle=function(e,t){var n=function(e){return e._private.rstyle.cleanConnected},r=[],i=[];if(!this.destroyed){void 0===t&&(t=!0);for(var a=0;a<e.length;a++){var o=e[a],s=o._private,l=s.rstyle;!o.isEdge()||n(o.source())&&n(o.target())||(l.clean=!1),t&&l.clean||o.removed()||"none"===o.pstyle("display").value||("nodes"===s.group?i.push(o):r.push(o),l.clean=!0)}for(var u=0;u<i.length;u++){var c=i[u],d=c._private.rstyle,h=c.position();this.recalculateNodeLabelProjection(c),d.nodeX=h.x,d.nodeY=h.y,d.nodeW=c.pstyle("width").pfValue,d.nodeH=c.pstyle("height").pfValue}this.recalculateEdgeProjections(r);for(var p=0;p<r.length;p++){var f=r[p]._private,g=f.rstyle,v=f.rscratch;g.srcX=v.arrowStartX,g.srcY=v.arrowStartY,g.tgtX=v.arrowEndX,g.tgtY=v.arrowEndY,g.midX=v.midX,g.midY=v.midY,g.labelAngle=v.labelAngle,g.sourceLabelAngle=v.sourceLabelAngle,g.targetLabelAngle=v.targetLabelAngle}}};var sC={};sC.updateCachedGrabbedEles=function(){var e=this.cachedZSortedEles;if(e){e.drag=[],e.nondrag=[];for(var t=[],n=0;n<e.length;n++){var r=e[n],i=r._private.rscratch;r.grabbed()&&!r.isParent()?t.push(r):i.inDragLayer?e.drag.push(r):e.nondrag.push(r)}for(var n=0;n<t.length;n++){var r=t[n];e.drag.push(r)}}},sC.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null},sC.getCachedZSortedEles=function(e){if(e||!this.cachedZSortedEles){var t=this.cy.mutableElements().toArray();t.sort(aa),t.interactive=t.filter(function(e){return e.interactive()}),this.cachedZSortedEles=t,this.updateCachedGrabbedEles()}else t=this.cachedZSortedEles;return t};var sS={};[oj,oq,sp,sg,sv,sy,sw,sk,sC].forEach(function(e){Z(sS,e)});var sD={};sD.getCachedImage=function(e,t,n){var r=this,i=r.imageCache=r.imageCache||{},a=i[e];if(a)return a.image.complete||a.image.addEventListener("load",n),a.image;var o=(a=i[e]=i[e]||{}).image=new Image;o.addEventListener("load",n),o.addEventListener("error",function(){o.error=!0});var s="data:";return e.substring(0,s.length).toLowerCase()!==s&&(t="null"===t?null:t,o.crossOrigin=t),o.src=e,o};var sT={};sT.registerBinding=function(e,t,n,r){var i=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(e)){for(var a=[],o=0;o<e.length;o++){var s=e[o];if(void 0!==s){var l=this.binder(s);a.push(l.on.apply(l,i))}}return a}var l=this.binder(e);return l.on.apply(l,i)},sT.binder=function(e){var t=this,n=t.cy.window(),r=e===n||e===n.document||e===n.document.body||"undefined"!=typeof HTMLElement&&e instanceof HTMLElement;if(null==t.supportsPassiveEvents){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){return i=!0,!0}});n.addEventListener("test",null,a)}catch(e){}t.supportsPassiveEvents=i}var o=function(n,i,a){var o=Array.prototype.slice.call(arguments);return r&&t.supportsPassiveEvents&&(o[2]={capture:null!=a&&a,passive:!1,once:!1}),t.bindings.push({target:e,args:o}),(e.addEventListener||e.on).apply(e,o),this};return{on:o,addEventListener:o,addListener:o,bind:o}},sT.nodeIsDraggable=function(e){return e&&e.isNode()&&!e.locked()&&e.grabbable()},sT.nodeIsGrabbable=function(e){return this.nodeIsDraggable(e)&&e.interactive()},sT.load=function(){var e,t,n,r,i,a,o,s,l,u,c,d,h,p,f,g,v,y,b,x,w,E,k,C=this,S=C.cy.window(),D=function(e){return e.selected()},T=function(e,t,n,r){null==e&&(e=C.cy);for(var i=0;i<t.length;i++){var a=t[i];e.emit({originalEvent:n,type:a,position:r})}},P=function(e){return e.shiftKey||e.metaKey||e.ctrlKey},_=function(e,t){var n=!0;if(C.cy.hasCompoundNodes()&&e&&e.pannable())for(var r=0;t&&r<t.length;r++){var e=t[r];if(e.isNode()&&e.isParent()&&!e.pannable()){n=!1;break}}else n=!0;return n},B=function(e){e[0]._private.grabbed=!0},N=function(e){e[0]._private.grabbed=!1},A=function(e){e[0]._private.rscratch.inDragLayer=!0},I=function(e){e[0]._private.rscratch.inDragLayer=!1},O=function(e){e[0]._private.rscratch.isGrabTarget=!0},R=function(e){e[0]._private.rscratch.isGrabTarget=!1},z=function(e,t){var n=t.addToList;!n.has(e)&&e.grabbable()&&!e.locked()&&(n.merge(e),B(e))},L=function(e,t){if(e.cy().hasCompoundNodes()&&(null!=t.inDragLayer||null!=t.addToList)){var n=e.descendants();t.inDragLayer&&(n.forEach(A),n.connectedEdges().forEach(A)),t.addToList&&z(n,t)}},V=function(e,t){t=t||{};var n=e.cy().hasCompoundNodes();t.inDragLayer&&(e.forEach(A),e.neighborhood().stdFilter(function(e){return!n||e.isEdge()}).forEach(A)),t.addToList&&e.forEach(function(e){z(e,t)}),L(e,t),j(e,{inDragLayer:t.inDragLayer}),C.updateCachedGrabbedEles()},F=function(e){e&&(C.getCachedZSortedEles().forEach(function(e){N(e),I(e),R(e)}),C.updateCachedGrabbedEles())},j=function(e,t){if((null!=t.inDragLayer||null!=t.addToList)&&e.cy().hasCompoundNodes()){var n=e.ancestors().orphans();if(!n.same(e)){var r=n.descendants().spawnSelf().merge(n).unmerge(e).unmerge(e.descendants()),i=r.connectedEdges();t.inDragLayer&&(i.forEach(A),r.forEach(A)),t.addToList&&r.forEach(function(e){z(e,t)})}}},q=function(){null!=document.activeElement&&null!=document.activeElement.blur&&document.activeElement.blur()},X="undefined"!=typeof MutationObserver,Y="undefined"!=typeof ResizeObserver;X?(C.removeObserver=new MutationObserver(function(e){for(var t=0;t<e.length;t++){var n=e[t].removedNodes;if(n){for(var r=0;r<n.length;r++)if(n[r]===C.container){C.destroy();break}}}}),C.container.parentNode&&C.removeObserver.observe(C.container.parentNode,{childList:!0})):C.registerBinding(C.container,"DOMNodeRemoved",function(e){C.destroy()});var W=eM(function(){C.cy.resize()},100);X&&(C.styleObserver=new MutationObserver(W),C.styleObserver.observe(C.container,{attributes:!0})),C.registerBinding(S,"resize",W),Y&&(C.resizeObserver=new ResizeObserver(W),C.resizeObserver.observe(C.container));var H=function(){C.invalidateContainerClientCoordsCache()};(function(e,t){for(;null!=e;)t(e),e=e.parentNode})(C.container,function(e){C.registerBinding(e,"transitionend",H),C.registerBinding(e,"animationend",H),C.registerBinding(e,"scroll",H)}),C.registerBinding(C.container,"contextmenu",function(e){e.preventDefault()});var G=function(e){for(var t=C.findContainerClientCoords(),n=t[0],r=t[1],i=t[2],a=t[3],o=e.touches?e.touches:[e],s=!1,l=0;l<o.length;l++){var u=o[l];if(n<=u.clientX&&u.clientX<=n+i&&r<=u.clientY&&u.clientY<=r+a){s=!0;break}}if(!s)return!1;for(var c=C.container,d=e.target.parentNode,h=!1;d;){if(d===c){h=!0;break}d=d.parentNode}return!!h};C.registerBinding(C.container,"mousedown",function(e){if(G(e)&&(1!==C.hoverData.which||1===e.which)){e.preventDefault(),q(),C.hoverData.capture=!0,C.hoverData.which=e.which;var t=C.cy,n=[e.clientX,e.clientY],r=C.projectIntoViewport(n[0],n[1]),i=C.selection,a=C.findNearestElements(r[0],r[1],!0,!1),o=a[0],s=C.dragData.possibleDragElements;if(C.hoverData.mdownPos=r,C.hoverData.mdownGPos=n,3==e.which){C.hoverData.cxtStarted=!0;var l={originalEvent:e,type:"cxttapstart",position:{x:r[0],y:r[1]}};o?(o.activate(),o.emit(l),C.hoverData.down=o):t.emit(l),C.hoverData.downTime=new Date().getTime(),C.hoverData.cxtDragged=!1}else if(1==e.which){if(o&&o.activate(),null!=o&&C.nodeIsGrabbable(o)){var u=function(t){return{originalEvent:e,type:t,position:{x:r[0],y:r[1]}}};if(O(o),o.selected()){s=C.dragData.possibleDragElements=t.collection();var c=t.$(function(e){return e.isNode()&&e.selected()&&C.nodeIsGrabbable(e)});V(c,{addToList:s}),o.emit(u("grabon")),c.forEach(function(e){e.emit(u("grab"))})}else s=C.dragData.possibleDragElements=t.collection(),V(o,{addToList:s}),o.emit(u("grabon")).emit(u("grab"));C.redrawHint("eles",!0),C.redrawHint("drag",!0)}C.hoverData.down=o,C.hoverData.downs=a,C.hoverData.downTime=new Date().getTime(),T(o,["mousedown","tapstart","vmousedown"],e,{x:r[0],y:r[1]}),null==o?(i[4]=1,C.data.bgActivePosistion={x:r[0],y:r[1]},C.redrawHint("select",!0),C.redraw()):o.pannable()&&(i[4]=1),C.hoverData.tapholdCancelled=!1,clearTimeout(C.hoverData.tapholdTimeout),C.hoverData.tapholdTimeout=setTimeout(function(){if(!C.hoverData.tapholdCancelled){var n=C.hoverData.down;n?n.emit({originalEvent:e,type:"taphold",position:{x:r[0],y:r[1]}}):t.emit({originalEvent:e,type:"taphold",position:{x:r[0],y:r[1]}})}},C.tapholdDuration)}i[0]=i[2]=r[0],i[1]=i[3]=r[1]}},!1);var U=function(e){var t=e.getRootNode();if(t&&11===t.nodeType&&void 0!==t.host)return t}(C.container);C.registerBinding([S,U],"mousemove",function(e){if(C.hoverData.capture||G(e)){var t=!1,n=C.cy,r=n.zoom(),i=[e.clientX,e.clientY],a=C.projectIntoViewport(i[0],i[1]),o=C.hoverData.mdownPos,s=C.hoverData.mdownGPos,l=C.selection,u=null;C.hoverData.draggingEles||C.hoverData.dragging||C.hoverData.selecting||(u=C.findNearestElement(a[0],a[1],!0,!1));var c=C.hoverData.last,d=C.hoverData.down,h=[a[0]-l[2],a[1]-l[3]],p=C.dragData.possibleDragElements;if(s){var f=i[0]-s[0],g=i[1]-s[1];C.hoverData.isOverThresholdDrag=w=f*f+g*g>=C.desktopTapThreshold2}var v=P(e);w&&(C.hoverData.tapholdCancelled=!0),t=!0,T(u,["mousemove","vmousemove","tapdrag"],e,{x:a[0],y:a[1]});var y=function(){C.data.bgActivePosistion=void 0,C.hoverData.selecting||n.emit({originalEvent:e,type:"boxstart",position:{x:a[0],y:a[1]}}),l[4]=1,C.hoverData.selecting=!0,C.redrawHint("select",!0),C.redraw()};if(3===C.hoverData.which){if(w){var b={originalEvent:e,type:"cxtdrag",position:{x:a[0],y:a[1]}};d?d.emit(b):n.emit(b),C.hoverData.cxtDragged=!0,(!C.hoverData.cxtOver||u!==C.hoverData.cxtOver)&&(C.hoverData.cxtOver&&C.hoverData.cxtOver.emit({originalEvent:e,type:"cxtdragout",position:{x:a[0],y:a[1]}}),C.hoverData.cxtOver=u,u&&u.emit({originalEvent:e,type:"cxtdragover",position:{x:a[0],y:a[1]}}))}}else if(C.hoverData.dragging){if(t=!0,n.panningEnabled()&&n.userPanningEnabled()){if(C.hoverData.justStartedPan){var x=C.hoverData.mdownPos;E={x:(a[0]-x[0])*r,y:(a[1]-x[1])*r},C.hoverData.justStartedPan=!1}else E={x:h[0]*r,y:h[1]*r};n.panBy(E),n.emit("dragpan"),C.hoverData.dragged=!0}a=C.projectIntoViewport(e.clientX,e.clientY)}else if(1==l[4]&&(null==d||d.pannable()))w&&(!C.hoverData.dragging&&n.boxSelectionEnabled()&&(v||!n.panningEnabled()||!n.userPanningEnabled())?y():!C.hoverData.selecting&&n.panningEnabled()&&n.userPanningEnabled()&&_(d,C.hoverData.downs)&&(C.hoverData.dragging=!0,C.hoverData.justStartedPan=!0,l[4]=0,C.data.bgActivePosistion=tm(o),C.redrawHint("select",!0),C.redraw()),d&&d.pannable()&&d.active()&&d.unactivate());else{if(d&&d.pannable()&&d.active()&&d.unactivate(),d&&d.grabbed()||u==c||(c&&T(c,["mouseout","tapdragout"],e,{x:a[0],y:a[1]}),u&&T(u,["mouseover","tapdragover"],e,{x:a[0],y:a[1]}),C.hoverData.last=u),d){if(w){if(n.boxSelectionEnabled()&&v)d&&d.grabbed()&&(F(p),d.emit("freeon"),p.emit("free"),C.dragData.didDrag&&(d.emit("dragfreeon"),p.emit("dragfree"))),y();else if(d&&d.grabbed()&&C.nodeIsDraggable(d)){var w,E,k,S=!C.dragData.didDrag;S&&C.redrawHint("eles",!0),C.dragData.didDrag=!0,C.hoverData.draggingEles||V(p,{inDragLayer:!0});var D={x:0,y:0};if(M(h[0])&&M(h[1])&&(D.x+=h[0],D.y+=h[1],S)){var B=C.hoverData.dragDelta;B&&M(B[0])&&M(B[1])&&(D.x+=B[0],D.y+=B[1])}C.hoverData.draggingEles=!0,p.silentShift(D).emit("position drag"),C.redrawHint("drag",!0),C.redraw()}}else 0===(k=C.hoverData.dragDelta=C.hoverData.dragDelta||[]).length?(k.push(h[0]),k.push(h[1])):(k[0]+=h[0],k[1]+=h[1])}t=!0}if(l[2]=a[0],l[3]=a[1],t)return e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault(),!1}},!1),C.registerBinding(S,"mouseup",function(r){if((1!==C.hoverData.which||1===r.which||!C.hoverData.capture)&&C.hoverData.capture){C.hoverData.capture=!1;var i=C.cy,a=C.projectIntoViewport(r.clientX,r.clientY),o=C.selection,s=C.findNearestElement(a[0],a[1],!0,!1),l=C.dragData.possibleDragElements,u=C.hoverData.down,c=P(r);if(C.data.bgActivePosistion&&(C.redrawHint("select",!0),C.redraw()),C.hoverData.tapholdCancelled=!0,C.data.bgActivePosistion=void 0,u&&u.unactivate(),3===C.hoverData.which){var d={originalEvent:r,type:"cxttapend",position:{x:a[0],y:a[1]}};if(u?u.emit(d):i.emit(d),!C.hoverData.cxtDragged){var h={originalEvent:r,type:"cxttap",position:{x:a[0],y:a[1]}};u?u.emit(h):i.emit(h)}C.hoverData.cxtDragged=!1,C.hoverData.which=null}else if(1===C.hoverData.which){if(T(s,["mouseup","tapend","vmouseup"],r,{x:a[0],y:a[1]}),C.dragData.didDrag||C.hoverData.dragged||C.hoverData.selecting||C.hoverData.isOverThresholdDrag||(T(u,["click","tap","vclick"],r,{x:a[0],y:a[1]}),t=!1,r.timeStamp-n<=i.multiClickDebounceTime()?(e&&clearTimeout(e),t=!0,n=null,T(u,["dblclick","dbltap","vdblclick"],r,{x:a[0],y:a[1]})):(e=setTimeout(function(){t||T(u,["oneclick","onetap","voneclick"],r,{x:a[0],y:a[1]})},i.multiClickDebounceTime()),n=r.timeStamp)),null!=u||C.dragData.didDrag||C.hoverData.selecting||C.hoverData.dragged||P(r)||(i.$(D).unselect(["tapunselect"]),l.length>0&&C.redrawHint("eles",!0),C.dragData.possibleDragElements=l=i.collection()),s!=u||C.dragData.didDrag||C.hoverData.selecting||null==s||!s._private.selectable||(C.hoverData.dragging||("additive"===i.selectionType()||c?s.selected()?s.unselect(["tapunselect"]):s.select(["tapselect"]):c||(i.$(D).unmerge(s).unselect(["tapunselect"]),s.select(["tapselect"]))),C.redrawHint("eles",!0)),C.hoverData.selecting){var p=i.collection(C.getAllInBox(o[0],o[1],o[2],o[3]));C.redrawHint("select",!0),p.length>0&&C.redrawHint("eles",!0),i.emit({type:"boxend",originalEvent:r,position:{x:a[0],y:a[1]}}),"additive"!==i.selectionType()&&(c||i.$(D).unmerge(p).unselect()),p.emit("box").stdFilter(function(e){return e.selectable()&&!e.selected()}).select().emit("boxselect"),C.redraw()}if(C.hoverData.dragging&&(C.hoverData.dragging=!1,C.redrawHint("select",!0),C.redrawHint("eles",!0),C.redraw()),!o[4]){C.redrawHint("drag",!0),C.redrawHint("eles",!0);var f=u&&u.grabbed();F(l),f&&(u.emit("freeon"),l.emit("free"),C.dragData.didDrag&&(u.emit("dragfreeon"),l.emit("dragfree")))}}o[4]=0,C.hoverData.down=null,C.hoverData.cxtStarted=!1,C.hoverData.draggingEles=!1,C.hoverData.selecting=!1,C.hoverData.isOverThresholdDrag=!1,C.dragData.didDrag=!1,C.hoverData.dragged=!1,C.hoverData.dragDelta=[],C.hoverData.mdownPos=null,C.hoverData.mdownGPos=null,C.hoverData.which=null}},!1);var K=function(e){if(!C.scrollingPage){var t=C.cy,n=t.zoom(),r=t.pan(),i=C.projectIntoViewport(e.clientX,e.clientY),a=[i[0]*n+r.x,i[1]*n+r.y];if(C.hoverData.draggingEles||C.hoverData.dragging||C.hoverData.cxtStarted||0!==C.selection[4]){e.preventDefault();return}if(t.panningEnabled()&&t.userPanningEnabled()&&t.zoomingEnabled()&&t.userZoomingEnabled()){e.preventDefault(),C.data.wheelZooming=!0,clearTimeout(C.data.wheelTimeout),C.data.wheelTimeout=setTimeout(function(){C.data.wheelZooming=!1,C.redrawHint("eles",!0),C.redraw()},150),o=(null!=e.deltaY?-(e.deltaY/250):null!=e.wheelDeltaY?e.wheelDeltaY/1e3:e.wheelDelta/1e3)*C.wheelSensitivity,1===e.deltaMode&&(o*=33);var o,s=t.zoom()*Math.pow(10,o);"gesturechange"===e.type&&(s=C.gestureStartZoom*e.scale),t.zoom({level:s,renderedPosition:{x:a[0],y:a[1]}}),t.emit("gesturechange"===e.type?"pinchzoom":"scrollzoom")}}};C.registerBinding(C.container,"wheel",K,!0),C.registerBinding(S,"scroll",function(e){C.scrollingPage=!0,clearTimeout(C.scrollingPageTimeout),C.scrollingPageTimeout=setTimeout(function(){C.scrollingPage=!1},250)},!0),C.registerBinding(C.container,"gesturestart",function(e){C.gestureStartZoom=C.cy.zoom(),C.hasTouchStarted||e.preventDefault()},!0),C.registerBinding(C.container,"gesturechange",function(e){C.hasTouchStarted||K(e)},!0),C.registerBinding(C.container,"mouseout",function(e){var t=C.projectIntoViewport(e.clientX,e.clientY);C.cy.emit({originalEvent:e,type:"mouseout",position:{x:t[0],y:t[1]}})},!1),C.registerBinding(C.container,"mouseover",function(e){var t=C.projectIntoViewport(e.clientX,e.clientY);C.cy.emit({originalEvent:e,type:"mouseover",position:{x:t[0],y:t[1]}})},!1);var Z=function(e,t,n,r){return Math.sqrt((n-e)*(n-e)+(r-t)*(r-t))},$=function(e,t,n,r){return(n-e)*(n-e)+(r-t)*(r-t)};if(C.registerBinding(C.container,"touchstart",v=function(e){if(C.hasTouchStarted=!0,G(e)){q(),C.touchData.capture=!0,C.data.bgActivePosistion=void 0;var t=C.cy,n=C.touchData.now,v=C.touchData.earlier;if(e.touches[0]){var y=C.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);n[0]=y[0],n[1]=y[1]}if(e.touches[1]){var y=C.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);n[2]=y[0],n[3]=y[1]}if(e.touches[2]){var y=C.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);n[4]=y[0],n[5]=y[1]}if(e.touches[1]){C.touchData.singleTouchMoved=!0,F(C.dragData.touchDragEles);var b=C.findContainerClientCoords();d=b[0],h=b[1],p=b[2],f=b[3],r=e.touches[0].clientX-d,i=e.touches[0].clientY-h,a=e.touches[1].clientX-d,o=e.touches[1].clientY-h,g=0<=r&&r<=p&&0<=a&&a<=p&&0<=i&&i<=f&&0<=o&&o<=f;var x=t.pan(),w=t.zoom();if(s=Z(r,i,a,o),l=$(r,i,a,o),c=[((u=[(r+a)/2,(i+o)/2])[0]-x.x)/w,(u[1]-x.y)/w],l<4e4&&!e.touches[2]){var E=C.findNearestElement(n[0],n[1],!0,!0),k=C.findNearestElement(n[2],n[3],!0,!0);E&&E.isNode()?(E.activate().emit({originalEvent:e,type:"cxttapstart",position:{x:n[0],y:n[1]}}),C.touchData.start=E):k&&k.isNode()?(k.activate().emit({originalEvent:e,type:"cxttapstart",position:{x:n[0],y:n[1]}}),C.touchData.start=k):t.emit({originalEvent:e,type:"cxttapstart",position:{x:n[0],y:n[1]}}),C.touchData.start&&(C.touchData.start._private.grabbed=!1),C.touchData.cxt=!0,C.touchData.cxtDragged=!1,C.data.bgActivePosistion=void 0,C.redraw();return}}if(e.touches[2])t.boxSelectionEnabled()&&e.preventDefault();else if(e.touches[1]);else if(e.touches[0]){var S=C.findNearestElements(n[0],n[1],!0,!0),D=S[0];if(null!=D&&(D.activate(),C.touchData.start=D,C.touchData.starts=S,C.nodeIsGrabbable(D))){var P=C.dragData.touchDragEles=t.collection(),_=null;C.redrawHint("eles",!0),C.redrawHint("drag",!0),D.selected()?V(_=t.$(function(e){return e.selected()&&C.nodeIsGrabbable(e)}),{addToList:P}):V(D,{addToList:P}),O(D);var M=function(t){return{originalEvent:e,type:t,position:{x:n[0],y:n[1]}}};D.emit(M("grabon")),_?_.forEach(function(e){e.emit(M("grab"))}):D.emit(M("grab"))}T(D,["touchstart","tapstart","vmousedown"],e,{x:n[0],y:n[1]}),null==D&&(C.data.bgActivePosistion={x:y[0],y:y[1]},C.redrawHint("select",!0),C.redraw()),C.touchData.singleTouchMoved=!1,C.touchData.singleTouchStartTime=+new Date,clearTimeout(C.touchData.tapholdTimeout),C.touchData.tapholdTimeout=setTimeout(function(){!1!==C.touchData.singleTouchMoved||C.pinching||C.touchData.selecting||T(C.touchData.start,["taphold"],e,{x:n[0],y:n[1]})},C.tapholdDuration)}if(e.touches.length>=1){for(var B=C.touchData.startPosition=[null,null,null,null,null,null],N=0;N<n.length;N++)B[N]=v[N]=n[N];var A=e.touches[0];C.touchData.startGPosition=[A.clientX,A.clientY]}}},!1),C.registerBinding(S,"touchmove",y=function(e){var t=C.touchData.capture;if(t||G(e)){var n=C.selection,u=C.cy,p=C.touchData.now,f=C.touchData.earlier,v=u.zoom();if(e.touches[0]){var y=C.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);p[0]=y[0],p[1]=y[1]}if(e.touches[1]){var y=C.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);p[2]=y[0],p[3]=y[1]}if(e.touches[2]){var y=C.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);p[4]=y[0],p[5]=y[1]}var b=C.touchData.startGPosition;if(t&&e.touches[0]&&b){for(var x=[],w=0;w<p.length;w++)x[w]=p[w]-f[w];var E=e.touches[0].clientX-b[0],k=E*E,S=e.touches[0].clientY-b[1];er=k+S*S>=C.touchTapThreshold2}if(t&&C.touchData.cxt){e.preventDefault();var D=e.touches[0].clientX-d,P=e.touches[0].clientY-h,B=e.touches[1].clientX-d,N=e.touches[1].clientY-h,A=$(D,P,B,N);if(A/l>=2.25||A>=22500){C.touchData.cxt=!1,C.data.bgActivePosistion=void 0,C.redrawHint("select",!0);var I={originalEvent:e,type:"cxttapend",position:{x:p[0],y:p[1]}};C.touchData.start?(C.touchData.start.unactivate().emit(I),C.touchData.start=null):u.emit(I)}}if(t&&C.touchData.cxt){var I={originalEvent:e,type:"cxtdrag",position:{x:p[0],y:p[1]}};C.data.bgActivePosistion=void 0,C.redrawHint("select",!0),C.touchData.start?C.touchData.start.emit(I):u.emit(I),C.touchData.start&&(C.touchData.start._private.grabbed=!1),C.touchData.cxtDragged=!0;var O=C.findNearestElement(p[0],p[1],!0,!0);(!C.touchData.cxtOver||O!==C.touchData.cxtOver)&&(C.touchData.cxtOver&&C.touchData.cxtOver.emit({originalEvent:e,type:"cxtdragout",position:{x:p[0],y:p[1]}}),C.touchData.cxtOver=O,O&&O.emit({originalEvent:e,type:"cxtdragover",position:{x:p[0],y:p[1]}}))}else if(t&&e.touches[2]&&u.boxSelectionEnabled())e.preventDefault(),C.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,C.touchData.selecting||u.emit({originalEvent:e,type:"boxstart",position:{x:p[0],y:p[1]}}),C.touchData.selecting=!0,C.touchData.didSelect=!0,n[4]=1,n&&0!==n.length&&void 0!==n[0]?(n[2]=(p[0]+p[2]+p[4])/3,n[3]=(p[1]+p[3]+p[5])/3):(n[0]=(p[0]+p[2]+p[4])/3,n[1]=(p[1]+p[3]+p[5])/3,n[2]=(p[0]+p[2]+p[4])/3+1,n[3]=(p[1]+p[3]+p[5])/3+1),C.redrawHint("select",!0),C.redraw();else if(t&&e.touches[1]&&!C.touchData.didSelect&&u.zoomingEnabled()&&u.panningEnabled()&&u.userZoomingEnabled()&&u.userPanningEnabled()){e.preventDefault(),C.data.bgActivePosistion=void 0,C.redrawHint("select",!0);var R=C.dragData.touchDragEles;if(R){C.redrawHint("drag",!0);for(var z=0;z<R.length;z++){var L=R[z]._private;L.grabbed=!1,L.rscratch.inDragLayer=!1}}var j=C.touchData.start,D=e.touches[0].clientX-d,P=e.touches[0].clientY-h,B=e.touches[1].clientX-d,N=e.touches[1].clientY-h,q=Z(D,P,B,N),X=q/s;if(g){var Y=D-r,W=P-i,H=B-a,U=N-o,K=u.zoom(),Q=K*X,J=u.pan(),ee=c[0]*K+J.x,et=c[1]*K+J.y,en={x:-Q/K*(ee-J.x-(Y+H)/2)+ee,y:-Q/K*(et-J.y-(W+U)/2)+et};if(j&&j.active()){var R=C.dragData.touchDragEles;F(R),C.redrawHint("drag",!0),C.redrawHint("eles",!0),j.unactivate().emit("freeon"),R.emit("free"),C.dragData.didDrag&&(j.emit("dragfreeon"),R.emit("dragfree"))}u.viewport({zoom:Q,pan:en,cancelOnFailedZoom:!0}),u.emit("pinchzoom"),s=q,r=D,i=P,a=B,o=N,C.pinching=!0}if(e.touches[0]){var y=C.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);p[0]=y[0],p[1]=y[1]}if(e.touches[1]){var y=C.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);p[2]=y[0],p[3]=y[1]}if(e.touches[2]){var y=C.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);p[4]=y[0],p[5]=y[1]}}else if(e.touches[0]&&!C.touchData.didSelect){var er,O,ei=C.touchData.start,ea=C.touchData.last;if(C.hoverData.draggingEles||C.swipePanning||(O=C.findNearestElement(p[0],p[1],!0,!0)),t&&null!=ei&&e.preventDefault(),t&&null!=ei&&C.nodeIsDraggable(ei)){if(er){var R=C.dragData.touchDragEles,eo=!C.dragData.didDrag;eo&&V(R,{inDragLayer:!0}),C.dragData.didDrag=!0;var es={x:0,y:0};if(M(x[0])&&M(x[1])&&(es.x+=x[0],es.y+=x[1],eo)){C.redrawHint("eles",!0);var el=C.touchData.dragDelta;el&&M(el[0])&&M(el[1])&&(es.x+=el[0],es.y+=el[1])}C.hoverData.draggingEles=!0,R.silentShift(es).emit("position drag"),C.redrawHint("drag",!0),C.touchData.startPosition[0]==f[0]&&C.touchData.startPosition[1]==f[1]&&C.redrawHint("eles",!0),C.redraw()}else{var el=C.touchData.dragDelta=C.touchData.dragDelta||[];0===el.length?(el.push(x[0]),el.push(x[1])):(el[0]+=x[0],el[1]+=x[1])}}if(T(ei||O,["touchmove","tapdrag","vmousemove"],e,{x:p[0],y:p[1]}),ei&&ei.grabbed()||O==ea||(ea&&ea.emit({originalEvent:e,type:"tapdragout",position:{x:p[0],y:p[1]}}),O&&O.emit({originalEvent:e,type:"tapdragover",position:{x:p[0],y:p[1]}})),C.touchData.last=O,t)for(var z=0;z<p.length;z++)p[z]&&C.touchData.startPosition[z]&&er&&(C.touchData.singleTouchMoved=!0);if(t&&(null==ei||ei.pannable())&&u.panningEnabled()&&u.userPanningEnabled()){_(ei,C.touchData.starts)&&(e.preventDefault(),C.data.bgActivePosistion||(C.data.bgActivePosistion=tm(C.touchData.startPosition)),C.swipePanning?(u.panBy({x:x[0]*v,y:x[1]*v}),u.emit("dragpan")):er&&(C.swipePanning=!0,u.panBy({x:E*v,y:S*v}),u.emit("dragpan"),ei&&(ei.unactivate(),C.redrawHint("select",!0),C.touchData.start=null)));var y=C.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);p[0]=y[0],p[1]=y[1]}}for(var w=0;w<p.length;w++)f[w]=p[w];t&&e.touches.length>0&&!C.hoverData.draggingEles&&!C.swipePanning&&null!=C.data.bgActivePosistion&&(C.data.bgActivePosistion=void 0,C.redrawHint("select",!0),C.redraw())}},!1),C.registerBinding(S,"touchcancel",b=function(e){var t=C.touchData.start;C.touchData.capture=!1,t&&t.unactivate()}),C.registerBinding(S,"touchend",x=function(e){var t,n=C.touchData.start;if(C.touchData.capture){0===e.touches.length&&(C.touchData.capture=!1),e.preventDefault();var r=C.selection;C.swipePanning=!1,C.hoverData.draggingEles=!1;var i=C.cy,a=i.zoom(),o=C.touchData.now,s=C.touchData.earlier;if(e.touches[0]){var l=C.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);o[0]=l[0],o[1]=l[1]}if(e.touches[1]){var l=C.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);o[2]=l[0],o[3]=l[1]}if(e.touches[2]){var l=C.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);o[4]=l[0],o[5]=l[1]}if(n&&n.unactivate(),C.touchData.cxt){if(t={originalEvent:e,type:"cxttapend",position:{x:o[0],y:o[1]}},n?n.emit(t):i.emit(t),!C.touchData.cxtDragged){var u={originalEvent:e,type:"cxttap",position:{x:o[0],y:o[1]}};n?n.emit(u):i.emit(u)}C.touchData.start&&(C.touchData.start._private.grabbed=!1),C.touchData.cxt=!1,C.touchData.start=null,C.redraw();return}if(!e.touches[2]&&i.boxSelectionEnabled()&&C.touchData.selecting){C.touchData.selecting=!1;var c=i.collection(C.getAllInBox(r[0],r[1],r[2],r[3]));r[0]=void 0,r[1]=void 0,r[2]=void 0,r[3]=void 0,r[4]=0,C.redrawHint("select",!0),i.emit({type:"boxend",originalEvent:e,position:{x:o[0],y:o[1]}}),c.emit("box").stdFilter(function(e){return e.selectable()&&!e.selected()}).select().emit("boxselect"),c.nonempty()&&C.redrawHint("eles",!0),C.redraw()}if(null!=n&&n.unactivate(),e.touches[2])C.data.bgActivePosistion=void 0,C.redrawHint("select",!0);else if(e.touches[1]);else if(e.touches[0]);else if(!e.touches[0]){C.data.bgActivePosistion=void 0,C.redrawHint("select",!0);var d=C.dragData.touchDragEles;if(null!=n){var h=n._private.grabbed;F(d),C.redrawHint("drag",!0),C.redrawHint("eles",!0),h&&(n.emit("freeon"),d.emit("free"),C.dragData.didDrag&&(n.emit("dragfreeon"),d.emit("dragfree"))),T(n,["touchend","tapend","vmouseup","tapdragout"],e,{x:o[0],y:o[1]}),n.unactivate(),C.touchData.start=null}else{var p=C.findNearestElement(o[0],o[1],!0,!0);T(p,["touchend","tapend","vmouseup","tapdragout"],e,{x:o[0],y:o[1]})}var f=C.touchData.startPosition[0]-o[0],g=C.touchData.startPosition[1]-o[1];C.touchData.singleTouchMoved||(n||i.$(":selected").unselect(["tapunselect"]),T(n,["tap","vclick"],e,{x:o[0],y:o[1]}),w=!1,e.timeStamp-k<=i.multiClickDebounceTime()?(E&&clearTimeout(E),w=!0,k=null,T(n,["dbltap","vdblclick"],e,{x:o[0],y:o[1]})):(E=setTimeout(function(){w||T(n,["onetap","voneclick"],e,{x:o[0],y:o[1]})},i.multiClickDebounceTime()),k=e.timeStamp)),null!=n&&!C.dragData.didDrag&&n._private.selectable&&(f*f+g*g)*a*a<C.touchTapThreshold2&&!C.pinching&&("single"===i.selectionType()?(i.$(D).unmerge(n).unselect(["tapunselect"]),n.select(["tapselect"])):n.selected()?n.unselect(["tapunselect"]):n.select(["tapselect"]),C.redrawHint("eles",!0)),C.touchData.singleTouchMoved=!0}for(var v=0;v<o.length;v++)s[v]=o[v];C.dragData.didDrag=!1,0===e.touches.length&&(C.touchData.dragDelta=[],C.touchData.startPosition=[null,null,null,null,null,null],C.touchData.startGPosition=null,C.touchData.didSelect=!1),e.touches.length<2&&(1===e.touches.length&&(C.touchData.startGPosition=[e.touches[0].clientX,e.touches[0].clientY]),C.pinching=!1,C.redrawHint("eles",!0),C.redraw())}},!1),"undefined"==typeof TouchEvent){var Q=[],J=function(e){return{clientX:e.clientX,clientY:e.clientY,force:1,identifier:e.pointerId,pageX:e.pageX,pageY:e.pageY,radiusX:e.width/2,radiusY:e.height/2,screenX:e.screenX,screenY:e.screenY,target:e.target}},ee=function(e){Q.push({event:e,touch:J(e)})},et=function(e){for(var t=0;t<Q.length;t++)if(Q[t].event.pointerId===e.pointerId){Q.splice(t,1);return}},en=function(e){var t=Q.filter(function(t){return t.event.pointerId===e.pointerId})[0];t.event=e,t.touch=J(e)},er=function(e){e.touches=Q.map(function(e){return e.touch})},ei=function(e){return"mouse"===e.pointerType||4===e.pointerType};C.registerBinding(C.container,"pointerdown",function(e){ei(e)||(e.preventDefault(),ee(e),er(e),v(e))}),C.registerBinding(C.container,"pointerup",function(e){ei(e)||(et(e),er(e),x(e))}),C.registerBinding(C.container,"pointercancel",function(e){ei(e)||(et(e),er(e),b(e))}),C.registerBinding(C.container,"pointermove",function(e){ei(e)||(e.preventDefault(),en(e),er(e),y(e))})}};var sP={};sP.generatePolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl("polygon",e,t,n,r,i,this.points)},intersectLine:function(e,t,n,r,i,a,o,s){return t4(i,a,this.points,e,t,n/2,r/2,o)},checkPoint:function(e,t,n,r,i,a,o,s){return tZ(e,t,this.points,a,o,r,i,[0,-1],n)}}},sP.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o,s){return t0(i,a,e,t,n/2+o,r/2+o)},checkPoint:function(e,t,n,r,i,a,o,s){return t1(e,t,r,i,a,o,n)}}},sP.generateRoundPolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,getOrCreateCorners:function(e,n,r,i,a,o,s){if(void 0!==o[s]&&o[s+"-cx"]===e&&o[s+"-cy"]===n)return o[s];o[s]=Array(t.length/2),o[s+"-cx"]=e,o[s+"-cy"]=n;var l=r/2,u=i/2;a="auto"===a?nn(r,i):a;for(var c=Array(t.length/2),d=0;d<t.length/2;d++)c[d]={x:e+l*t[2*d],y:n+u*t[2*d+1]};var h,p,f,g,v=c.length;for(h=0,p=c[v-1];h<v;h++)f=c[h%v],g=c[(h+1)%v],o[s][h]=sh(p,f,g,a),p=f,f=g;return o[s]},draw:function(e,t,n,r,i,a,o){this.renderer.nodeShapeImpl("round-polygon",e,t,n,r,i,this.points,this.getOrCreateCorners(t,n,r,i,a,o,"drawCorners"))},intersectLine:function(e,t,n,r,i,a,o,s,l){return t9(i,a,this.points,e,t,n,r,o,this.getOrCreateCorners(e,t,n,r,s,l,"corners"))},checkPoint:function(e,t,n,r,i,a,o,s,l){return t$(e,t,this.points,a,o,r,i,this.getOrCreateCorners(a,o,r,i,s,l,"corners"))}}},sP.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:t8(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i,this.points,a)},intersectLine:function(e,t,n,r,i,a,o,s){return tX(i,a,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,i,a,o,s){var l=r/2,u=i/2;s="auto"===s?nt(r,i):s;var c=2*(s=Math.min(l,u,s));return!!(tZ(e,t,this.points,a,o,r,i-c,[0,-1],n)||tZ(e,t,this.points,a,o,r-c,i,[0,-1],n)||t1(e,t,c,c,a-l+s,o-u+s,n)||t1(e,t,c,c,a+l-s,o-u+s,n)||t1(e,t,c,c,a+l-s,o+u-s,n)||t1(e,t,c,c,a-l+s,o+u-s,n))}}},sP.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:nr(),points:t8(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i,null,a)},generateCutTrianglePts:function(e,t,n,r,i){var a="auto"===i?this.cornerLength:i,o=t/2,s=e/2,l=n-s,u=n+s,c=r-o,d=r+o;return{topLeft:[l,c+a,l+a,c,l+a,c+a],topRight:[u-a,c,u,c+a,u-a,c+a],bottomRight:[u,d-a,u-a,d,u-a,d-a],bottomLeft:[l+a,d,l,d-a,l+a,d-a]}},intersectLine:function(e,t,n,r,i,a,o,s){var l=this.generateCutTrianglePts(n+2*o,r+2*o,e,t,s);return t4(i,a,[].concat.apply([],[l.topLeft.splice(0,4),l.topRight.splice(0,4),l.bottomRight.splice(0,4),l.bottomLeft.splice(0,4)]),e,t)},checkPoint:function(e,t,n,r,i,a,o,s){var l="auto"===s?this.cornerLength:s;if(tZ(e,t,this.points,a,o,r,i-2*l,[0,-1],n)||tZ(e,t,this.points,a,o,r-2*l,i,[0,-1],n))return!0;var u=this.generateCutTrianglePts(r,i,a,o);return tK(e,t,u.topLeft)||tK(e,t,u.topRight)||tK(e,t,u.bottomRight)||tK(e,t,u.bottomLeft)}}},sP.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:t8(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o,s){var l=this.generateBarrelBezierPts(n+2*o,r+2*o,e,t),u=function(e){var t=tM({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.15),n=tM({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.5),r=tM({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.85);return[e[0],e[1],t.x,t.y,n.x,n.y,r.x,r.y,e[4],e[5]]};return t4(i,a,[].concat(u(l.topLeft),u(l.topRight),u(l.bottomRight),u(l.bottomLeft)),e,t)},generateBarrelBezierPts:function(e,t,n,r){var i=t/2,a=e/2,o=n-a,s=n+a,l=r-i,u=r+i,c=ni(e,t),d=c.heightOffset,h=c.widthOffset,p=c.ctrlPtOffsetPct*e,f={topLeft:[o,l+d,o+p,l,o+h,l],topRight:[s-h,l,s-p,l,s,l+d],bottomRight:[s,u-d,s-p,u,s-h,u],bottomLeft:[o+h,u,o+p,u,o,u-d]};return f.topLeft.isTop=!0,f.topRight.isTop=!0,f.bottomLeft.isBottom=!0,f.bottomRight.isBottom=!0,f},checkPoint:function(e,t,n,r,i,a,o,s){var l=ni(r,i),u=l.heightOffset,c=l.widthOffset;if(tZ(e,t,this.points,a,o,r,i-2*u,[0,-1],n)||tZ(e,t,this.points,a,o,r-2*c,i,[0,-1],n))return!0;for(var d=this.generateBarrelBezierPts(r,i,a,o),h=Object.keys(d),p=0;p<h.length;p++){var f=d[h[p]],g=function(e,t,n){var r=n[4],i=n[2],a=n[0],o=n[5],s=n[1];if(Math.min(r,a)<=e&&e<=Math.max(r,a)&&Math.min(o,s)<=t&&t<=Math.max(o,s)){var l=[r-2*i+a,2*(i-r),r],u=tW(l[0],l[1],l[2],e).filter(function(e){return 0<=e&&e<=1});if(u.length>0)return u[0]}return null}(e,t,f);if(null!=g){var v=t_(f[5],f[3],f[1],g);if(f.isTop&&v<=t||f.isBottom&&t<=v)return!0}}return!1}}},sP.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:t8(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i,this.points,a)},intersectLine:function(e,t,n,r,i,a,o,s){var l=t-(r/2+o),u=t3(i,a,e,t,e-(n/2+o),l,e+(n/2+o),l,!1);return u.length>0?u:tX(i,a,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,i,a,o,s){var l=2*(s="auto"===s?nt(r,i):s);if(tZ(e,t,this.points,a,o,r,i-l,[0,-1],n)||tZ(e,t,this.points,a,o,r-l,i,[0,-1],n))return!0;var u=r/2+2*n,c=i/2+2*n;return!!(tK(e,t,[a-u,o-c,a-u,o,a+u,o,a+u,o-c])||t1(e,t,l,l,a+r/2-s,o+i/2-s,n)||t1(e,t,l,l,a-r/2+s,o+i/2-s,n))}}},sP.registerNodeShapes=function(){var e=this.nodeShapes={},t=this;this.generateEllipse(),this.generatePolygon("triangle",t8(3,0)),this.generateRoundPolygon("round-triangle",t8(3,0)),this.generatePolygon("rectangle",t8(4,0)),e.square=e.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();var n=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",n),this.generateRoundPolygon("round-diamond",n),this.generatePolygon("pentagon",t8(5,0)),this.generateRoundPolygon("round-pentagon",t8(5,0)),this.generatePolygon("hexagon",t8(6,0)),this.generateRoundPolygon("round-hexagon",t8(6,0)),this.generatePolygon("heptagon",t8(7,0)),this.generateRoundPolygon("round-heptagon",t8(7,0)),this.generatePolygon("octagon",t8(8,0)),this.generateRoundPolygon("round-octagon",t8(8,0));var r=Array(20),i=ne(5,0),a=ne(5,Math.PI/5),o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<a.length/2;s++)a[2*s]*=o,a[2*s+1]*=o;for(var s=0;s<5;s++)r[4*s]=i[2*s],r[4*s+1]=i[2*s+1],r[4*s+2]=a[2*s],r[4*s+3]=a[2*s+1];r=t7(r),this.generatePolygon("star",r),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l),e.makePolygon=function(e){var n,r="polygon-"+e.join("$");return(n=this[r])?n:t.generatePolygon(r,e)}};var s_={};s_.timeToRender=function(){return this.redrawTotalTime/this.redrawCount},s_.redraw=function(e){e=e||e3();var t=this;void 0===t.averageRedrawTime&&(t.averageRedrawTime=0),void 0===t.lastRedrawTime&&(t.lastRedrawTime=0),void 0===t.lastDrawTime&&(t.lastDrawTime=0),t.requestedFrame=!0,t.renderOptions=e},s_.beforeRender=function(e,t){if(!this.destroyed){null==t&&eQ("Priority is not optional for beforeRender");var n=this.beforeRenderCallbacks;n.push({fn:e,priority:t}),n.sort(function(e,t){return t.priority-e.priority})}};var sM=function(e,t,n){for(var r=e.beforeRenderCallbacks,i=0;i<r.length;i++)r[i].fn(t,n)};s_.startRenderLoop=function(){var e=this,t=e.cy;e.renderLoopStarted||(e.renderLoopStarted=!0,eI(function n(r){if(!e.destroyed){if(t.batching());else if(e.requestedFrame&&!e.skipFrame){sM(e,!0,r);var i=eN();e.render(e.renderOptions);var a=e.lastDrawTime=eN();void 0===e.averageRedrawTime&&(e.averageRedrawTime=a-i),void 0===e.redrawCount&&(e.redrawCount=0),e.redrawCount++,void 0===e.redrawTotalTime&&(e.redrawTotalTime=0);var o=a-i;e.redrawTotalTime+=o,e.lastRedrawTime=o,e.averageRedrawTime=e.averageRedrawTime/2+o/2,e.requestedFrame=!1}else sM(e,!1,r);e.skipFrame=!1,eI(n)}}))};var sB=function(e){this.init(e)},sN=sB.prototype;sN.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],sN.init=function(e){var t=this;t.options=e,t.cy=e.cy;var n=t.container=e.cy.container(),r=t.cy.window();if(r){var i=r.document,a=i.head,o="__________cytoscape_stylesheet",s="__________cytoscape_container",l=null!=i.getElementById(o);if(0>n.className.indexOf(s)&&(n.className=(n.className||"")+" "+s),!l){var u=i.createElement("style");u.id=o,u.textContent="."+s+" { position: relative; }",a.insertBefore(u,a.children[0])}"static"===r.getComputedStyle(n).getPropertyValue("position")&&e0("A Cytoscape container has style position:static and so can not use UI extensions properly")}t.selection=[void 0,void 0,void 0,void 0,0],t.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],t.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},t.dragData={possibleDragElements:[]},t.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},t.redraws=0,t.showFps=e.showFps,t.debug=e.debug,t.hideEdgesOnViewport=e.hideEdgesOnViewport,t.textureOnViewport=e.textureOnViewport,t.wheelSensitivity=e.wheelSensitivity,t.motionBlurEnabled=e.motionBlur,t.forcedPixelRatio=M(e.pixelRatio)?e.pixelRatio:null,t.motionBlur=e.motionBlur,t.motionBlurOpacity=e.motionBlurOpacity,t.motionBlurTransparency=1-t.motionBlurOpacity,t.motionBlurPxRatio=1,t.mbPxRBlurry=1,t.minMbLowQualFrames=4,t.fullQualityMb=!1,t.clearedForMotionBlur=[],t.desktopTapThreshold=e.desktopTapThreshold,t.desktopTapThreshold2=e.desktopTapThreshold*e.desktopTapThreshold,t.touchTapThreshold=e.touchTapThreshold,t.touchTapThreshold2=e.touchTapThreshold*e.touchTapThreshold,t.tapholdDuration=500,t.bindings=[],t.beforeRenderCallbacks=[],t.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},t.registerNodeShapes(),t.registerArrowShapes(),t.registerCalculationListeners()},sN.notify=function(e,t){var n=this.cy;if(!this.destroyed){if("init"===e){this.load();return}if("destroy"===e){this.destroy();return}("add"===e||"remove"===e||"move"===e&&n.hasCompoundNodes()||"load"===e||"zorder"===e||"mount"===e)&&this.invalidateCachedZSortedEles(),"viewport"===e&&this.redrawHint("select",!0),("load"===e||"resize"===e||"mount"===e)&&(this.invalidateContainerClientCoordsCache(),this.matchCanvasSize(this.container)),this.redrawHint("eles",!0),this.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}},sN.destroy=function(){var e=this;e.destroyed=!0,e.cy.stopAnimationLoop();for(var t=0;t<e.bindings.length;t++){var n=e.bindings[t],r=n.target;(r.off||r.removeEventListener).apply(r,n.args)}if(e.bindings=[],e.beforeRenderCallbacks=[],e.onUpdateEleCalcsFns=[],e.removeObserver&&e.removeObserver.disconnect(),e.styleObserver&&e.styleObserver.disconnect(),e.resizeObserver&&e.resizeObserver.disconnect(),e.labelCalcDiv)try{document.body.removeChild(e.labelCalcDiv)}catch(e){}},sN.isHeadless=function(){return!1},[oF,sS,sD,sT,sP,s_].forEach(function(e){Z(sN,e)});var sA=1e3/60,sI={setupDequeueing:function(e){return function(){var t=this,n=this.renderer;if(!t.dequeueingSetup){t.dequeueingSetup=!0;var r=eM(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),i=e.priority||e$;n.beforeRender(function(i,a){var o=eN(),s=n.averageRedrawTime,l=n.lastRedrawTime,u=[],c=n.cy.extent(),d=n.getPixelRatio();for(i||n.flushRenderedStyleQueue();;){var h=eN(),p=h-o,f=h-a;if(l<sA){var g=sA-(i?s:0);if(f>=e.deqFastCost*g)break}else if(i){if(p>=e.deqCost*l||p>=e.deqAvgCost*s)break}else if(f>=e.deqNoDrawCost*sA)break;var v=e.deq(t,d,c);if(v.length>0)for(var y=0;y<v.length;y++)u.push(v[y]);else break}u.length>0&&(e.onDeqd(t,u),!i&&e.shouldRedraw(t,u,d,c)&&r())},i(t))}}}},sO=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eK;i(this,e),this.idsByKey=new tn,this.keyForId=new tn,this.cachesByLvl=new tn,this.lvls=[],this.getKey=t,this.doesEleInvalidateKey=n}return o(e,[{key:"getIdsFor",value:function(e){null==e&&eQ("Can not get id list for null key");var t=this.idsByKey,n=this.idsByKey.get(e);return n||(n=new ti,t.set(e,n)),n}},{key:"addIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).add(t)}},{key:"deleteIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).delete(t)}},{key:"getNumberOfIdsForKey",value:function(e){return null==e?0:this.getIdsFor(e).size}},{key:"updateKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t),r=this.getKey(e);this.deleteIdForKey(n,t),this.addIdForKey(r,t),this.keyForId.set(t,r)}},{key:"deleteKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteIdForKey(n,t),this.keyForId.delete(t)}},{key:"keyHasChangedFor",value:function(e){var t=e.id();return this.keyForId.get(t)!==this.getKey(e)}},{key:"isInvalid",value:function(e){return this.keyHasChangedFor(e)||this.doesEleInvalidateKey(e)}},{key:"getCachesAt",value:function(e){var t=this.cachesByLvl,n=this.lvls,r=t.get(e);return r||(r=new tn,t.set(e,r),n.push(e)),r}},{key:"getCache",value:function(e,t){return this.getCachesAt(t).get(e)}},{key:"get",value:function(e,t){var n=this.getKey(e),r=this.getCache(n,t);return null!=r&&this.updateKeyMappingFor(e),r}},{key:"getForCachedKey",value:function(e,t){var n=this.keyForId.get(e.id());return this.getCache(n,t)}},{key:"hasCache",value:function(e,t){return this.getCachesAt(t).has(e)}},{key:"has",value:function(e,t){var n=this.getKey(e);return this.hasCache(n,t)}},{key:"setCache",value:function(e,t,n){n.key=e,this.getCachesAt(t).set(e,n)}},{key:"set",value:function(e,t,n){var r=this.getKey(e);this.setCache(r,t,n),this.updateKeyMappingFor(e)}},{key:"deleteCache",value:function(e,t){this.getCachesAt(t).delete(e)}},{key:"delete",value:function(e,t){var n=this.getKey(e);this.deleteCache(n,t)}},{key:"invalidateKey",value:function(e){var t=this;this.lvls.forEach(function(n){return t.deleteCache(e,n)})}},{key:"invalidate",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteKeyMappingFor(e);var r=this.doesEleInvalidateKey(e);return r&&this.invalidateKey(n),r||0===this.getNumberOfIdsForKey(n)}}]),e}(),sR={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},sz=e4({getKey:null,doesEleInvalidateKey:eK,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:eU,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),sL=function(e,t){var n=this;n.renderer=e,n.onDequeues=[];var r=sz(t);Z(n,r),n.lookup=new sO(r.getKey,r.doesEleInvalidateKey),n.setupDequeueing()},sV=sL.prototype;sV.reasons=sR,sV.getTextureQueue=function(e){var t=this;return t.eleImgCaches=t.eleImgCaches||{},t.eleImgCaches[e]=t.eleImgCaches[e]||[]},sV.getRetiredTextureQueue=function(e){var t=this,n=t.eleImgCaches.retired=t.eleImgCaches.retired||{};return n[e]=n[e]||[]},sV.getElementQueue=function(){var e=this;return e.eleCacheQueue=e.eleCacheQueue||new tl(function(e,t){return t.reqs-e.reqs})},sV.getElementKeyToQueue=function(){var e=this;return e.eleKeyToCacheQueue=e.eleKeyToCacheQueue||{}},sV.getElement=function(e,t,n,r,i){var a,o,s,l=this,u=this.renderer,c=u.cy.zoom(),d=this.lookup;if(!t||0===t.w||0===t.h||isNaN(t.w)||isNaN(t.h)||!e.visible()||e.removed()||!l.allowEdgeTxrCaching&&e.isEdge()||!l.allowParentTxrCaching&&e.isParent())return null;if(null==r&&(r=Math.ceil(tC(c*n))),r<-4)r=-4;else if(c>=7.99||r>3)return null;var h=Math.pow(2,r),p=t.h*h,f=t.w*h,g=u.eleTextBiggerThanMin(e,h);if(!this.isVisible(e,g))return null;var v=d.get(e,r);if(v&&v.invalidated&&(v.invalidated=!1,v.texture.invalidatedWidth-=v.width),v)return v;if(a=p<=25?25:p<=50?50:50*Math.ceil(p/50),p>1024||f>1024)return null;var y=l.getTextureQueue(a),b=y[y.length-2],x=function(){return l.recycleTexture(a,f)||l.addTexture(a,f)};b||(b=y[y.length-1]),b||(b=x()),b.width-b.usedWidth<f&&(b=x());for(var w=function(e){return e&&e.scaledLabelShown===g},E=i&&i===sR.dequeue,k=i&&i===sR.highQuality,C=i&&i===sR.downscale,S=r+1;S<=3;S++){var D=d.get(e,S);if(D){o=D;break}}var T=o&&o.level===r+1?o:null,P=function(){b.context.drawImage(T.texture.canvas,T.x,0,T.width,T.height,b.usedWidth,0,f,p)};if(b.context.setTransform(1,0,0,1,0,0),b.context.clearRect(b.usedWidth,0,f,a),w(T))P();else if(w(o)){if(!k)return l.queueElement(e,o.level-1),o;for(var _=o.level;_>r;_--)T=l.getElement(e,t,n,_,sR.downscale);P()}else{if(!E&&!k&&!C)for(var M=r-1;M>=-4;M--){var B=d.get(e,M);if(B){s=B;break}}if(w(s))return l.queueElement(e,r),s;b.context.translate(b.usedWidth,0),b.context.scale(h,h),this.drawElement(b.context,e,t,g,!1),b.context.scale(1/h,1/h),b.context.translate(-b.usedWidth,0)}return v={x:b.usedWidth,texture:b,level:r,scale:h,width:f,height:p,scaledLabelShown:g},b.usedWidth+=Math.ceil(f+8),b.eleCaches.push(v),d.set(e,r,v),l.checkTextureFullness(b),v},sV.invalidateElements=function(e){for(var t=0;t<e.length;t++)this.invalidateElement(e[t])},sV.invalidateElement=function(e){var t=this.lookup,n=[];if(t.isInvalid(e)){for(var r=-4;r<=3;r++){var i=t.getForCachedKey(e,r);i&&n.push(i)}if(t.invalidate(e))for(var a=0;a<n.length;a++){var o=n[a],s=o.texture;s.invalidatedWidth+=o.width,o.invalidated=!0,this.checkTextureUtility(s)}this.removeFromQueue(e)}},sV.checkTextureUtility=function(e){e.invalidatedWidth>=.2*e.width&&this.retireTexture(e)},sV.checkTextureFullness=function(e){var t=this.getTextureQueue(e.height);e.usedWidth/e.width>.8&&e.fullnessChecks>=10?e9(t,e):e.fullnessChecks++},sV.retireTexture=function(e){var t=e.height,n=this.getTextureQueue(t),r=this.lookup;e9(n,e),e.retired=!0;for(var i=e.eleCaches,a=0;a<i.length;a++){var o=i[a];r.deleteCache(o.key,o.level)}e6(i),this.getRetiredTextureQueue(t).push(e)},sV.addTexture=function(e,t){var n=this.getTextureQueue(e),r={};return n.push(r),r.eleCaches=[],r.height=e,r.width=Math.max(1024,t),r.usedWidth=0,r.invalidatedWidth=0,r.fullnessChecks=0,r.canvas=this.renderer.makeOffscreenCanvas(r.width,r.height),r.context=r.canvas.getContext("2d"),r},sV.recycleTexture=function(e,t){for(var n=this.getTextureQueue(e),r=this.getRetiredTextureQueue(e),i=0;i<r.length;i++){var a=r[i];if(a.width>=t)return a.retired=!1,a.usedWidth=0,a.invalidatedWidth=0,a.fullnessChecks=0,e6(a.eleCaches),a.context.setTransform(1,0,0,1,0,0),a.context.clearRect(0,0,a.width,a.height),e9(r,a),n.push(a),a}},sV.queueElement=function(e,t){var n=this.getElementQueue(),r=this.getElementKeyToQueue(),i=this.getKey(e),a=r[i];if(a)a.level=Math.max(a.level,t),a.eles.merge(e),a.reqs++,n.updateItem(a);else{var o={eles:e.spawn().merge(e),level:t,reqs:1,key:i};n.push(o),r[i]=o}},sV.dequeue=function(e){for(var t=this.getElementQueue(),n=this.getElementKeyToQueue(),r=[],i=this.lookup,a=0;a<1;a++)if(t.size()>0){var o=t.pop(),s=o.key,l=o.eles[0],u=i.hasCache(l,o.level);if(n[s]=null,u)continue;r.push(o);var c=this.getBoundingBox(l);this.getElement(l,c,e,o.level,sR.dequeue)}else break;return r},sV.removeFromQueue=function(e){var t=this.getElementQueue(),n=this.getElementKeyToQueue(),r=this.getKey(e),i=n[r];null!=i&&(1===i.eles.length?(i.reqs=eG,t.updateItem(i),t.pop(),n[r]=null):i.eles.unmerge(e))},sV.onDequeue=function(e){this.onDequeues.push(e)},sV.offDequeue=function(e){e9(this.onDequeues,e)},sV.setupDequeueing=sI.setupDequeueing({deqRedrawThreshold:100,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t,n){return e.dequeue(t,n)},onDeqd:function(e,t){for(var n=0;n<e.onDequeues.length;n++)(0,e.onDequeues[n])(t)},shouldRedraw:function(e,t,n,r){for(var i=0;i<t.length;i++)for(var a=t[i].eles,o=0;o<a.length;o++)if(tF(a[o].boundingBox(),r))return!0;return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var sF=function(e){var t=this,n=t.renderer=e,r=n.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=eN()-500,t.skipping=!1,t.eleTxrDeqs=r.collection(),t.scheduleElementRefinement=eM(function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)},50),n.beforeRender(function(e,n){n-t.lastInvalidationTime<=250?t.skipping=!0:t.skipping=!1},n.beforeRenderPriorities.lyrTxrSkip),t.layersQueue=new tl(function(e,t){return t.reqs-e.reqs}),t.setupDequeueing()},sj=sF.prototype,sq=0;sj.makeLayer=function(e,t){var n=Math.pow(2,t),r=Math.ceil(e.w*n),i=Math.ceil(e.h*n),a=this.renderer.makeOffscreenCanvas(r,i),o={id:sq=++sq%9007199254740991,bb:e,level:t,width:r,height:i,canvas:a,context:a.getContext("2d"),eles:[],elesQueue:[],reqs:0},s=o.context,l=-o.bb.x1,u=-o.bb.y1;return s.scale(n,n),s.translate(l,u),o},sj.getLayers=function(e,t,n){var r,i,a=this,o=a.renderer.cy.zoom(),s=a.firstGet;if(a.firstGet=!1,null==n){if((n=Math.ceil(tC(o*t)))<-4)n=-4;else if(o>=3.99||n>2)return null}a.validateLayersElesOrdering(n,e);var l=a.layersByLevel,u=Math.pow(2,n),c=l[n]=l[n]||[];if(a.levelIsComplete(n,e))return c;!function(){var t=function(t){if(a.validateLayersElesOrdering(t,e),a.levelIsComplete(t,e))return i=l[t],!0},r=function(e){if(!i)for(var r=n+e;-4<=r&&r<=2&&!t(r);r+=e);};r(1),r(-1);for(var o=c.length-1;o>=0;o--){var s=c[o];s.invalid&&e9(c,s)}}();var d=function(){if(!r){r=tA();for(var t=0;t<e.length;t++)tO(r,e[t].boundingBox())}return r};if(a.skipping&&!s)return null;for(var h=null,p=e.length/1,f=!s,g=0;g<e.length;g++){var v=e[g],y=v._private.rscratch,b=y.imgLayerCaches=y.imgLayerCaches||{},x=b[n];if(x){h=x;continue}if((!h||h.eles.length>=p||!tq(h.bb,v.boundingBox()))&&!(h=function(e){var t=(e=e||{}).after;d();var i=Math.ceil(r.w*u),o=Math.ceil(r.h*u);if(i>32767||o>32767||i*o>16e6)return null;var s=a.makeLayer(r,n);if(null!=t){var l=c.indexOf(t)+1;c.splice(l,0,s)}else(void 0===e.insert||e.insert)&&c.unshift(s);return s}({insert:!0,after:h})))return null;i||f?a.queueLayer(h,v):a.drawEleInLayer(h,v,n,t),h.eles.push(v),b[n]=h}return i||(f?null:c)},sj.getEleLevelForLayerLevel=function(e,t){return e},sj.drawEleInLayer=function(e,t,n,r){var i=this.renderer,a=e.context,o=t.boundingBox();0!==o.w&&0!==o.h&&t.visible()&&(n=this.getEleLevelForLayerLevel(n,r),i.setImgSmoothing(a,!1),i.drawCachedElement(a,t,null,null,n,!0),i.setImgSmoothing(a,!0))},sj.levelIsComplete=function(e,t){var n=this.layersByLevel[e];if(!n||0===n.length)return!1;for(var r=0,i=0;i<n.length;i++){var a=n[i];if(a.reqs>0||a.invalid)return!1;r+=a.eles.length}return r===t.length},sj.validateLayersElesOrdering=function(e,t){var n=this.layersByLevel[e];if(n)for(var r=0;r<n.length;r++){for(var i=n[r],a=-1,o=0;o<t.length;o++)if(i.eles[0]===t[o]){a=o;break}if(a<0){this.invalidateLayer(i);continue}for(var s=a,o=0;o<i.eles.length;o++)if(i.eles[o]!==t[s+o]){this.invalidateLayer(i);break}}},sj.updateElementsInLayers=function(e,t){for(var n=A(e[0]),r=0;r<e.length;r++)for(var i=n?null:e[r],a=n?e[r]:e[r].ele,o=a._private.rscratch,s=o.imgLayerCaches=o.imgLayerCaches||{},l=-4;l<=2;l++){var u=s[l];u&&(!i||this.getEleLevelForLayerLevel(u.level)===i.level)&&t(u,a,i)}},sj.haveLayers=function(){for(var e=!1,t=-4;t<=2;t++){var n=this.layersByLevel[t];if(n&&n.length>0){e=!0;break}}return e},sj.invalidateElements=function(e){var t=this;0!==e.length&&(t.lastInvalidationTime=eN(),0!==e.length&&t.haveLayers()&&t.updateElementsInLayers(e,function(e,n,r){t.invalidateLayer(e)}))},sj.invalidateLayer=function(e){if(this.lastInvalidationTime=eN(),!e.invalid){var t=e.level,n=e.eles;e9(this.layersByLevel[t],e),e.elesQueue=[],e.invalid=!0,e.replacement&&(e.replacement.invalid=!0);for(var r=0;r<n.length;r++){var i=n[r]._private.rscratch.imgLayerCaches;i&&(i[t]=null)}}},sj.refineElementTextures=function(e){var t=this;t.updateElementsInLayers(e,function(e,n,r){var i=e.replacement;if(i||((i=e.replacement=t.makeLayer(e.bb,e.level)).replaces=e,i.eles=e.eles),!i.reqs)for(var a=0;a<i.eles.length;a++)t.queueLayer(i,i.eles[a])})},sj.enqueueElementRefinement=function(e){this.eleTxrDeqs.merge(e),this.scheduleElementRefinement()},sj.queueLayer=function(e,t){var n=this.layersQueue,r=e.elesQueue,i=r.hasId=r.hasId||{};if(!e.replacement){if(t){if(i[t.id()])return;r.push(t),i[t.id()]=!0}e.reqs?(e.reqs++,n.updateItem(e)):(e.reqs=1,n.push(e))}},sj.dequeue=function(e){for(var t=this.layersQueue,n=[],r=0;r<1&&0!==t.size();){var i=t.peek();if(i.replacement||i.replaces&&i!==i.replaces.replacement||i.invalid){t.pop();continue}var a=i.elesQueue.shift();a&&(this.drawEleInLayer(i,a,i.level,e),r++),0===n.length&&n.push(!0),0===i.elesQueue.length&&(t.pop(),i.reqs=0,i.replaces&&this.applyLayerReplacement(i),this.requestRedraw())}return n},sj.applyLayerReplacement=function(e){var t=this.layersByLevel[e.level],n=e.replaces,r=t.indexOf(n);if(!(r<0)&&!n.invalid){t[r]=e;for(var i=0;i<e.eles.length;i++){var a=e.eles[i]._private,o=a.imgLayerCaches=a.imgLayerCaches||{};o&&(o[e.level]=e)}this.requestRedraw()}},sj.requestRedraw=eM(function(){var e=this.renderer;e.redrawHint("eles",!0),e.redrawHint("drag",!0),e.redraw()},100),sj.setupDequeueing=sI.setupDequeueing({deqRedrawThreshold:50,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t){return e.dequeue(t)},onDeqd:e$,shouldRedraw:eU,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var sX={};function sY(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.lineTo(r.x,r.y)}}function sW(e,t,n){for(var r,i=0;i<t.length;i++){var a=t[i];0===i&&(r=a),e.lineTo(a.x,a.y)}e.quadraticCurveTo(n.x,n.y,r.x,r.y)}function sH(e,t,n){e.beginPath&&e.beginPath();for(var r=0;r<t.length;r++){var i=t[r];e.lineTo(i.x,i.y)}var a=n[0];e.moveTo(a.x,a.y);for(var r=1;r<n.length;r++){var i=n[r];e.lineTo(i.x,i.y)}e.closePath&&e.closePath()}function sG(e,t,n,r,i){e.beginPath&&e.beginPath(),e.arc(n,r,i,0,2*Math.PI,!1);var a=t[0];e.moveTo(a.x,a.y);for(var o=0;o<t.length;o++){var s=t[o];e.lineTo(s.x,s.y)}e.closePath&&e.closePath()}function sU(e,t,n,r){e.arc(t,n,r,0,2*Math.PI,!1)}sX.arrowShapeImpl=function(e){return(y||(y={polygon:sY,"triangle-backcurve":sW,"triangle-tee":sH,"circle-triangle":sG,"triangle-cross":sH,circle:sU}))[e]};var sK={};sK.drawElement=function(e,t,n,r,i,a){t.isNode()?this.drawNode(e,t,n,r,i,a):this.drawEdge(e,t,n,r,i,a)},sK.drawElementOverlay=function(e,t){t.isNode()?this.drawNodeOverlay(e,t):this.drawEdgeOverlay(e,t)},sK.drawElementUnderlay=function(e,t){t.isNode()?this.drawNodeUnderlay(e,t):this.drawEdgeUnderlay(e,t)},sK.drawCachedElementPortion=function(e,t,n,r,i,a,o,s){var l=n.getBoundingBox(t);if(0!==l.w&&0!==l.h){var u=n.getElement(t,l,r,i,a);if(null!=u){var c,d,h,p,f,g,v=s(this,t);if(0===v)return;var y=o(this,t),b=l.x1,x=l.y1,w=l.w,E=l.h;if(0!==y){var k=n.getRotationPoint(t);h=k.x,p=k.y,e.translate(h,p),e.rotate(y),(f=this.getImgSmoothing(e))||this.setImgSmoothing(e,!0);var C=n.getRotationOffset(t);c=C.x,d=C.y}else c=b,d=x;1!==v&&(g=e.globalAlpha,e.globalAlpha=g*v),e.drawImage(u.texture.canvas,u.x,0,u.width,u.height,c,d,w,E),1!==v&&(e.globalAlpha=g),0===y||(e.rotate(-y),e.translate(-h,-p),f||this.setImgSmoothing(e,!1))}else n.drawElement(e,t)}};var sZ=function(){return 0},s$=function(e,t){return e.getTextAngle(t,null)},sQ=function(e,t){return e.getTextAngle(t,"source")},sJ=function(e,t){return e.getTextAngle(t,"target")},s0=function(e,t){return t.effectiveOpacity()},s1=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};sK.drawCachedElement=function(e,t,n,r,i,a){var o=this.data,s=o.eleTxrCache,l=o.lblTxrCache,u=o.slbTxrCache,c=o.tlbTxrCache,d=t.boundingBox(),h=!0===a?s.reasons.highQuality:null;if(0!==d.w&&0!==d.h&&t.visible()&&(!r||tF(d,r))){var p=t.isEdge(),f=t.element()._private.rscratch.badLine;this.drawElementUnderlay(e,t),this.drawCachedElementPortion(e,t,s,n,i,h,sZ,s0),p&&f||this.drawCachedElementPortion(e,t,l,n,i,h,s$,s1),p&&!f&&(this.drawCachedElementPortion(e,t,u,n,i,h,sQ,s1),this.drawCachedElementPortion(e,t,c,n,i,h,sJ,s1)),this.drawElementOverlay(e,t)}},sK.drawElements=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];this.drawElement(e,r)}},sK.drawCachedElements=function(e,t,n,r){for(var i=0;i<t.length;i++){var a=t[i];this.drawCachedElement(e,a,n,r)}},sK.drawCachedNodes=function(e,t,n,r){for(var i=0;i<t.length;i++){var a=t[i];a.isNode()&&this.drawCachedElement(e,a,n,r)}},sK.drawLayeredElements=function(e,t,n,r){var i=this.data.lyrTxrCache.getLayers(t,n);if(i)for(var a=0;a<i.length;a++){var o=i[a],s=o.bb;0!==s.w&&0!==s.h&&e.drawImage(o.canvas,s.x1,s.y1,s.w,s.h)}else this.drawCachedElements(e,t,n,r)};var s2={};s2.drawEdge=function(e,t,n){var r,i=!(arguments.length>3)||void 0===arguments[3]||arguments[3],a=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=!(arguments.length>5)||void 0===arguments[5]||arguments[5],s=this,l=t._private.rscratch;if(!(o&&!t.visible()||l.badLine||null==l.allpts||isNaN(l.allpts[0]))){n&&(r=n,e.translate(-r.x1,-r.y1));var u=o?t.pstyle("opacity").value:1,c=o?t.pstyle("line-opacity").value:1,d=t.pstyle("curve-style").value,h=t.pstyle("line-style").value,p=t.pstyle("width").pfValue,f=t.pstyle("line-cap").value,g=t.pstyle("line-outline-width").value,v=t.pstyle("line-outline-color").value,y=u*c,b=u*c,x=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y;"straight-triangle"===d?(s.eleStrokeStyle(e,t,n),s.drawEdgeTrianglePath(t,e,l.allpts)):(e.lineWidth=p,e.lineCap=f,s.eleStrokeStyle(e,t,n),s.drawEdgePath(t,e,l.allpts,h),e.lineCap="butt")},w=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:b;s.drawArrowheads(e,t,n)};if(e.lineJoin="round","yes"===t.pstyle("ghost").value){var E=t.pstyle("ghost-offset-x").pfValue,k=t.pstyle("ghost-offset-y").pfValue,C=y*t.pstyle("ghost-opacity").value;e.translate(E,k),x(C),w(C),e.translate(-E,-k)}else!function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y;if(e.lineWidth=p+g,e.lineCap=f,g>0)s.colorStrokeStyle(e,v[0],v[1],v[2],n);else{e.lineCap="butt";return}"straight-triangle"===d?s.drawEdgeTrianglePath(t,e,l.allpts):(s.drawEdgePath(t,e,l.allpts,h),e.lineCap="butt")}();a&&s.drawEdgeUnderlay(e,t),x(),w(),a&&s.drawEdgeOverlay(e,t),s.drawElementText(e,t,null,i),n&&e.translate(r.x1,r.y1)}};var s5=function(e){if(!["overlay","underlay"].includes(e))throw Error("Invalid state");return function(t,n){if(n.visible()){var r=n.pstyle("".concat(e,"-opacity")).value;if(0!==r){var i=this.usePaths(),a=n._private.rscratch,o=n.pstyle("".concat(e,"-padding")).pfValue,s=n.pstyle("".concat(e,"-color")).value;t.lineWidth=2*o,"self"!==a.edgeType||i?t.lineCap="round":t.lineCap="butt",this.colorStrokeStyle(t,s[0],s[1],s[2],r),this.drawEdgePath(n,t,a.allpts,"solid")}}}};s2.drawEdgeOverlay=s5("overlay"),s2.drawEdgeUnderlay=s5("underlay"),s2.drawEdgePath=function(e,t,n,r){var i=e._private.rscratch,a=t,o=!1,s=this.usePaths(),l=e.pstyle("line-dash-pattern").pfValue,u=e.pstyle("line-dash-offset").pfValue;if(s){var c=n.join("$");i.pathCacheKey&&i.pathCacheKey===c?(f=t=i.pathCache,o=!0):(f=t=new Path2D,i.pathCacheKey=c,i.pathCache=f)}if(a.setLineDash)switch(r){case"dotted":a.setLineDash([1,1]);break;case"dashed":a.setLineDash(l),a.lineDashOffset=u;break;case"solid":a.setLineDash([])}if(!o&&!i.badLine)switch(t.beginPath&&t.beginPath(),t.moveTo(n[0],n[1]),i.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<n.length;h+=4)t.quadraticCurveTo(n[h],n[h+1],n[h+2],n[h+3]);break;case"straight":case"haystack":for(var p=2;p+1<n.length;p+=2)t.lineTo(n[p],n[p+1]);break;case"segments":if(i.isRound){var f,g,v=d(i.roundCorners);try{for(v.s();!(g=v.n()).done;){var y=g.value;sd(t,y)}}catch(e){v.e(e)}finally{v.f()}t.lineTo(n[n.length-2],n[n.length-1])}else for(var b=2;b+1<n.length;b+=2)t.lineTo(n[b],n[b+1])}t=a,s?t.stroke(f):t.stroke(),t.setLineDash&&t.setLineDash([])},s2.drawEdgeTrianglePath=function(e,t,n){t.fillStyle=t.strokeStyle;for(var r=e.pstyle("width").pfValue,i=0;i+1<n.length;i+=2){var a=[n[i+2]-n[i],n[i+3]-n[i+1]],o=Math.sqrt(a[0]*a[0]+a[1]*a[1]),s=[a[1]/o,-a[0]/o],l=[s[0]*r/2,s[1]*r/2];t.beginPath(),t.moveTo(n[i]-l[0],n[i+1]-l[1]),t.lineTo(n[i]+l[0],n[i+1]+l[1]),t.lineTo(n[i+2],n[i+3]),t.closePath(),t.fill()}},s2.drawArrowheads=function(e,t,n){var r=t._private.rscratch,i="haystack"===r.edgeType;i||this.drawArrowhead(e,t,"source",r.arrowStartX,r.arrowStartY,r.srcArrowAngle,n),this.drawArrowhead(e,t,"mid-target",r.midX,r.midY,r.midtgtArrowAngle,n),this.drawArrowhead(e,t,"mid-source",r.midX,r.midY,r.midsrcArrowAngle,n),i||this.drawArrowhead(e,t,"target",r.arrowEndX,r.arrowEndY,r.tgtArrowAngle,n)},s2.drawArrowhead=function(e,t,n,r,i,a,o){if(!(isNaN(r)||null==r||isNaN(i)||null==i||isNaN(a))&&null!=a){var s=t.pstyle(n+"-arrow-shape").value;if("none"!==s){var l="hollow"===t.pstyle(n+"-arrow-fill").value?"both":"filled",u=t.pstyle(n+"-arrow-fill").value,c=t.pstyle("width").pfValue,d=t.pstyle(n+"-arrow-width"),h="match-line"===d.value?c:d.pfValue;"%"===d.units&&(h*=c);var p=t.pstyle("opacity").value;void 0===o&&(o=p);var f=e.globalCompositeOperation;(1!==o||"hollow"===u)&&(e.globalCompositeOperation="destination-out",this.colorFillStyle(e,255,255,255,1),this.colorStrokeStyle(e,255,255,255,1),this.drawArrowShape(t,e,l,c,s,h,r,i,a),e.globalCompositeOperation=f);var g=t.pstyle(n+"-arrow-color").value;this.colorFillStyle(e,g[0],g[1],g[2],o),this.colorStrokeStyle(e,g[0],g[1],g[2],o),this.drawArrowShape(t,e,u,c,s,h,r,i,a)}}},s2.drawArrowShape=function(e,t,n,r,i,a,o,s,l){var u,c=this,d=this.usePaths()&&"triangle-cross"!==i,h=!1,p=t,f=e.pstyle("arrow-scale").value,g=this.getArrowWidth(r,f),v=c.arrowShapes[i];if(d){var y=c.arrowPathCache=c.arrowPathCache||[],b=ej(i),x=y[b];null!=x?(u=t=x,h=!0):(u=t=new Path2D,y[b]=u)}!h&&(t.beginPath&&t.beginPath(),d?v.draw(t,1,0,{x:0,y:0},1):v.draw(t,g,l,{x:o,y:s},r),t.closePath&&t.closePath()),t=p,d&&(t.translate(o,s),t.rotate(l),t.scale(g,g)),("filled"===n||"both"===n)&&(d?t.fill(u):t.fill()),("hollow"===n||"both"===n)&&(t.lineWidth=a/(d?g:1),t.lineJoin="miter",d?t.stroke(u):t.stroke()),d&&(t.scale(1/g,1/g),t.rotate(-l),t.translate(-o,-s))};var s3={};s3.safeDrawImage=function(e,t,n,r,i,a,o,s,l,u){if(!(i<=0)&&!(a<=0)&&!(l<=0)&&!(u<=0))try{e.drawImage(t,n,r,i,a,o,s,l,u)}catch(e){e0(e)}},s3.drawInscribedImage=function(e,t,n,r,i){var a=n.position(),o=a.x,s=a.y,l=n.cy().style(),u=l.getIndexedStyle.bind(l),c=u(n,"background-fit","value",r),d=u(n,"background-repeat","value",r),h=n.width(),p=n.height(),f=2*n.padding(),g=h+("inner"===u(n,"background-width-relative-to","value",r)?0:f),v=p+("inner"===u(n,"background-height-relative-to","value",r)?0:f),y=n._private.rscratch,b="node"===u(n,"background-clip","value",r),x=u(n,"background-image-opacity","value",r)*i,w=u(n,"background-image-smoothing","value",r),E=n.pstyle("corner-radius").value;"auto"!==E&&(E=n.pstyle("corner-radius").pfValue);var k=t.width||t.cachedW,C=t.height||t.cachedH;(null==k||null==C)&&(document.body.appendChild(t),k=t.cachedW=t.width||t.offsetWidth,C=t.cachedH=t.height||t.offsetHeight,document.body.removeChild(t));var S=k,D=C;if("auto"!==u(n,"background-width","value",r)&&(S="%"===u(n,"background-width","units",r)?u(n,"background-width","pfValue",r)*g:u(n,"background-width","pfValue",r)),"auto"!==u(n,"background-height","value",r)&&(D="%"===u(n,"background-height","units",r)?u(n,"background-height","pfValue",r)*v:u(n,"background-height","pfValue",r)),0!==S&&0!==D){if("contain"===c){var T=Math.min(g/S,v/D);S*=T,D*=T}else if("cover"===c){var T=Math.max(g/S,v/D);S*=T,D*=T}var P=o-g/2,_=u(n,"background-position-x","units",r),M=u(n,"background-position-x","pfValue",r);"%"===_?P+=(g-S)*M:P+=M;var B=u(n,"background-offset-x","units",r),N=u(n,"background-offset-x","pfValue",r);"%"===B?P+=(g-S)*N:P+=N;var A=s-v/2,I=u(n,"background-position-y","units",r),O=u(n,"background-position-y","pfValue",r);"%"===I?A+=(v-D)*O:A+=O;var R=u(n,"background-offset-y","units",r),z=u(n,"background-offset-y","pfValue",r);"%"===R?A+=(v-D)*z:A+=z,y.pathCache&&(P-=o,A-=s,o=0,s=0);var L=e.globalAlpha;e.globalAlpha=x;var V=this.getImgSmoothing(e),F=!1;if("no"===w&&V?(this.setImgSmoothing(e,!1),F=!0):"yes"!==w||V||(this.setImgSmoothing(e,!0),F=!0),"no-repeat"===d)b&&(e.save(),y.pathCache?e.clip(y.pathCache):(this.nodeShapes[this.getNodeShape(n)].draw(e,o,s,g,v,E,y),e.clip())),this.safeDrawImage(e,t,0,0,k,C,P,A,S,D),b&&e.restore();else{var j=e.createPattern(t,d);e.fillStyle=j,this.nodeShapes[this.getNodeShape(n)].draw(e,o,s,g,v,E,y),e.translate(P,A),e.fill(),e.translate(-P,-A)}e.globalAlpha=L,F&&this.setImgSmoothing(e,V)}};var s4={};function s9(e,t,n,r,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:5,o=arguments.length>6?arguments[6]:void 0;e.beginPath(),e.moveTo(t+a,n),e.lineTo(t+r-a,n),e.quadraticCurveTo(t+r,n,t+r,n+a),e.lineTo(t+r,n+i-a),e.quadraticCurveTo(t+r,n+i,t+r-a,n+i),e.lineTo(t+a,n+i),e.quadraticCurveTo(t,n+i,t,n+i-a),e.lineTo(t,n+a),e.quadraticCurveTo(t,n,t+a,n),e.closePath(),o?e.stroke():e.fill()}s4.eleTextBiggerThanMin=function(e,t){return t||(t=Math.pow(2,Math.ceil(tC(e.cy().zoom()*this.getPixelRatio())))),!(e.pstyle("font-size").pfValue*t<e.pstyle("min-zoomed-font-size").pfValue)},s4.drawElementText=function(e,t,n,r,i){var a,o=!(arguments.length>5)||void 0===arguments[5]||arguments[5];if(null==r){if(o&&!this.eleTextBiggerThanMin(t))return}else if(!1===r)return;if(t.isNode()){var s=t.pstyle("label");if(!s||!s.value)return;var l=this.getLabelJustification(t);e.textAlign=l,e.textBaseline="bottom"}else{var u=t.element()._private.rscratch.badLine,c=t.pstyle("label"),d=t.pstyle("source-label"),h=t.pstyle("target-label");if(u||(!c||!c.value)&&(!d||!d.value)&&(!h||!h.value))return;e.textAlign="center",e.textBaseline="bottom"}var p=!n;n&&(a=n,e.translate(-a.x1,-a.y1)),null==i?(this.drawText(e,t,null,p,o),t.isEdge()&&(this.drawText(e,t,"source",p,o),this.drawText(e,t,"target",p,o))):this.drawText(e,t,i,p,o),n&&e.translate(a.x1,a.y1)},s4.getFontCache=function(e){var t;this.fontCaches=this.fontCaches||[];for(var n=0;n<this.fontCaches.length;n++)if((t=this.fontCaches[n]).context===e)return t;return t={context:e},this.fontCaches.push(t),t},s4.setupTextStyle=function(e,t){var n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=t.pstyle("font-style").strValue,i=t.pstyle("font-size").pfValue+"px",a=t.pstyle("font-family").strValue,o=t.pstyle("font-weight").strValue,s=n?t.effectiveOpacity()*t.pstyle("text-opacity").value:1,l=t.pstyle("text-outline-opacity").value*s,u=t.pstyle("color").value,c=t.pstyle("text-outline-color").value;e.font=r+" "+o+" "+i+" "+a,e.lineJoin="round",this.colorFillStyle(e,u[0],u[1],u[2],s),this.colorStrokeStyle(e,c[0],c[1],c[2],l)},s4.getTextAngle=function(e,t){var n=e._private.rscratch,r=e.pstyle((t?t+"-":"")+"text-rotation"),i=e7(n,"labelAngle",t);return"autorotate"===r.strValue?e.isEdge()?i:0:"none"===r.strValue?0:r.pfValue},s4.drawText=function(e,t,n){var r=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],a=t._private.rscratch,o=i?t.effectiveOpacity():1;if(!i||0!==o&&0!==t.pstyle("text-opacity").value){"main"===n&&(n=null);var s=e7(a,"labelX",n),l=e7(a,"labelY",n),u=this.getLabelText(t,n);if(null!=u&&""!==u&&!isNaN(s)&&!isNaN(l)){this.setupTextStyle(e,t,i);var c,d,h,p=n?n+"-":"",f=e7(a,"labelWidth",n),g=e7(a,"labelHeight",n),v=t.pstyle(p+"text-margin-x").pfValue,y=t.pstyle(p+"text-margin-y").pfValue,b=t.isEdge(),x=t.pstyle("text-halign").value,w=t.pstyle("text-valign").value;switch(b&&(x="center",w="center"),s+=v,l+=y,0!==(h=r?this.getTextAngle(t,n):0)&&(c=s,d=l,e.translate(c,d),e.rotate(h),s=0,l=0),w){case"top":break;case"center":l+=g/2;break;case"bottom":l+=g}var E=t.pstyle("text-background-opacity").value,k=t.pstyle("text-border-opacity").value,C=t.pstyle("text-border-width").pfValue,S=t.pstyle("text-background-padding").pfValue,D=0===t.pstyle("text-background-shape").strValue.indexOf("round");if(E>0||C>0&&k>0){var T=s-S;switch(x){case"left":T-=f;break;case"center":T-=f/2}var P=l-g-S,_=f+2*S,M=g+2*S;if(E>0){var B=e.fillStyle,N=t.pstyle("text-background-color").value;e.fillStyle="rgba("+N[0]+","+N[1]+","+N[2]+","+E*o+")",D?s9(e,T,P,_,M,2):e.fillRect(T,P,_,M),e.fillStyle=B}if(C>0&&k>0){var A=e.strokeStyle,I=e.lineWidth,O=t.pstyle("text-border-color").value,R=t.pstyle("text-border-style").value;if(e.strokeStyle="rgba("+O[0]+","+O[1]+","+O[2]+","+k*o+")",e.lineWidth=C,e.setLineDash)switch(R){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"double":e.lineWidth=C/4,e.setLineDash([]);break;case"solid":e.setLineDash([])}if(D?s9(e,T,P,_,M,2,"stroke"):e.strokeRect(T,P,_,M),"double"===R){var z=C/2;D?s9(e,T+z,P+z,_-2*z,M-2*z,2,"stroke"):e.strokeRect(T+z,P+z,_-2*z,M-2*z)}e.setLineDash&&e.setLineDash([]),e.lineWidth=I,e.strokeStyle=A}}var L=2*t.pstyle("text-outline-width").pfValue;if(L>0&&(e.lineWidth=L),"wrap"===t.pstyle("text-wrap").value){var V=e7(a,"labelWrapCachedLines",n),F=e7(a,"labelLineHeight",n),j=f/2,q=this.getLabelJustification(t);switch("auto"===q||("left"===x?"left"===q?s+=-f:"center"===q&&(s+=-j):"center"===x?"left"===q?s+=-j:"right"===q&&(s+=j):"right"===x&&("center"===q?s+=j:"right"===q&&(s+=f))),w){case"top":case"center":case"bottom":l-=(V.length-1)*F}for(var X=0;X<V.length;X++)L>0&&e.strokeText(V[X],s,l),e.fillText(V[X],s,l),l+=F}else L>0&&e.strokeText(u,s,l),e.fillText(u,s,l);0!==h&&(e.rotate(-h),e.translate(-c,-d))}}};var s6={};s6.drawNode=function(e,t,n){var r,i,a,o,s=!(arguments.length>3)||void 0===arguments[3]||arguments[3],l=!(arguments.length>4)||void 0===arguments[4]||arguments[4],u=!(arguments.length>5)||void 0===arguments[5]||arguments[5],c=this,d=t._private,h=d.rscratch,p=t.position();if(M(p.x)&&M(p.y)&&(!u||t.visible())){var f=u?t.effectiveOpacity():1,g=c.usePaths(),v=!1,y=t.padding();r=t.width()+2*y,i=t.height()+2*y,n&&(o=n,e.translate(-o.x1,-o.y1));for(var b=t.pstyle("background-image").value,x=Array(b.length),w=Array(b.length),E=0,k=0;k<b.length;k++){var C=b[k];if(x[k]=null!=C&&"none"!==C){var S=t.cy().style().getIndexedStyle(t,"background-image-crossorigin","value",k);E++,w[k]=c.getCachedImage(C,S,function(){d.backgroundTimestamp=Date.now(),t.emitAndNotify("background")})}}var D=t.pstyle("background-blacken").value,T=t.pstyle("border-width").pfValue,P=t.pstyle("background-opacity").value*f,_=t.pstyle("border-color").value,B=t.pstyle("border-style").value,N=t.pstyle("border-join").value,A=t.pstyle("border-cap").value,I=t.pstyle("border-position").value,O=t.pstyle("border-dash-pattern").pfValue,R=t.pstyle("border-dash-offset").pfValue,z=t.pstyle("border-opacity").value*f,L=t.pstyle("outline-width").pfValue,V=t.pstyle("outline-color").value,F=t.pstyle("outline-style").value,j=t.pstyle("outline-opacity").value*f,q=t.pstyle("outline-offset").value,X=t.pstyle("corner-radius").value;"auto"!==X&&(X=t.pstyle("corner-radius").pfValue);var Y=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:P;c.eleFillStyle(e,t,n)},W=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:z;c.colorStrokeStyle(e,_[0],_[1],_[2],t)},H=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:j;c.colorStrokeStyle(e,V[0],V[1],V[2],t)},G=function(e,t,n,r){var i,a=c.nodePathCache=c.nodePathCache||[],o=eq("polygon"===n?n+","+r.join(","):n,""+t,""+e,""+X),s=a[o],l=!1;return null!=s?(i=s,l=!0,h.pathCache=i):(i=new Path2D,a[o]=h.pathCache=i),{path:i,cacheHit:l}},U=t.pstyle("shape").strValue,K=t.pstyle("shape-polygon-points").pfValue;if(g){e.translate(p.x,p.y);var Z=G(r,i,U,K);a=Z.path,v=Z.cacheHit}var $=function(){if(!v){var n=p;g&&(n={x:0,y:0}),c.nodeShapes[c.getNodeShape(t)].draw(a||e,n.x,n.y,r,i,X,h)}g?e.fill(a):e.fill()},Q=function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,r=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=d.backgrounding,a=0,o=0;o<w.length;o++){var s=t.cy().style().getIndexedStyle(t,"background-image-containment","value",o);if(r&&"over"===s||!r&&"inside"===s){a++;continue}x[o]&&w[o].complete&&!w[o].error&&(a++,c.drawInscribedImage(e,w[o],t,o,n))}d.backgrounding=a!==E,i!==d.backgrounding&&t.updateStyle(!1)},J=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f;c.hasPie(t)&&(c.drawPie(e,t,a),n&&!g&&c.nodeShapes[c.getNodeShape(t)].draw(e,p.x,p.y,r,i,X,h))},ee=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,n=D>0?0:255;0!==D&&(c.colorFillStyle(e,n,n,n,(D>0?D:-D)*t),g?e.fill(a):e.fill())},et=function(){if(T>0){if(e.lineWidth=T,e.lineCap=A,e.lineJoin=N,e.setLineDash)switch(B){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash(O),e.lineDashOffset=R;break;case"solid":case"double":e.setLineDash([])}if("center"!==I){if(e.save(),e.lineWidth*=2,"inside"===I)g?e.clip(a):e.clip();else{var t=new Path2D;t.rect(-r/2-T,-i/2-T,r+2*T,i+2*T),t.addPath(a),e.clip(t,"evenodd")}g?e.stroke(a):e.stroke(),e.restore()}else g?e.stroke(a):e.stroke();if("double"===B){e.lineWidth=T/3;var n=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",g?e.stroke(a):e.stroke(),e.globalCompositeOperation=n}e.setLineDash&&e.setLineDash([])}},en=function(){if(L>0){if(e.lineWidth=L,e.lineCap="butt",e.setLineDash)switch(F){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"solid":case"double":e.setLineDash([])}var n=p;g&&(n={x:0,y:0});var a=c.getNodeShape(t),o=T;"inside"===I&&(o=0),"outside"===I&&(o*=2);var s=(r+o+(L+q))/r,l=(i+o+(L+q))/i,u=r*s,d=i*l,h=c.nodeShapes[a].points;if(g&&(S=G(u,d,a,h).path),"ellipse"===a)c.drawEllipsePath(S||e,n.x,n.y,u,d);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(a)){var f=0,v=0,y=0;"round-diamond"===a?f=(o+q+L)*1.4:"round-heptagon"===a?(f=(o+q+L)*1.075,y=-(o/2+q+L)/35):"round-hexagon"===a?f=(o+q+L)*1.12:"round-pentagon"===a?(f=(o+q+L)*1.13,y=-(o/2+q+L)/15):"round-tag"===a?(f=(o+q+L)*1.12,v=(o/2+L+q)*.07):"round-triangle"===a&&(f=(o+q+L)*(Math.PI/2),y=-(o+q/2+L)/Math.PI),0===f||(s=(r+f)/r,u=r*s,["round-hexagon","round-tag"].includes(a)||(l=(i+f)/i,d=i*l)),X="auto"===X?nn(u,d):X;for(var b=u/2,x=d/2,w=X+(o+L+q)/2,E=Array(h.length/2),k=Array(h.length/2),C=0;C<h.length/2;C++)E[C]={x:n.x+v+b*h[2*C],y:n.y+y+x*h[2*C+1]};var S,D,P,_,M,B=E.length;for(D=0,P=E[B-1];D<B;D++)_=E[D%B],M=E[(D+1)%B],k[D]=sh(P,_,M,w),P=_,_=M;c.drawRoundPolygonPath(S||e,n.x+v,n.y+y,r*s,i*l,h,k)}else["roundrectangle","round-rectangle"].includes(a)?(X="auto"===X?nt(u,d):X,c.drawRoundRectanglePath(S||e,n.x,n.y,u,d,X+(o+L+q)/2)):["cutrectangle","cut-rectangle"].includes(a)?(X="auto"===X?nr():X,c.drawCutRectanglePath(S||e,n.x,n.y,u,d,null,X+(o+L+q)/4)):["bottomroundrectangle","bottom-round-rectangle"].includes(a)?(X="auto"===X?nt(u,d):X,c.drawBottomRoundRectanglePath(S||e,n.x,n.y,u,d,X+(o+L+q)/2)):"barrel"===a?c.drawBarrelPath(S||e,n.x,n.y,u,d):a.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(a)?(h=tQ(tJ(h,(o+L+q)/r)),c.drawPolygonPath(S||e,n.x,n.y,r,i,h)):(h=tQ(tJ(h,-((o+L+q)/r))),c.drawPolygonPath(S||e,n.x,n.y,r,i,h));if(g?e.stroke(S):e.stroke(),"double"===F){e.lineWidth=o/3;var N=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",g?e.stroke(S):e.stroke(),e.globalCompositeOperation=N}e.setLineDash&&e.setLineDash([])}};if("yes"===t.pstyle("ghost").value){var er=t.pstyle("ghost-offset-x").pfValue,ei=t.pstyle("ghost-offset-y").pfValue,ea=t.pstyle("ghost-opacity").value,eo=ea*f;e.translate(er,ei),H(),en(),Y(ea*P),$(),Q(eo,!0),W(ea*z),et(),J(0!==D||0!==T),Q(eo,!1),ee(eo),e.translate(-er,-ei)}g&&e.translate(-p.x,-p.y),l&&c.drawNodeUnderlay(e,t,p,r,i),g&&e.translate(p.x,p.y),H(),en(),Y(),$(),Q(f,!0),W(),et(),J(0!==D||0!==T),Q(f,!1),ee(),g&&e.translate(-p.x,-p.y),c.drawElementText(e,t,null,s),l&&c.drawNodeOverlay(e,t,p,r,i),n&&e.translate(o.x1,o.y1)}};var s8=function(e){if(!["overlay","underlay"].includes(e))throw Error("Invalid state");return function(t,n,r,i,a){if(n.visible()){var o=n.pstyle("".concat(e,"-padding")).pfValue,s=n.pstyle("".concat(e,"-opacity")).value,l=n.pstyle("".concat(e,"-color")).value,u=n.pstyle("".concat(e,"-shape")).value,c=n.pstyle("".concat(e,"-corner-radius")).value;if(s>0){if(r=r||n.position(),null==i||null==a){var d=n.padding();i=n.width()+2*d,a=n.height()+2*d}this.colorFillStyle(t,l[0],l[1],l[2],s),this.nodeShapes[u].draw(t,r.x,r.y,i+2*o,a+2*o,c),t.fill()}}}};s6.drawNodeOverlay=s8("overlay"),s6.drawNodeUnderlay=s8("underlay"),s6.hasPie=function(e){return(e=e[0])._private.hasPie},s6.drawPie=function(e,t,n,r){t=t[0],r=r||t.position();var i=t.cy().style(),a=t.pstyle("pie-size"),o=r.x,s=r.y,l=Math.min(t.width(),t.height())/2,u=0;this.usePaths()&&(o=0,s=0),"%"===a.units?l*=a.pfValue:void 0!==a.pfValue&&(l=a.pfValue/2);for(var c=1;c<=i.pieBackgroundN;c++){var d=t.pstyle("pie-"+c+"-background-size").value,h=t.pstyle("pie-"+c+"-background-color").value,p=t.pstyle("pie-"+c+"-background-opacity").value*n,f=d/100;f+u>1&&(f=1-u);var g=1.5*Math.PI+2*Math.PI*u,v=g+2*Math.PI*f;0===d||u>=1||u+f>1||(e.beginPath(),e.moveTo(o,s),e.arc(o,s,l,g,v),e.closePath(),this.colorFillStyle(e,h[0],h[1],h[2],p),e.fill(),u+=f)}};var s7={};s7.getPixelRatio=function(){var e=this.data.contexts[0];if(null!=this.forcedPixelRatio)return this.forcedPixelRatio;var t=this.cy.window(),n=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(t.devicePixelRatio||1)/n},s7.paintCache=function(e){for(var t,n=this.paintCaches=this.paintCaches||[],r=!0,i=0;i<n.length;i++)if((t=n[i]).context===e){r=!1;break}return r&&(t={context:e},n.push(t)),t},s7.createGradientStyleFor=function(e,t,n,r,i){var a,o=this.usePaths(),s=n.pstyle(t+"-gradient-stop-colors").value,l=n.pstyle(t+"-gradient-stop-positions").pfValue;if("radial-gradient"===r){if(n.isEdge()){var u=n.sourceEndpoint(),c=n.targetEndpoint(),d=n.midpoint(),h=tD(u,d),p=tD(c,d);a=e.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(h,p))}else{var f=o?{x:0,y:0}:n.position(),g=n.paddedWidth(),v=n.paddedHeight();a=e.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(g,v))}}else if(n.isEdge()){var y=n.sourceEndpoint(),b=n.targetEndpoint();a=e.createLinearGradient(y.x,y.y,b.x,b.y)}else{var x=o?{x:0,y:0}:n.position(),w=n.paddedWidth(),E=n.paddedHeight(),k=w/2,C=E/2;switch(n.pstyle("background-gradient-direction").value){case"to-bottom":a=e.createLinearGradient(x.x,x.y-C,x.x,x.y+C);break;case"to-top":a=e.createLinearGradient(x.x,x.y+C,x.x,x.y-C);break;case"to-left":a=e.createLinearGradient(x.x+k,x.y,x.x-k,x.y);break;case"to-right":a=e.createLinearGradient(x.x-k,x.y,x.x+k,x.y);break;case"to-bottom-right":case"to-right-bottom":a=e.createLinearGradient(x.x-k,x.y-C,x.x+k,x.y+C);break;case"to-top-right":case"to-right-top":a=e.createLinearGradient(x.x-k,x.y+C,x.x+k,x.y-C);break;case"to-bottom-left":case"to-left-bottom":a=e.createLinearGradient(x.x+k,x.y-C,x.x-k,x.y+C);break;case"to-top-left":case"to-left-top":a=e.createLinearGradient(x.x+k,x.y+C,x.x-k,x.y-C)}}if(!a)return null;for(var S=l.length===s.length,D=s.length,T=0;T<D;T++)a.addColorStop(S?l[T]:T/(D-1),"rgba("+s[T][0]+","+s[T][1]+","+s[T][2]+","+i+")");return a},s7.gradientFillStyle=function(e,t,n,r){var i=this.createGradientStyleFor(e,"background",t,n,r);if(!i)return null;e.fillStyle=i},s7.colorFillStyle=function(e,t,n,r,i){e.fillStyle="rgba("+t+","+n+","+r+","+i+")"},s7.eleFillStyle=function(e,t,n){var r=t.pstyle("background-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientFillStyle(e,t,r,n);else{var i=t.pstyle("background-color").value;this.colorFillStyle(e,i[0],i[1],i[2],n)}},s7.gradientStrokeStyle=function(e,t,n,r){var i=this.createGradientStyleFor(e,"line",t,n,r);if(!i)return null;e.strokeStyle=i},s7.colorStrokeStyle=function(e,t,n,r,i){e.strokeStyle="rgba("+t+","+n+","+r+","+i+")"},s7.eleStrokeStyle=function(e,t,n){var r=t.pstyle("line-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientStrokeStyle(e,t,r,n);else{var i=t.pstyle("line-color").value;this.colorStrokeStyle(e,i[0],i[1],i[2],n)}},s7.matchCanvasSize=function(e){var t,n=this,r=n.data,i=n.findContainerClientCoords(),a=i[2],o=i[3],s=n.getPixelRatio(),l=n.motionBlurPxRatio;(e===n.data.bufferCanvases[n.MOTIONBLUR_BUFFER_NODE]||e===n.data.bufferCanvases[n.MOTIONBLUR_BUFFER_DRAG])&&(s=l);var u=a*s,c=o*s;if(u!==n.canvasWidth||c!==n.canvasHeight){n.fontCaches=null;var d=r.canvasContainer;d.style.width=a+"px",d.style.height=o+"px";for(var h=0;h<n.CANVAS_LAYERS;h++)(t=r.canvases[h]).width=u,t.height=c,t.style.width=a+"px",t.style.height=o+"px";for(var h=0;h<n.BUFFER_COUNT;h++)(t=r.bufferCanvases[h]).width=u,t.height=c,t.style.width=a+"px",t.style.height=o+"px";n.textureMult=1,s<=1&&(t=r.bufferCanvases[n.TEXTURE_BUFFER],n.textureMult=2,t.width=u*n.textureMult,t.height=c*n.textureMult),n.canvasWidth=u,n.canvasHeight=c}},s7.renderTo=function(e,t,n,r){this.render({forcedContext:e,forcedZoom:t,forcedPan:n,drawAllLayers:!0,forcedPxRatio:r})},s7.render=function(e){var t=(e=e||e3()).forcedContext,n=e.drawAllLayers,r=e.drawOnlyNodeLayer,i=e.forcedZoom,a=e.forcedPan,o=this,s=void 0===e.forcedPxRatio?this.getPixelRatio():e.forcedPxRatio,l=o.cy,u=o.data,c=u.canvasNeedsRedraw,d=o.textureOnViewport&&!t&&(o.pinching||o.hoverData.dragging||o.swipePanning||o.data.wheelZooming),h=void 0!==e.motionBlur?e.motionBlur:o.motionBlur,p=o.motionBlurPxRatio,f=l.hasCompoundNodes(),g=o.hoverData.draggingEles,v=!!o.hoverData.selecting||!!o.touchData.selecting,y=h=h&&!t&&o.motionBlurEnabled&&!v;t||(o.prevPxRatio!==s&&(o.invalidateContainerClientCoordsCache(),o.matchCanvasSize(o.container),o.redrawHint("eles",!0),o.redrawHint("drag",!0)),o.prevPxRatio=s),!t&&o.motionBlurTimeout&&clearTimeout(o.motionBlurTimeout),h&&(null==o.mbFrames&&(o.mbFrames=0),o.mbFrames++,o.mbFrames<3&&(y=!1),o.mbFrames>o.minMbLowQualFrames&&(o.motionBlurPxRatio=o.mbPxRBlurry)),o.clearingMotionBlur&&(o.motionBlurPxRatio=1),o.textureDrawLastFrame&&!d&&(c[o.NODE]=!0,c[o.SELECT_BOX]=!0);var b=l.style(),x=l.zoom(),w=void 0!==i?i:x,E=l.pan(),k={x:E.x,y:E.y},C={zoom:x,pan:{x:E.x,y:E.y}},S=o.prevViewport;void 0===S||C.zoom!==S.zoom||C.pan.x!==S.pan.x||C.pan.y!==S.pan.y||g&&!f||(o.motionBlurPxRatio=1),a&&(k=a),w*=s,k.x*=s,k.y*=s;var D=o.getCachedZSortedEles();function T(e,t,n,r,i){var a=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",o.colorFillStyle(e,255,255,255,o.motionBlurTransparency),e.fillRect(t,n,r,i),e.globalCompositeOperation=a}function P(e,r){var s,l,c,d;o.clearingMotionBlur||e!==u.bufferContexts[o.MOTIONBLUR_BUFFER_NODE]&&e!==u.bufferContexts[o.MOTIONBLUR_BUFFER_DRAG]?(s=k,l=w,c=o.canvasWidth,d=o.canvasHeight):(s={x:E.x*p,y:E.y*p},l=x*p,c=o.canvasWidth*p,d=o.canvasHeight*p),e.setTransform(1,0,0,1,0,0),"motionBlur"===r?T(e,0,0,c,d):!t&&(void 0===r||r)&&e.clearRect(0,0,c,d),n||(e.translate(s.x,s.y),e.scale(l,l)),a&&e.translate(a.x,a.y),i&&e.scale(i,i)}if(d||(o.textureDrawLastFrame=!1),d){if(o.textureDrawLastFrame=!0,!o.textureCache){o.textureCache={},o.textureCache.bb=l.mutableElements().boundingBox(),o.textureCache.texture=o.data.bufferCanvases[o.TEXTURE_BUFFER];var _=o.data.bufferContexts[o.TEXTURE_BUFFER];_.setTransform(1,0,0,1,0,0),_.clearRect(0,0,o.canvasWidth*o.textureMult,o.canvasHeight*o.textureMult),o.render({forcedContext:_,drawOnlyNodeLayer:!0,forcedPxRatio:s*o.textureMult});var C=o.textureCache.viewport={zoom:l.zoom(),pan:l.pan(),width:o.canvasWidth,height:o.canvasHeight};C.mpan={x:(0-C.pan.x)/C.zoom,y:(0-C.pan.y)/C.zoom}}c[o.DRAG]=!1,c[o.NODE]=!1;var M=u.contexts[o.NODE],B=o.textureCache.texture,C=o.textureCache.viewport;M.setTransform(1,0,0,1,0,0),h?T(M,0,0,C.width,C.height):M.clearRect(0,0,C.width,C.height);var N=b.core("outside-texture-bg-color").value,A=b.core("outside-texture-bg-opacity").value;o.colorFillStyle(M,N[0],N[1],N[2],A),M.fillRect(0,0,C.width,C.height);var x=l.zoom();P(M,!1),M.clearRect(C.mpan.x,C.mpan.y,C.width/C.zoom/s,C.height/C.zoom/s),M.drawImage(B,C.mpan.x,C.mpan.y,C.width/C.zoom/s,C.height/C.zoom/s)}else o.textureOnViewport&&!t&&(o.textureCache=null);var I=l.extent(),O=o.pinching||o.hoverData.dragging||o.swipePanning||o.data.wheelZooming||o.hoverData.draggingEles||o.cy.animated(),R=o.hideEdgesOnViewport&&O,z=[];if(z[o.NODE]=!c[o.NODE]&&h&&!o.clearedForMotionBlur[o.NODE]||o.clearingMotionBlur,z[o.NODE]&&(o.clearedForMotionBlur[o.NODE]=!0),z[o.DRAG]=!c[o.DRAG]&&h&&!o.clearedForMotionBlur[o.DRAG]||o.clearingMotionBlur,z[o.DRAG]&&(o.clearedForMotionBlur[o.DRAG]=!0),c[o.NODE]||n||r||z[o.NODE]){var L=h&&!z[o.NODE]&&1!==p,M=t||(L?o.data.bufferContexts[o.MOTIONBLUR_BUFFER_NODE]:u.contexts[o.NODE]);P(M,h&&!L?"motionBlur":void 0),R?o.drawCachedNodes(M,D.nondrag,s,I):o.drawLayeredElements(M,D.nondrag,s,I),o.debug&&o.drawDebugPoints(M,D.nondrag),n||h||(c[o.NODE]=!1)}if(!r&&(c[o.DRAG]||n||z[o.DRAG])){var L=h&&!z[o.DRAG]&&1!==p,M=t||(L?o.data.bufferContexts[o.MOTIONBLUR_BUFFER_DRAG]:u.contexts[o.DRAG]);P(M,h&&!L?"motionBlur":void 0),R?o.drawCachedNodes(M,D.drag,s,I):o.drawCachedElements(M,D.drag,s,I),o.debug&&o.drawDebugPoints(M,D.drag),n||h||(c[o.DRAG]=!1)}if(o.showFps||!r&&c[o.SELECT_BOX]&&!n){var M=t||u.contexts[o.SELECT_BOX];if(P(M),1==o.selection[4]&&(o.hoverData.selecting||o.touchData.selecting)){var x=o.cy.zoom(),V=b.core("selection-box-border-width").value/x;M.lineWidth=V,M.fillStyle="rgba("+b.core("selection-box-color").value[0]+","+b.core("selection-box-color").value[1]+","+b.core("selection-box-color").value[2]+","+b.core("selection-box-opacity").value+")",M.fillRect(o.selection[0],o.selection[1],o.selection[2]-o.selection[0],o.selection[3]-o.selection[1]),V>0&&(M.strokeStyle="rgba("+b.core("selection-box-border-color").value[0]+","+b.core("selection-box-border-color").value[1]+","+b.core("selection-box-border-color").value[2]+","+b.core("selection-box-opacity").value+")",M.strokeRect(o.selection[0],o.selection[1],o.selection[2]-o.selection[0],o.selection[3]-o.selection[1]))}if(u.bgActivePosistion&&!o.hoverData.selecting){var x=o.cy.zoom(),F=u.bgActivePosistion;M.fillStyle="rgba("+b.core("active-bg-color").value[0]+","+b.core("active-bg-color").value[1]+","+b.core("active-bg-color").value[2]+","+b.core("active-bg-opacity").value+")",M.beginPath(),M.arc(F.x,F.y,b.core("active-bg-size").pfValue/x,0,2*Math.PI),M.fill()}var j=o.lastRedrawTime;if(o.showFps&&j){var q=Math.round(1e3/(j=Math.round(j)));M.setTransform(1,0,0,1,0,0),M.fillStyle="rgba(255, 0, 0, 0.75)",M.strokeStyle="rgba(255, 0, 0, 0.75)",M.lineWidth=1,M.fillText("1 frame = "+j+" ms = "+q+" fps",0,20),M.strokeRect(0,30,250,20),M.fillRect(0,30,250*Math.min(q/60,1),20)}n||(c[o.SELECT_BOX]=!1)}if(h&&1!==p){var X=u.contexts[o.NODE],Y=o.data.bufferCanvases[o.MOTIONBLUR_BUFFER_NODE],W=u.contexts[o.DRAG],H=o.data.bufferCanvases[o.MOTIONBLUR_BUFFER_DRAG],G=function(e,t,n){e.setTransform(1,0,0,1,0,0),n||!y?e.clearRect(0,0,o.canvasWidth,o.canvasHeight):T(e,0,0,o.canvasWidth,o.canvasHeight),e.drawImage(t,0,0,o.canvasWidth*p,o.canvasHeight*p,0,0,o.canvasWidth,o.canvasHeight)};(c[o.NODE]||z[o.NODE])&&(G(X,Y,z[o.NODE]),c[o.NODE]=!1),(c[o.DRAG]||z[o.DRAG])&&(G(W,H,z[o.DRAG]),c[o.DRAG]=!1)}o.prevViewport=C,o.clearingMotionBlur&&(o.clearingMotionBlur=!1,o.motionBlurCleared=!0,o.motionBlur=!0),h&&(o.motionBlurTimeout=setTimeout(function(){o.motionBlurTimeout=null,o.clearedForMotionBlur[o.NODE]=!1,o.clearedForMotionBlur[o.DRAG]=!1,o.motionBlur=!1,o.clearingMotionBlur=!d,o.mbFrames=0,c[o.NODE]=!0,c[o.DRAG]=!0,o.redraw()},100)),t||l.emit("render")};var le={};le.drawPolygonPath=function(e,t,n,r,i,a){var o=r/2,s=i/2;e.beginPath&&e.beginPath(),e.moveTo(t+o*a[0],n+s*a[1]);for(var l=1;l<a.length/2;l++)e.lineTo(t+o*a[2*l],n+s*a[2*l+1]);e.closePath()},le.drawRoundPolygonPath=function(e,t,n,r,i,a,o){o.forEach(function(t){return sd(e,t)}),e.closePath()},le.drawRoundRectanglePath=function(e,t,n,r,i,a){var o=r/2,s=i/2,l="auto"===a?nt(r,i):Math.min(a,s,o);e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.arcTo(t+o,n-s,t+o,n,l),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.arcTo(t-o,n-s,t,n-s,l),e.lineTo(t,n-s),e.closePath()},le.drawBottomRoundRectanglePath=function(e,t,n,r,i,a){var o=r/2,s=i/2,l="auto"===a?nt(r,i):a;e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.lineTo(t+o,n-s),e.lineTo(t+o,n),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.lineTo(t-o,n-s),e.lineTo(t,n-s),e.closePath()},le.drawCutRectanglePath=function(e,t,n,r,i,a,o){var s=r/2,l=i/2,u="auto"===o?nr():o;e.beginPath&&e.beginPath(),e.moveTo(t-s+u,n-l),e.lineTo(t+s-u,n-l),e.lineTo(t+s,n-l+u),e.lineTo(t+s,n+l-u),e.lineTo(t+s-u,n+l),e.lineTo(t-s+u,n+l),e.lineTo(t-s,n+l-u),e.lineTo(t-s,n-l+u),e.closePath()},le.drawBarrelPath=function(e,t,n,r,i){var a=r/2,o=i/2,s=t-a,l=t+a,u=n-o,c=n+o,d=ni(r,i),h=d.widthOffset,p=d.heightOffset,f=d.ctrlPtOffsetPct*h;e.beginPath&&e.beginPath(),e.moveTo(s,u+p),e.lineTo(s,c-p),e.quadraticCurveTo(s+f,c,s+h,c),e.lineTo(l-h,c),e.quadraticCurveTo(l-f,c,l,c-p),e.lineTo(l,u+p),e.quadraticCurveTo(l-f,u,l-h,u),e.lineTo(s+h,u),e.quadraticCurveTo(s+f,u,s,u+p),e.closePath()};for(var lt={},ln={},lr=Math.PI/40,li=0*Math.PI;li<2*Math.PI;li+=lr)lt[li]=Math.sin(li),ln[li]=Math.cos(li);le.drawEllipsePath=function(e,t,n,r,i){if(e.beginPath&&e.beginPath(),e.ellipse)e.ellipse(t,n,r/2,i/2,0,0,2*Math.PI);else for(var a,o,s=r/2,l=i/2,u=0*Math.PI;u<2*Math.PI;u+=lr)a=t-s*lt[u]*0+s*ln[u]*1,o=n+l*ln[u]*0+l*lt[u]*1,0===u?e.moveTo(a,o):e.lineTo(a,o);e.closePath()};var la={};function lo(e){var t=e.indexOf(",");return e.substr(t+1)}function ls(e,t,n){var r=function(){return t.toDataURL(n,e.quality)};switch(e.output){case"blob-promise":return new rl(function(r,i){try{t.toBlob(function(e){null!=e?r(e):i(Error("`canvas.toBlob()` sent a null value in its callback"))},n,e.quality)}catch(e){i(e)}});case"blob":return function(e,t){for(var n=atob(e),r=new ArrayBuffer(n.length),i=new Uint8Array(r),a=0;a<n.length;a++)i[a]=n.charCodeAt(a);return new Blob([r],{type:t})}(lo(r()),n);case"base64":return lo(r());default:return r()}}la.createBuffer=function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,[n,n.getContext("2d")]},la.bufferCanvasImage=function(e){var t=this.cy,n=t.mutableElements().boundingBox(),r=this.findContainerClientCoords(),i=e.full?Math.ceil(n.w):r[2],a=e.full?Math.ceil(n.h):r[3],o=M(e.maxWidth)||M(e.maxHeight),s=this.getPixelRatio(),l=1;if(void 0!==e.scale)i*=e.scale,a*=e.scale,l=e.scale;else if(o){var u=1/0,c=1/0;M(e.maxWidth)&&(u=l*e.maxWidth/i),M(e.maxHeight)&&(c=l*e.maxHeight/a),i*=l=Math.min(u,c),a*=l}o||(i*=s,a*=s,l*=s);var d=document.createElement("canvas");d.width=i,d.height=a,d.style.width=i+"px",d.style.height=a+"px";var h=d.getContext("2d");if(i>0&&a>0){h.clearRect(0,0,i,a),h.globalCompositeOperation="source-over";var p=this.getCachedZSortedEles();if(e.full)h.translate(-n.x1*l,-n.y1*l),h.scale(l,l),this.drawElements(h,p),h.scale(1/l,1/l),h.translate(n.x1*l,n.y1*l);else{var f=t.pan(),g={x:f.x*l,y:f.y*l};l*=t.zoom(),h.translate(g.x,g.y),h.scale(l,l),this.drawElements(h,p),h.scale(1/l,1/l),h.translate(-g.x,-g.y)}e.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=e.bg,h.rect(0,0,i,a),h.fill())}return d},la.png=function(e){return ls(e,this.bufferCanvasImage(e),"image/png")},la.jpg=function(e){return ls(e,this.bufferCanvasImage(e),"image/jpeg")};var ll={};ll.nodeShapeImpl=function(e,t,n,r,i,a,o,s){switch(e){case"ellipse":return this.drawEllipsePath(t,n,r,i,a);case"polygon":return this.drawPolygonPath(t,n,r,i,a,o);case"round-polygon":return this.drawRoundPolygonPath(t,n,r,i,a,o,s);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(t,n,r,i,a,s);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(t,n,r,i,a,o,s);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(t,n,r,i,a,s);case"barrel":return this.drawBarrelPath(t,n,r,i,a)}};var lu=lc.prototype;function lc(e){var t=this,n=t.cy.window().document;t.data={canvases:Array(lu.CANVAS_LAYERS),contexts:Array(lu.CANVAS_LAYERS),canvasNeedsRedraw:Array(lu.CANVAS_LAYERS),bufferCanvases:Array(lu.BUFFER_COUNT),bufferContexts:Array(lu.CANVAS_LAYERS)};var r="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";t.data.canvasContainer=n.createElement("div");var a=t.data.canvasContainer.style;t.data.canvasContainer.style[r]=i,a.position="relative",a.zIndex="0",a.overflow="hidden";var o=e.cy.container();o.appendChild(t.data.canvasContainer),o.style[r]=i;var s={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};x&&x.userAgent.match(/msie|trident|edge/i)&&(s["-ms-touch-action"]="none",s["touch-action"]="none");for(var l=0;l<lu.CANVAS_LAYERS;l++){var u=t.data.canvases[l]=n.createElement("canvas");t.data.contexts[l]=u.getContext("2d"),Object.keys(s).forEach(function(e){u.style[e]=s[e]}),u.style.position="absolute",u.setAttribute("data-id","layer"+l),u.style.zIndex=String(lu.CANVAS_LAYERS-l),t.data.canvasContainer.appendChild(u),t.data.canvasNeedsRedraw[l]=!1}t.data.topCanvas=t.data.canvases[0],t.data.canvases[lu.NODE].setAttribute("data-id","layer"+lu.NODE+"-node"),t.data.canvases[lu.SELECT_BOX].setAttribute("data-id","layer"+lu.SELECT_BOX+"-selectbox"),t.data.canvases[lu.DRAG].setAttribute("data-id","layer"+lu.DRAG+"-drag");for(var l=0;l<lu.BUFFER_COUNT;l++)t.data.bufferCanvases[l]=n.createElement("canvas"),t.data.bufferContexts[l]=t.data.bufferCanvases[l].getContext("2d"),t.data.bufferCanvases[l].style.position="absolute",t.data.bufferCanvases[l].setAttribute("data-id","buffer"+l),t.data.bufferCanvases[l].style.zIndex=String(-l-1),t.data.bufferCanvases[l].style.visibility="hidden";t.pathsEnabled=!0;var c=tA(),d=function(e){return{x:-e.w/2,y:-e.h/2}},h=function(e){return e.boundingBox(),e[0]._private.bodyBounds},p=function(e){return e.boundingBox(),e[0]._private.labelBounds.main||c},f=function(e){return e.boundingBox(),e[0]._private.labelBounds.source||c},g=function(e){return e.boundingBox(),e[0]._private.labelBounds.target||c},v=function(e,t){return t},y=function(e,t,n){var r=e?e+"-":"";return{x:t.x+n.pstyle(r+"text-margin-x").pfValue,y:t.y+n.pstyle(r+"text-margin-y").pfValue}},b=function(e,t,n){var r=e[0]._private.rscratch;return{x:r[t],y:r[n]}},w=t.data.eleTxrCache=new sL(t,{getKey:function(e){return e[0]._private.nodeKey},doesEleInvalidateKey:function(e){var t=e[0]._private;return t.oldBackgroundTimestamp!==t.backgroundTimestamp},drawElement:function(e,n,r,i,a){return t.drawElement(e,n,r,!1,!1,a)},getBoundingBox:h,getRotationPoint:function(e){var t;return{x:((t=h(e)).x1+t.x2)/2,y:(t.y1+t.y2)/2}},getRotationOffset:function(e){return d(h(e))},allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),E=t.data.lblTxrCache=new sL(t,{getKey:function(e){return e[0]._private.labelStyleKey},drawElement:function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"main",a)},getBoundingBox:p,getRotationPoint:function(e){return y("",b(e,"labelX","labelY"),e)},getRotationOffset:function(e){var t=p(e),n=d(p(e));if(e.isNode()){switch(e.pstyle("text-halign").value){case"left":n.x=-t.w-(t.leftPad||0);break;case"right":n.x=-(t.rightPad||0)}switch(e.pstyle("text-valign").value){case"top":n.y=-t.h-(t.topPad||0);break;case"bottom":n.y=-(t.botPad||0)}}return n},isVisible:v}),k=t.data.slbTxrCache=new sL(t,{getKey:function(e){return e[0]._private.sourceLabelStyleKey},drawElement:function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"source",a)},getBoundingBox:f,getRotationPoint:function(e){return y("source",b(e,"sourceLabelX","sourceLabelY"),e)},getRotationOffset:function(e){return d(f(e))},isVisible:v}),C=t.data.tlbTxrCache=new sL(t,{getKey:function(e){return e[0]._private.targetLabelStyleKey},drawElement:function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"target",a)},getBoundingBox:g,getRotationPoint:function(e){return y("target",b(e,"targetLabelX","targetLabelY"),e)},getRotationOffset:function(e){return d(g(e))},isVisible:v}),S=t.data.lyrTxrCache=new sF(t);t.onUpdateEleCalcs(function(e,t){w.invalidateElements(t),E.invalidateElements(t),k.invalidateElements(t),C.invalidateElements(t),S.invalidateElements(t);for(var n=0;n<t.length;n++){var r=t[n]._private;r.oldBackgroundTimestamp=r.backgroundTimestamp}});var D=function(e){for(var t=0;t<e.length;t++)S.enqueueElementRefinement(e[t].ele)};w.onDequeue(D),E.onDequeue(D),k.onDequeue(D),C.onDequeue(D)}lu.CANVAS_LAYERS=3,lu.SELECT_BOX=0,lu.DRAG=1,lu.NODE=2,lu.BUFFER_COUNT=3,lu.TEXTURE_BUFFER=0,lu.MOTIONBLUR_BUFFER_NODE=1,lu.MOTIONBLUR_BUFFER_DRAG=2,lu.redrawHint=function(e,t){var n=this;switch(e){case"eles":n.data.canvasNeedsRedraw[lu.NODE]=t;break;case"drag":n.data.canvasNeedsRedraw[lu.DRAG]=t;break;case"select":n.data.canvasNeedsRedraw[lu.SELECT_BOX]=t}};var ld="undefined"!=typeof Path2D;lu.path2dEnabled=function(e){if(void 0===e)return this.pathsEnabled;this.pathsEnabled=!!e},lu.usePaths=function(){return ld&&this.pathsEnabled},lu.setImgSmoothing=function(e,t){null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled=t:(e.webkitImageSmoothingEnabled=t,e.mozImageSmoothingEnabled=t,e.msImageSmoothingEnabled=t)},lu.getImgSmoothing=function(e){return null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled:e.webkitImageSmoothingEnabled||e.mozImageSmoothingEnabled||e.msImageSmoothingEnabled},lu.makeOffscreenCanvas=function(e,t){var n;return("undefined"==typeof OffscreenCanvas?"undefined":r(OffscreenCanvas))!=="undefined"?n=new OffscreenCanvas(e,t):((n=this.cy.window().document.createElement("canvas")).width=e,n.height=t),n},[sX,sK,s2,s3,s4,s6,s7,le,la,ll].forEach(function(e){Z(lu,e)});var lh=[{name:"null",impl:oz},{name:"base",impl:sB},{name:"canvas",impl:lc}],lp={},lf={};function lg(e,t,n){var r=n,i=function(n){e0("Can not register `"+t+"` for `"+e+"` since `"+n+"` already exists in the prototype and can not be overridden")};if("core"===e){if(a9.prototype[t])return i(t);a9.prototype[t]=n}else if("collection"===e){if(a_.prototype[t])return i(t);a_.prototype[t]=n}else if("layout"===e){for(var a=function(e){this.options=e,n.call(this,e),_(this._private)||(this._private={}),this._private.cy=e.cy,this._private.listeners=[],this.createEmitter()},o=a.prototype=Object.create(n.prototype),s=[],l=0;l<s.length;l++){var u=s[l];o[u]=o[u]||function(){return this}}o.start&&!o.run?o.run=function(){return this.start(),this}:!o.start&&o.run&&(o.start=function(){return this.run(),this});var c=n.prototype.stop;o.stop=function(){var e=this.options;if(e&&e.animate){var t=this.animations;if(t)for(var n=0;n<t.length;n++)t[n].stop()}return c?c.call(this):this.emit("layoutstop"),this},o.destroy||(o.destroy=function(){return this}),o.cy=function(){return this._private.cy};var d=function(e){return e._private.cy},h={addEventFields:function(e,t){t.layout=e,t.cy=d(e),t.target=e},bubble:function(){return!0},parent:function(e){return d(e)}};Z(o,{createEmitter:function(){return this._private.emitter=new i4(h,this),this},emitter:function(){return this._private.emitter},on:function(e,t){return this.emitter().on(e,t),this},one:function(e,t){return this.emitter().one(e,t),this},once:function(e,t){return this.emitter().one(e,t),this},removeListener:function(e,t){return this.emitter().removeListener(e,t),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(e,t){return this.emitter().emit(e,t),this}}),r6.eventAliasesOn(o),r=a}else if("renderer"===e&&"null"!==t&&"base"!==t){var p=lv("renderer","base"),f=p.prototype,g=n.prototype,v=function(){p.apply(this,arguments),n.apply(this,arguments)},y=v.prototype;for(var b in f){var x=f[b];if(null!=g[b])return i(b);y[b]=x}for(var w in g)y[w]=g[w];f.clientFunctions.forEach(function(e){y[e]=y[e]||function(){eQ("Renderer does not implement `renderer."+e+"()` on its prototype")}}),r=v}else if("__proto__"===e||"constructor"===e||"prototype"===e)return eQ(e+" is an illegal type to be registered, possibly lead to prototype pollutions");return et({map:lp,keys:[e,t],value:r})}function lv(e,t){return en({map:lp,keys:[e,t]})}function ly(e,t,n,r,i){return et({map:lf,keys:[e,t,n,r],value:i})}function lm(e,t,n,r){return en({map:lf,keys:[e,t,n,r]})}var lb=function(){return 2==arguments.length?lv.apply(null,arguments):3==arguments.length?lg.apply(null,arguments):4==arguments.length?lm.apply(null,arguments):5==arguments.length?ly.apply(null,arguments):void eQ("Invalid extension access syntax")};a9.prototype.extension=lb,[{type:"layout",extensions:oR},{type:"renderer",extensions:lh}].forEach(function(e){e.extensions.forEach(function(t){lg(e.type,t.name,t.impl)})});var lx=function e(){if(!(this instanceof e))return new e;this.length=0},lw=lx.prototype;lw.instanceString=function(){return"stylesheet"},lw.selector=function(e){return this[this.length++]={selector:e,properties:[]},this},lw.css=function(e,t){var n=this.length-1;if(D(e))this[n].properties.push({name:e,value:t});else if(_(e))for(var r=Object.keys(e),i=0;i<r.length;i++){var a=r[i],o=e[a];if(null!=o){var s=a2.properties[a]||a2.properties[j(a)];if(null!=s){var l=s.name;this[n].properties.push({name:l,value:o})}}}return this},lw.style=lw.css,lw.generateStyle=function(e){var t=new a2(e);return this.appendToStyle(t)},lw.appendToStyle=function(e){for(var t=0;t<this.length;t++){var n=this[t],r=n.selector,i=n.properties;e.selector(r);for(var a=0;a<i.length;a++){var o=i[a];e.css(o.name,o.value)}}return e};var lE=function(e){return(void 0===e&&(e={}),_(e))?new a9(e):D(e)?lb.apply(lb,arguments):void 0};lE.use=function(e){var t=Array.prototype.slice.call(arguments,1);return t.unshift(lE),e.apply(null,t),this},lE.warnings=function(e){return eJ(e)},lE.version="3.30.4",lE.stylesheet=lE.Stylesheet=lx}}]);