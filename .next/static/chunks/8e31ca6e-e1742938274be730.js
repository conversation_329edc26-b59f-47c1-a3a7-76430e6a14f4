"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[805],{68839:function(t,e,a){a.d(e,{C1:function(){return d},Lf:function(){return es},XO:function(){return b},Yn:function(){return eh},ZH:function(){return P},aH:function(){return ed},dW:function(){return ei},gU:function(){return eo},jr:function(){return g},us:function(){return L}});var l=a(52720),r=a(54703),i=a(7749),n=a(68414),s=a(58222),h=a(13406),o=a(74247),d=(0,s.eW)(async(t,e,a)=>{let l;let r=e.useHtmlLabels||(0,s.ku)(s.nV()?.htmlLabels),o=t.insert("g").attr("class",a||"node default").attr("id",e.domId||e.id),d=o.insert("g").attr("class","label").attr("style",(0,n.R7)(e.labelStyle));l=void 0===e.label?"":"string"==typeof e.label?e.label:e.label[0];let c=await (0,i.rw)(d,(0,s.oO)((0,n.SH)(l),(0,s.nV)()),{useHtmlLabels:r,width:e.width||s.nV().flowchart?.wrappingWidth,cssClasses:"markdown-node-label",style:e.labelStyle,addSvgBackground:!!e.icon||!!e.img}),g=c.getBBox(),y=(e?.padding??0)/2;if(r){let t=c.children[0],e=(0,h.Ys)(c),a=t.getElementsByTagName("img");if(a){let t=""===l.replace(/<img[^>]*>/g,"").trim();await Promise.all([...a].map(e=>new Promise(a=>{function l(){if(e.style.display="flex",e.style.flexDirection="column",t){let t=(0,s.nV)().fontSize?(0,s.nV)().fontSize:window.getComputedStyle(document.body).fontSize,[a=s.vZ.fontSize]=(0,n.VG)(t),l=5*a+"px";e.style.minWidth=l,e.style.maxWidth=l}else e.style.width="100%";a(e)}(0,s.eW)(l,"setupImage"),setTimeout(()=>{e.complete&&l()}),e.addEventListener("error",l),e.addEventListener("load",l)})))}g=t.getBoundingClientRect(),e.attr("width",g.width),e.attr("height",g.height)}return r?d.attr("transform","translate("+-g.width/2+", "+-g.height/2+")"):d.attr("transform","translate(0, "+-g.height/2+")"),e.centerLabel&&d.attr("transform","translate("+-g.width/2+", "+-g.height/2+")"),d.insert("rect",":first-child"),{shapeSvg:o,bbox:g,halfPadding:y,label:d}},"labelHelper"),c=(0,s.eW)(async(t,e,a)=>{let l=a.useHtmlLabels||(0,s.ku)(s.nV()?.flowchart?.htmlLabels),r=t.insert("g").attr("class","label").attr("style",a.labelStyle||""),o=await (0,i.rw)(r,(0,s.oO)((0,n.SH)(e),(0,s.nV)()),{useHtmlLabels:l,width:a.width||s.nV()?.flowchart?.wrappingWidth,style:a.labelStyle,addSvgBackground:!!a.icon||!!a.img}),d=o.getBBox(),c=a.padding/2;if((0,s.ku)(s.nV()?.flowchart?.htmlLabels)){let t=o.children[0],e=(0,h.Ys)(o);d=t.getBoundingClientRect(),e.attr("width",d.width),e.attr("height",d.height)}return l?r.attr("transform","translate("+-d.width/2+", "+-d.height/2+")"):r.attr("transform","translate(0, "+-d.height/2+")"),a.centerLabel&&r.attr("transform","translate("+-d.width/2+", "+-d.height/2+")"),r.insert("rect",":first-child"),{shapeSvg:t,bbox:d,halfPadding:c,label:r}},"insertLabel"),g=(0,s.eW)((t,e)=>{let a=e.node().getBBox();t.width=a.width,t.height=a.height},"updateNodeBounds"),y=(0,s.eW)((t,e)=>("handDrawn"===t.look?"rough-node":"node")+" "+t.cssClasses+" "+(e||""),"getNodeClasses");function f(t){let e=t.map((t,e)=>`${0===e?"M":"L"}${t.x},${t.y}`);return e.push("Z"),e.join(" ")}function p(t,e,a,l,r,i){let n=[],s=a-t,h=2*Math.PI/(s/i),o=e+(l-e)/2;for(let e=0;e<=50;e++){let a=e/50,l=t+a*s,i=o+r*Math.sin(h*(l-t));n.push({x:l,y:i})}return n}function u(t,e,a,l,r,i){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:-i,y:-o})}return n}(0,s.eW)(f,"createPathFromPoints"),(0,s.eW)(p,"generateFullSineWavePoints"),(0,s.eW)(u,"generateCirclePoints");var m=(0,s.eW)((t,e)=>{var a,l,r=t.x,i=t.y,n=e.x-r,s=e.y-i,h=t.width/2,o=t.height/2;return Math.abs(s)*h>Math.abs(n)*o?(s<0&&(o=-o),a=0===s?0:o*n/s,l=o):(n<0&&(h=-h),a=h,l=0===n?0:h*s/n),{x:r+a,y:i+l}},"intersectRect");function x(t,e){e&&t.attr("style",e)}async function w(t){let e=(0,h.Ys)(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),a=e.append("xhtml:div"),l=t.label;t.label&&(0,s.l0)(t.label)&&(l=await (0,s.uT)(t.label.replace(s.SY.lineBreakRegex,"\n"),(0,s.nV)()));let r=t.isNode?"nodeLabel":"edgeLabel";return a.html('<span class="'+r+'" '+(t.labelStyle?'style="'+t.labelStyle+'"':"")+">"+l+"</span>"),x(a,t.labelStyle),a.style("display","inline-block"),a.style("padding-right","1px"),a.style("white-space","nowrap"),a.attr("xmlns","http://www.w3.org/1999/xhtml"),e.node()}(0,s.eW)(x,"applyStyle"),(0,s.eW)(w,"addHtmlLabel");var b=(0,s.eW)(async(t,e,a,l)=>{let r=t||"";if("object"==typeof r&&(r=r[0]),(0,s.ku)((0,s.nV)().flowchart.htmlLabels)){r=r.replace(/\\n|\n/g,"<br />"),s.cM.info("vertexText"+r);let t={isNode:l,label:(0,n.SH)(r).replace(/fa[blrs]?:fa-[\w-]+/g,t=>`<i class='${t.replace(":"," ")}'></i>`),labelStyle:e?e.replace("fill:","color:"):e};return await w(t)}{let t=document.createElementNS("http://www.w3.org/2000/svg","text");t.setAttribute("style",e.replace("color:","fill:"));for(let e of"string"==typeof r?r.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(r)?r:[]){let l=document.createElementNS("http://www.w3.org/2000/svg","tspan");l.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),l.setAttribute("dy","1em"),l.setAttribute("x","0"),a?l.setAttribute("class","title-row"):l.setAttribute("class","row"),l.textContent=e.trim(),t.appendChild(l)}return t}},"createLabel"),$=(0,s.eW)((t,e,a,l,r)=>["M",t+r,e,"H",t+a-r,"A",r,r,0,0,1,t+a,e+r,"V",e+l-r,"A",r,r,0,0,1,t+a-r,e+l,"H",t+r,"A",r,r,0,0,1,t,e+l-r,"V",e+r,"A",r,r,0,0,1,t+r,e,"Z"].join(" "),"createRoundedRectPathD"),k=(0,s.eW)(t=>{let{handDrawnSeed:e}=(0,s.nV)();return{fill:t,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:t,seed:e}},"solidStateFill"),M=(0,s.eW)(t=>{let e=S([...t.cssCompiledStyles||[],...t.cssStyles||[]]);return{stylesMap:e,stylesArray:[...e]}},"compileStyles"),S=(0,s.eW)(t=>{let e=new Map;return t.forEach(t=>{let[a,l]=t.split(":");e.set(a.trim(),l?.trim())}),e},"styles2Map"),D=(0,s.eW)(t=>{let{stylesArray:e}=M(t),a=[],l=[],r=[],i=[];return e.forEach(t=>{let e=t[0];"color"===e||"font-size"===e||"font-family"===e||"font-weight"===e||"font-style"===e||"text-decoration"===e||"text-align"===e||"text-transform"===e||"line-height"===e||"letter-spacing"===e||"word-spacing"===e||"text-shadow"===e||"text-overflow"===e||"white-space"===e||"word-wrap"===e||"word-break"===e||"overflow-wrap"===e||"hyphens"===e?a.push(t.join(":")+" !important"):(l.push(t.join(":")+" !important"),e.includes("stroke")&&r.push(t.join(":")+" !important"),"fill"===e&&i.push(t.join(":")+" !important"))}),{labelStyles:a.join(";"),nodeStyles:l.join(";"),stylesArray:e,borderStyles:r,backgroundStyles:i}},"styles2String"),W=(0,s.eW)((t,e)=>{let{themeVariables:a,handDrawnSeed:l}=(0,s.nV)(),{nodeBorder:r,mainBkg:i}=a,{stylesMap:n}=M(t),h=Object.assign({roughness:.7,fill:n.get("fill")||i,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:n.get("stroke")||r,seed:l,strokeWidth:n.get("stroke-width")?.replace("px","")||1.3,fillLineDash:[0,0]},e);return h},"userNodeOverrides"),v=(0,s.eW)(async(t,e)=>{let a;s.cM.info("Creating subgraph rect for ",e.id,e);let l=(0,s.nV)(),{themeVariables:n,handDrawnSeed:d}=l,{clusterBkg:c,clusterBorder:g}=n,{labelStyles:y,nodeStyles:f,borderStyles:p,backgroundStyles:u}=D(e),x=t.insert("g").attr("class","cluster "+e.cssClasses).attr("id",e.id).attr("data-look",e.look),w=(0,s.ku)(l.flowchart.htmlLabels),b=x.insert("g").attr("class","cluster-label "),k=await (0,i.rw)(b,e.label,{style:e.labelStyle,useHtmlLabels:w,isNode:!0}),M=k.getBBox();if((0,s.ku)(l.flowchart.htmlLabels)){let t=k.children[0],e=(0,h.Ys)(k);M=t.getBoundingClientRect(),e.attr("width",M.width),e.attr("height",M.height)}let S=e.width<=M.width+e.padding?M.width+e.padding:e.width;e.width<=M.width+e.padding?e.diff=(S-e.width)/2-e.padding:e.diff=-e.padding;let v=e.height,N=e.x-S/2,C=e.y-v/2;if(s.cM.trace("Data ",e,JSON.stringify(e)),"handDrawn"===e.look){let t=o.Z.svg(x),l=W(e,{roughness:.7,fill:c,stroke:g,fillWeight:3,seed:d}),r=t.path($(N,C,S,v,0),l);(a=x.insert(()=>(s.cM.debug("Rough node insert CXC",r),r),":first-child")).select("path:nth-child(2)").attr("style",p.join(";")),a.select("path").attr("style",u.join(";").replace("fill","stroke"))}else(a=x.insert("rect",":first-child")).attr("style",f).attr("rx",e.rx).attr("ry",e.ry).attr("x",N).attr("y",C).attr("width",S).attr("height",v);let{subGraphTitleTopMargin:B}=(0,r.L)(l);if(b.attr("transform",`translate(${e.x-M.width/2}, ${e.y-e.height/2+B})`),y){let t=b.select("span");t&&t.attr("style",y)}let A=a.node().getBBox();return e.offsetX=0,e.width=A.width,e.height=A.height,e.offsetY=M.height-e.padding/2,e.intersect=function(t){return m(e,t)},{cluster:x,labelBBox:M}},"rect"),N=(0,s.eW)((t,e)=>{let a=t.insert("g").attr("class","note-cluster").attr("id",e.id),l=a.insert("rect",":first-child"),r=0*e.padding,i=r/2;l.attr("rx",e.rx).attr("ry",e.ry).attr("x",e.x-e.width/2-i).attr("y",e.y-e.height/2-i).attr("width",e.width+r).attr("height",e.height+r).attr("fill","none");let n=l.node().getBBox();return e.width=n.width,e.height=n.height,e.intersect=function(t){return m(e,t)},{cluster:a,labelBBox:{width:0,height:0}}},"noteGroup"),C=(0,s.eW)(async(t,e)=>{let a;let l=(0,s.nV)(),{themeVariables:r,handDrawnSeed:i}=l,{altBackground:n,compositeBackground:d,compositeTitleBackground:c,nodeBorder:g}=r,y=t.insert("g").attr("class",e.cssClasses).attr("id",e.id).attr("data-id",e.id).attr("data-look",e.look),f=y.insert("g",":first-child"),p=y.insert("g").attr("class","cluster-label"),u=y.append("rect"),x=p.node().appendChild(await b(e.label,e.labelStyle,void 0,!0)),w=x.getBBox();if((0,s.ku)(l.flowchart.htmlLabels)){let t=x.children[0],e=(0,h.Ys)(x);w=t.getBoundingClientRect(),e.attr("width",w.width),e.attr("height",w.height)}let k=0*e.padding,M=(e.width<=w.width+e.padding?w.width+e.padding:e.width)+k;e.width<=w.width+e.padding?e.diff=(M-e.width)/2-e.padding:e.diff=-e.padding;let S=e.height+k,D=e.height+k-w.height-6,W=e.x-M/2,v=e.y-S/2;e.width=M;let N=e.y-e.height/2-k/2+w.height+2;if("handDrawn"===e.look){let t=e.cssClasses.includes("statediagram-cluster-alt"),l=o.Z.svg(y),r=e.rx||e.ry?l.path($(W,v,M,S,10),{roughness:.7,fill:c,fillStyle:"solid",stroke:g,seed:i}):l.rectangle(W,v,M,S,{seed:i});a=y.insert(()=>r,":first-child");let s=l.rectangle(W,N,M,D,{fill:t?n:d,fillStyle:t?"hachure":"solid",stroke:g,seed:i});a=y.insert(()=>r,":first-child"),u=y.insert(()=>s)}else(a=f.insert("rect",":first-child")).attr("class","outer").attr("x",W).attr("y",v).attr("width",M).attr("height",S).attr("data-look",e.look),u.attr("class","inner").attr("x",W).attr("y",N).attr("width",M).attr("height",D);p.attr("transform",`translate(${e.x-w.width/2}, ${v+1-((0,s.ku)(l.flowchart.htmlLabels)?0:3)})`);let C=a.node().getBBox();return e.height=C.height,e.offsetX=0,e.offsetY=w.height-e.padding/2,e.labelBBox=w,e.intersect=function(t){return m(e,t)},{cluster:y,labelBBox:w}},"roundedWithTitle"),B=(0,s.eW)(async(t,e)=>{let a;s.cM.info("Creating subgraph rect for ",e.id,e);let l=(0,s.nV)(),{themeVariables:n,handDrawnSeed:d}=l,{clusterBkg:c,clusterBorder:g}=n,{labelStyles:y,nodeStyles:f,borderStyles:p,backgroundStyles:u}=D(e),x=t.insert("g").attr("class","cluster "+e.cssClasses).attr("id",e.id).attr("data-look",e.look),w=(0,s.ku)(l.flowchart.htmlLabels),b=x.insert("g").attr("class","cluster-label "),k=await (0,i.rw)(b,e.label,{style:e.labelStyle,useHtmlLabels:w,isNode:!0,width:e.width}),M=k.getBBox();if((0,s.ku)(l.flowchart.htmlLabels)){let t=k.children[0],e=(0,h.Ys)(k);M=t.getBoundingClientRect(),e.attr("width",M.width),e.attr("height",M.height)}let S=e.width<=M.width+e.padding?M.width+e.padding:e.width;e.width<=M.width+e.padding?e.diff=(S-e.width)/2-e.padding:e.diff=-e.padding;let v=e.height,N=e.x-S/2,C=e.y-v/2;if(s.cM.trace("Data ",e,JSON.stringify(e)),"handDrawn"===e.look){let t=o.Z.svg(x),l=W(e,{roughness:.7,fill:c,stroke:g,fillWeight:4,seed:d}),r=t.path($(N,C,S,v,e.rx),l);(a=x.insert(()=>(s.cM.debug("Rough node insert CXC",r),r),":first-child")).select("path:nth-child(2)").attr("style",p.join(";")),a.select("path").attr("style",u.join(";").replace("fill","stroke"))}else(a=x.insert("rect",":first-child")).attr("style",f).attr("rx",e.rx).attr("ry",e.ry).attr("x",N).attr("y",C).attr("width",S).attr("height",v);let{subGraphTitleTopMargin:B}=(0,r.L)(l);if(b.attr("transform",`translate(${e.x-M.width/2}, ${e.y-e.height/2+B})`),y){let t=b.select("span");t&&t.attr("style",y)}let A=a.node().getBBox();return e.offsetX=0,e.width=A.width,e.height=A.height,e.offsetY=M.height-e.padding/2,e.intersect=function(t){return m(e,t)},{cluster:x,labelBBox:M}},"kanbanSection"),A={rect:v,squareRect:v,roundedWithTitle:C,noteGroup:N,divider:(0,s.eW)((t,e)=>{let a;let l=(0,s.nV)(),{themeVariables:r,handDrawnSeed:i}=l,{nodeBorder:n}=r,h=t.insert("g").attr("class",e.cssClasses).attr("id",e.id).attr("data-look",e.look),d=h.insert("g",":first-child"),c=0*e.padding,g=e.width+c;e.diff=-e.padding;let y=e.height+c,f=e.x-g/2,p=e.y-y/2;if(e.width=g,"handDrawn"===e.look){let t=o.Z.svg(h),e=t.rectangle(f,p,g,y,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:n,seed:i});a=h.insert(()=>e,":first-child")}else(a=d.insert("rect",":first-child")).attr("class","divider").attr("x",f).attr("y",p).attr("width",g).attr("height",y).attr("data-look",e.look);let u=a.node().getBBox();return e.height=u.height,e.offsetX=0,e.offsetY=0,e.intersect=function(t){return m(e,t)},{cluster:h,labelBBox:{}}},"divider"),kanbanSection:B},R=new Map,L=(0,s.eW)(async(t,e)=>{let a=e.shape||"rect",l=await A[a](t,e);return R.set(e.id,l),l},"insertCluster"),P=(0,s.eW)(()=>{R=new Map},"clear");function Z(t,e){return t.intersect(e)}function I(t,e,a,l){var r=t.x,i=t.y,n=r-l.x,s=i-l.y,h=Math.sqrt(e*e*s*s+a*a*n*n),o=Math.abs(e*a*n/h);l.x<r&&(o=-o);var d=Math.abs(e*a*s/h);return l.y<i&&(d=-d),{x:r+o,y:i+d}}function T(t,e,a){return I(t,e,e,a)}function E(t,e,a,l){var r,i,n,s,h,o,d,c,g,y,f,p,u;if(r=e.y-t.y,n=t.x-e.x,h=e.x*t.y-t.x*e.y,g=r*a.x+n*a.y+h,y=r*l.x+n*l.y+h,!(0!==g&&0!==y&&H(g,y))&&(i=l.y-a.y,s=a.x-l.x,o=l.x*a.y-a.x*l.y,d=i*t.x+s*t.y+o,c=i*e.x+s*e.y+o,!(0!==d&&0!==c&&H(d,c))&&0!=(f=r*s-i*n)))return p=Math.abs(f/2),{x:(u=n*o-s*h)<0?(u-p)/f:(u+p)/f,y:(u=i*h-r*o)<0?(u-p)/f:(u+p)/f}}function H(t,e){return t*e>0}function V(t,e,a){let l=t.x,r=t.y,i=[],n=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;"function"==typeof e.forEach?e.forEach(function(t){n=Math.min(n,t.x),s=Math.min(s,t.y)}):(n=Math.min(n,e.x),s=Math.min(s,e.y));let h=l-t.width/2-n,o=r-t.height/2-s;for(let l=0;l<e.length;l++){let r=e[l],n=e[l<e.length-1?l+1:0],s=E(t,a,{x:h+r.x,y:o+r.y},{x:h+n.x,y:o+n.y});s&&i.push(s)}return i.length?(i.length>1&&i.sort(function(t,e){let l=t.x-a.x,r=t.y-a.y,i=Math.sqrt(l*l+r*r),n=e.x-a.x,s=e.y-a.y,h=Math.sqrt(n*n+s*s);return i<h?-1:i===h?0:1}),i[0]):t}(0,s.eW)(Z,"intersectNode"),(0,s.eW)(I,"intersectEllipse"),(0,s.eW)(T,"intersectCircle"),(0,s.eW)(E,"intersectLine"),(0,s.eW)(H,"sameSign"),(0,s.eW)(V,"intersectPolygon");var j={node:Z,circle:T,ellipse:I,polygon:V,rect:m};function _(t,e){let{labelStyles:a}=D(e);e.labelStyle=a;let l=y(e),r=l;l||(r="anchor");let i=t.insert("g").attr("class",r).attr("id",e.domId||e.id),{cssStyles:h}=e,d=o.Z.svg(i),c=W(e,{fill:"black",stroke:"none",fillStyle:"solid"});"handDrawn"!==e.look&&(c.roughness=0);let f=d.circle(0,0,2,c),p=i.insert(()=>f,":first-child");return p.attr("class","anchor").attr("style",(0,n.R7)(h)),g(e,p),e.intersect=function(t){return s.cM.info("Circle intersect",e,1,t),j.circle(e,1,t)},i}function Y(t,e,a,l,r,i,n){let s=Math.atan2(l-e,a-t),h=Math.sqrt(((a-t)/2/r)**2+((l-e)/2/i)**2);if(h>1)throw Error("The given radii are too small to create an arc between the points.");let o=Math.sqrt(1-h**2),d=(t+a)/2+o*i*Math.sin(s)*(n?-1:1),c=(e+l)/2-o*r*Math.cos(s)*(n?-1:1),g=Math.atan2((e-c)/i,(t-d)/r),y=Math.atan2((l-c)/i,(a-d)/r)-g;n&&y<0&&(y+=2*Math.PI),!n&&y>0&&(y-=2*Math.PI);let f=[];for(let t=0;t<20;t++){let e=t/19,a=g+e*y,l=d+r*Math.cos(a),n=c+i*Math.sin(a);f.push({x:l,y:n})}return f}async function z(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=i.width+e.padding+20,s=i.height+e.padding,h=s/2,c=h/(2.5+s/50),{cssStyles:p}=e,u=[{x:n/2,y:-s/2},{x:-n/2,y:-s/2},...Y(-n/2,-s/2,-n/2,s/2,c,h,!1),{x:n/2,y:s/2},...Y(n/2,s/2,n/2,-s/2,c,h,!0)],m=o.Z.svg(r),x=W(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=f(u),b=m.path(w,x),$=r.insert(()=>b,":first-child");return $.attr("class","basic label-container"),p&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),$.attr("transform",`translate(${c/2}, 0)`),g(e,$),e.intersect=function(t){let a=j.polygon(e,u,t);return a},r}function q(t,e,a,l){return t.insert("polygon",":first-child").attr("points",l.map(function(t){return t.x+","+t.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-e/2+","+a/2+")")}async function O(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=n.height+e.padding,h=n.width+e.padding+12,c=-s,p=[{x:12,y:c},{x:h,y:c},{x:h,y:0},{x:0,y:0},{x:0,y:c+12},{x:12,y:c}],{cssStyles:u}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=f(p),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-h/2}, ${s/2})`),u&&a.attr("style",u)}else a=q(i,h,s,p);return r&&a.attr("style",r),g(e,a),e.intersect=function(t){return j.polygon(e,p,t)},i}function F(t,e){let{nodeStyles:a}=D(e);e.label="";let l=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:r}=e,i=Math.max(28,e.width??0),n=[{x:0,y:i/2},{x:i/2,y:0},{x:0,y:-i/2},{x:-i/2,y:0}],s=o.Z.svg(l),h=W(e,{});"handDrawn"!==e.look&&(h.roughness=0,h.fillStyle="solid");let d=f(n),c=s.path(d,h),g=l.insert(()=>c,":first-child");return r&&"handDrawn"!==e.look&&g.selectAll("path").attr("style",r),a&&"handDrawn"!==e.look&&g.selectAll("path").attr("style",a),e.width=28,e.height=28,e.intersect=function(t){return j.polygon(e,n,t)},l}async function X(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:h,halfPadding:c}=await d(t,e,y(e)),f=h.width/2+c,{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=t.circle(0,0,2*f,l);(a=i.insert(()=>r,":first-child")).attr("class","basic label-container").attr("style",(0,n.R7)(p))}else a=i.insert("circle",":first-child").attr("class","basic label-container").attr("style",r).attr("r",f).attr("cx",0).attr("cy",0);return g(e,a),e.intersect=function(t){return s.cM.info("Circle intersect",e,f,t),j.circle(e,f,t)},i}function J(t){let e=Math.cos(Math.PI/4),a=Math.sin(Math.PI/4),l=2*t,r={x:l/2*e,y:l/2*a},i={x:-(l/2)*e,y:l/2*a},n={x:-(l/2)*e,y:-(l/2)*a},s={x:l/2*e,y:-(l/2)*a};return`M ${i.x},${i.y} L ${s.x},${s.y}
                   M ${r.x},${r.y} L ${n.x},${n.y}`}function G(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a,e.label="";let r=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),i=Math.max(30,e?.width??0),{cssStyles:n}=e,h=o.Z.svg(r),d=W(e,{});"handDrawn"!==e.look&&(d.roughness=0,d.fillStyle="solid");let c=h.circle(0,0,2*i,d),f=J(i),p=h.path(f,d),u=r.insert(()=>c,":first-child");return u.insert(()=>p),n&&"handDrawn"!==e.look&&u.selectAll("path").attr("style",n),l&&"handDrawn"!==e.look&&u.selectAll("path").attr("style",l),g(e,u),e.intersect=function(t){s.cM.info("crossedCircle intersect",e,{radius:i,point:t});let a=j.circle(e,i,t);return a},r}function U(t,e,a,l=100,r=0,i=180){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:-i,y:-o})}return n}async function K(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+(e.padding??0),h=i.height+(e.padding??0),c=Math.max(5,.1*h),{cssStyles:p}=e,u=[...U(s/2,-h/2,c,30,-90,0),{x:-s/2-c,y:c},...U(s/2+2*c,-c,c,20,-180,-270),...U(s/2+2*c,c,c,20,-90,-180),{x:-s/2-c,y:-h/2},...U(s/2,h/2,c,20,0,90)],m=[{x:s/2,y:-h/2-c},{x:-s/2,y:-h/2-c},...U(s/2,-h/2,c,20,-90,0),{x:-s/2-c,y:-c},...U(s/2+.1*s,-c,c,20,-180,-270),...U(s/2+.1*s,c,c,20,-90,-180),{x:-s/2-c,y:h/2},...U(s/2,h/2,c,20,0,90),{x:-s/2,y:h/2+c},{x:s/2,y:h/2+c}],x=o.Z.svg(r),w=W(e,{fill:"none"});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=f(u),$=b.replace("Z",""),k=x.path($,w),M=f(m),S=x.path(M,{...w}),v=r.insert("g",":first-child");return v.insert(()=>S,":first-child").attr("stroke-opacity",0),v.insert(()=>k,":first-child"),v.attr("class","text"),p&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",l),v.attr("transform",`translate(${c}, 0)`),n.attr("transform",`translate(${-s/2+c-(i.x-(i.left??0))},${-h/2+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,v),e.intersect=function(t){let a=j.polygon(e,m,t);return a},r}function Q(t,e,a,l=100,r=0,i=180){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:i,y:o})}return n}async function tt(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+(e.padding??0),h=i.height+(e.padding??0),c=Math.max(5,.1*h),{cssStyles:p}=e,u=[...Q(s/2,-h/2,c,20,-90,0),{x:s/2+c,y:-c},...Q(s/2+2*c,-c,c,20,-180,-270),...Q(s/2+2*c,c,c,20,-90,-180),{x:s/2+c,y:h/2},...Q(s/2,h/2,c,20,0,90)],m=[{x:-s/2,y:-h/2-c},{x:s/2,y:-h/2-c},...Q(s/2,-h/2,c,20,-90,0),{x:s/2+c,y:-c},...Q(s/2+2*c,-c,c,20,-180,-270),...Q(s/2+2*c,c,c,20,-90,-180),{x:s/2+c,y:h/2},...Q(s/2,h/2,c,20,0,90),{x:s/2,y:h/2+c},{x:-s/2,y:h/2+c}],x=o.Z.svg(r),w=W(e,{fill:"none"});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=f(u),$=b.replace("Z",""),k=x.path($,w),M=f(m),S=x.path(M,{...w}),v=r.insert("g",":first-child");return v.insert(()=>S,":first-child").attr("stroke-opacity",0),v.insert(()=>k,":first-child"),v.attr("class","text"),p&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",l),v.attr("transform",`translate(${-c}, 0)`),n.attr("transform",`translate(${-s/2+(e.padding??0)/2-(i.x-(i.left??0))},${-h/2+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,v),e.intersect=function(t){let a=j.polygon(e,m,t);return a},r}function te(t,e,a,l=100,r=0,i=180){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:-i,y:-o})}return n}async function ta(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+(e.padding??0),h=i.height+(e.padding??0),c=Math.max(5,.1*h),{cssStyles:p}=e,u=[...te(s/2,-h/2,c,30,-90,0),{x:-s/2-c,y:c},...te(s/2+2*c,-c,c,20,-180,-270),...te(s/2+2*c,c,c,20,-90,-180),{x:-s/2-c,y:-h/2},...te(s/2,h/2,c,20,0,90)],m=[...te(-s/2+c+c/2,-h/2,c,20,-90,-180),{x:s/2-c/2,y:c},...te(-s/2-c/2,-c,c,20,0,90),...te(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},...te(-s/2+c+c/2,h/2,c,30,-180,-270)],x=[{x:s/2,y:-h/2-c},{x:-s/2,y:-h/2-c},...te(s/2,-h/2,c,20,-90,0),{x:-s/2-c,y:-c},...te(s/2+2*c,-c,c,20,-180,-270),...te(s/2+2*c,c,c,20,-90,-180),{x:-s/2-c,y:h/2},...te(s/2,h/2,c,20,0,90),{x:-s/2,y:h/2+c},{x:s/2-c-c/2,y:h/2+c},...te(-s/2+c+c/2,-h/2,c,20,-90,-180),{x:s/2-c/2,y:c},...te(-s/2-c/2,-c,c,20,0,90),...te(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},...te(-s/2+c+c/2,h/2,c,30,-180,-270)],w=o.Z.svg(r),b=W(e,{fill:"none"});"handDrawn"!==e.look&&(b.roughness=0,b.fillStyle="solid");let $=f(u),k=$.replace("Z",""),M=w.path(k,b),S=f(m),v=S.replace("Z",""),N=w.path(v,b),C=f(x),B=w.path(C,{...b}),A=r.insert("g",":first-child");return A.insert(()=>B,":first-child").attr("stroke-opacity",0),A.insert(()=>M,":first-child"),A.insert(()=>N,":first-child"),A.attr("class","text"),p&&"handDrawn"!==e.look&&A.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&A.selectAll("path").attr("style",l),A.attr("transform",`translate(${c-c/4}, 0)`),n.attr("transform",`translate(${-s/2+(e.padding??0)/2-(i.x-(i.left??0))},${-h/2+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,A),e.intersect=function(t){let a=j.polygon(e,x,t);return a},r}async function tl(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(80,(i.width+(e.padding??0)*2)*1.25,e?.width??0),s=Math.max(20,i.height+(e.padding??0)*2,e?.height??0),h=s/2,{cssStyles:c}=e,p=o.Z.svg(r),m=W(e,{});"handDrawn"!==e.look&&(m.roughness=0,m.fillStyle="solid");let x=n-h,w=s/4,b=[{x:x,y:0},{x:w,y:0},{x:0,y:s/2},{x:w,y:s},{x:x,y:s},...u(-x,-s/2,h,50,270,90)],$=f(b),k=p.path($,m),M=r.insert(()=>k,":first-child");return M.attr("class","basic label-container"),c&&"handDrawn"!==e.look&&M.selectChildren("path").attr("style",c),l&&"handDrawn"!==e.look&&M.selectChildren("path").attr("style",l),M.attr("transform",`translate(${-n/2}, ${-s/2})`),g(e,M),e.intersect=function(t){let a=j.polygon(e,b,t);return a},r}(0,s.eW)(_,"anchor"),(0,s.eW)(Y,"generateArcPoints"),(0,s.eW)(z,"bowTieRect"),(0,s.eW)(q,"insertPolygonShape"),(0,s.eW)(O,"card"),(0,s.eW)(F,"choice"),(0,s.eW)(X,"circle"),(0,s.eW)(J,"createLine"),(0,s.eW)(G,"crossedCircle"),(0,s.eW)(U,"generateCirclePoints"),(0,s.eW)(K,"curlyBraceLeft"),(0,s.eW)(Q,"generateCirclePoints"),(0,s.eW)(tt,"curlyBraceRight"),(0,s.eW)(te,"generateCirclePoints"),(0,s.eW)(ta,"curlyBraces"),(0,s.eW)(tl,"curvedTrapezoid");var tr=(0,s.eW)((t,e,a,l,r,i)=>`M${t},${e+i} a${r},${i} 0,0,0 ${a},0 a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l}`,"createCylinderPathD"),ti=(0,s.eW)((t,e,a,l,r,i)=>`M${t},${e+i} M${t+a},${e+i} a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l}`,"createOuterCylinderPathD"),tn=(0,s.eW)((t,e,a,l,r,i)=>`M${t-a/2},${-l/2} a${r},${i} 0,0,0 ${a},0`,"createInnerCylinderPathD");async function ts(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:s,label:h}=await d(t,e,y(e)),c=Math.max(s.width+e.padding,e.width??0),f=c/2,p=f/(2.5+c/50),u=Math.max(s.height+p+e.padding,e.height??0),{cssStyles:m}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=ti(0,0,c,u,f,p),r=tn(0,p,c,u,f,p),n=t.path(l,W(e,{})),s=t.path(r,W(e,{fill:"none"}));a=i.insert(()=>s,":first-child"),(a=i.insert(()=>n,":first-child")).attr("class","basic label-container"),m&&a.attr("style",m)}else{let t=tr(0,0,c,u,f,p);a=i.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,n.R7)(m)).attr("style",r)}return a.attr("label-offset-y",p),a.attr("transform",`translate(${-c/2}, ${-(u/2+p)})`),g(e,a),h.attr("transform",`translate(${-(s.width/2)-(s.x-(s.left??0))}, ${-(s.height/2)+(e.padding??0)/1.5-(s.y-(s.top??0))})`),e.intersect=function(t){let a=j.rect(e,t),l=a.x-(e.x??0);if(0!=f&&(Math.abs(l)<(e.width??0)/2||Math.abs(l)==(e.width??0)/2&&Math.abs(a.y-(e.y??0))>(e.height??0)/2-p)){let r=p*p*(1-l*l/(f*f));r>0&&(r=Math.sqrt(r)),r=p-r,t.y-(e.y??0)>0&&(r=-r),a.y+=r}return a},i}async function th(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+e.padding,h=i.height+e.padding,c=.2*h,f=-s/2,p=-h/2-c/2,{cssStyles:u}=e,m=o.Z.svg(r),x=W(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=m.polygon([{x:f,y:p+c},{x:-f,y:p+c},{x:-f,y:-p},{x:f,y:-p},{x:f,y:p},{x:-f,y:p},{x:-f,y:p+c}].map(t=>[t.x,t.y]),x),b=r.insert(()=>w,":first-child");return b.attr("class","basic label-container"),u&&"handDrawn"!==e.look&&b.selectAll("path").attr("style",u),l&&"handDrawn"!==e.look&&b.selectAll("path").attr("style",l),n.attr("transform",`translate(${f+(e.padding??0)/2-(i.x-(i.left??0))}, ${p+c+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,b),e.intersect=function(t){let a=j.rect(e,t);return a},r}async function to(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:h,halfPadding:c}=await d(t,e,y(e)),f=h.width/2+c+5,p=h.width/2+c,{cssStyles:u}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{roughness:.2,strokeWidth:2.5}),r=W(e,{roughness:.2,strokeWidth:1.5}),s=t.circle(0,0,2*f,l),h=t.circle(0,0,2*p,r);(a=i.insert("g",":first-child")).attr("class",(0,n.R7)(e.cssClasses)).attr("style",(0,n.R7)(u)),a.node()?.appendChild(s),a.node()?.appendChild(h)}else{a=i.insert("g",":first-child");let t=a.insert("circle",":first-child"),e=a.insert("circle");a.attr("class","basic label-container").attr("style",r),t.attr("class","outer-circle").attr("style",r).attr("r",f).attr("cx",0).attr("cy",0),e.attr("class","inner-circle").attr("style",r).attr("r",p).attr("cx",0).attr("cy",0)}return g(e,a),e.intersect=function(t){return s.cM.info("DoubleCircle intersect",e,f,t),j.circle(e,f,t)},i}function td(t,e,{config:{themeVariables:a}}){let{labelStyles:l,nodeStyles:r}=D(e);e.label="",e.labelStyle=l;let i=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:n}=e,h=o.Z.svg(i),{nodeBorder:d}=a,c=W(e,{fillStyle:"solid"});"handDrawn"!==e.look&&(c.roughness=0);let f=h.circle(0,0,14,c),p=i.insert(()=>f,":first-child");return p.selectAll("path").attr("style",`fill: ${d} !important;`),n&&n.length>0&&"handDrawn"!==e.look&&p.selectAll("path").attr("style",n),r&&"handDrawn"!==e.look&&p.selectAll("path").attr("style",r),g(e,p),e.intersect=function(t){s.cM.info("filledCircle intersect",e,{radius:7,point:t});let a=j.circle(e,7,t);return a},i}async function tc(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),h=i.width+(e.padding??0),c=h+i.height,p=h+i.height,u=[{x:0,y:-c},{x:p,y:-c},{x:p/2,y:0}],{cssStyles:m}=e,x=o.Z.svg(r),w=W(e,{});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=f(u),$=x.path(b,w),k=r.insert(()=>$,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return m&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",m),l&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",l),e.width=h,e.height=c,g(e,k),n.attr("transform",`translate(${-i.width/2-(i.x-(i.left??0))}, ${-c/2+(e.padding??0)/2+(i.y-(i.top??0))})`),e.intersect=function(t){return s.cM.info("Triangle intersect",e,u,t),j.polygon(e,u,t)},r}function tg(t,e,{dir:a,config:{state:l,themeVariables:r}}){let{nodeStyles:i}=D(e);e.label="";let n=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:s}=e,h=Math.max(70,e?.width??0),d=Math.max(10,e?.height??0);"LR"===a&&(h=Math.max(10,e?.width??0),d=Math.max(70,e?.height??0));let c=-1*h/2,f=-1*d/2,p=o.Z.svg(n),u=W(e,{stroke:r.lineColor,fill:r.lineColor});"handDrawn"!==e.look&&(u.roughness=0,u.fillStyle="solid");let m=p.rectangle(c,f,h,d,u),x=n.insert(()=>m,":first-child");s&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",s),i&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",i),g(e,x);let w=l?.padding??0;return e.width&&e.height&&(e.width+=w/2||0,e.height+=w/2||0),e.intersect=function(t){return j.rect(e,t)},n}async function ty(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(80,i.width+(e.padding??0)*2,e?.width??0),h=Math.max(50,i.height+(e.padding??0)*2,e?.height??0),c=h/2,{cssStyles:p}=e,m=o.Z.svg(r),x=W(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=[{x:-n/2,y:-h/2},{x:n/2-c,y:-h/2},...u(-n/2+c,0,c,50,90,270),{x:n/2-c,y:h/2},{x:-n/2,y:h/2}],b=f(w),$=m.path(b,x),k=r.insert(()=>$,":first-child");return k.attr("class","basic label-container"),p&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",p),l&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",l),g(e,k),e.intersect=function(t){s.cM.info("Pill intersect",e,{radius:c,point:t});let a=j.polygon(e,w,t);return a},r}(0,s.eW)(ts,"cylinder"),(0,s.eW)(th,"dividedRectangle"),(0,s.eW)(to,"doublecircle"),(0,s.eW)(td,"filledCircle"),(0,s.eW)(tc,"flippedTriangle"),(0,s.eW)(tg,"forkJoin"),(0,s.eW)(ty,"halfRoundedRectangle");var tf=(0,s.eW)((t,e,a,l,r)=>`M${t+r},${e} L${t+a-r},${e} L${t+a},${e-l/2} L${t+a-r},${e-l} L${t+r},${e-l} L${t},${e-l/2} Z`,"createHexagonPathD");async function tp(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=n.height+e.padding,h=s/4,c=n.width+2*h+e.padding,f=[{x:h,y:0},{x:c-h,y:0},{x:c,y:-s/2},{x:c-h,y:-s},{x:h,y:-s},{x:0,y:-s/2}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=tf(0,0,c,s,h),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-c/2}, ${s/2})`),p&&a.attr("style",p)}else a=q(i,c,s,f);return r&&a.attr("style",r),e.width=c,e.height=s,g(e,a),e.intersect=function(t){return j.polygon(e,f,t)},i}async function tu(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.label="",e.labelStyle=a;let{shapeSvg:r}=await d(t,e,y(e)),i=Math.max(30,e?.width??0),n=Math.max(30,e?.height??0),{cssStyles:h}=e,c=o.Z.svg(r),p=W(e,{});"handDrawn"!==e.look&&(p.roughness=0,p.fillStyle="solid");let u=[{x:0,y:0},{x:i,y:0},{x:0,y:n},{x:i,y:n}],m=f(u),x=c.path(m,p),w=r.insert(()=>x,":first-child");return w.attr("class","basic label-container"),h&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",h),l&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",l),w.attr("transform",`translate(${-i/2}, ${-n/2})`),g(e,w),e.intersect=function(t){s.cM.info("Pill intersect",e,{points:u});let a=j.polygon(e,u,t);return a},r}async function tm(t,e,{config:{themeVariables:a,flowchart:r}}){let{labelStyles:i}=D(e);e.labelStyle=i;let n=e.assetHeight??48,h=e.assetWidth??48,c=Math.max(n,h),y=r?.wrappingWidth;e.width=Math.max(c,y??0);let{shapeSvg:f,bbox:p,label:u}=await d(t,e,"icon-shape default"),m="t"===e.pos,{nodeBorder:x}=a,{stylesMap:w}=M(e),b=e.label?8:0,$=o.Z.svg(f),k=W(e,{stroke:"none",fill:"none"});"handDrawn"!==e.look&&(k.roughness=0,k.fillStyle="solid");let S=$.rectangle(-c/2,-c/2,c,c,k),v=Math.max(c,p.width),N=c+p.height+b,C=$.rectangle(-v/2,-N/2,v,N,{...k,fill:"transparent",stroke:"none"}),B=f.insert(()=>S,":first-child"),A=f.insert(()=>C);if(e.icon){let t=f.append("g");t.html(`<g>${await (0,l.s4)(e.icon,{height:c,width:c,fallbackPrefix:""})}</g>`);let a=t.node().getBBox(),r=a.width,i=a.height,n=a.x,s=a.y;t.attr("transform",`translate(${-r/2-n},${m?p.height/2+b/2-i/2-s:-p.height/2-b/2-i/2-s})`),t.attr("style",`color: ${w.get("stroke")??x};`)}return u.attr("transform",`translate(${-p.width/2-(p.x-(p.left??0))},${m?-N/2:N/2-p.height})`),B.attr("transform",`translate(0,${m?p.height/2+b/2:-p.height/2-b/2})`),g(e,A),e.intersect=function(t){if(s.cM.info("iconSquare intersect",e,t),!e.label)return j.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];i=m?[{x:a-p.width/2,y:l-r/2},{x:a+p.width/2,y:l-r/2},{x:a+p.width/2,y:l-r/2+p.height+b},{x:a+c/2,y:l-r/2+p.height+b},{x:a+c/2,y:l+r/2},{x:a-c/2,y:l+r/2},{x:a-c/2,y:l-r/2+p.height+b},{x:a-p.width/2,y:l-r/2+p.height+b}]:[{x:a-c/2,y:l-r/2},{x:a+c/2,y:l-r/2},{x:a+c/2,y:l-r/2+c},{x:a+p.width/2,y:l-r/2+c},{x:a+p.width/2/2,y:l+r/2},{x:a-p.width/2,y:l+r/2},{x:a-p.width/2,y:l-r/2+c},{x:a-c/2,y:l-r/2+c}];let n=j.polygon(e,i,t);return n},f}async function tx(t,e,{config:{themeVariables:a,flowchart:r}}){let{labelStyles:i}=D(e);e.labelStyle=i;let n=e.assetHeight??48,h=e.assetWidth??48,c=Math.max(n,h),y=r?.wrappingWidth;e.width=Math.max(c,y??0);let{shapeSvg:f,bbox:p,label:u}=await d(t,e,"icon-shape default"),m=e.label?8:0,x="t"===e.pos,{nodeBorder:w,mainBkg:b}=a,{stylesMap:$}=M(e),k=o.Z.svg(f),S=W(e,{});"handDrawn"!==e.look&&(S.roughness=0,S.fillStyle="solid");let v=$.get("fill");S.stroke=v??b;let N=f.append("g");e.icon&&N.html(`<g>${await (0,l.s4)(e.icon,{height:c,width:c,fallbackPrefix:""})}</g>`);let C=N.node().getBBox(),B=C.width,A=C.height,R=C.x,L=C.y,P=Math.max(B,A)*Math.SQRT2+40,Z=k.circle(0,0,P,S),I=Math.max(P,p.width),T=P+p.height+m,E=k.rectangle(-I/2,-T/2,I,T,{...S,fill:"transparent",stroke:"none"}),H=f.insert(()=>Z,":first-child"),V=f.insert(()=>E);return N.attr("transform",`translate(${-B/2-R},${x?p.height/2+m/2-A/2-L:-p.height/2-m/2-A/2-L})`),N.attr("style",`color: ${$.get("stroke")??w};`),u.attr("transform",`translate(${-p.width/2-(p.x-(p.left??0))},${x?-T/2:T/2-p.height})`),H.attr("transform",`translate(0,${x?p.height/2+m/2:-p.height/2-m/2})`),g(e,V),e.intersect=function(t){s.cM.info("iconSquare intersect",e,t);let a=j.rect(e,t);return a},f}async function tw(t,e,{config:{themeVariables:a,flowchart:r}}){let{labelStyles:i}=D(e);e.labelStyle=i;let n=e.assetHeight??48,h=e.assetWidth??48,c=Math.max(n,h),y=r?.wrappingWidth;e.width=Math.max(c,y??0);let{shapeSvg:f,bbox:p,halfPadding:u,label:m}=await d(t,e,"icon-shape default"),x="t"===e.pos,w=c+2*u,b=c+2*u,{nodeBorder:k,mainBkg:S}=a,{stylesMap:v}=M(e),N=e.label?8:0,C=o.Z.svg(f),B=W(e,{});"handDrawn"!==e.look&&(B.roughness=0,B.fillStyle="solid");let A=v.get("fill");B.stroke=A??S;let R=C.path($(-b/2,-w/2,b,w,5),B),L=Math.max(b,p.width),P=w+p.height+N,Z=C.rectangle(-L/2,-P/2,L,P,{...B,fill:"transparent",stroke:"none"}),I=f.insert(()=>R,":first-child").attr("class","icon-shape2"),T=f.insert(()=>Z);if(e.icon){let t=f.append("g");t.html(`<g>${await (0,l.s4)(e.icon,{height:c,width:c,fallbackPrefix:""})}</g>`);let a=t.node().getBBox(),r=a.width,i=a.height,n=a.x,s=a.y;t.attr("transform",`translate(${-r/2-n},${x?p.height/2+N/2-i/2-s:-p.height/2-N/2-i/2-s})`),t.attr("style",`color: ${v.get("stroke")??k};`)}return m.attr("transform",`translate(${-p.width/2-(p.x-(p.left??0))},${x?-P/2:P/2-p.height})`),I.attr("transform",`translate(0,${x?p.height/2+N/2:-p.height/2-N/2})`),g(e,T),e.intersect=function(t){if(s.cM.info("iconSquare intersect",e,t),!e.label)return j.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];i=x?[{x:a-p.width/2,y:l-r/2},{x:a+p.width/2,y:l-r/2},{x:a+p.width/2,y:l-r/2+p.height+N},{x:a+b/2,y:l-r/2+p.height+N},{x:a+b/2,y:l+r/2},{x:a-b/2,y:l+r/2},{x:a-b/2,y:l-r/2+p.height+N},{x:a-p.width/2,y:l-r/2+p.height+N}]:[{x:a-b/2,y:l-r/2},{x:a+b/2,y:l-r/2},{x:a+b/2,y:l-r/2+w},{x:a+p.width/2,y:l-r/2+w},{x:a+p.width/2/2,y:l+r/2},{x:a-p.width/2,y:l+r/2},{x:a-p.width/2,y:l-r/2+w},{x:a-b/2,y:l-r/2+w}];let n=j.polygon(e,i,t);return n},f}async function tb(t,e,{config:{themeVariables:a,flowchart:r}}){let{labelStyles:i}=D(e);e.labelStyle=i;let n=e.assetHeight??48,h=e.assetWidth??48,c=Math.max(n,h),y=r?.wrappingWidth;e.width=Math.max(c,y??0);let{shapeSvg:f,bbox:p,halfPadding:u,label:m}=await d(t,e,"icon-shape default"),x="t"===e.pos,w=c+2*u,b=c+2*u,{nodeBorder:k,mainBkg:S}=a,{stylesMap:v}=M(e),N=e.label?8:0,C=o.Z.svg(f),B=W(e,{});"handDrawn"!==e.look&&(B.roughness=0,B.fillStyle="solid");let A=v.get("fill");B.stroke=A??S;let R=C.path($(-b/2,-w/2,b,w,.1),B),L=Math.max(b,p.width),P=w+p.height+N,Z=C.rectangle(-L/2,-P/2,L,P,{...B,fill:"transparent",stroke:"none"}),I=f.insert(()=>R,":first-child"),T=f.insert(()=>Z);if(e.icon){let t=f.append("g");t.html(`<g>${await (0,l.s4)(e.icon,{height:c,width:c,fallbackPrefix:""})}</g>`);let a=t.node().getBBox(),r=a.width,i=a.height,n=a.x,s=a.y;t.attr("transform",`translate(${-r/2-n},${x?p.height/2+N/2-i/2-s:-p.height/2-N/2-i/2-s})`),t.attr("style",`color: ${v.get("stroke")??k};`)}return m.attr("transform",`translate(${-p.width/2-(p.x-(p.left??0))},${x?-P/2:P/2-p.height})`),I.attr("transform",`translate(0,${x?p.height/2+N/2:-p.height/2-N/2})`),g(e,T),e.intersect=function(t){if(s.cM.info("iconSquare intersect",e,t),!e.label)return j.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];i=x?[{x:a-p.width/2,y:l-r/2},{x:a+p.width/2,y:l-r/2},{x:a+p.width/2,y:l-r/2+p.height+N},{x:a+b/2,y:l-r/2+p.height+N},{x:a+b/2,y:l+r/2},{x:a-b/2,y:l+r/2},{x:a-b/2,y:l-r/2+p.height+N},{x:a-p.width/2,y:l-r/2+p.height+N}]:[{x:a-b/2,y:l-r/2},{x:a+b/2,y:l-r/2},{x:a+b/2,y:l-r/2+w},{x:a+p.width/2,y:l-r/2+w},{x:a+p.width/2/2,y:l+r/2},{x:a-p.width/2,y:l+r/2},{x:a-p.width/2,y:l-r/2+w},{x:a-b/2,y:l-r/2+w}];let n=j.polygon(e,i,t);return n},f}async function t$(t,e,{config:{flowchart:a}}){let l=new Image;l.src=e?.img??"",await l.decode();let r=Number(l.naturalWidth.toString().replace("px","")),i=Number(l.naturalHeight.toString().replace("px",""));e.imageAspectRatio=r/i;let{labelStyles:n}=D(e);e.labelStyle=n;let h=a?.wrappingWidth;e.defaultWidth=a?.wrappingWidth;let c=Math.max(e.label?h??0:0,e?.assetWidth??r),y="on"===e.constraint&&e?.assetHeight?e.assetHeight*e.imageAspectRatio:c,f="on"===e.constraint?y/e.imageAspectRatio:e?.assetHeight??i;e.width=Math.max(y,h??0);let{shapeSvg:p,bbox:u,label:m}=await d(t,e,"image-shape default"),x="t"===e.pos,w=e.label?8:0,b=o.Z.svg(p),$=W(e,{});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=b.rectangle(-y/2,-f/2,y,f,$),M=Math.max(y,u.width),S=f+u.height+w,v=b.rectangle(-M/2,-S/2,M,S,{...$,fill:"none",stroke:"none"}),N=p.insert(()=>k,":first-child"),C=p.insert(()=>v);if(e.img){let t=p.append("image");t.attr("href",e.img),t.attr("width",y),t.attr("height",f),t.attr("preserveAspectRatio","none"),t.attr("transform",`translate(${-y/2},${x?S/2-f:-S/2})`)}return m.attr("transform",`translate(${-u.width/2-(u.x-(u.left??0))},${x?-f/2-u.height/2-w/2:f/2-u.height/2+w/2})`),N.attr("transform",`translate(0,${x?u.height/2+w/2:-u.height/2-w/2})`),g(e,C),e.intersect=function(t){if(s.cM.info("iconSquare intersect",e,t),!e.label)return j.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];i=x?[{x:a-u.width/2,y:l-r/2},{x:a+u.width/2,y:l-r/2},{x:a+u.width/2,y:l-r/2+u.height+w},{x:a+y/2,y:l-r/2+u.height+w},{x:a+y/2,y:l+r/2},{x:a-y/2,y:l+r/2},{x:a-y/2,y:l-r/2+u.height+w},{x:a-u.width/2,y:l-r/2+u.height+w}]:[{x:a-y/2,y:l-r/2},{x:a+y/2,y:l-r/2},{x:a+y/2,y:l-r/2+f},{x:a+u.width/2,y:l-r/2+f},{x:a+u.width/2/2,y:l+r/2},{x:a-u.width/2,y:l+r/2},{x:a-u.width/2,y:l-r/2+f},{x:a-y/2,y:l-r/2+f}];let n=j.polygon(e,i,t);return n},p}async function tk(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=Math.max(n.width+(e.padding??0)*2,e?.width??0),h=Math.max(n.height+(e.padding??0)*2,e?.height??0),c=[{x:0,y:0},{x:s,y:0},{x:s+3*h/6,y:-h},{x:-3*h/6,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=q(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return j.polygon(e,c,t)},i}async function tM(t,e,a){let l;let{labelStyles:r,nodeStyles:i}=D(e);e.labelStyle=r;let{shapeSvg:s,bbox:h}=await d(t,e,y(e)),c=Math.max(h.width+2*a.labelPaddingX,e?.width||0),f=Math.max(h.height+2*a.labelPaddingY,e?.height||0),p=-c/2,u=-f/2,{rx:m,ry:x}=e,{cssStyles:w}=e;if(a?.rx&&a.ry&&(m=a.rx,x=a.ry),"handDrawn"===e.look){let t=o.Z.svg(s),a=W(e,{}),r=m||x?t.path($(p,u,c,f,m||0),a):t.rectangle(p,u,c,f,a);(l=s.insert(()=>r,":first-child")).attr("class","basic label-container").attr("style",(0,n.R7)(w))}else(l=s.insert("rect",":first-child")).attr("class","basic label-container").attr("style",i).attr("rx",(0,n.R7)(m)).attr("ry",(0,n.R7)(x)).attr("x",p).attr("y",u).attr("width",c).attr("height",f);return g(e,l),e.intersect=function(t){return j.rect(e,t)},s}async function tS(t,e){let{shapeSvg:a,bbox:l,label:r}=await d(t,e,"label"),i=a.insert("rect",":first-child");return i.attr("width",.1).attr("height",.1),a.attr("class","label edgeLabel"),r.attr("transform",`translate(${-(l.width/2)-(l.x-(l.left??0))}, ${-(l.height/2)-(l.y-(l.top??0))})`),g(e,i),e.intersect=function(t){return j.rect(e,t)},a}async function tD(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=Math.max(n.width+(e.padding??0),e?.width??0),h=Math.max(n.height+(e.padding??0),e?.height??0),c=[{x:0,y:0},{x:s+3*h/6,y:0},{x:s,y:-h},{x:-(3*h)/6,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=q(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return j.polygon(e,c,t)},i}async function tW(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=Math.max(n.width+(e.padding??0),e?.width??0),h=Math.max(n.height+(e.padding??0),e?.height??0),c=[{x:-3*h/6,y:0},{x:s,y:0},{x:s+3*h/6,y:-h},{x:0,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=q(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return j.polygon(e,c,t)},i}function tv(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.label="",e.labelStyle=a;let r=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:i}=e,n=Math.max(35,e?.width??0),h=Math.max(35,e?.height??0),d=[{x:n,y:0},{x:0,y:h+3.5},{x:n-14,y:h+3.5},{x:0,y:2*h},{x:n,y:h-3.5},{x:14,y:h-3.5}],c=o.Z.svg(r),p=W(e,{});"handDrawn"!==e.look&&(p.roughness=0,p.fillStyle="solid");let u=f(d),m=c.path(u,p),x=r.insert(()=>m,":first-child");return i&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",i),l&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",l),x.attr("transform",`translate(-${n/2},${-h})`),g(e,x),e.intersect=function(t){s.cM.info("lightningBolt intersect",e,t);let a=j.polygon(e,d,t);return a},r}(0,s.eW)(tp,"hexagon"),(0,s.eW)(tu,"hourglass"),(0,s.eW)(tm,"icon"),(0,s.eW)(tx,"iconCircle"),(0,s.eW)(tw,"iconRounded"),(0,s.eW)(tb,"iconSquare"),(0,s.eW)(t$,"imageSquare"),(0,s.eW)(tk,"inv_trapezoid"),(0,s.eW)(tM,"drawRect"),(0,s.eW)(tS,"labelRect"),(0,s.eW)(tD,"lean_left"),(0,s.eW)(tW,"lean_right"),(0,s.eW)(tv,"lightningBolt");var tN=(0,s.eW)((t,e,a,l,r,i,n)=>`M${t},${e+i} a${r},${i} 0,0,0 ${a},0 a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l} M${t},${e+i+n} a${r},${i} 0,0,0 ${a},0`,"createCylinderPathD"),tC=(0,s.eW)((t,e,a,l,r,i,n)=>`M${t},${e+i} M${t+a},${e+i} a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l} M${t},${e+i+n} a${r},${i} 0,0,0 ${a},0`,"createOuterCylinderPathD"),tB=(0,s.eW)((t,e,a,l,r,i)=>`M${t-a/2},${-l/2} a${r},${i} 0,0,0 ${a},0`,"createInnerCylinderPathD");async function tA(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:s,label:h}=await d(t,e,y(e)),c=Math.max(s.width+(e.padding??0),e.width??0),f=c/2,p=f/(2.5+c/50),u=Math.max(s.height+p+(e.padding??0),e.height??0),m=.1*u,{cssStyles:x}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=tC(0,0,c,u,f,p,m),r=tB(0,p,c,u,f,p),n=W(e,{}),s=t.path(l,n),h=t.path(r,n),d=i.insert(()=>h,":first-child");d.attr("class","line"),(a=i.insert(()=>s,":first-child")).attr("class","basic label-container"),x&&a.attr("style",x)}else{let t=tN(0,0,c,u,f,p,m);a=i.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,n.R7)(x)).attr("style",r)}return a.attr("label-offset-y",p),a.attr("transform",`translate(${-c/2}, ${-(u/2+p)})`),g(e,a),h.attr("transform",`translate(${-(s.width/2)-(s.x-(s.left??0))}, ${-(s.height/2)+p-(s.y-(s.top??0))})`),e.intersect=function(t){let a=j.rect(e,t),l=a.x-(e.x??0);if(0!=f&&(Math.abs(l)<(e.width??0)/2||Math.abs(l)==(e.width??0)/2&&Math.abs(a.y-(e.y??0))>(e.height??0)/2-p)){let r=p*p*(1-l*l/(f*f));r>0&&(r=Math.sqrt(r)),r=p-r,t.y-(e.y??0)>0&&(r=-r),a.y+=r}return a},i}async function tR(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/4,f=h+c,{cssStyles:u}=e,m=o.Z.svg(r),x=W(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=[{x:-s/2-s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:f/2},...p(-s/2-s/2*.1,f/2,s/2+s/2*.1,f/2,c,.8),{x:s/2+s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:-f/2},{x:-s/2,y:-f/2},{x:-s/2,y:f/2*1.1},{x:-s/2,y:-f/2}],b=m.polygon(w.map(t=>[t.x,t.y]),x),$=r.insert(()=>b,":first-child");return $.attr("class","basic label-container"),u&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",u),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),$.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-s/2+(e.padding??0)+s/2*.1/2-(i.x-(i.left??0))},${-h/2+(e.padding??0)-c/2-(i.y-(i.top??0))})`),g(e,$),e.intersect=function(t){let a=j.polygon(e,w,t);return a},r}async function tL(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=-s/2,p=-h/2,{cssStyles:u}=e,m=o.Z.svg(r),x=W(e,{}),w=[{x:c-5,y:p+5},{x:c-5,y:p+h+5},{x:c+s-5,y:p+h+5},{x:c+s-5,y:p+h},{x:c+s,y:p+h},{x:c+s,y:p+h-5},{x:c+s+5,y:p+h-5},{x:c+s+5,y:p-5},{x:c+5,y:p-5},{x:c+5,y:p},{x:c,y:p},{x:c,y:p+5}];"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let b=f(w),$=m.path(b,x),k=f([{x:c,y:p+5},{x:c+s-5,y:p+5},{x:c+s-5,y:p+h},{x:c+s,y:p+h},{x:c+s,y:p},{x:c,y:p}]),M=m.path(k,{...x,fill:"none"}),S=r.insert(()=>M,":first-child");return S.insert(()=>$,":first-child"),S.attr("class","basic label-container"),u&&"handDrawn"!==e.look&&S.selectAll("path").attr("style",u),l&&"handDrawn"!==e.look&&S.selectAll("path").attr("style",l),n.attr("transform",`translate(${-(i.width/2)-5-(i.x-(i.left??0))}, ${-(i.height/2)+5-(i.y-(i.top??0))})`),g(e,S),e.intersect=function(t){let a=j.polygon(e,w,t);return a},r}async function tP(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/4,u=h+c,m=-s/2,x=-u/2,{cssStyles:w}=e,b=p(m-5,x+u+5,m+s-5,x+u+5,c,.8),$=b?.[b.length-1],k=[{x:m-5,y:x+5},{x:m-5,y:x+u+5},...b,{x:m+s-5,y:$.y-5},{x:m+s,y:$.y-5},{x:m+s,y:$.y-10},{x:m+s+5,y:$.y-10},{x:m+s+5,y:x-5},{x:m+5,y:x-5},{x:m+5,y:x},{x:m,y:x},{x:m,y:x+5}],M=[{x:m,y:x+5},{x:m+s-5,y:x+5},{x:m+s-5,y:$.y-5},{x:m+s,y:$.y-5},{x:m+s,y:x},{x:m,y:x}],S=o.Z.svg(r),v=W(e,{});"handDrawn"!==e.look&&(v.roughness=0,v.fillStyle="solid");let N=f(k),C=S.path(N,v),B=f(M),A=S.path(B,v),R=r.insert(()=>C,":first-child");return R.insert(()=>A),R.attr("class","basic label-container"),w&&"handDrawn"!==e.look&&R.selectAll("path").attr("style",w),l&&"handDrawn"!==e.look&&R.selectAll("path").attr("style",l),R.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-(i.width/2)-5-(i.x-(i.left??0))}, ${-(i.height/2)+5-c/2-(i.y-(i.top??0))})`),g(e,R),e.intersect=function(t){let a=j.polygon(e,k,t);return a},r}async function tZ(t,e,{config:{themeVariables:a}}){let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let i=e.useHtmlLabels||s.iE().flowchart?.htmlLabels!==!1;i||(e.centerLabel=!0);let{shapeSvg:n,bbox:h}=await d(t,e,y(e)),c=Math.max(h.width+(e.padding??0)*2,e?.width??0),f=Math.max(h.height+(e.padding??0)*2,e?.height??0),{cssStyles:p}=e,u=o.Z.svg(n),m=W(e,{fill:a.noteBkgColor,stroke:a.noteBorderColor});"handDrawn"!==e.look&&(m.roughness=0,m.fillStyle="solid");let x=u.rectangle(-c/2,-f/2,c,f,m),w=n.insert(()=>x,":first-child");return w.attr("class","basic label-container"),p&&"handDrawn"!==e.look&&w.selectAll("path").attr("style",p),r&&"handDrawn"!==e.look&&w.selectAll("path").attr("style",r),g(e,w),e.intersect=function(t){return j.rect(e,t)},n}(0,s.eW)(tA,"linedCylinder"),(0,s.eW)(tR,"linedWaveEdgedRect"),(0,s.eW)(tL,"multiRect"),(0,s.eW)(tP,"multiWaveEdgedRectangle"),(0,s.eW)(tZ,"note");var tI=(0,s.eW)((t,e,a)=>`M${t+a/2},${e} L${t+a},${e-a/2} L${t+a/2},${e-a} L${t},${e-a/2} Z`,"createDecisionBoxPathD");async function tT(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),h=n.width+e.padding,c=n.height+e.padding,f=h+c,p=[{x:f/2,y:0},{x:f,y:-f/2},{x:f/2,y:-f},{x:0,y:-f/2}],{cssStyles:u}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=tI(0,0,f),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-f/2}, ${f/2})`),u&&a.attr("style",u)}else a=q(i,f,f,p);return r&&a.attr("style",r),g(e,a),e.intersect=function(t){return s.cM.debug("APA12 Intersect called SPLIT\npoint:",t,"\nnode:\n",e,"\nres:",j.polygon(e,p,t)),j.polygon(e,p,t)},i}async function tE(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0),e?.width??0),h=Math.max(i.height+(e.padding??0),e?.height??0),c=-s/2,p=-h/2,u=p/2,m=[{x:c+u,y:p},{x:c,y:0},{x:c+u,y:-p},{x:-c,y:-p},{x:-c,y:p}],{cssStyles:x}=e,w=o.Z.svg(r),b=W(e,{});"handDrawn"!==e.look&&(b.roughness=0,b.fillStyle="solid");let $=f(m),k=w.path($,b),M=r.insert(()=>k,":first-child");return M.attr("class","basic label-container"),x&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",x),l&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",l),M.attr("transform",`translate(${-u/2},0)`),n.attr("transform",`translate(${-u/2-i.width/2-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),g(e,M),e.intersect=function(t){return j.polygon(e,m,t)},r}async function tH(t,e){let a,l,r;let{labelStyles:i,nodeStyles:n}=D(e);e.labelStyle=i,a=e.cssClasses?"node "+e.cssClasses:"node default";let d=t.insert("g").attr("class",a).attr("id",e.domId||e.id),c=d.insert("g"),y=d.insert("g").attr("class","label").attr("style",n),f=e.description,p=e.label,u=y.node().appendChild(await b(p,e.labelStyle,!0,!0)),m={width:0,height:0};if((0,s.ku)(s.nV()?.flowchart?.htmlLabels)){let t=u.children[0],e=(0,h.Ys)(u);m=t.getBoundingClientRect(),e.attr("width",m.width),e.attr("height",m.height)}s.cM.info("Text 2",f);let x=f||[],w=u.getBBox(),k=y.node().appendChild(await b(x.join?x.join("<br/>"):x,e.labelStyle,!0,!0)),M=k.children[0],S=(0,h.Ys)(k);m=M.getBoundingClientRect(),S.attr("width",m.width),S.attr("height",m.height);let v=(e.padding||0)/2;(0,h.Ys)(k).attr("transform","translate( "+(m.width>w.width?0:(w.width-m.width)/2)+", "+(w.height+v+5)+")"),(0,h.Ys)(u).attr("transform","translate( "+(m.width<w.width?0:-(w.width-m.width)/2)+", 0)"),m=y.node().getBBox(),y.attr("transform","translate("+-m.width/2+", "+(-m.height/2-v+3)+")");let N=m.width+(e.padding||0),C=m.height+(e.padding||0),B=-m.width/2-v,A=-m.height/2-v;if("handDrawn"===e.look){let t=o.Z.svg(d),a=W(e,{}),i=t.path($(B,A,N,C,e.rx||0),a),n=t.line(-m.width/2-v,-m.height/2-v+w.height+v,m.width/2+v,-m.height/2-v+w.height+v,a);r=d.insert(()=>(s.cM.debug("Rough node insert CXC",i),n),":first-child"),l=d.insert(()=>(s.cM.debug("Rough node insert CXC",i),i),":first-child")}else l=c.insert("rect",":first-child"),r=c.insert("line"),l.attr("class","outer title-state").attr("style",n).attr("x",-m.width/2-v).attr("y",-m.height/2-v).attr("width",m.width+(e.padding||0)).attr("height",m.height+(e.padding||0)),r.attr("class","divider").attr("x1",-m.width/2-v).attr("x2",m.width/2+v).attr("y1",-m.height/2-v+w.height+v).attr("y2",-m.height/2-v+w.height+v);return g(e,l),e.intersect=function(t){return j.rect(e,t)},d}async function tV(t,e){let a={rx:5,ry:5,classes:"",labelPaddingX:1*(e?.padding||0),labelPaddingY:1*(e?.padding||0)};return tM(t,e,a)}async function tj(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:s}=await d(t,e,y(e)),h=e?.padding??0,c=Math.max(i.width+(e.padding??0)*2,e?.width??0),f=Math.max(i.height+(e.padding??0)*2,e?.height??0),p=-i.width/2-h,u=-i.height/2-h,{cssStyles:m}=e,x=o.Z.svg(r),w=W(e,{});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=x.polygon([{x:p,y:u},{x:p+c+8,y:u},{x:p+c+8,y:u+f},{x:p-8,y:u+f},{x:p-8,y:u},{x:p,y:u},{x:p,y:u+f}].map(t=>[t.x,t.y]),w),$=r.insert(()=>b,":first-child");return $.attr("class","basic label-container").attr("style",(0,n.R7)(m)),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),m&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),s.attr("transform",`translate(${-c/2+4+(e.padding??0)-(i.x-(i.left??0))},${-f/2+(e.padding??0)-(i.y-(i.top??0))})`),g(e,$),e.intersect=function(t){return j.rect(e,t)},r}async function t_(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=-s/2,p=-h/2,{cssStyles:u}=e,m=o.Z.svg(r),x=W(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=[{x:c,y:p},{x:c,y:p+h},{x:c+s,y:p+h},{x:c+s,y:p-h/2}],b=f(w),$=m.path(b,x),k=r.insert(()=>$,":first-child");return k.attr("class","basic label-container"),u&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",u),l&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",l),k.attr("transform",`translate(0, ${h/4})`),n.attr("transform",`translate(${-s/2+(e.padding??0)-(i.x-(i.left??0))}, ${-h/4+(e.padding??0)-(i.y-(i.top??0))})`),g(e,k),e.intersect=function(t){let a=j.polygon(e,w,t);return a},r}async function tY(t,e){let a={rx:0,ry:0,classes:"",labelPaddingX:2*(e?.padding||0),labelPaddingY:1*(e?.padding||0)};return tM(t,e,a)}async function tz(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:s}=await d(t,e,y(e)),h=s.height+e.padding,c=s.width+h/4+e.padding,{cssStyles:f}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=$(-c/2,-h/2,c,h,h/2),s=t.path(r,l);(a=i.insert(()=>s,":first-child")).attr("class","basic label-container").attr("style",(0,n.R7)(f))}else(a=i.insert("rect",":first-child")).attr("class","basic label-container").attr("style",r).attr("rx",h/2).attr("ry",h/2).attr("x",-c/2).attr("y",-h/2).attr("width",c).attr("height",h);return g(e,a),e.intersect=function(t){return j.rect(e,t)},i}async function tq(t,e){return tM(t,e,{rx:5,ry:5,classes:"flowchart-node"})}function tO(t,e,{config:{themeVariables:a}}){let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{cssStyles:i}=e,{lineColor:n,stateBorder:s,nodeBorder:h}=a,d=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),c=o.Z.svg(d),y=W(e,{});"handDrawn"!==e.look&&(y.roughness=0,y.fillStyle="solid");let f=c.circle(0,0,14,{...y,stroke:n,strokeWidth:2}),p=s??h,u=c.circle(0,0,5,{...y,fill:p,stroke:p,strokeWidth:2,fillStyle:"solid"}),m=d.insert(()=>f,":first-child");return m.insert(()=>u),i&&m.selectAll("path").attr("style",i),r&&m.selectAll("path").attr("style",r),g(e,m),e.intersect=function(t){return j.circle(e,7,t)},d}function tF(t,e,{config:{themeVariables:a}}){let l;let{lineColor:r}=a,i=t.insert("g").attr("class","node default").attr("id",e.domId||e.id);if("handDrawn"===e.look){let t=o.Z.svg(i),e=t.circle(0,0,14,k(r));(l=i.insert(()=>e)).attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else(l=i.insert("circle",":first-child")).attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return g(e,l),e.intersect=function(t){return j.circle(e,7,t)},i}async function tX(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),s=(e?.padding||0)/2,h=i.width+e.padding,c=i.height+e.padding,f=-i.width/2-s,p=-i.height/2-s,u=[{x:0,y:0},{x:h,y:0},{x:h,y:-c},{x:0,y:-c},{x:0,y:0},{x:-8,y:0},{x:h+8,y:0},{x:h+8,y:-c},{x:-8,y:-c},{x:-8,y:0}];if("handDrawn"===e.look){let t=o.Z.svg(r),a=W(e,{}),l=t.rectangle(f-8,p,h+16,c,a),i=t.line(f,p,f,p+c,a),s=t.line(f+h,p,f+h,p+c,a);r.insert(()=>i,":first-child"),r.insert(()=>s,":first-child");let d=r.insert(()=>l,":first-child"),{cssStyles:y}=e;d.attr("class","basic label-container").attr("style",(0,n.R7)(y)),g(e,d)}else{let t=q(r,h,c,u);l&&t.attr("style",l),g(e,t)}return e.intersect=function(t){return j.polygon(e,u,t)},r}async function tJ(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(i.width+(e.padding??0)*2,e?.width??0),s=Math.max(i.height+(e.padding??0)*2,e?.height??0),h=-n/2,c=-s/2,p=.2*s,{cssStyles:u}=e,m=o.Z.svg(r),x=W(e,{}),w=[{x:h-p/2,y:c},{x:h+n+p/2,y:c},{x:h+n+p/2,y:c+s},{x:h-p/2,y:c+s}];"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let b=f(w),$=m.path(b,x),k=f([{x:h+n-p/2,y:c+s},{x:h+n+p/2,y:c+s},{x:h+n+p/2,y:c+s-.2*s}]),M=m.path(k,{...x,fillStyle:"solid"}),S=r.insert(()=>M,":first-child");return S.insert(()=>$,":first-child"),S.attr("class","basic label-container"),u&&"handDrawn"!==e.look&&S.selectAll("path").attr("style",u),l&&"handDrawn"!==e.look&&S.selectAll("path").attr("style",l),g(e,S),e.intersect=function(t){let a=j.polygon(e,w,t);return a},r}async function tG(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/4,u=.2*s,m=.2*h,x=h+c,{cssStyles:w}=e,b=o.Z.svg(r),$=W(e,{});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=[{x:-s/2-s/2*.1,y:x/2},...p(-s/2-s/2*.1,x/2,s/2+s/2*.1,x/2,c,.8),{x:s/2+s/2*.1,y:-x/2},{x:-s/2-s/2*.1,y:-x/2}],M=-s/2+s/2*.1,S=-x/2-.4*m,v=[{x:M+s-u,y:(S+h)*1.4},{x:M+s,y:S+h-m},{x:M+s,y:(S+h)*.9},...p(M+s,(S+h)*1.3,M+s-u,(S+h)*1.5,-(.03*h),.5)],N=f(k),C=b.path(N,$),B=f(v),A=b.path(B,{...$,fillStyle:"solid"}),R=r.insert(()=>A,":first-child");return R.insert(()=>C,":first-child"),R.attr("class","basic label-container"),w&&"handDrawn"!==e.look&&R.selectAll("path").attr("style",w),l&&"handDrawn"!==e.look&&R.selectAll("path").attr("style",l),R.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-s/2+(e.padding??0)-(i.x-(i.left??0))},${-h/2+(e.padding??0)-c/2-(i.y-(i.top??0))})`),g(e,R),e.intersect=function(t){let a=j.polygon(e,k,t);return a},r}async function tU(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(i.width+e.padding,e?.width||0),s=Math.max(i.height+e.padding,e?.height||0),h=r.insert("rect",":first-child");return h.attr("class","text").attr("style",l).attr("rx",0).attr("ry",0).attr("x",-n/2).attr("y",-s/2).attr("width",n).attr("height",s),g(e,h),e.intersect=function(t){return j.rect(e,t)},r}(0,s.eW)(tT,"question"),(0,s.eW)(tE,"rect_left_inv_arrow"),(0,s.eW)(tH,"rectWithTitle"),(0,s.eW)(tV,"roundedRect"),(0,s.eW)(tj,"shadedProcess"),(0,s.eW)(t_,"slopedRect"),(0,s.eW)(tY,"squareRect"),(0,s.eW)(tz,"stadium"),(0,s.eW)(tq,"state"),(0,s.eW)(tO,"stateEnd"),(0,s.eW)(tF,"stateStart"),(0,s.eW)(tX,"subroutine"),(0,s.eW)(tJ,"taggedRect"),(0,s.eW)(tG,"taggedWaveEdgedRectangle"),(0,s.eW)(tU,"text");var tK=(0,s.eW)((t,e,a,l,r,i)=>`M${t},${e}
    a${r},${i} 0,0,1 0,${-l}
    l${a},0
    a${r},${i} 0,0,1 0,${l}
    M${a},${-l}
    a${r},${i} 0,0,0 0,${l}
    l${-a},0`,"createCylinderPathD"),tQ=(0,s.eW)((t,e,a,l,r,i)=>`M${t},${e} M${t+a},${e} a${r},${i} 0,0,0 0,${-l} l${-a},0 a${r},${i} 0,0,0 0,${l} l${a},0`,"createOuterCylinderPathD"),t0=(0,s.eW)((t,e,a,l,r,i)=>`M${t+a/2},${-l/2} a${r},${i} 0,0,0 0,${l}`,"createInnerCylinderPathD");async function t2(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:s,label:h,halfPadding:c}=await d(t,e,y(e)),f="neo"===e.look?2*c:c,p=s.height+f,u=p/2,m=u/(2.5+p/50),x=s.width+m+f,{cssStyles:w}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=tQ(0,0,x,p,m,u),r=t0(0,0,x,p,m,u),n=t.path(l,W(e,{})),s=t.path(r,W(e,{fill:"none"}));a=i.insert(()=>s,":first-child"),(a=i.insert(()=>n,":first-child")).attr("class","basic label-container"),w&&a.attr("style",w)}else{let t=tK(0,0,x,p,m,u);(a=i.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,n.R7)(w)).attr("style",r)).attr("class","basic label-container"),w&&a.selectAll("path").attr("style",w),r&&a.selectAll("path").attr("style",r)}return a.attr("label-offset-x",m),a.attr("transform",`translate(${-x/2}, ${p/2} )`),h.attr("transform",`translate(${-(s.width/2)-m-(s.x-(s.left??0))}, ${-(s.height/2)-(s.y-(s.top??0))})`),g(e,a),e.intersect=function(t){let a=j.rect(e,t),l=a.y-(e.y??0);if(0!=u&&(Math.abs(l)<(e.height??0)/2||Math.abs(l)==(e.height??0)/2&&Math.abs(a.x-(e.x??0))>(e.width??0)/2-m)){let r=m*m*(1-l*l/(u*u));0!=r&&(r=Math.sqrt(Math.abs(r))),r=m-r,t.x-(e.x??0)>0&&(r=-r),a.x+=r}return a},i}async function t1(t,e){let a;let{labelStyles:l,nodeStyles:r}=D(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=n.width+e.padding,h=n.height+e.padding,c=[{x:-3*h/6,y:0},{x:s+3*h/6,y:0},{x:s,y:-h},{x:0,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.Z.svg(i),l=W(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=q(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return j.polygon(e,c,t)},i}async function t5(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(60,i.width+(e.padding??0)*2,e?.width??0),s=Math.max(20,i.height+(e.padding??0)*2,e?.height??0),{cssStyles:h}=e,c=o.Z.svg(r),p=W(e,{});"handDrawn"!==e.look&&(p.roughness=0,p.fillStyle="solid");let u=[{x:-n/2*.8,y:-s/2},{x:n/2*.8,y:-s/2},{x:n/2,y:-s/2*.6},{x:n/2,y:s/2},{x:-n/2,y:s/2},{x:-n/2,y:-s/2*.6}],m=f(u),x=c.path(m,p),w=r.insert(()=>x,":first-child");return w.attr("class","basic label-container"),h&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",h),l&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",l),g(e,w),e.intersect=function(t){let a=j.polygon(e,u,t);return a},r}async function t8(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),h=(0,s.ku)(s.nV().flowchart?.htmlLabels),c=i.width+(e.padding??0),p=c+i.height,u=c+i.height,m=[{x:0,y:0},{x:u,y:0},{x:u/2,y:-p}],{cssStyles:x}=e,w=o.Z.svg(r),b=W(e,{});"handDrawn"!==e.look&&(b.roughness=0,b.fillStyle="solid");let $=f(m),k=w.path($,b),M=r.insert(()=>k,":first-child").attr("transform",`translate(${-p/2}, ${p/2})`);return x&&"handDrawn"!==e.look&&M.selectChildren("path").attr("style",x),l&&"handDrawn"!==e.look&&M.selectChildren("path").attr("style",l),e.width=c,e.height=p,g(e,M),n.attr("transform",`translate(${-i.width/2-(i.x-(i.left??0))}, ${p/2-(i.height+(e.padding??0)/(h?2:1)-(i.y-(i.top??0)))})`),e.intersect=function(t){return s.cM.info("Triangle intersect",e,m,t),j.polygon(e,m,t)},r}async function t4(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/8,u=h+c,{cssStyles:m}=e,x=70-s,w=x>0?x/2:0,b=o.Z.svg(r),$=W(e,{});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=[{x:-s/2-w,y:u/2},...p(-s/2-w,u/2,s/2+w,u/2,c,.8),{x:s/2+w,y:-u/2},{x:-s/2-w,y:-u/2}],M=f(k),S=b.path(M,$),v=r.insert(()=>S,":first-child");return v.attr("class","basic label-container"),m&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",m),l&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",l),v.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-s/2+(e.padding??0)-(i.x-(i.left??0))},${-h/2+(e.padding??0)-c-(i.y-(i.top??0))})`),g(e,v),e.intersect=function(t){let a=j.polygon(e,k,t);return a},r}async function t7(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(i.width+(e.padding??0)*2,e?.width??0),s=Math.max(i.height+(e.padding??0)*2,e?.height??0),h=n/s,c=n,u=s;c>u*h?u=c/h:c=u*h,c=Math.max(c,100),u=Math.max(u,50);let m=Math.min(.2*u,u/4),x=u+2*m,{cssStyles:w}=e,b=o.Z.svg(r),$=W(e,{});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=[{x:-c/2,y:x/2},...p(-c/2,x/2,c/2,x/2,m,1),{x:c/2,y:-x/2},...p(c/2,-x/2,-c/2,-x/2,m,-1)],M=f(k),S=b.path(M,$),v=r.insert(()=>S,":first-child");return v.attr("class","basic label-container"),w&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",w),l&&"handDrawn"!==e.look&&v.selectAll("path").attr("style",l),g(e,v),e.intersect=function(t){let a=j.polygon(e,k,t);return a},r}async function t3(t,e){let{labelStyles:a,nodeStyles:l}=D(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=-s/2,f=-h/2,{cssStyles:p}=e,u=o.Z.svg(r),m=W(e,{}),x=[{x:c-5,y:f-5},{x:c-5,y:f+h},{x:c+s,y:f+h},{x:c+s,y:f-5}],w=`M${c-5},${f-5} L${c+s},${f-5} L${c+s},${f+h} L${c-5},${f+h} L${c-5},${f-5}
                M${c-5},${f} L${c+s},${f}
                M${c},${f-5} L${c},${f+h}`;"handDrawn"!==e.look&&(m.roughness=0,m.fillStyle="solid");let b=u.path(w,m),$=r.insert(()=>b,":first-child");return $.attr("transform","translate(2.5, 2.5)"),$.attr("class","basic label-container"),p&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),n.attr("transform",`translate(${-(i.width/2)+2.5-(i.x-(i.left??0))}, ${-(i.height/2)+2.5-(i.y-(i.top??0))})`),g(e,$),e.intersect=function(t){let a=j.polygon(e,x,t);return a},r}async function t9(t,e,a,l,r=a.class.padding??12){let i=l?0:3,n=t.insert("g").attr("class",y(e)).attr("id",e.domId||e.id),s=null,h=null,o=null,d=null,c=0,g=0,f=0;if(s=n.insert("g").attr("class","annotation-group text"),e.annotations.length>0){let t=e.annotations[0];await t6(s,{text:`\xab${t}\xbb`},0);let a=s.node().getBBox();c=a.height}h=n.insert("g").attr("class","label-group text"),await t6(h,e,0,["font-weight: bolder"]);let p=h.node().getBBox();g=p.height,o=n.insert("g").attr("class","members-group text");let u=0;for(let t of e.members){let e=await t6(o,t,u,[t.parseClassifier()]);u+=e+i}(f=o.node().getBBox().height)<=0&&(f=r/2),d=n.insert("g").attr("class","methods-group text");let m=0;for(let t of e.methods){let e=await t6(d,t,m,[t.parseClassifier()]);m+=e+i}if(n.node().getBBox(),null!==s){let t=s.node().getBBox();s.attr("transform",`translate(${-t.width/2})`)}return h.attr("transform",`translate(${-p.width/2}, ${c})`),n.node().getBBox(),o.attr("transform",`translate(0, ${c+g+2*r})`),n.node().getBBox(),d.attr("transform",`translate(0, ${c+g+(f?f+4*r:2*r)})`),{shapeSvg:n,bbox:n.node().getBBox()}}async function t6(t,e,a,l=[]){let r;let o=t.insert("g").attr("class","label").attr("style",l.join("; ")),d=(0,s.iE)(),c="useHtmlLabels"in e?e.useHtmlLabels:(0,s.ku)(d.htmlLabels)??!0,g="";g="text"in e?e.text:e.label,!c&&g.startsWith("\\")&&(g=g.substring(1)),(0,s.l0)(g)&&(c=!0);let y=await (0,i.rw)(o,(0,s.uX)((0,n.SH)(g)),{width:(0,n.Cq)(g,d)+50,classes:"markdown-node-label",useHtmlLabels:c},d),f=1;if(c){let t=y.children[0],e=(0,h.Ys)(y);f=t.innerHTML.split("<br>").length,t.innerHTML.includes("</math>")&&(f+=t.innerHTML.split("<mrow>").length-1);let a=t.getElementsByTagName("img");if(a){let t=""===g.replace(/<img[^>]*>/g,"").trim();await Promise.all([...a].map(e=>new Promise(a=>{function l(){if(e.style.display="flex",e.style.flexDirection="column",t){let t=d.fontSize?.toString()??window.getComputedStyle(document.body).fontSize,a=5*parseInt(t,10)+"px";e.style.minWidth=a,e.style.maxWidth=a}else e.style.width="100%";a(e)}(0,s.eW)(l,"setupImage"),setTimeout(()=>{e.complete&&l()}),e.addEventListener("error",l),e.addEventListener("load",l)})))}r=t.getBoundingClientRect(),e.attr("width",r.width),e.attr("height",r.height)}else{l.includes("font-weight: bolder")&&(0,h.Ys)(y).selectAll("tspan").attr("font-weight",""),f=y.children.length;let t=y.children[0];if(""===y.textContent||y.textContent.includes("&gt")){t.textContent=g[0]+g.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim();let e=" "===g[1];e&&(t.textContent=t.textContent[0]+" "+t.textContent.substring(1))}"undefined"===t.textContent&&(t.textContent=""),r=y.getBBox()}return o.attr("transform","translate(0,"+(-r.height/(2*f)+a)+")"),r.height}async function et(t,e){let a=(0,s.nV)(),l=a.class.padding??12,r=e.useHtmlLabels??(0,s.ku)(a.htmlLabels)??!0,i=e;i.annotations=i.annotations??[],i.members=i.members??[],i.methods=i.methods??[];let{shapeSvg:n,bbox:d}=await t9(t,e,a,r,l),{labelStyles:c,nodeStyles:y}=D(e);e.labelStyle=c,e.cssStyles=i.styles||"";let f=i.styles?.join(";")||y||"";e.cssStyles||(e.cssStyles=f.replaceAll("!important","").split(";"));let p=0===i.members.length&&0===i.methods.length&&!a.class?.hideEmptyMembersBox,u=o.Z.svg(n),m=W(e,{});"handDrawn"!==e.look&&(m.roughness=0,m.fillStyle="solid");let x=d.width,w=d.height;0===i.members.length&&0===i.methods.length?w+=l:i.members.length>0&&0===i.methods.length&&(w+=2*l);let b=-x/2,$=-w/2,k=u.rectangle(b-l,$-l-(p?l:0===i.members.length&&0===i.methods.length?-l/2:0),x+2*l,w+2*l+(p?2*l:0===i.members.length&&0===i.methods.length?-l:0),m),M=n.insert(()=>k,":first-child");M.attr("class","basic label-container");let S=M.node().getBBox();n.selectAll(".text").each((t,e,a)=>{let s=(0,h.Ys)(a[e]),o=s.attr("transform"),d=0;if(o){let t=RegExp(/translate\(([^,]+),([^)]+)\)/),e=t.exec(o);e&&(d=parseFloat(e[2]))}let c=d+$+l-(p?l:0===i.members.length&&0===i.methods.length?-l/2:0);r||(c-=4);let g=b;(s.attr("class").includes("label-group")||s.attr("class").includes("annotation-group"))&&(g=-s.node()?.getBBox().width/2||0,n.selectAll("text").each(function(t,e,a){"middle"===window.getComputedStyle(a[e]).textAnchor&&(g=0)})),s.attr("transform",`translate(${g}, ${c})`)});let v=n.select(".annotation-group").node().getBBox().height-(p?l/2:0)||0,N=n.select(".label-group").node().getBBox().height-(p?l/2:0)||0,C=n.select(".members-group").node().getBBox().height-(p?l/2:0)||0;if(i.members.length>0||i.methods.length>0||p){let t=u.line(S.x,v+N+$+l,S.x+S.width,v+N+$+l,m),e=n.insert(()=>t);e.attr("class","divider").attr("style",f)}if(p||i.members.length>0||i.methods.length>0){let t=u.line(S.x,v+N+C+$+2*l+l,S.x+S.width,v+N+C+$+l+2*l,m),e=n.insert(()=>t);e.attr("class","divider").attr("style",f)}if("handDrawn"!==i.look&&n.selectAll("path").attr("style",f),M.select(":nth-child(2)").attr("style",f),n.selectAll(".divider").select("path").attr("style",f),e.labelStyle?n.selectAll("span").attr("style",e.labelStyle):n.selectAll("span").attr("style",f),!r){let t=RegExp(/color\s*:\s*([^;]*)/),e=t.exec(f);if(e){let t=e[0].replace("color","fill");n.selectAll("tspan").attr("style",t)}else if(c){let e=t.exec(c);if(e){let t=e[0].replace("color","fill");n.selectAll("tspan").attr("style",t)}}}return g(e,M),e.intersect=function(t){return j.rect(e,t)},n}(0,s.eW)(t2,"tiltedCylinder"),(0,s.eW)(t1,"trapezoid"),(0,s.eW)(t5,"trapezoidalPentagon"),(0,s.eW)(t8,"triangle"),(0,s.eW)(t4,"waveEdgedRectangle"),(0,s.eW)(t7,"waveRectangle"),(0,s.eW)(t3,"windowPane"),(0,s.eW)(t9,"textHelper"),(0,s.eW)(t6,"addText"),(0,s.eW)(et,"classBox");var ee=(0,s.eW)(t=>{switch(t){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function ea(t,e,{config:a}){let l,r,i,n;let{labelStyles:s,nodeStyles:h}=D(e);e.labelStyle=s||"";let f=e.width;e.width=(e.width??200)-10;let{shapeSvg:p,bbox:u,label:m}=await d(t,e,y(e)),x=e.padding||10,w="";"ticket"in e&&e.ticket&&a?.kanban?.ticketBaseUrl&&(w=a?.kanban?.ticketBaseUrl.replace("#TICKET#",e.ticket),l=p.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",w).attr("target","_blank"));let b={useHtmlLabels:e.useHtmlLabels,labelStyle:e.labelStyle||"",width:e.width,img:e.img,padding:e.padding||8,centerLabel:!1};l?{label:r,bbox:i}=await c(l,"ticket"in e&&e.ticket||"",b):{label:r,bbox:i}=await c(p,"ticket"in e&&e.ticket||"",b);let{label:k,bbox:M}=await c(p,"assigned"in e&&e.assigned||"",b);e.width=f;let S=e?.width||0,v=Math.max(i.height,M.height)/2,N=Math.max(u.height+20,e?.height||0)+v,C=-S/2,B=-N/2;m.attr("transform","translate("+(x-S/2)+", "+(-v-u.height/2)+")"),r.attr("transform","translate("+(x-S/2)+", "+(-v+u.height/2)+")"),k.attr("transform","translate("+(x+S/2-M.width-20)+", "+(-v+u.height/2)+")");let{rx:A,ry:R}=e,{cssStyles:L}=e;if("handDrawn"===e.look){let t=o.Z.svg(p),a=W(e,{}),l=A||R?t.path($(C,B,S,N,A||0),a):t.rectangle(C,B,S,N,a);(n=p.insert(()=>l,":first-child")).attr("class","basic label-container").attr("style",L||null)}else{(n=p.insert("rect",":first-child")).attr("class","basic label-container __APA__").attr("style",h).attr("rx",A??5).attr("ry",R??5).attr("x",C).attr("y",B).attr("width",S).attr("height",N);let t="priority"in e&&e.priority;if(t){let e=p.append("line"),a=C+2;e.attr("x1",a).attr("y1",B+Math.floor((A??0)/2)).attr("x2",a).attr("y2",B+N-Math.floor((A??0)/2)).attr("stroke-width","4").attr("stroke",ee(t))}}return g(e,n),e.height=N,e.intersect=function(t){return j.rect(e,t)},p}(0,s.eW)(ea,"kanbanItem");var el=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:tY},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:tV},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:tz},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:tX},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:ts},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:X},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:tT},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:tp},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:tW},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:tD},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:t1},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:tk},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:to},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:tU},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:O},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:tj},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:tF},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:tO},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:tg},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:tu},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:K},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:tt},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:ta},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:tv},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:t4},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:ty},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:t2},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:tA},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:tl},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:th},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:t8},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:t3},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:td},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:t5},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:tc},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:t_},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:tP},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:tL},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:z},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:G},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:tG},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:tJ},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:t7},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:tE},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:tR}],er=(0,s.eW)(()=>{let t=[...Object.entries({state:tq,choice:F,note:tZ,rectWithTitle:tH,labelRect:tS,iconSquare:tb,iconCircle:tx,icon:tm,iconRounded:tw,imageSquare:t$,anchor:_,kanbanItem:ea,classBox:et}),...el.flatMap(t=>{let e=[t.shortName,..."aliases"in t?t.aliases:[],..."internalAliases"in t?t.internalAliases:[]];return e.map(e=>[e,t.handler])})];return Object.fromEntries(t)},"generateShapeMap")();function ei(t){return t in er}(0,s.eW)(ei,"isValidShape");var en=new Map;async function es(t,e,a){let l,r;"rect"===e.shape&&(e.rx&&e.ry?e.shape="roundedRect":e.shape="squareRect");let i=e.shape?er[e.shape]:void 0;if(!i)throw Error(`No such shape: ${e.shape}. Please check your syntax.`);if(e.link){let n;"sandbox"===a.config.securityLevel?n="_top":e.linkTarget&&(n=e.linkTarget||"_blank"),l=t.insert("svg:a").attr("xlink:href",e.link).attr("target",n??null),r=await i(l,e,a)}else l=r=await i(t,e,a);return e.tooltip&&r.attr("title",e.tooltip),en.set(e.id,l),e.haveCallback&&l.attr("class",l.attr("class")+" clickable"),l}(0,s.eW)(es,"insertNode");var eh=(0,s.eW)((t,e)=>{en.set(e.id,t)},"setNodeElem"),eo=(0,s.eW)(()=>{en.clear()},"clear"),ed=(0,s.eW)(t=>{let e=en.get(t.id);s.cM.trace("Transforming node",t.diff,t,"translate("+(t.x-t.width/2-5)+", "+t.width/2+")");let a=t.diff||0;return t.clusterNode?e.attr("transform","translate("+(t.x+a-t.width/2)+", "+(t.y-t.height/2-8)+")"):e.attr("transform","translate("+t.x+", "+t.y+")"),a},"positionNode")}}]);