"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2491],{13881:function(e,t,r){var n,i;t.AU=t.Ts=void 0;let a=r(30147),s=r(67574),o=r(27135);(i=n||(t.Ts=n={})).None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:o.Event.None}),i.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:o.Event.None}),i.is=function(e){return e&&(e===i.None||e===i.Cancelled||s.boolean(e.isCancellationRequested)&&!!e.onCancellationRequested)};let l=Object.freeze(function(e,t){let r=(0,a.default)().timer.setTimeout(e.bind(t),0);return{dispose(){r.dispose()}}});class u{constructor(){this._isCancelled=!1}cancel(){!this._isCancelled&&(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?l:(this._emitter||(this._emitter=new o.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}t.AU=class{get token(){return this._token||(this._token=new u),this._token}cancel(){this._token?this._token.cancel():this._token=n.Cancelled}dispose(){this._token?this._token instanceof u&&this._token.dispose():this._token=n.None}}},27135:function(e,t,r){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=t.Event=void 0;let i=r(30147);!function(e){let t={dispose(){}};e.None=function(){return t}}(n||(t.Event=n={}));class a{add(e,t=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(r)&&r.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let r=!1;for(let n=0,i=this._callbacks.length;n<i;n++)if(this._callbacks[n]===e){if(this._contexts[n]===t){this._callbacks.splice(n,1),this._contexts.splice(n,1);return}r=!0}if(r)throw Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let t=[],r=this._callbacks.slice(0),n=this._contexts.slice(0);for(let a=0,s=r.length;a<s;a++)try{t.push(r[a].apply(n[a],e))}catch(e){(0,i.default)().console.error(e)}return t}isEmpty(){return!this._callbacks||0===this._callbacks.length}dispose(){this._callbacks=void 0,this._contexts=void 0}}class s{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,r)=>{this._callbacks||(this._callbacks=new a),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);let n={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),n.dispose=s._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(r)&&r.push(n),n}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}t.Emitter=s,s._noop=function(){}},67574:function(e,t){function r(e){return"string"==typeof e||e instanceof String}function n(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0,t.boolean=function(e){return!0===e||!1===e},t.string=r,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=n,t.stringArray=function(e){return n(e)&&e.every(e=>r(e))}},30147:function(e,t){let r;function n(){if(void 0===r)throw Error("No runtime abstraction layer installed");return r}Object.defineProperty(t,"__esModule",{value:!0}),(n||(n={})).install=function(e){if(void 0===e)throw Error("No runtime abstraction layer provided");r=e},t.default=n},11241:function(e,t,r){r.d(t,{H:function(){return s},M:function(){return o}});var n=r(96218),i=r(73571),a=class extends n.T7{static{(0,n.eW)(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}},s={parser:{TokenBuilder:(0,n.eW)(()=>new a,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new n.nr,"ValueConverter")}};function o(e=i.uZ){let t=(0,i.f3)((0,i.Jr)(e),n.GS),r=(0,i.f3)((0,i.Q)({shared:t}),n.F_,s);return t.ServiceRegistry.register(r),{shared:t,Info:r}}(0,n.eW)(o,"createInfoServices")},40225:function(e,t,r){r.d(t,{F:function(){return o},l:function(){return l}});var n=r(96218),i=r(73571),a=class extends n.T7{static{(0,n.eW)(this,"PieTokenBuilder")}constructor(){super(["pie","showData"])}},s=class extends n.kb{static{(0,n.eW)(this,"PieValueConverter")}runCustomConverter(e,t,r){if("PIE_SECTION_LABEL"===e.name)return t.replace(/"/g,"").trim()}},o={parser:{TokenBuilder:(0,n.eW)(()=>new a,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new s,"ValueConverter")}};function l(e=i.uZ){let t=(0,i.f3)((0,i.Jr)(e),n.GS),r=(0,i.f3)((0,i.Q)({shared:t}),n.WH,o);return t.ServiceRegistry.register(r),{shared:t,Pie:r}}(0,n.eW)(l,"createPieServices")},46520:function(e,t,r){r.d(t,{$:function(){return s},g:function(){return o}});var n=r(96218),i=r(73571),a=class extends n.T7{static{(0,n.eW)(this,"PacketTokenBuilder")}constructor(){super(["packet-beta"])}},s={parser:{TokenBuilder:(0,n.eW)(()=>new a,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new n.nr,"ValueConverter")}};function o(e=i.uZ){let t=(0,i.f3)((0,i.Jr)(e),n.GS),r=(0,i.f3)((0,i.Q)({shared:t}),n.bb,s);return t.ServiceRegistry.register(r),{shared:t,Packet:r}}(0,n.eW)(o,"createPacketServices")},25345:function(e,t,r){r.d(t,{i:function(){return l},s:function(){return o}});var n=r(96218),i=r(73571),a=class extends n.T7{static{(0,n.eW)(this,"ArchitectureTokenBuilder")}constructor(){super(["architecture"])}},s=class extends n.kb{static{(0,n.eW)(this,"ArchitectureValueConverter")}runCustomConverter(e,t,r){return"ARCH_ICON"===e.name?t.replace(/[()]/g,"").trim():"ARCH_TEXT_ICON"===e.name?t.replace(/["()]/g,""):"ARCH_TITLE"===e.name?t.replace(/[[\]]/g,"").trim():void 0}},o={parser:{TokenBuilder:(0,n.eW)(()=>new a,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new s,"ValueConverter")}};function l(e=i.uZ){let t=(0,i.f3)((0,i.Jr)(e),n.GS),r=(0,i.f3)((0,i.Q)({shared:t}),n.Qr,o);return t.ServiceRegistry.register(r),{shared:t,Architecture:r}}(0,n.eW)(l,"createArchitectureServices")},77599:function(e,t,r){r.d(t,{n:function(){return s},z:function(){return o}});var n=r(96218),i=r(73571),a=class extends n.T7{static{(0,n.eW)(this,"GitGraphTokenBuilder")}constructor(){super(["gitGraph"])}},s={parser:{TokenBuilder:(0,n.eW)(()=>new a,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new n.nr,"ValueConverter")}};function o(e=i.uZ){let t=(0,i.f3)((0,i.Jr)(e),n.GS),r=(0,i.f3)((0,i.Q)({shared:t}),n.vn,s);return t.ServiceRegistry.register(r),{shared:t,GitGraph:r}}(0,n.eW)(o,"createGitGraphServices")},96218:function(e,t,r){r.d(t,{F_:function(){return C},GS:function(){return N},Qr:function(){return w},T7:function(){return M},WH:function(){return L},bb:function(){return $},eW:function(){return c},kb:function(){return _},nr:function(){return P},vn:function(){return b}});var n,i,a,s,o,l=r(73571),u=Object.defineProperty,c=(e,t)=>u(e,"name",{value:t,configurable:!0});c(function(e){return g.isInstance(e,"Architecture")},"isArchitecture");var d="Branch";c(function(e){return g.isInstance(e,d)},"isBranch");var h="Commit";c(function(e){return g.isInstance(e,h)},"isCommit"),c(function(e){return g.isInstance(e,"Common")},"isCommon");var f="GitGraph";c(function(e){return g.isInstance(e,f)},"isGitGraph"),c(function(e){return g.isInstance(e,"Info")},"isInfo");var p="Merge";c(function(e){return g.isInstance(e,p)},"isMerge"),c(function(e){return g.isInstance(e,"Packet")},"isPacket"),c(function(e){return g.isInstance(e,"PacketBlock")},"isPacketBlock"),c(function(e){return g.isInstance(e,"Pie")},"isPie"),c(function(e){return g.isInstance(e,"PieSection")},"isPieSection");var m=class extends l.$v{static{c(this,"MermaidAstReflection")}getAllTypes(){return["Architecture","Branch","Checkout","CherryPicking","Commit","Common","Direction","Edge","GitGraph","Group","Info","Junction","Merge","Packet","PacketBlock","Pie","PieSection","Service","Statement"]}computeIsSubtype(e,t){switch(e){case d:case"Checkout":case"CherryPicking":case h:case p:return this.isSubtype("Statement",t);case"Direction":return this.isSubtype(f,t);default:return!1}}getReferenceType(e){let t=`${e.container.$type}:${e.property}`;throw Error(`${t} is not a valid reference id.`)}getTypeMetaData(e){switch(e){case"Architecture":return{name:"Architecture",properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case"Branch":return{name:"Branch",properties:[{name:"name"},{name:"order"}]};case"Checkout":return{name:"Checkout",properties:[{name:"branch"}]};case"CherryPicking":return{name:"CherryPicking",properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case"Commit":return{name:"Commit",properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case"Common":return{name:"Common",properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case"Edge":return{name:"Edge",properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case"GitGraph":return{name:"GitGraph",properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case"Group":return{name:"Group",properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case"Info":return{name:"Info",properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case"Junction":return{name:"Junction",properties:[{name:"id"},{name:"in"}]};case"Merge":return{name:"Merge",properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case"Packet":return{name:"Packet",properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case"PacketBlock":return{name:"PacketBlock",properties:[{name:"end"},{name:"label"},{name:"start"}]};case"Pie":return{name:"Pie",properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case"PieSection":return{name:"PieSection",properties:[{name:"label"},{name:"value"}]};case"Service":return{name:"Service",properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case"Direction":return{name:"Direction",properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},g=new m,y=c(()=>n??(n=(0,l.sC)('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","name":"Info","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"InfoGrammar"),T=c(()=>i??(i=(0,l.sC)(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","name":"Packet","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),v=c(()=>a??(a=(0,l.sC)('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","name":"Pie","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"PieGrammar"),E=c(()=>s??(s=(0,l.sC)('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","name":"Architecture","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","fragment":true,"definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"LeftPort","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"RightPort","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Arrow","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"ArchitectureGrammar"),R=c(()=>o??(o=(0,l.sC)(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","name":"TitleAndAccessibilities","fragment":true,"definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"EOL","fragment":true,"dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","name":"GitGraph","entry":true,"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),A={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},k={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},I={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},x={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},S={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1},N={AstReflection:c(()=>new m,"AstReflection")},C={Grammar:c(()=>y(),"Grammar"),LanguageMetaData:c(()=>A,"LanguageMetaData"),parser:{}},$={Grammar:c(()=>T(),"Grammar"),LanguageMetaData:c(()=>k,"LanguageMetaData"),parser:{}},L={Grammar:c(()=>v(),"Grammar"),LanguageMetaData:c(()=>I,"LanguageMetaData"),parser:{}},w={Grammar:c(()=>E(),"Grammar"),LanguageMetaData:c(()=>x,"LanguageMetaData"),parser:{}},b={Grammar:c(()=>R(),"Grammar"),LanguageMetaData:c(()=>S,"LanguageMetaData"),parser:{}},O={ACC_DESCR:/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ACC_TITLE:/accTitle[\t ]*:([^\n\r]*)/,TITLE:/title([\t ][^\n\r]*|)/},_=class extends l.tI{static{c(this,"AbstractMermaidValueConverter")}runConverter(e,t,r){let n=this.runCommonConverter(e,t,r);return(void 0===n&&(n=this.runCustomConverter(e,t,r)),void 0===n)?super.runConverter(e,t,r):n}runCommonConverter(e,t,r){let n=O[e.name];if(void 0===n)return;let i=n.exec(t);return null===i?void 0:void 0!==i[1]?i[1].trim().replace(/[\t ]{2,}/gm," "):void 0!==i[2]?i[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,"\n"):void 0}},P=class extends _{static{c(this,"CommonValueConverter")}runCustomConverter(e,t,r){}},M=class extends l.PH{static{c(this,"AbstractMermaidTokenBuilder")}constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,r){let n=super.buildKeywordTokens(e,t,r);return n.forEach(e=>{this.keywords.has(e.name)&&void 0!==e.PATTERN&&(e.PATTERN=RegExp(e.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),n}}},12491:function(e,t,r){r.d(t,{Qc:function(){return s}}),r(77599),r(11241),r(46520),r(40225),r(25345);var n=r(96218),i={},a={info:(0,n.eW)(async()=>{let{createInfoServices:e}=await r.e(5270).then(r.bind(r,65270)),t=e().Info.parser.LangiumParser;i.info=t},"info"),packet:(0,n.eW)(async()=>{let{createPacketServices:e}=await r.e(5705).then(r.bind(r,65705)),t=e().Packet.parser.LangiumParser;i.packet=t},"packet"),pie:(0,n.eW)(async()=>{let{createPieServices:e}=await r.e(1425).then(r.bind(r,11425)),t=e().Pie.parser.LangiumParser;i.pie=t},"pie"),architecture:(0,n.eW)(async()=>{let{createArchitectureServices:e}=await r.e(7035).then(r.bind(r,67035)),t=e().Architecture.parser.LangiumParser;i.architecture=t},"architecture"),gitGraph:(0,n.eW)(async()=>{let{createGitGraphServices:e}=await r.e(288).then(r.bind(r,90288)),t=e().GitGraph.parser.LangiumParser;i.gitGraph=t},"gitGraph")};async function s(e,t){let r=a[e];if(!r)throw Error(`Unknown diagram type: ${e}`);i[e]||await r();let n=i[e],s=n.parse(t);if(s.lexerErrors.length>0||s.parserErrors.length>0)throw new o(s);return s.value}(0,n.eW)(s,"parse");var o=class extends Error{constructor(e){let t=e.lexerErrors.map(e=>e.message).join("\n"),r=e.parserErrors.map(e=>e.message).join("\n");super(`Parsing failed: ${t} ${r}`),this.result=e}static{(0,n.eW)(this,"MermaidParseError")}}},73571:function(e,t,r){function n(e){return"object"==typeof e&&null!==e&&"string"==typeof e.$type}function i(e){return"object"==typeof e&&null!==e&&"string"==typeof e.$refText}function a(e){return"object"==typeof e&&null!==e&&n(e.container)&&i(e.reference)&&"string"==typeof e.message}r.d(t,{$v:function(){return s},PH:function(){return oU},tI:function(){return oF},uZ:function(){return lX},Q:function(){return lB},Jr:function(){return lK},f3:function(){return lj},sC:function(){return l0}});class s{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return n(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let r=this.subtypes[e];r||(r=this.subtypes[e]={});let n=r[t];if(void 0!==n)return n;{let n=this.computeIsSubtype(e,t);return r[t]=n,n}}getAllSubTypes(e){let t=this.allSubtypes[e];if(t)return t;{let t=this.getAllTypes(),r=[];for(let n of t)this.isSubtype(n,e)&&r.push(n);return this.allSubtypes[e]=r,r}}}function o(e){return"object"==typeof e&&null!==e&&Array.isArray(e.content)}function l(e){return"object"==typeof e&&null!==e&&"object"==typeof e.tokenType}function u(e){return o(e)&&"string"==typeof e.fullText}class c{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){let e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){let e=this.iterator();return Boolean(e.next().done)}count(){let e=this.iterator(),t=0,r=e.next();for(;!r.done;)t++,r=e.next();return t}toArray(){let e;let t=[],r=this.iterator();do void 0!==(e=r.next()).value&&t.push(e.value);while(!e.done);return t}toSet(){return new Set(this)}toMap(e,t){let r=this.map(r=>[e?e(r):r,t?t(r):r]);return new Map(r)}toString(){return this.join()}concat(e){let t=e[Symbol.iterator]();return new c(()=>({first:this.startFn(),firstDone:!1}),e=>{let r;if(!e.firstDone){do if(!(r=this.nextFn(e.first)).done)return r;while(!r.done);e.firstDone=!0}do if(!(r=t.next()).done)return r;while(!r.done);return f})}join(e=","){let t;let r=this.iterator(),n="",i=!1;do{var a;(t=r.next()).done||(i&&(n+=e),n+="string"==typeof(a=t.value)?a:void 0===a?"undefined":"function"==typeof a.toString?a.toString():Object.prototype.toString.call(a)),i=!0}while(!t.done);return n}indexOf(e,t=0){let r=this.iterator(),n=0,i=r.next();for(;!i.done;){if(n>=t&&i.value===e)return n;i=r.next(),n++}return -1}every(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(!e(r.value))return!1;r=t.next()}return!0}some(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(e(r.value))return!0;r=t.next()}return!1}forEach(e){let t=this.iterator(),r=0,n=t.next();for(;!n.done;)e(n.value,r),n=t.next(),r++}map(e){return new c(this.startFn,t=>{let{done:r,value:n}=this.nextFn(t);return r?f:{done:!1,value:e(n)}})}filter(e){return new c(this.startFn,t=>{let r;do if(!(r=this.nextFn(t)).done&&e(r.value))return r;while(!r.done);return f})}nonNullable(){return this.filter(e=>null!=e)}reduce(e,t){let r=this.iterator(),n=t,i=r.next();for(;!i.done;)n=void 0===n?i.value:e(n,i.value),i=r.next();return n}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,r){let n=e.next();if(n.done)return r;let i=this.recursiveReduce(e,t,r);return void 0===i?n.value:t(i,n.value)}find(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(e(r.value))return r.value;r=t.next()}}findIndex(e){let t=this.iterator(),r=0,n=t.next();for(;!n.done;){if(e(n.value))return r;n=t.next(),r++}return -1}includes(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(r.value===e)return!0;r=t.next()}return!1}flatMap(e){return new c(()=>({this:this.startFn()}),t=>{do{if(t.iterator){let e=t.iterator.next();if(!e.done)return e;t.iterator=void 0}let{done:r,value:n}=this.nextFn(t.this);if(!r){let r=e(n);if(!d(r))return{done:!1,value:r};t.iterator=r[Symbol.iterator]()}}while(t.iterator);return f})}flat(e){if(void 0===e&&(e=1),e<=0)return this;let t=e>1?this.flat(e-1):this;return new c(()=>({this:t.startFn()}),e=>{do{if(e.iterator){let t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}let{done:r,value:n}=t.nextFn(e.this);if(!r){if(!d(n))return{done:!1,value:n};e.iterator=n[Symbol.iterator]()}}while(e.iterator);return f})}head(){let e=this.iterator(),t=e.next();if(!t.done)return t.value}tail(e=1){return new c(()=>{let t=this.startFn();for(let r=0;r<e;r++){let e=this.nextFn(t);if(e.done)break}return t},this.nextFn)}limit(e){return new c(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e)?f:this.nextFn(t.state))}distinct(e){let t=new Set;return this.filter(r=>{let n=e?e(r):r;return!t.has(n)&&(t.add(n),!0)})}exclude(e,t){let r=new Set;for(let n of e){let e=t?t(n):n;r.add(e)}return this.filter(e=>{let n=t?t(e):e;return!r.has(n)})}}function d(e){return!!e&&"function"==typeof e[Symbol.iterator]}let h=new c(()=>void 0,()=>f),f=Object.freeze({done:!0,value:void 0});function p(...e){if(1===e.length){let t=e[0];if(t instanceof c)return t;if(d(t))return new c(()=>t[Symbol.iterator](),e=>e.next());if("number"==typeof t.length)return new c(()=>({index:0}),e=>e.index<t.length?{done:!1,value:t[e.index++]}:f)}return e.length>1?new c(()=>({collIndex:0,arrIndex:0}),t=>{do{if(t.iterator){let e=t.iterator.next();if(!e.done)return e;t.iterator=void 0}if(t.array){if(t.arrIndex<t.array.length)return{done:!1,value:t.array[t.arrIndex++]};t.array=void 0,t.arrIndex=0}if(t.collIndex<e.length){let r=e[t.collIndex++];d(r)?t.iterator=r[Symbol.iterator]():r&&"number"==typeof r.length&&(t.array=r)}}while(t.iterator||t.array||t.collIndex<e.length);return f}):h}class m extends c{constructor(e,t,r){super(()=>({iterators:(null==r?void 0:r.includeRoot)?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),e=>{for(e.pruned&&(e.iterators.pop(),e.pruned=!1);e.iterators.length>0;){let r=e.iterators[e.iterators.length-1],n=r.next();if(!n.done)return e.iterators.push(t(n.value)[Symbol.iterator]()),n;e.iterators.pop()}return f})}iterator(){let e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}function g(e){return new m(e,e=>o(e)?e.content:[],{includeRoot:!0})}function y(e){return{start:{character:e.startColumn-1,line:e.startLine-1},end:{character:e.endColumn,line:e.endLine-1}}}function T(e){if(!e)return;let{offset:t,end:r,range:n}=e;return{range:n,offset:t,end:r,length:r-t}}(t3=t4||(t4={})).sum=function(e){return e.reduce((e,t)=>e+t,0)},t3.product=function(e){return e.reduce((e,t)=>e*t,0)},t3.min=function(e){return e.reduce((e,t)=>Math.min(e,t))},t3.max=function(e){return e.reduce((e,t)=>Math.max(e,t))},(t7=t5||(t5={}))[t7.Before=0]="Before",t7[t7.After=1]="After",t7[t7.OverlapFront=2]="OverlapFront",t7[t7.OverlapBack=3]="OverlapBack",t7[t7.Inside=4]="Inside";let v=/^[\w\p{L}]$/u;function E(e,t){return l(e)&&t.includes(e.tokenType.name)}class R extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function A(e){throw Error("Error! The input value was not handled.")}let k="AbstractRule",I="AbstractType",x="Condition",S="ValueLiteral",N="AbstractElement",C="BooleanLiteral",$="Conjunction",L="Disjunction",w="InferredType";function b(e){return ey.isInstance(e,w)}let O="Interface";function _(e){return ey.isInstance(e,O)}let P="Negation",M="ParameterReference",D="ParserRule";function Z(e){return ey.isInstance(e,D)}let U="SimpleType",F="TerminalRule";function G(e){return ey.isInstance(e,F)}let B="Type";function K(e){return ey.isInstance(e,B)}let j="Action";function V(e){return ey.isInstance(e,j)}let W="Alternatives";function H(e){return ey.isInstance(e,W)}let z="Assignment";function Y(e){return ey.isInstance(e,z)}let q="CharacterRange",X="CrossReference";function Q(e){return ey.isInstance(e,X)}let J="EndOfFile",ee="Group";function et(e){return ey.isInstance(e,ee)}let er="Keyword";function en(e){return ey.isInstance(e,er)}let ei="NegatedToken",ea="RegexToken",es="RuleCall";function eo(e){return ey.isInstance(e,es)}let el="TerminalAlternatives",eu="TerminalGroup",ec="TerminalRuleCall";function ed(e){return ey.isInstance(e,ec)}let eh="UnorderedGroup";function ef(e){return ey.isInstance(e,eh)}let ep="UntilToken",em="Wildcard";class eg extends s{getAllTypes(){return["AbstractElement","AbstractRule","AbstractType","Action","Alternatives","ArrayLiteral","ArrayType","Assignment","BooleanLiteral","CharacterRange","Condition","Conjunction","CrossReference","Disjunction","EndOfFile","Grammar","GrammarImport","Group","InferredType","Interface","Keyword","NamedArgument","NegatedToken","Negation","NumberLiteral","Parameter","ParameterReference","ParserRule","ReferenceType","RegexToken","ReturnType","RuleCall","SimpleType","StringLiteral","TerminalAlternatives","TerminalGroup","TerminalRule","TerminalRuleCall","Type","TypeAttribute","TypeDefinition","UnionType","UnorderedGroup","UntilToken","ValueLiteral","Wildcard"]}computeIsSubtype(e,t){switch(e){case j:case W:case z:case q:case X:case J:case ee:case er:case ei:case ea:case es:case el:case eu:case ec:case eh:case ep:case em:return this.isSubtype(N,t);case"ArrayLiteral":case"NumberLiteral":case"StringLiteral":return this.isSubtype(S,t);case"ArrayType":case"ReferenceType":case U:case"UnionType":return this.isSubtype("TypeDefinition",t);case C:return this.isSubtype(x,t)||this.isSubtype(S,t);case $:case L:case P:case M:return this.isSubtype(x,t);case w:case O:case B:return this.isSubtype(I,t);case D:return this.isSubtype(k,t)||this.isSubtype(I,t);case F:return this.isSubtype(k,t);default:return!1}}getReferenceType(e){let t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return I;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return k;case"Grammar:usedGrammars":return"Grammar";case"NamedArgument:parameter":case"ParameterReference:parameter":return"Parameter";case"TerminalRuleCall:rule":return F;default:throw Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case"AbstractElement":return{name:"AbstractElement",properties:[{name:"cardinality"},{name:"lookahead"}]};case"ArrayLiteral":return{name:"ArrayLiteral",properties:[{name:"elements",defaultValue:[]}]};case"ArrayType":return{name:"ArrayType",properties:[{name:"elementType"}]};case"BooleanLiteral":return{name:"BooleanLiteral",properties:[{name:"true",defaultValue:!1}]};case"Conjunction":return{name:"Conjunction",properties:[{name:"left"},{name:"right"}]};case"Disjunction":return{name:"Disjunction",properties:[{name:"left"},{name:"right"}]};case"Grammar":return{name:"Grammar",properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case"GrammarImport":return{name:"GrammarImport",properties:[{name:"path"}]};case"InferredType":return{name:"InferredType",properties:[{name:"name"}]};case"Interface":return{name:"Interface",properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case"NamedArgument":return{name:"NamedArgument",properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case"Negation":return{name:"Negation",properties:[{name:"value"}]};case"NumberLiteral":return{name:"NumberLiteral",properties:[{name:"value"}]};case"Parameter":return{name:"Parameter",properties:[{name:"name"}]};case"ParameterReference":return{name:"ParameterReference",properties:[{name:"parameter"}]};case"ParserRule":return{name:"ParserRule",properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case"ReferenceType":return{name:"ReferenceType",properties:[{name:"referenceType"}]};case"ReturnType":return{name:"ReturnType",properties:[{name:"name"}]};case"SimpleType":return{name:"SimpleType",properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case"StringLiteral":return{name:"StringLiteral",properties:[{name:"value"}]};case"TerminalRule":return{name:"TerminalRule",properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case"Type":return{name:"Type",properties:[{name:"name"},{name:"type"}]};case"TypeAttribute":return{name:"TypeAttribute",properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case"UnionType":return{name:"UnionType",properties:[{name:"types",defaultValue:[]}]};case"Action":return{name:"Action",properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case"Alternatives":return{name:"Alternatives",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"Assignment":return{name:"Assignment",properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case"CharacterRange":return{name:"CharacterRange",properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case"CrossReference":return{name:"CrossReference",properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case"EndOfFile":return{name:"EndOfFile",properties:[{name:"cardinality"},{name:"lookahead"}]};case"Group":return{name:"Group",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case"Keyword":return{name:"Keyword",properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case"NegatedToken":return{name:"NegatedToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case"RegexToken":return{name:"RegexToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case"RuleCall":return{name:"RuleCall",properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case"TerminalAlternatives":return{name:"TerminalAlternatives",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"TerminalGroup":return{name:"TerminalGroup",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"TerminalRuleCall":return{name:"TerminalRuleCall",properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case"UnorderedGroup":return{name:"UnorderedGroup",properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case"UntilToken":return{name:"UntilToken",properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case"Wildcard":return{name:"Wildcard",properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}let ey=new eg;function eT(e,t){let r=e;for(;r;){if(t(r))return r;r=r.$container}}function ev(e){let t=function(e){for(;e.$container;)e=e.$container;return e}(e),r=t.$document;if(!r)throw Error("AST node has no document.");return r}function eE(e,t){if(!e)throw Error("Node must be an AstNode.");let r=null==t?void 0:t.range;return new c(()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0}),t=>{for(;t.keyIndex<t.keys.length;){let i=t.keys[t.keyIndex];if(!i.startsWith("$")){let a=e[i];if(n(a)){if(t.keyIndex++,ek(a,r))return{done:!1,value:a}}else if(Array.isArray(a)){for(;t.arrayIndex<a.length;){let e=t.arrayIndex++,i=a[e];if(n(i)&&ek(i,r))return{done:!1,value:i}}t.arrayIndex=0}}t.keyIndex++}return f})}function eR(e,t){if(!e)throw Error("Root node must be an AstNode.");return new m(e,e=>eE(e,t))}function eA(e,t){if(e){if((null==t?void 0:t.range)&&!ek(e,t.range))return new m(e,()=>[])}else throw Error("Root node must be an AstNode.");return new m(e,e=>eE(e,t),{includeRoot:!0})}function ek(e,t){var r;if(!t)return!0;let n=null===(r=e.$cstNode)||void 0===r?void 0:r.range;return!!n&&function(e,t){let r=function(e,t){if(e.end.line<t.start.line||e.end.line===t.start.line&&e.end.character<e.start.character)return t5.Before;if(e.start.line>t.end.line||e.start.line===t.end.line&&e.start.character>t.end.character)return t5.After;let r=e.start.line>t.start.line||e.start.line===t.start.line&&e.start.character>=t.start.character,n=e.end.line<t.end.line||e.end.line===t.end.line&&e.end.character<=t.end.character;return r&&n?t5.Inside:r?t5.OverlapBack:t5.OverlapFront}(e,t);return r>t5.After}(n,t)}function eI(e){return new c(()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0}),t=>{for(;t.keyIndex<t.keys.length;){let r=t.keys[t.keyIndex];if(!r.startsWith("$")){let n=e[r];if(i(n))return t.keyIndex++,{done:!1,value:{reference:n,container:e,property:r}};if(Array.isArray(n)){for(;t.arrayIndex<n.length;){let a=t.arrayIndex++,s=n[a];if(i(s))return{done:!1,value:{reference:s,container:e,property:r,index:a}}}t.arrayIndex=0}}t.keyIndex++}return f})}function ex(e){return e.charCodeAt(0)}function eS(e,t){Array.isArray(e)?e.forEach(function(e){t.push(e)}):t.push(e)}function eN(e,t){if(!0===e[t])throw"duplicate flag "+t;e[t],e[t]=!0}function eC(e){if(void 0===e)throw Error("Internal Error - Should never get here!");return!0}function e$(){throw Error("Internal Error - Should never get here!")}function eL(e){return"Character"===e.type}let ew=[];for(let e=ex("0");e<=ex("9");e++)ew.push(e);let eb=[ex("_")].concat(ew);for(let e=ex("a");e<=ex("z");e++)eb.push(e);for(let e=ex("A");e<=ex("Z");e++)eb.push(e);let eO=[ex(" "),ex("\f"),ex("\n"),ex("\r"),ex("	"),ex("\v"),ex("	"),ex("\xa0"),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex(" "),ex("\u2028"),ex("\u2029"),ex(" "),ex(" "),ex("　"),ex("\uFEFF")],e_=/[0-9a-fA-F]/,eP=/[0-9]/,eM=/[1-9]/;class eD{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");let t=this.disjunction();this.consumeChar("/");let r={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":eN(r,"global");break;case"i":eN(r,"ignoreCase");break;case"m":eN(r,"multiLine");break;case"u":eN(r,"unicode");break;case"y":eN(r,"sticky")}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:r,value:t,loc:this.loc(0)}}disjunction(){let e=[],t=this.idx;for(e.push(this.alternative());"|"===this.peekChar();)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){let e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){let e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":let t;switch(this.consumeChar("?"),this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead"}eC(t);let r=this.disjunction();return this.consumeChar(")"),{type:t,value:r,loc:this.loc(e)}}return e$()}quantifier(e=!1){let t;let r=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":let n=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:n,atMost:n};break;case",":t=this.isDigit()?{atLeast:n,atMost:this.integerIncludingZero()}:{atLeast:n,atMost:1/0},this.consumeChar("}")}if(!0===e&&void 0===t)return;eC(t)}if((!0!==e||void 0!==t)&&eC(t))return"?"===this.peekChar(0)?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(r),t}atom(){let e;let t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group()}return(void 0===e&&this.isPatternCharacter()&&(e=this.patternCharacter()),eC(e))?(e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e):e$()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[ex("\n"),ex("\r"),ex("\u2028"),ex("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){let e=this.positiveInteger();return{type:"GroupBackReference",value:e}}characterClassEscape(){let e;let t=!1;switch(this.popChar()){case"d":e=ew;break;case"D":e=ew,t=!0;break;case"s":e=eO;break;case"S":e=eO,t=!0;break;case"w":e=eb;break;case"W":e=eb,t=!0}return eC(e)?{type:"Set",value:e,complement:t}:e$()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=ex("\f");break;case"n":e=ex("\n");break;case"r":e=ex("\r");break;case"t":e=ex("	");break;case"v":e=ex("\v")}return eC(e)?{type:"Character",value:e}:e$()}controlLetterEscapeAtom(){this.consumeChar("c");let e=this.popChar();if(!1===/[a-zA-Z]/.test(e))throw Error("Invalid ");let t=e.toUpperCase().charCodeAt(0)-64;return{type:"Character",value:t}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:ex("\x00")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){let e=this.popChar();return{type:"Character",value:ex(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case"\n":case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:let e=this.popChar();return{type:"Character",value:ex(e)}}}characterClass(){let e=[],t=!1;for(this.consumeChar("["),"^"===this.peekChar(0)&&(this.consumeChar("^"),t=!0);this.isClassAtom();){let t=this.classAtom();if(t.type,eL(t)&&this.isRangeDash()){this.consumeChar("-");let r=this.classAtom();if(r.type,eL(r)){if(r.value<t.value)throw Error("Range out of order in character class");e.push({from:t.value,to:r.value})}else eS(t.value,e),e.push(ex("-")),eS(r.value,e)}else eS(t.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case"\n":case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:ex("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;(this.consumeChar("("),"?"===this.peekChar(0))?(this.consumeChar("?"),this.consumeChar(":"),e=!1):this.groupIdx++;let t=this.disjunction();this.consumeChar(")");let r={type:"Group",capturing:e,value:t};return e&&(r.idx=this.groupIdx),r}positiveInteger(){let e=this.popChar();if(!1===eM.test(e))throw Error("Expecting a positive integer");for(;eP.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(!1===eP.test(e))throw Error("Expecting an integer");for(;eP.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){let e=this.popChar();switch(e){case"\n":case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:ex(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return"-"===this.peekChar()&&this.isClassAtom(1)}isDigit(){return eP.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return"?"===this.peekChar(1)&&("="===this.peekChar(2)||"!"===this.peekChar(2));default:return!1}}isQuantifier(){let e=this.saveState();try{return void 0!==this.quantifier(!0)}catch(e){return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let r=0;r<e;r++){let e=this.popChar();if(!1===e_.test(e))throw Error("Expecting a HexDecimal digits");t+=e}let r=parseInt(t,16);return{type:"Character",value:r}}peekChar(e=0){return this.input[this.idx+e]}popChar(){let e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(void 0!==e&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class eZ{visitChildren(e){for(let t in e){let r=e[t];e.hasOwnProperty(t)&&(void 0!==r.type?this.visit(r):Array.isArray(r)&&r.forEach(e=>{this.visit(e)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e)}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}let eU=/\r?\n/gm,eF=new eD,eG=new class extends eZ{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){let t=String.fromCharCode(e.value);if(this.multiline||"\n"!==t||(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let e=eK(t);this.endRegexpStack.push(e),this.isStarting&&(this.startRegexp+=e)}}visitSet(e){if(!this.multiline){let t=this.regex.substring(e.loc.begin,e.loc.end),r=RegExp(t);this.multiline=Boolean("\n".match(r))}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){"Group"===e.type&&e.quantifier||super.visitChildren(e)}};function eB(e){let t="string"==typeof e?RegExp(e):e;return t.test(" ")}function eK(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ej(e,t){let r=new Set,n=e.rules.find(e=>Z(e)&&e.entry);if(!n)return new Set(e.rules);let i=[n].concat(e.rules.filter(e=>G(e)&&e.hidden));for(let e of i)!function e(t,r,n){r.add(t.name),eR(t).forEach(t=>{if(eo(t)||n&&ed(t)){let i=t.rule.ref;i&&!r.has(i.name)&&e(i,r,n)}})}(e,r,t);let a=new Set;for(let t of e.rules)(r.has(t.name)||G(t)&&t.hidden)&&a.add(t);return a}function eV(e,t,r){if(!e||!t)return;let n=eW(e,t,e.astNode,!0);if(0!==n.length)return r=void 0!==r?Math.max(0,Math.min(r,n.length-1)):0,n[r]}function eW(e,t,r,n){if(!n){let r=eT(e.grammarSource,Y);if(r&&r.feature===t)return[e]}return o(e)&&e.astNode===r?e.content.flatMap(e=>eW(e,t,r,!1)):[]}function eH(e){let t=e;return b(t)&&(V(t.$container)?t=t.$container.$container:Z(t.$container)?t=t.$container:A(t.$container)),function e(t,r,n){var i,a;function s(r,i){let a;let s=eT(r,Y);return s||(a=e(i,i,n)),n.set(t,a),a}if(n.has(t))return n.get(t);for(let e of(n.set(t,void 0),eR(r))){if(Y(e)&&"name"===e.feature.toLowerCase())return n.set(t,e),e;if(eo(e)&&Z(e.rule.ref))return s(e,e.rule.ref);if(a=e,ey.isInstance(a,U)&&(null===(i=e.typeRef)||void 0===i?void 0:i.ref))return s(e,e.typeRef.ref)}}(e,t,new Map)}function ez(e){return function e(t,r){if(r.has(t))return!0;for(let n of(r.add(t),eR(t)))if(eo(n)){if(!n.rule.ref||Z(n.rule.ref)&&!e(n.rule.ref,r))return!1}else if(Y(n))return!1;else if(V(n))return!1;return Boolean(t.definition)}(e,new Set)}function eY(e){if(e.inferredType)return e.inferredType.name;if(e.dataType)return e.dataType;if(e.returnType){let t=e.returnType.ref;if(t&&(Z(t)||_(t)||K(t)))return t.name}}function eq(e){var t,r;if(Z(e))return ez(e)?e.name:null!==(t=eY(e))&&void 0!==t?t:e.name;if(_(e)||K(e)||ey.isInstance(e,"ReturnType"))return e.name;if(V(e)){let t=e.inferredType?e.inferredType.name:(null===(r=e.type)||void 0===r?void 0:r.ref)?eq(e.type.ref):void 0;if(t)return t}else if(b(e))return e.name;throw Error("Cannot get name of Unknown Type")}function eX(e){let t={s:!1,i:!1,u:!1},r=function e(t,r){if(ey.isInstance(t,el))return e0(t.elements.map(t=>e(t)).join("|"),{cardinality:t.cardinality,lookahead:t.lookahead});if(ey.isInstance(t,eu))return e0(t.elements.map(t=>e(t)).join(""),{cardinality:t.cardinality,lookahead:t.lookahead});if(ey.isInstance(t,q))return t.right?e0(`[${eJ(t.left)}-${eJ(t.right)}]`,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1}):e0(eJ(t.left),{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1});if(ed(t)){let r=t.rule.ref;if(!r)throw Error("Missing rule reference.");return e0(e(r.definition),{cardinality:t.cardinality,lookahead:t.lookahead})}if(ey.isInstance(t,ei))return e0(`(?!${e(t.terminal)})${eQ}*?`,{cardinality:t.cardinality,lookahead:t.lookahead});if(ey.isInstance(t,ep))return e0(`${eQ}*?${e(t.terminal)}`,{cardinality:t.cardinality,lookahead:t.lookahead});if(ey.isInstance(t,ea)){let e=t.regex.lastIndexOf("/"),n=t.regex.substring(1,e),i=t.regex.substring(e+1);return r&&(r.i=i.includes("i"),r.s=i.includes("s"),r.u=i.includes("u")),e0(n,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}if(ey.isInstance(t,em))return e0(eQ,{cardinality:t.cardinality,lookahead:t.lookahead});throw Error(`Invalid terminal element: ${null==t?void 0:t.$type}`)}(e.definition,t),n=Object.entries(t).filter(([,e])=>e).map(([e])=>e).join("");return RegExp(r,n)}let eQ=/[\s\S]/.source;function eJ(e){return eK(e.value)}function e0(e,t){var r;return((!1!==t.wrap||t.lookahead)&&(e=`(${null!==(r=t.lookahead)&&void 0!==r?r:""}${e})`),t.cardinality)?`${e}${t.cardinality}`:e}var e1,e2,e3,e7,e4,e5,e6,e9,e8,te,tt,tr,tn,ti,ta,ts,to,tl,tu,tc,td,th,tf,tp,tm,tg,ty,tT,tv,tE,tR,tA,tk,tI,tx,tS,tN,tC,t$,tL,tw,tb,tO,t_,tP,tM,tD,tZ,tU,tF,tG,tB,tK,tj,tV,tW,tH,tz,tY,tq,tX,tQ,tJ,t0,t1,t2,t3,t7,t4,t5,t6,t9,t8,re,rt,rr,rn,ri,ra,rs,ro,rl,ru,rc,rd,rh,rf,rp,rm,rg,ry,rT,rv,rE,rR,rA,rk,rI,rx,rS,rN,rC,r$,rL,rw,rb,rO,r_,rP,rM,rD,rZ,rU,rF,rG,rB,rK,rj,rV,rW,rH,rz,rY,rq,rX,rQ,rJ,r0,r1,r2,r3,r7,r4,r5,r6,r9,r8,ne,nt,nr,nn,ni,na,ns,no,nl,nu,nc,nd,nh,nf,np,nm,ng,ny,nT,nv,nE,nR,nA,nk,nI,nx,nS=r(70870),nN=r(34148),nC=r(79697),n$=r(43836),nL=r(17452),nw=r(92346);function nb(e){function t(){}t.prototype=e;let r=new t;function n(){return typeof r.bar}return n(),n(),e}var nO=function(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a},n_=r(24930),nP=function(e,t,r){var n=null==e?0:e.length;return n?nO(e,(t=r||void 0===t?1:(0,n_.Z)(t))<0?0:t,n):[]},nM=r(36378),nD=r(72954),nZ=r(31899),nU=r(49268),nF=r(50585),nG=r(72764),nB=r(17179),nK=Object.prototype.hasOwnProperty,nj=(0,nU.Z)(function(e,t){if((0,nG.Z)(t)||(0,nF.Z)(t)){(0,nZ.Z)(t,(0,nB.Z)(t),e);return}for(var r in t)nK.call(t,r)&&(0,nD.Z)(e,r,t[r])}),nV=r(74073),nW=r(2e3),nH=r(51795),nz=r(4403),nY=function(e,t){if(null==e)return{};var r=(0,nV.Z)((0,nz.Z)(e),function(e){return[e]});return t=(0,nW.Z)(t),(0,nH.Z)(e,r,function(e,r){return t(e,r[0])})},nq=r(93589),nX=r(18533),nQ=r(21162),nJ=r(98351),n0=nJ.Z&&nJ.Z.isRegExp,n1=n0?(0,nQ.Z)(n0):function(e){return(0,nX.Z)(e)&&"[object RegExp]"==(0,nq.Z)(e)};class n2{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),(0,nS.Z)(this.definition,t=>{t.accept(e)})}}class n3 extends n2{constructor(e){super([]),this.idx=1,nj(this,nY(e,e=>void 0!==e))}set definition(e){}get definition(){return void 0!==this.referencedRule?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class n7 extends n2{constructor(e){super(e.definition),this.orgText="",nj(this,nY(e,e=>void 0!==e))}}class n4 extends n2{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,nj(this,nY(e,e=>void 0!==e))}}class n5 extends n2{constructor(e){super(e.definition),this.idx=1,nj(this,nY(e,e=>void 0!==e))}}class n6 extends n2{constructor(e){super(e.definition),this.idx=1,nj(this,nY(e,e=>void 0!==e))}}class n9 extends n2{constructor(e){super(e.definition),this.idx=1,nj(this,nY(e,e=>void 0!==e))}}class n8 extends n2{constructor(e){super(e.definition),this.idx=1,nj(this,nY(e,e=>void 0!==e))}}class ie extends n2{constructor(e){super(e.definition),this.idx=1,nj(this,nY(e,e=>void 0!==e))}}class it extends n2{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,nj(this,nY(e,e=>void 0!==e))}}class ir{constructor(e){this.idx=1,nj(this,nY(e,e=>void 0!==e))}accept(e){e.visit(this)}}class ii{walk(e,t=[]){(0,nS.Z)(e.definition,(r,n)=>{let i=nP(e.definition,n+1);if(r instanceof n3)this.walkProdRef(r,i,t);else if(r instanceof ir)this.walkTerminal(r,i,t);else if(r instanceof n4)this.walkFlat(r,i,t);else if(r instanceof n5)this.walkOption(r,i,t);else if(r instanceof n6)this.walkAtLeastOne(r,i,t);else if(r instanceof n9)this.walkAtLeastOneSep(r,i,t);else if(r instanceof ie)this.walkManySep(r,i,t);else if(r instanceof n8)this.walkMany(r,i,t);else if(r instanceof it)this.walkOr(r,i,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,r){}walkProdRef(e,t,r){}walkFlat(e,t,r){let n=t.concat(r);this.walk(e,n)}walkOption(e,t,r){let n=t.concat(r);this.walk(e,n)}walkAtLeastOne(e,t,r){let n=[new n5({definition:e.definition})].concat(t,r);this.walk(e,n)}walkAtLeastOneSep(e,t,r){let n=ia(e,t,r);this.walk(e,n)}walkMany(e,t,r){let n=[new n5({definition:e.definition})].concat(t,r);this.walk(e,n)}walkManySep(e,t,r){let n=ia(e,t,r);this.walk(e,n)}walkOr(e,t,r){let n=t.concat(r);(0,nS.Z)(e.definition,e=>{let t=new n4({definition:[e]});this.walk(t,n)})}}function ia(e,t,r){let n=[new n5({definition:[new ir({terminalType:e.separator})].concat(e.definition)})],i=n.concat(t,r);return i}var is=r(69958),io=function(e){return e&&e.length?(0,is.Z)(e):[]},il=r(27961),iu=r(2338),ic=r(49811),id=function(e,t){var r;return(0,ic.Z)(e,function(e,n,i){return!(r=t(e,n,i))}),!!r},ih=r(27771),ip=r(50439),im=function(e,t,r){var n=(0,ih.Z)(e)?iu.Z:id;return r&&(0,ip.Z)(e,t,r)&&(t=void 0),n(e,(0,nW.Z)(t,3))},ig=r(39044),iy=Math.max,iT=function(e,t,r,n){e=(0,nF.Z)(e)?e:(0,nN.Z)(e),r=r&&!n?(0,n_.Z)(r):0;var i=e.length;return r<0&&(r=iy(i+r,0)),(0,nM.Z)(e)?r<=i&&e.indexOf(t,r)>-1:!!i&&(0,ig.Z)(e,t,r)>-1},iv=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0},iE=function(e,t){var r=!0;return(0,ic.Z)(e,function(e,n,i){return r=!!t(e,n,i)}),r},iR=function(e,t,r){var n=(0,ih.Z)(e)?iv:iE;return r&&(0,ip.Z)(e,t,r)&&(t=void 0),n(e,(0,nW.Z)(t,3))};function iA(e,t=[]){return e instanceof n5||e instanceof n8||e instanceof ie||(e instanceof it?im(e.definition,e=>iA(e,t)):!(e instanceof n3&&iT(t,e))&&e instanceof n2&&(e instanceof n3&&t.push(e),iR(e.definition,e=>iA(e,t))))}function ik(e){if(e instanceof n3)return"SUBRULE";if(e instanceof n5)return"OPTION";if(e instanceof it)return"OR";if(e instanceof n6)return"AT_LEAST_ONE";if(e instanceof n9)return"AT_LEAST_ONE_SEP";if(e instanceof ie)return"MANY_SEP";if(e instanceof n8)return"MANY";else if(e instanceof ir)return"CONSUME";else throw Error("non exhaustive match")}function iI(e){if(e instanceof n3)return iI(e.referencedRule);if(e instanceof ir)return[e.terminalType];if(e instanceof n4||e instanceof n5||e instanceof n8||e instanceof n6||e instanceof n9||e instanceof ie||e instanceof ir||e instanceof n7)return function(e){let t,r=[],n=e.definition,i=0,a=n.length>i,s=!0;for(;a&&s;)s=iA(t=n[i]),r=r.concat(iI(t)),i+=1,a=n.length>i;return io(r)}(e);if(e instanceof it)return function(e){let t=(0,n$.Z)(e.definition,e=>iI(e));return io((0,il.Z)(t))}(e);throw Error("non exhaustive match")}let ix="_~IN~_";class iS extends ii{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,r){}walkProdRef(e,t,r){var n,i;let a=(n=e.referencedRule,i=e.idx,n.name+i+ix+this.topProd.name),s=t.concat(r),o=new n4({definition:s}),l=iI(o);this.follows[a]=l}}var iN=r(49360),iC=r(3688),i$=r(68774),iL=r(94749),iw=function(e){if("function"!=typeof e)throw TypeError("Expected a function");return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}},ib=function(e,t){return((0,ih.Z)(e)?i$.Z:iL.Z)(e,iw((0,nW.Z)(t,3)))},iO=r(73234),i_=Math.max,iP=function(e,t,r){var n=null==e?0:e.length;if(!n)return -1;var i=null==r?0:(0,n_.Z)(r);return i<0&&(i=i_(n+i,0)),(0,ig.Z)(e,t,i)},iM=r(92344),iD=r(76579),iZ=r(63001),iU=r(39370),iF=r(22783),iG=r(59548),iB=function(e,t,r,n){var i=-1,a=iU.Z,s=!0,o=e.length,l=[],u=t.length;if(!o)return l;r&&(t=(0,nV.Z)(t,(0,nQ.Z)(r))),n?(a=iF.Z,s=!1):t.length>=200&&(a=iG.Z,s=!1,t=new iZ.Z(t));e:for(;++i<o;){var c=e[i],d=null==r?c:r(c);if(c=n||0!==c?c:0,s&&d==d){for(var h=u;h--;)if(t[h]===d)continue e;l.push(c)}else a(t,d,n)||l.push(c)}return l},iK=r(10626),ij=r(69581),iV=r(836),iW=(0,ij.Z)(function(e,t){return(0,iV.Z)(e)?iB(e,(0,iK.Z)(t,1,iV.Z,!0)):[]}),iH=function(e){for(var t=-1,r=null==e?0:e.length,n=0,i=[];++t<r;){var a=e[t];a&&(i[n++]=a)}return i},iz=function(e){return e&&e.length?e[0]:void 0},iY=r(38656);function iq(e){console&&console.error&&console.error(`Error: ${e}`)}function iX(e){console&&console.warn&&console.warn(`Warning: ${e}`)}let iQ={},iJ=new eD;function i0(e){let t=e.toString();if(iQ.hasOwnProperty(t))return iQ[t];{let e=iJ.pattern(t);return iQ[t]=e,e}}let i1="Complement Sets are not supported for first char optimization",i2='Unable to use "first char" lexer optimizations:\n';function i3(e,t,r){let n=ah(e);t[n]=n,!0===r&&function(e,t){let r=String.fromCharCode(e),n=r.toUpperCase();if(n!==r){let e=ah(n.charCodeAt(0));t[e]=e}else{let e=r.toLowerCase();if(e!==r){let r=ah(e.charCodeAt(0));t[r]=r}}}(e,t)}function i7(e,t){return(0,iY.Z)(e.value,e=>"number"==typeof e?iT(t,e):void 0!==(0,iY.Z)(t,t=>e.from<=t&&t<=e.to))}class i4 extends eZ{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(!0!==this.found){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){iT(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?void 0===i7(e,this.targetCharCodes)&&(this.found=!0):void 0!==i7(e,this.targetCharCodes)&&(this.found=!0)}}function i5(e,t){if(!(t instanceof RegExp))return void 0!==(0,iY.Z)(t,t=>iT(e,t.charCodeAt(0)));{let r=i0(t),n=new i4(e);return n.visit(r),n.found}}let i6="PATTERN",i9="defaultMode",i8="modes",ae="boolean"==typeof RegExp("(?:)").sticky,at=/[^\\][$]/,ar=/[^\\[][\^]|^\^/;function an(e){let t=e.ignoreCase?"i":"";return RegExp(`^(?:${e.source})`,t)}function ai(e){let t=e.ignoreCase?"iy":"y";return RegExp(`${e.source}`,t)}function aa(e){let t=e.PATTERN;if(n1(t))return!1;if((0,iO.Z)(t)||(0,nL.Z)(t,"exec"))return!0;if((0,nM.Z)(t))return!1;throw Error("non exhaustive match")}function as(e){return!!(0,nM.Z)(e)&&1===e.length&&e.charCodeAt(0)}let ao={test:function(e){let t=e.length;for(let r=this.lastIndex;r<t;r++){let t=e.charCodeAt(r);if(10===t)return this.lastIndex=r+1,!0;if(13===t)return 10===e.charCodeAt(r+1)?this.lastIndex=r+2:this.lastIndex=r+1,!0}return!1},lastIndex:0};function al(e,t){if((0,nL.Z)(e,"LINE_BREAKS"))return!1;if(n1(e.PATTERN)){try{i5(t,e.PATTERN)}catch(e){return{issue:t6.IDENTIFY_TERMINATOR,errMsg:e.message}}return!1}if((0,nM.Z)(e.PATTERN))return!1;if(aa(e))return{issue:t6.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}function au(e){let t=(0,n$.Z)(e,e=>(0,nM.Z)(e)?e.charCodeAt(0):e);return t}function ac(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}let ad=[];function ah(e){return e<256?e:ad[e]}var af=r(69203),ap=r(42054),am=r(935);function ag(e){let t=new Date().getTime(),r=e(),n=new Date().getTime();return{time:n-t,value:r}}function ay(e,t){let r=e.tokenTypeIdx;return r===t.tokenTypeIdx||!0===t.isParent&&!0===t.categoryMatchesMap[r]}function aT(e,t){return e.tokenTypeIdx===t.tokenTypeIdx}let av=1,aE={};function aR(e){let t=function(e){let t=(0,nw.Z)(e),r=e,n=!0;for(;n;){r=iH((0,il.Z)((0,n$.Z)(r,e=>e.CATEGORIES)));let e=iW(r,t);t=t.concat(e),(0,nC.Z)(e)?n=!1:r=e}return t}(e);(0,nS.Z)(t,e=>{aA(e)||(aE[av]=e,e.tokenTypeIdx=av++),ak(e)&&!(0,ih.Z)(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),ak(e)||(e.CATEGORIES=[]),(0,nL.Z)(e,"categoryMatches")||(e.categoryMatches=[]),(0,nL.Z)(e,"categoryMatchesMap")||(e.categoryMatchesMap={})}),(0,nS.Z)(t,e=>{(function e(t,r){(0,nS.Z)(t,e=>{r.categoryMatchesMap[e.tokenTypeIdx]=!0}),(0,nS.Z)(r.CATEGORIES,n=>{let i=t.concat(r);iT(i,n)||e(i,n)})})([],e)}),(0,nS.Z)(t,e=>{e.categoryMatches=[],(0,nS.Z)(e.categoryMatchesMap,(t,r)=>{e.categoryMatches.push(aE[r].tokenTypeIdx)})}),(0,nS.Z)(t,e=>{e.isParent=e.categoryMatches.length>0})}function aA(e){return(0,nL.Z)(e,"tokenTypeIdx")}function ak(e){return(0,nL.Z)(e,"CATEGORIES")}function aI(e){return(0,nL.Z)(e,"tokenTypeIdx")}(e1=t6||(t6={}))[e1.MISSING_PATTERN=0]="MISSING_PATTERN",e1[e1.INVALID_PATTERN=1]="INVALID_PATTERN",e1[e1.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",e1[e1.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",e1[e1.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",e1[e1.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",e1[e1.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",e1[e1.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",e1[e1.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",e1[e1.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",e1[e1.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",e1[e1.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",e1[e1.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",e1[e1.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",e1[e1.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",e1[e1.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",e1[e1.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",e1[e1.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE";let ax={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:["\n","\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:{buildUnableToPopLexerModeMessage:e=>`Unable to pop Lexer Mode after encountering Token ->${e.image}<- The Mode Stack is empty`,buildUnexpectedCharactersMessage:(e,t,r,n,i)=>`unexpected character: ->${e.charAt(t)}<- at offset: ${t}, skipped ${r} characters.`},traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(ax);class aS{constructor(e,t=ax){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(e,t)=>{if(!0!==this.traceInitPerf)return t();{this.traceInitIndent++;let r=Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${r}--> <${e}>`);let{time:n,value:i}=ag(t),a=n>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${n}ms`),this.traceInitIndent--,i}},"boolean"==typeof t)throw Error("The second argument to the Lexer constructor is now an ILexerConfig Object.\na boolean 2nd argument is no longer supported");this.config=nj({},ax,t);let r=this.config.traceInitPerf;!0===r?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):"number"==typeof r&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let r;let n=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===ax.lineTerminatorsPattern)this.config.lineTerminatorsPattern=ao;else if(this.config.lineTerminatorCharacters===ax.lineTerminatorCharacters)throw Error("Error: Missing <lineTerminatorCharacters> property on the Lexer config.\n	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS");if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),(0,ih.Z)(e)?r={modes:{defaultMode:(0,nw.Z)(e)},defaultMode:i9}:(n=!1,r=(0,nw.Z)(e))}),!1===this.config.skipValidations&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(function(e,t,r){let n=[];return(0,nL.Z)(e,i9)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+i9+"> property in its definition\n",type:t6.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),(0,nL.Z)(e,i8)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+i8+"> property in its definition\n",type:t6.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),(0,nL.Z)(e,i8)&&(0,nL.Z)(e,i9)&&!(0,nL.Z)(e.modes,e.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${i9}: <${e.defaultMode}>which does not exist
`,type:t6.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),(0,nL.Z)(e,i8)&&(0,nS.Z)(e.modes,(e,t)=>{(0,nS.Z)(e,(r,i)=>{if((0,iN.Z)(r))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${t}> at index: <${i}>
`,type:t6.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if((0,nL.Z)(r,"LONGER_ALT")){let i=(0,ih.Z)(r.LONGER_ALT)?r.LONGER_ALT:[r.LONGER_ALT];(0,nS.Z)(i,i=>{(0,iN.Z)(i)||iT(e,i)||n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${i.name}> on token <${r.name}> outside of mode <${t}>
`,type:t6.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}(r,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(function(e,t,r){let n=[],i=!1,a=iH((0,il.Z)((0,nN.Z)(e.modes))),s=ib(a,e=>e[i6]===aS.NA),o=au(r);return t&&(0,nS.Z)(s,e=>{let t=al(e,o);if(!1!==t){let r=function(e,t){if(t.issue===t6.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${e.name}> Token Type
	 Root cause: ${t.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(t.issue===t6.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${e.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}(e,t),i={message:r,type:t.issue,tokenType:e};n.push(i)}else(0,nL.Z)(e,"LINE_BREAKS")?!0===e.LINE_BREAKS&&(i=!0):i5(o,e.PATTERN)&&(i=!0)}),t&&!i&&n.push({message:"Warning: No LINE_BREAKS Found.\n	This Lexer has been defined to track line and column information,\n	But none of the Token Types can be identified as matching a line terminator.\n	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS \n	for details.",type:t6.NO_LINE_BREAKS_FLAGS}),n}(r,this.trackStartLines,this.config.lineTerminatorCharacters))})),r.modes=r.modes?r.modes:{},(0,nS.Z)(r.modes,(e,t)=>{r.modes[t]=ib(e,e=>(0,iN.Z)(e))});let i=(0,nB.Z)(r.modes);if((0,nS.Z)(r.modes,(e,r)=>{this.TRACE_INIT(`Mode: <${r}> processing`,()=>{if(this.modes.push(r),!1===this.config.skipValidations&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(function(e,t){let r=[],n=function(e){let t=(0,iD.Z)(e,e=>!(0,nL.Z)(e,i6)),r=(0,n$.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- missing static 'PATTERN' property",type:t6.MISSING_PATTERN,tokenTypes:[e]})),n=iW(e,t);return{errors:r,valid:n}}(e);r=r.concat(n.errors);let i=function(e){let t=(0,iD.Z)(e,e=>{let t=e[i6];return!n1(t)&&!(0,iO.Z)(t)&&!(0,nL.Z)(t,"exec")&&!(0,nM.Z)(t)}),r=(0,n$.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:t6.INVALID_PATTERN,tokenTypes:[e]})),n=iW(e,t);return{errors:r,valid:n}}(n.valid),a=i.valid;return(r=(r=(r=(r=r.concat(i.errors)).concat(function(e){let t=[],r=(0,iD.Z)(e,e=>n1(e[i6]));return(t=(t=(t=(t=t.concat(function(e){class t extends eZ{constructor(){super(...arguments),this.found=!1}visitEndAnchor(e){this.found=!0}}let r=(0,iD.Z)(e,e=>{let r=e.PATTERN;try{let e=i0(r),n=new t;return n.visit(e),n.found}catch(e){return at.test(r.source)}}),n=(0,n$.Z)(r,e=>({message:"Unexpected RegExp Anchor Error:\n	Token Type: ->"+e.name+"<- static 'PATTERN' cannot contain end of input anchor '$'\n	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.",type:t6.EOI_ANCHOR_FOUND,tokenTypes:[e]}));return n}(r))).concat(function(e){class t extends eZ{constructor(){super(...arguments),this.found=!1}visitStartAnchor(e){this.found=!0}}let r=(0,iD.Z)(e,e=>{let r=e.PATTERN;try{let e=i0(r),n=new t;return n.visit(e),n.found}catch(e){return ar.test(r.source)}}),n=(0,n$.Z)(r,e=>({message:"Unexpected RegExp Anchor Error:\n	Token Type: ->"+e.name+"<- static 'PATTERN' cannot contain start of input anchor '^'\n	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.",type:t6.SOI_ANCHOR_FOUND,tokenTypes:[e]}));return n}(r))).concat(function(e){let t=(0,iD.Z)(e,e=>{let t=e[i6];return t instanceof RegExp&&(t.multiline||t.global)}),r=(0,n$.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:t6.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[e]}));return r}(r))).concat(function(e){let t=[],r=(0,n$.Z)(e,r=>(0,iM.Z)(e,(e,n)=>(r.PATTERN.source!==n.PATTERN.source||iT(t,n)||n.PATTERN===aS.NA||(t.push(n),e.push(n)),e),[]));r=iH(r);let n=(0,iD.Z)(r,e=>e.length>1),i=(0,n$.Z)(n,e=>{let t=(0,n$.Z)(e,e=>e.name),r=iz(e).PATTERN;return{message:`The same RegExp pattern ->${r}<-has been used in all of the following Token Types: ${t.join(", ")} <-`,type:t6.DUPLICATE_PATTERNS_FOUND,tokenTypes:e}});return i}(r))).concat(function(e){let t=(0,iD.Z)(e,e=>{let t=e.PATTERN;return t.test("")}),r=(0,n$.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' must not match an empty string",type:t6.EMPTY_MATCH_PATTERN,tokenTypes:[e]}));return r}(r))}(a))).concat(function(e){let t=(0,iD.Z)(e,e=>{if(!(0,nL.Z)(e,"GROUP"))return!1;let t=e.GROUP;return t!==aS.SKIPPED&&t!==aS.NA&&!(0,nM.Z)(t)}),r=(0,n$.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:t6.INVALID_GROUP_TYPE_FOUND,tokenTypes:[e]}));return r}(a))).concat(function(e,t){let r=(0,iD.Z)(e,e=>void 0!==e.PUSH_MODE&&!iT(t,e.PUSH_MODE)),n=(0,n$.Z)(r,e=>{let t=`Token Type: ->${e.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${e.PUSH_MODE}<-which does not exist`;return{message:t,type:t6.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[e]}});return n}(a,t))).concat(function(e){let t=[],r=(0,iM.Z)(e,(e,t,r)=>{let n=t.PATTERN;return n===aS.NA||((0,nM.Z)(n)?e.push({str:n,idx:r,tokenType:t}):n1(n)&&void 0===(0,iY.Z)([".","\\","[","]","|","^","$","(",")","?","*","+","{"],e=>-1!==n.source.indexOf(e))&&e.push({str:n.source,idx:r,tokenType:t})),e},[]);return(0,nS.Z)(e,(e,n)=>{(0,nS.Z)(r,({str:r,idx:i,tokenType:a})=>{if(n<i&&function(e,t){if(n1(t)){let r=t.exec(e);return null!==r&&0===r.index}if((0,iO.Z)(t))return t(e,0,[],{});if((0,nL.Z)(t,"exec"))return t.exec(e,0,[],{});if("string"==typeof t)return t===e;throw Error("non exhaustive match")}(r,e.PATTERN)){let r=`Token: ->${a.name}<- can never be matched.
Because it appears AFTER the Token Type ->${e.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;t.push({message:r,type:t6.UNREACHABLE_PATTERN,tokenTypes:[e,a]})}})}),t}(a))}(e,i))}),(0,nC.Z)(this.lexerDefinitionErrors)){let n;aR(e),this.TRACE_INIT("analyzeTokenTypes",()=>{n=function(e,t){let r,n,i,a,s,o,l,u,c,d,h,f;t=(0,iC.Z)(t,{useSticky:ae,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r","\n"],tracer:(e,t)=>t()});let p=t.tracer;p("initCharCodeToOptimizedIndexMap",()=>{(function(){if((0,nC.Z)(ad)){ad=Array(65536);for(let e=0;e<65536;e++)ad[e]=e>255?255+~~(e/255):e}})()}),p("Reject Lexer.NA",()=>{r=ib(e,e=>e[i6]===aS.NA)});let m=!1;p("Transform Patterns",()=>{m=!1,n=(0,n$.Z)(r,e=>{let r=e[i6];if(n1(r)){let e=r.source;return 1!==e.length||"^"===e||"$"===e||"."===e||r.ignoreCase?2!==e.length||"\\"!==e[0]||iT(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],e[1])?t.useSticky?ai(r):an(r):e[1]:e}if((0,iO.Z)(r))return m=!0,{exec:r};if("object"==typeof r)return m=!0,r;if("string"==typeof r){if(1===r.length)return r;{let e=r.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),n=RegExp(e);return t.useSticky?ai(n):an(n)}}throw Error("non exhaustive match")})}),p("misc mapping",()=>{i=(0,n$.Z)(r,e=>e.tokenTypeIdx),a=(0,n$.Z)(r,e=>{let t=e.GROUP;if(t!==aS.SKIPPED){if((0,nM.Z)(t))return t;if((0,iN.Z)(t))return!1;throw Error("non exhaustive match")}}),s=(0,n$.Z)(r,e=>{let t=e.LONGER_ALT;if(t){let e=(0,ih.Z)(t)?(0,n$.Z)(t,e=>iP(r,e)):[iP(r,t)];return e}}),o=(0,n$.Z)(r,e=>e.PUSH_MODE),l=(0,n$.Z)(r,e=>(0,nL.Z)(e,"POP_MODE"))}),p("Line Terminator Handling",()=>{let e=au(t.lineTerminatorCharacters);u=(0,n$.Z)(r,e=>!1),"onlyOffset"!==t.positionTracking&&(u=(0,n$.Z)(r,t=>(0,nL.Z)(t,"LINE_BREAKS")?!!t.LINE_BREAKS:!1===al(t,e)&&i5(e,t.PATTERN)))}),p("Misc Mapping #2",()=>{c=(0,n$.Z)(r,aa),d=(0,n$.Z)(n,as),h=(0,iM.Z)(r,(e,t)=>{let r=t.GROUP;return(0,nM.Z)(r)&&r!==aS.SKIPPED&&(e[r]=[]),e},{}),f=(0,n$.Z)(n,(e,t)=>({pattern:n[t],longerAlt:s[t],canLineTerminator:u[t],isCustom:c[t],short:d[t],group:a[t],push:o[t],pop:l[t],tokenTypeIdx:i[t],tokenType:r[t]}))});let g=!0,y=[];return t.safeMode||p("First Char Optimization",()=>{y=(0,iM.Z)(r,(e,r,n)=>{if("string"==typeof r.PATTERN){let t=r.PATTERN.charCodeAt(0),i=ah(t);ac(e,i,f[n])}else if((0,ih.Z)(r.START_CHARS_HINT)){let t;(0,nS.Z)(r.START_CHARS_HINT,r=>{let i="string"==typeof r?r.charCodeAt(0):r,a=ah(i);t!==a&&(t=a,ac(e,a,f[n]))})}else if(n1(r.PATTERN)){if(r.PATTERN.unicode)g=!1,t.ensureOptimizations&&iq(`${i2}	Unable to analyze < ${r.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{let i=function(e,t=!1){try{let t=i0(e),r=function e(t,r,n){switch(t.type){case"Disjunction":for(let i=0;i<t.value.length;i++)e(t.value[i],r,n);break;case"Alternative":let i=t.value;for(let t=0;t<i.length;t++){let a=i[t];switch(a.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}switch(a.type){case"Character":i3(a.value,r,n);break;case"Set":if(!0===a.complement)throw Error(i1);(0,nS.Z)(a.value,e=>{if("number"==typeof e)i3(e,r,n);else if(!0===n)for(let t=e.from;t<=e.to;t++)i3(t,r,n);else{for(let t=e.from;t<=e.to&&t<256;t++)i3(t,r,n);if(e.to>=256){let t=e.from>=256?e.from:256,n=e.to,i=ah(t),a=ah(n);for(let e=i;e<=a;e++)r[e]=e}}});break;case"Group":e(a.value,r,n);break;default:throw Error("Non Exhaustive Match")}let s=void 0!==a.quantifier&&0===a.quantifier.atLeast;if("Group"===a.type&&!1===function e(t){let r=t.quantifier;return!!r&&0===r.atLeast||!!t.value&&((0,ih.Z)(t.value)?iR(t.value,e):e(t.value))}(a)||"Group"!==a.type&&!1===s)break}break;default:throw Error("non exhaustive match!")}return(0,nN.Z)(r)}(t.value,{},t.flags.ignoreCase);return r}catch(r){if(r.message===i1)t&&iX(`${i2}	Unable to optimize: < ${e.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let r="";t&&(r="\n	This will disable the lexer's first char optimizations.\n	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details."),iq(`${i2}
	Failed parsing: < ${e.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+r)}}return[]}(r.PATTERN,t.ensureOptimizations);(0,nC.Z)(i)&&(g=!1),(0,nS.Z)(i,t=>{ac(e,t,f[n])})}}else t.ensureOptimizations&&iq(`${i2}	TokenType: <${r.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),g=!1;return e},[])}),{emptyGroups:h,patternIdxToConfig:f,charCodeToPatternIdxToConfig:y,hasCustom:m,canBeOptimized:g}}(e,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[r]=n.patternIdxToConfig,this.charCodeToPatternIdxToConfig[r]=n.charCodeToPatternIdxToConfig,this.emptyGroups=nj({},this.emptyGroups,n.emptyGroups),this.hasCustom=n.hasCustom||this.hasCustom,this.canModeBeOptimized[r]=n.canBeOptimized}})}),this.defaultMode=r.defaultMode,!(0,nC.Z)(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){let e=(0,n$.Z)(this.lexerDefinitionErrors,e=>e.message),t=e.join("-----------------------\n");throw Error("Errors detected in definition of Lexer:\n"+t)}(0,nS.Z)(this.lexerDefinitionWarning,e=>{iX(e.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(ae?(this.chopInput=af.Z,this.match=this.matchWithTest):(this.updateLastIndex=ap.Z,this.match=this.matchWithExec),n&&(this.handleModes=ap.Z),!1===this.trackStartLines&&(this.computeNewColumn=af.Z),!1===this.trackEndLines&&(this.updateTokenEndLineColumnLocation=ap.Z),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{let e=(0,iM.Z)(this.canModeBeOptimized,(e,t,r)=>(!1===t&&e.push(r),e),[]);if(t.ensureOptimizations&&!(0,nC.Z)(e))throw Error(`Lexer Modes: < ${e.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{iQ={}}),this.TRACE_INIT("toFastProperties",()=>{nb(this)})})}tokenize(e,t=this.defaultMode){if(!(0,nC.Z)(this.lexerDefinitionErrors)){let e=(0,n$.Z)(this.lexerDefinitionErrors,e=>e.message),t=e.join("-----------------------\n");throw Error("Unable to Tokenize because Errors detected in definition of Lexer:\n"+t)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let r,n,i,a,s,o,l,u,c,d,h,f,p,m,g,y,T;let v=e,E=v.length,R=0,A=0,k=this.hasCustom?0:Math.floor(e.length/10),I=Array(k),x=[],S=this.trackStartLines?1:void 0,N=this.trackStartLines?1:void 0,C=function(e){let t={},r=(0,nB.Z)(e);return(0,nS.Z)(r,r=>{let n=e[r];if((0,ih.Z)(n))t[r]=[];else throw Error("non exhaustive match")}),t}(this.emptyGroups),$=this.trackStartLines,L=this.config.lineTerminatorsPattern,w=0,b=[],O=[],_=[],P=[];function M(){return b}function D(e){let t=ah(e),r=O[t];return void 0===r?P:r}Object.freeze(P);let Z=e=>{if(1===_.length&&void 0===e.tokenType.PUSH_MODE){let t=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(e);x.push({offset:e.startOffset,line:e.startLine,column:e.startColumn,length:e.image.length,message:t})}else{_.pop();let e=(0,am.Z)(_);b=this.patternIdxToConfig[e],O=this.charCodeToPatternIdxToConfig[e],w=b.length;let t=this.canModeBeOptimized[e]&&!1===this.config.safeMode;y=O&&t?D:M}};function U(e){_.push(e),O=this.charCodeToPatternIdxToConfig[e],w=(b=this.patternIdxToConfig[e]).length,w=b.length;let t=this.canModeBeOptimized[e]&&!1===this.config.safeMode;y=O&&t?D:M}U.call(this,t);let F=this.config.recoveryEnabled;for(;R<E;){o=null;let t=v.charCodeAt(R),k=y(t),O=k.length;for(r=0;r<O;r++){T=k[r];let n=T.pattern;l=null;let c=T.short;if(!1!==c?t===c&&(o=n):!0===T.isCustom?null!==(g=n.exec(v,R,I,C))?(o=g[0],void 0!==g.payload&&(l=g.payload)):o=null:(this.updateLastIndex(n,R),o=this.match(n,e,R)),null!==o){if(void 0!==(s=T.longerAlt)){let t=s.length;for(i=0;i<t;i++){let t=b[s[i]],r=t.pattern;if(u=null,!0===t.isCustom?null!==(g=r.exec(v,R,I,C))?(a=g[0],void 0!==g.payload&&(u=g.payload)):a=null:(this.updateLastIndex(r,R),a=this.match(r,e,R)),a&&a.length>o.length){o=a,l=u,T=t;break}}}break}}if(null!==o){if(c=o.length,void 0!==(d=T.group)&&(h=T.tokenTypeIdx,f=this.createTokenInstance(o,R,h,T.tokenType,S,N,c),this.handlePayload(f,l),!1===d?A=this.addToken(I,A,f):C[d].push(f)),e=this.chopInput(e,c),R+=c,N=this.computeNewColumn(N,c),!0===$&&!0===T.canLineTerminator){let e,t,r=0;L.lastIndex=0;do!0===(e=L.test(o))&&(t=L.lastIndex-1,r++);while(!0===e);0!==r&&(S+=r,N=c-t,this.updateTokenEndLineColumnLocation(f,d,t,r,S,N,c))}this.handleModes(T,Z,U,f)}else{let t=R,r=S,i=N,a=!1===F;for(;!1===a&&R<E;)for(e=this.chopInput(e,1),R++,n=0;n<w;n++){let t=b[n],r=t.pattern,i=t.short;if(!1!==i?v.charCodeAt(R)===i&&(a=!0):!0===t.isCustom?a=null!==r.exec(v,R,I,C):(this.updateLastIndex(r,R),a=null!==r.exec(e)),!0===a)break}if(p=R-t,N=this.computeNewColumn(N,p),m=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(v,t,p,r,i),x.push({offset:t,line:r,column:i,length:p,message:m}),!1===F)break}}return this.hasCustom||(I.length=A),{tokens:I,groups:C,errors:x}}handleModes(e,t,r,n){if(!0===e.pop){let i=e.push;t(n),void 0!==i&&r.call(this,i)}else void 0!==e.push&&r.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,r,n,i,a,s){let o,l;void 0===t||(l=(o=r===s-1)?-1:0,1===n&&!0===o||(e.endLine=i+l,e.endColumn=a-1+-l))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,r,n){return{image:e,startOffset:t,tokenTypeIdx:r,tokenType:n}}createStartOnlyToken(e,t,r,n,i,a){return{image:e,startOffset:t,startLine:i,startColumn:a,tokenTypeIdx:r,tokenType:n}}createFullToken(e,t,r,n,i,a,s){return{image:e,startOffset:t,endOffset:t+s-1,startLine:i,endLine:i,startColumn:a,endColumn:a+s-1,tokenTypeIdx:r,tokenType:n}}addTokenUsingPush(e,t,r){return e.push(r),t}addTokenUsingMemberAccess(e,t,r){return e[t]=r,++t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){null!==t&&(e.payload=t)}matchWithTest(e,t,r){let n=e.test(t);return!0===n?t.substring(r,e.lastIndex):null}matchWithExec(e,t){let r=e.exec(t);return null!==r?r[0]:null}}function aN(e){return aC(e)?e.LABEL:e.name}function aC(e){return(0,nM.Z)(e.LABEL)&&""!==e.LABEL}aS.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.",aS.NA=/NOT_APPLICABLE/;let a$="categories",aL="label",aw="group",ab="push_mode",aO="pop_mode",a_="longer_alt",aP="line_breaks",aM="start_chars_hint";function aD(e){let t=e.pattern,r={};if(r.name=e.name,(0,iN.Z)(t)||(r.PATTERN=t),(0,nL.Z)(e,"parent"))throw"The parent property is no longer supported.\nSee: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.";return(0,nL.Z)(e,a$)&&(r.CATEGORIES=e[a$]),aR([r]),(0,nL.Z)(e,aL)&&(r.LABEL=e[aL]),(0,nL.Z)(e,aw)&&(r.GROUP=e[aw]),(0,nL.Z)(e,aO)&&(r.POP_MODE=e[aO]),(0,nL.Z)(e,ab)&&(r.PUSH_MODE=e[ab]),(0,nL.Z)(e,a_)&&(r.LONGER_ALT=e[a_]),(0,nL.Z)(e,aP)&&(r.LINE_BREAKS=e[aP]),(0,nL.Z)(e,aM)&&(r.START_CHARS_HINT=e[aM]),r}let aZ=aD({name:"EOF",pattern:aS.NA});function aU(e,t,r,n,i,a,s,o){return{image:t,startOffset:r,endOffset:n,startLine:i,endLine:a,startColumn:s,endColumn:o,tokenTypeIdx:e.tokenTypeIdx,tokenType:e}}aR([aZ]);let aF={buildMismatchTokenMessage({expected:e,actual:t,previous:r,ruleName:n}){let i=aC(e),a=i?`--> ${aN(e)} <--`:`token of type --> ${e.name} <--`,s=`Expecting ${a} but found --> '${t.image}' <--`;return s},buildNotAllInputParsedMessage:({firstRedundant:e,ruleName:t})=>"Redundant input, expecting EOF but found: "+e.image,buildNoViableAltMessage({expectedPathsPerAlt:e,actual:t,previous:r,customUserDescription:n,ruleName:i}){let a="Expecting: ",s=iz(t).image,o="\nbut found: '"+s+"'";if(n)return a+n+o;{let t=(0,iM.Z)(e,(e,t)=>e.concat(t),[]),r=(0,n$.Z)(t,e=>`[${(0,n$.Z)(e,e=>aN(e)).join(", ")}]`),n=(0,n$.Z)(r,(e,t)=>`  ${t+1}. ${e}`),i=`one of these possible Token sequences:
${n.join("\n")}`;return a+i+o}},buildEarlyExitMessage({expectedIterationPaths:e,actual:t,customUserDescription:r,ruleName:n}){let i="Expecting: ",a=iz(t).image,s="\nbut found: '"+a+"'";if(r)return i+r+s;{let t=(0,n$.Z)(e,e=>`[${(0,n$.Z)(e,e=>aN(e)).join(",")}]`),r=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${t.join(" ,")}>`;return i+r+s}}};Object.freeze(aF);let aG={buildRuleNotFoundError(e,t){let r="Invalid grammar, reference to a rule which is not defined: ->"+t.nonTerminalName+"<-\ninside top level rule: ->"+e.name+"<-";return r}},aB={buildDuplicateFoundError(e,t){let r=e.name,n=iz(t),i=n.idx,a=ik(n),s=n instanceof ir?n.terminalType.name:n instanceof n3?n.nonTerminalName:"",o=`->${a}${i>0?i:""}<- ${s?`with argument: ->${s}<-`:""}
                  appears more than once (${t.length} times) in the top level rule: ->${r}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return(o=o.replace(/[ \t]+/g," ")).replace(/\s\s+/g,"\n")},buildNamespaceConflictError(e){let t=`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${e.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`;return t},buildAlternationPrefixAmbiguityError(e){let t=(0,n$.Z)(e.prefixPath,e=>aN(e)).join(", "),r=0===e.alternation.idx?"":e.alternation.idx,n=`Ambiguous alternatives: <${e.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`;return n},buildAlternationAmbiguityError(e){let t=(0,n$.Z)(e.prefixPath,e=>aN(e)).join(", "),r=0===e.alternation.idx?"":e.alternation.idx;return`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(" ,")}> in <OR${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`},buildEmptyRepetitionError(e){let t=ik(e.repetition);0!==e.repetition.idx&&(t+=e.repetition.idx);let r=`The repetition <${t}> within Rule <${e.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`;return r},buildTokenNameError:e=>"deprecated",buildEmptyAlternationError(e){let t=`Ambiguous empty alternative: <${e.emptyChoiceIdx+1}> in <OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`;return t},buildTooManyAlternativesError(e){let t=`An Alternation cannot have more than 256 alternatives:
<OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.
 has ${e.alternation.definition.length+1} alternatives.`;return t},buildLeftRecursionError(e){let t=e.topLevelRule.name,r=(0,n$.Z)(e.leftRecursionPath,e=>e.name),n=`${t} --> ${r.concat([t]).join(" --> ")}`,i=`Left Recursion found in grammar.
rule: <${t}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`;return i},buildInvalidRuleNameError:e=>"deprecated",buildDuplicateRuleNameError(e){let t;t=e.topLevelRule instanceof n7?e.topLevelRule.name:e.topLevelRule;let r=`Duplicate definition, rule: ->${t}<- is already defined in the grammar: ->${e.grammarName}<-`;return r}};class aK{visit(e){switch(e.constructor){case n3:return this.visitNonTerminal(e);case n4:return this.visitAlternative(e);case n5:return this.visitOption(e);case n6:return this.visitRepetitionMandatory(e);case n9:return this.visitRepetitionMandatoryWithSeparator(e);case ie:return this.visitRepetitionWithSeparator(e);case n8:return this.visitRepetition(e);case it:return this.visitAlternation(e);case ir:return this.visitTerminal(e);case n7:return this.visitRule(e);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}class aj extends aK{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){(0,nS.Z)((0,nN.Z)(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){let t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{let t=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:t,type:re.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}var aV=function(e,t){return(0,iK.Z)((0,n$.Z)(e,t),1)},aW=r(74752),aH=function(e,t,r,n){for(var i=-1,a=null==e?0:e.length;++i<a;){var s=e[i];t(n,s,r(s),e)}return n},az=function(e,t,r,n){return(0,ic.Z)(e,function(e,i,a){t(n,e,r(e),a)}),n},aY=Object.prototype.hasOwnProperty,aq=(e2=function(e,t,r){aY.call(e,r)?e[r].push(t):(0,aW.Z)(e,r,[t])},function(e,t){var r=(0,ih.Z)(e)?aH:az,n=e3?e3():{};return r(e,e2,(0,nW.Z)(t,2),n)}),aX=function(e,t,r){var n=null==e?0:e.length;return n?nO(e,0,(t=n-(t=r||void 0===t?1:(0,n_.Z)(t)))<0?0:t):[]};class aQ extends ii{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=(0,nw.Z)(this.path.ruleStack).reverse(),this.occurrenceStack=(0,nw.Z)(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,r){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){let n=t.concat(r);this.updateExpectedNext(),this.walk(e.referencedRule,n)}}updateExpectedNext(){(0,nC.Z)(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class aJ extends aQ{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,r){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){let e=t.concat(r),n=new n4({definition:e});this.possibleTokTypes=iI(n),this.found=!0}}}class a0 extends ii{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class a1 extends a0{walkMany(e,t,r){if(e.idx===this.occurrence){let e=iz(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof ir&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkMany(e,t,r)}}class a2 extends a0{walkManySep(e,t,r){if(e.idx===this.occurrence){let e=iz(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof ir&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkManySep(e,t,r)}}class a3 extends a0{walkAtLeastOne(e,t,r){if(e.idx===this.occurrence){let e=iz(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof ir&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkAtLeastOne(e,t,r)}}class a7 extends a0{walkAtLeastOneSep(e,t,r){if(e.idx===this.occurrence){let e=iz(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof ir&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkAtLeastOneSep(e,t,r)}}function a4(e,t,r=[]){r=(0,nw.Z)(r);let n=[],i=0;function a(a){let s=a4(a.concat(nP(e,i+1)),t,r);return n.concat(s)}for(;r.length<t&&i<e.length;){let t=e[i];if(t instanceof n4||t instanceof n3)return a(t.definition);if(t instanceof n5)n=a(t.definition);else if(t instanceof n6){let e=t.definition.concat([new n8({definition:t.definition})]);return a(e)}else if(t instanceof n9){let e=[new n4({definition:t.definition}),new n8({definition:[new ir({terminalType:t.separator})].concat(t.definition)})];return a(e)}else if(t instanceof ie){let e=t.definition.concat([new n8({definition:[new ir({terminalType:t.separator})].concat(t.definition)})]);n=a(e)}else if(t instanceof n8){let e=t.definition.concat([new n8({definition:t.definition})]);n=a(e)}else if(t instanceof it)return(0,nS.Z)(t.definition,e=>{!1===(0,nC.Z)(e.definition)&&(n=a(e.definition))}),n;else if(t instanceof ir)r.push(t.terminalType);else throw Error("non exhaustive match");i++}return n.push({partialPath:r,suffixDef:nP(e,i)}),n}function a5(e,t,r,n){let i="EXIT_NONE_TERMINAL",a=[i],s="EXIT_ALTERNATIVE",o=!1,l=t.length,u=l-n-1,c=[],d=[];for(d.push({idx:-1,def:e,ruleStack:[],occurrenceStack:[]});!(0,nC.Z)(d);){let e=d.pop();if(e===s){o&&(0,am.Z)(d).idx<=u&&d.pop();continue}let n=e.def,h=e.idx,f=e.ruleStack,p=e.occurrenceStack;if((0,nC.Z)(n))continue;let m=n[0];if(m===i){let e={idx:h,def:nP(n),ruleStack:aX(f),occurrenceStack:aX(p)};d.push(e)}else if(m instanceof ir){if(h<l-1){let e=h+1,i=t[e];if(r(i,m.terminalType)){let t={idx:e,def:nP(n),ruleStack:f,occurrenceStack:p};d.push(t)}}else if(h===l-1)c.push({nextTokenType:m.terminalType,nextTokenOccurrence:m.idx,ruleStack:f,occurrenceStack:p}),o=!0;else throw Error("non exhaustive match")}else if(m instanceof n3){let e=(0,nw.Z)(f);e.push(m.nonTerminalName);let t=(0,nw.Z)(p);t.push(m.idx);let r={idx:h,def:m.definition.concat(a,nP(n)),ruleStack:e,occurrenceStack:t};d.push(r)}else if(m instanceof n5){let e={idx:h,def:nP(n),ruleStack:f,occurrenceStack:p};d.push(e),d.push(s);let t={idx:h,def:m.definition.concat(nP(n)),ruleStack:f,occurrenceStack:p};d.push(t)}else if(m instanceof n6){let e=new n8({definition:m.definition,idx:m.idx}),t=m.definition.concat([e],nP(n)),r={idx:h,def:t,ruleStack:f,occurrenceStack:p};d.push(r)}else if(m instanceof n9){let e=new ir({terminalType:m.separator}),t=new n8({definition:[e].concat(m.definition),idx:m.idx}),r=m.definition.concat([t],nP(n)),i={idx:h,def:r,ruleStack:f,occurrenceStack:p};d.push(i)}else if(m instanceof ie){let e={idx:h,def:nP(n),ruleStack:f,occurrenceStack:p};d.push(e),d.push(s);let t=new ir({terminalType:m.separator}),r=new n8({definition:[t].concat(m.definition),idx:m.idx}),i=m.definition.concat([r],nP(n)),a={idx:h,def:i,ruleStack:f,occurrenceStack:p};d.push(a)}else if(m instanceof n8){let e={idx:h,def:nP(n),ruleStack:f,occurrenceStack:p};d.push(e),d.push(s);let t=new n8({definition:m.definition,idx:m.idx}),r=m.definition.concat([t],nP(n)),i={idx:h,def:r,ruleStack:f,occurrenceStack:p};d.push(i)}else if(m instanceof it)for(let e=m.definition.length-1;e>=0;e--){let t=m.definition[e],r={idx:h,def:t.definition.concat(nP(n)),ruleStack:f,occurrenceStack:p};d.push(r),d.push(s)}else if(m instanceof n4)d.push({idx:h,def:m.definition.concat(nP(n)),ruleStack:f,occurrenceStack:p});else if(m instanceof n7)d.push(function(e,t,r,n){let i=(0,nw.Z)(r);i.push(e.name);let a=(0,nw.Z)(n);return a.push(1),{idx:t,def:e.definition,ruleStack:i,occurrenceStack:a}}(m,h,f,p));else throw Error("non exhaustive match")}return c}function a6(e){if(e instanceof n5||"Option"===e)return t9.OPTION;if(e instanceof n8||"Repetition"===e)return t9.REPETITION;if(e instanceof n6||"RepetitionMandatory"===e)return t9.REPETITION_MANDATORY;if(e instanceof n9||"RepetitionMandatoryWithSeparator"===e)return t9.REPETITION_MANDATORY_WITH_SEPARATOR;if(e instanceof ie||"RepetitionWithSeparator"===e)return t9.REPETITION_WITH_SEPARATOR;if(e instanceof it||"Alternation"===e)return t9.ALTERNATION;throw Error("non exhaustive match")}function a9(e){let{occurrence:t,rule:r,prodType:n,maxLookahead:i}=e,a=a6(n);return a===t9.ALTERNATION?ss(t,r,i):so(t,r,a,i)}function a8(e,t,r,n){let i=e.length,a=iR(e,e=>iR(e,e=>1===e.length));if(t)return function(t){let n=(0,n$.Z)(t,e=>e.GATE);for(let t=0;t<i;t++){let i=e[t],a=i.length,s=n[t];if(void 0===s||!1!==s.call(this))t:for(let e=0;e<a;e++){let n=i[e],a=n.length;for(let e=0;e<a;e++){let t=this.LA(e+1);if(!1===r(t,n[e]))continue t}return t}}};if(!a||n)return function(){for(let t=0;t<i;t++){let n=e[t],i=n.length;t:for(let e=0;e<i;e++){let i=n[e],a=i.length;for(let e=0;e<a;e++){let t=this.LA(e+1);if(!1===r(t,i[e]))continue t}return t}}};{let t=(0,n$.Z)(e,e=>(0,il.Z)(e)),r=(0,iM.Z)(t,(e,t,r)=>((0,nS.Z)(t,t=>{(0,nL.Z)(e,t.tokenTypeIdx)||(e[t.tokenTypeIdx]=r),(0,nS.Z)(t.categoryMatches,t=>{(0,nL.Z)(e,t)||(e[t]=r)})}),e),{});return function(){let e=this.LA(1);return r[e.tokenTypeIdx]}}}function se(e,t,r){let n=iR(e,e=>1===e.length),i=e.length;if(!n||r)return function(){t:for(let r=0;r<i;r++){let n=e[r],i=n.length;for(let e=0;e<i;e++){let r=this.LA(e+1);if(!1===t(r,n[e]))continue t}return!0}return!1};{let t=(0,il.Z)(e);if(1===t.length&&(0,nC.Z)(t[0].categoryMatches)){let e=t[0],r=e.tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===r}}{let e=(0,iM.Z)(t,(e,t,r)=>(e[t.tokenTypeIdx]=!0,(0,nS.Z)(t.categoryMatches,t=>{e[t]=!0}),e),[]);return function(){let t=this.LA(1);return!0===e[t.tokenTypeIdx]}}}}(e7=t9||(t9={}))[e7.OPTION=0]="OPTION",e7[e7.REPETITION=1]="REPETITION",e7[e7.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",e7[e7.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",e7[e7.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",e7[e7.ALTERNATION=5]="ALTERNATION";class st extends ii{constructor(e,t,r){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=r}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,r,n){return e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.restDef=r.concat(n),!0)}walkOption(e,t,r){this.checkIsTarget(e,t9.OPTION,t,r)||super.walkOption(e,t,r)}walkAtLeastOne(e,t,r){this.checkIsTarget(e,t9.REPETITION_MANDATORY,t,r)||super.walkOption(e,t,r)}walkAtLeastOneSep(e,t,r){this.checkIsTarget(e,t9.REPETITION_MANDATORY_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}walkMany(e,t,r){this.checkIsTarget(e,t9.REPETITION,t,r)||super.walkOption(e,t,r)}walkManySep(e,t,r){this.checkIsTarget(e,t9.REPETITION_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}}class sr extends aK{constructor(e,t,r){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=r,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(void 0===this.targetRef||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,t9.OPTION)}visitRepetition(e){this.checkIsTarget(e,t9.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,t9.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,t9.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,t9.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,t9.ALTERNATION)}}function sn(e){let t=Array(e);for(let r=0;r<e;r++)t[r]=[];return t}function si(e){let t=[""];for(let r=0;r<e.length;r++){let n=e[r],i=[];for(let e=0;e<t.length;e++){let r=t[e];i.push(r+"_"+n.tokenTypeIdx);for(let e=0;e<n.categoryMatches.length;e++){let t="_"+n.categoryMatches[e];i.push(r+t)}}t=i}return t}function sa(e,t){let r=(0,n$.Z)(e,e=>a4([e],1)),n=sn(r.length),i=(0,n$.Z)(r,e=>{let t={};return(0,nS.Z)(e,e=>{let r=si(e.partialPath);(0,nS.Z)(r,e=>{t[e]=!0})}),t}),a=r;for(let e=1;e<=t;e++){let r=a;a=sn(r.length);for(let s=0;s<r.length;s++){let o=r[s];for(let r=0;r<o.length;r++){let l=o[r].partialPath,u=o[r].suffixDef,c=si(l),d=function(e,t,r){for(let n=0;n<e.length;n++){if(n===r)continue;let i=e[n];for(let e=0;e<t.length;e++){let r=t[e];if(!0===i[r])return!1}}return!0}(i,c,s);if(d||(0,nC.Z)(u)||l.length===t){let e=n[s];if(!1===sl(e,l)){e.push(l);for(let e=0;e<c.length;e++){let t=c[e];i[s][t]=!0}}}else{let t=a4(u,e+1,l);a[s]=a[s].concat(t),(0,nS.Z)(t,e=>{let t=si(e.partialPath);(0,nS.Z)(t,e=>{i[s][e]=!0})})}}}}return n}function ss(e,t,r,n){let i=new sr(e,t9.ALTERNATION,n);return t.accept(i),sa(i.result,r)}function so(e,t,r,n){let i=new sr(e,r);t.accept(i);let a=i.result,s=new st(t,e,r),o=s.startWalking(),l=new n4({definition:a}),u=new n4({definition:o});return sa([l,u],n)}function sl(e,t){r:for(let r=0;r<e.length;r++){let n=e[r];if(n.length===t.length){for(let e=0;e<n.length;e++){let r=t[e],i=n[e],a=r===i||void 0!==i.categoryMatchesMap[r.tokenTypeIdx];if(!1===a)continue r}return!0}}return!1}function su(e){return iR(e,e=>iR(e,e=>iR(e,e=>(0,nC.Z)(e.categoryMatches))))}function sc(e){return`${ik(e)}_#_${e.idx}_#_${sd(e)}`}function sd(e){return e instanceof ir?e.terminalType.name:e instanceof n3?e.nonTerminalName:""}class sh extends aK{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}class sf extends aK{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}class sp extends aK{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}let sm="MismatchedTokenException",sg="NoViableAltException",sy="EarlyExitException",sT="NotAllInputParsedException",sv=[sm,sg,sy,sT];function sE(e){return iT(sv,e.name)}Object.freeze(sv);class sR extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class sA extends sR{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=sm}}class sk extends sR{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=sg}}class sI extends sR{constructor(e,t){super(e,t),this.name=sT}}class sx extends sR{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=sy}}let sS={},sN="InRuleRecoveryException";class sC extends Error{constructor(e){super(e),this.name=sN}}function s$(e,t,r,n,i,a,s){let o=this.getKeyForAutomaticLookahead(n,i),l=this.firstAfterRepMap[o];if(void 0===l){let e=this.getCurrRuleFullName(),t=this.getGAstProductions()[e],r=new a(t,i);l=r.startWalking(),this.firstAfterRepMap[o]=l}let u=l.token,c=l.occurrence,d=l.isEndOfRule;1===this.RULE_STACK.length&&d&&void 0===u&&(u=aZ,c=1),void 0!==u&&void 0!==c&&this.shouldInRepetitionRecoveryBeTried(u,c,s)&&this.tryInRepetitionRecovery(e,t,r,u)}class sL{constructor(e){var t;this.maxLookahead=null!==(t=null==e?void 0:e.maxLookahead)&&void 0!==t?t:sW.maxLookahead}validate(e){let t=this.validateNoLeftRecursion(e.rules);if((0,nC.Z)(t)){let r=this.validateEmptyOrAlternatives(e.rules),n=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),i=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead),a=[...t,...r,...n,...i];return a}return t}validateNoLeftRecursion(e){return aV(e,e=>(function e(t,r,n,i=[]){let a=[],s=function e(t){let r=[];if((0,nC.Z)(t))return r;let n=iz(t);if(n instanceof n3)r.push(n.referencedRule);else if(n instanceof n4||n instanceof n5||n instanceof n6||n instanceof n9||n instanceof ie||n instanceof n8)r=r.concat(e(n.definition));else if(n instanceof it)r=(0,il.Z)((0,n$.Z)(n.definition,t=>e(t.definition)));else if(n instanceof ir);else throw Error("non exhaustive match");let i=iA(n),a=t.length>1;if(!i||!a)return r;{let n=nP(t);return r.concat(e(n))}}(r.definition);if((0,nC.Z)(s))return[];{let r=t.name,o=iT(s,t);o&&a.push({message:n.buildLeftRecursionError({topLevelRule:t,leftRecursionPath:i}),type:re.LEFT_RECURSION,ruleName:r});let l=iW(s,i.concat([t])),u=aV(l,r=>{let a=(0,nw.Z)(i);return a.push(r),e(t,r,n,a)});return a.concat(u)}})(e,e,aB))}validateEmptyOrAlternatives(e){return aV(e,e=>(function(e,t){let r=new sf;e.accept(r);let n=r.alternations,i=aV(n,r=>{let n=aX(r.definition);return aV(n,(n,i)=>{let a=a5([n],[],ay,1);return(0,nC.Z)(a)?[{message:t.buildEmptyAlternationError({topLevelRule:e,alternation:r,emptyChoiceIdx:i}),type:re.NONE_LAST_EMPTY_ALT,ruleName:e.name,occurrence:r.idx,alternative:i+1}]:[]})});return i})(e,aB))}validateAmbiguousAlternationAlternatives(e,t){return aV(e,e=>(function(e,t,r){let n=new sf;e.accept(n);let i=n.alternations;i=ib(i,e=>!0===e.ignoreAmbiguities);let a=aV(i,n=>{let i=n.idx,a=n.maxLookahead||t,s=ss(i,e,a,n),o=function(e,t,r,n){let i=[],a=(0,iM.Z)(e,(r,n,a)=>(!0===t.definition[a].ignoreAmbiguities||(0,nS.Z)(n,n=>{let s=[a];(0,nS.Z)(e,(e,r)=>{a!==r&&sl(e,n)&&!0!==t.definition[r].ignoreAmbiguities&&s.push(r)}),s.length>1&&!sl(i,n)&&(i.push(n),r.push({alts:s,path:n}))}),r),[]),s=(0,n$.Z)(a,e=>{let i=(0,n$.Z)(e.alts,e=>e+1),a=n.buildAlternationAmbiguityError({topLevelRule:r,alternation:t,ambiguityIndices:i,prefixPath:e.path});return{message:a,type:re.AMBIGUOUS_ALTS,ruleName:r.name,occurrence:t.idx,alternatives:e.alts}});return s}(s,n,e,r),l=function(e,t,r,n){let i=(0,iM.Z)(e,(e,t,r)=>{let n=(0,n$.Z)(t,e=>({idx:r,path:e}));return e.concat(n)},[]),a=iH(aV(i,e=>{let a=t.definition[e.idx];if(!0===a.ignoreAmbiguities)return[];let s=e.idx,o=e.path,l=(0,iD.Z)(i,e=>{var r;return!0!==t.definition[e.idx].ignoreAmbiguities&&e.idx<s&&(r=e.path).length<o.length&&iR(r,(e,t)=>{let r=o[t];return e===r||r.categoryMatchesMap[e.tokenTypeIdx]})}),u=(0,n$.Z)(l,e=>{let i=[e.idx+1,s+1],a=0===t.idx?"":t.idx,o=n.buildAlternationPrefixAmbiguityError({topLevelRule:r,alternation:t,ambiguityIndices:i,prefixPath:e.path});return{message:o,type:re.AMBIGUOUS_PREFIX_ALTS,ruleName:r.name,occurrence:a,alternatives:i}});return u}));return a}(s,n,e,r);return o.concat(l)});return a})(e,t,aB))}validateSomeNonEmptyLookaheadPath(e,t){return function(e,t,r){let n=[];return(0,nS.Z)(e,e=>{let i=new sp;e.accept(i);let a=i.allProductions;(0,nS.Z)(a,i=>{let a=a6(i),s=i.maxLookahead||t,o=i.idx,l=so(o,e,a,s),u=l[0];if((0,nC.Z)((0,il.Z)(u))){let t=r.buildEmptyRepetitionError({topLevelRule:e,repetition:i});n.push({message:t,type:re.NO_NON_EMPTY_LOOKAHEAD,ruleName:e.name})}})}),n}(e,t,aB)}buildLookaheadForAlternation(e){return function(e,t,r,n,i,a){let s=ss(e,t,r),o=su(s)?aT:ay;return a(s,n,o,i)}(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,a8)}buildLookaheadForOptional(e){return function(e,t,r,n,i,a){let s=so(e,t,i,r),o=su(s)?aT:ay;return a(s[0],o,n)}(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,a6(e.prodType),se)}}let sw=new class extends aK{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}};function sb(e,t){!0===isNaN(e.startOffset)?(e.startOffset=t.startOffset,e.endOffset=t.endOffset):e.endOffset<t.endOffset==!0&&(e.endOffset=t.endOffset)}function sO(e,t){!0===isNaN(e.startOffset)?(e.startOffset=t.startOffset,e.startColumn=t.startColumn,e.startLine=t.startLine,e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine):e.endOffset<t.endOffset==!0&&(e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine)}function s_(e,t){Object.defineProperty(e,"name",{enumerable:!1,configurable:!0,writable:!1,value:t})}function sP(e,t){let r=(0,nB.Z)(e),n=r.length;for(let i=0;i<n;i++){let n=r[i],a=e[n],s=a.length;for(let e=0;e<s;e++){let r=a[e];void 0===r.tokenTypeIdx&&this[r.name](r.children,t)}}}(e4=t8||(t8={}))[e4.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",e4[e4.MISSING_METHOD=1]="MISSING_METHOD";var sM=r(77226);let sD={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(sD);let sZ=aD({name:"RECORDING_PHASE_TOKEN",pattern:aS.NA});aR([sZ]);let sU=aU(sZ,"This IToken indicates the Parser is in Recording Phase\n	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",-1,-1,-1,-1,-1,-1);Object.freeze(sU);let sF={name:"This CSTNode indicates the Parser is in Recording Phase\n	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",children:{}};function sG(e,t,r,n=!1){sj(r);let i=(0,am.Z)(this.recordingProdStack),a=(0,iO.Z)(t)?t:t.DEF,s=new e({definition:[],idx:r});return n&&(s.separator=t.SEP),(0,nL.Z)(t,"MAX_LOOKAHEAD")&&(s.maxLookahead=t.MAX_LOOKAHEAD),this.recordingProdStack.push(s),a.call(this),i.definition.push(s),this.recordingProdStack.pop(),sD}function sB(e,t){sj(t);let r=(0,am.Z)(this.recordingProdStack),n=!1===(0,ih.Z)(e),i=!1===n?e:e.DEF,a=new it({definition:[],idx:t,ignoreAmbiguities:n&&!0===e.IGNORE_AMBIGUITIES});(0,nL.Z)(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD);let s=im(i,e=>(0,iO.Z)(e.GATE));return a.hasPredicates=s,r.definition.push(a),(0,nS.Z)(i,e=>{let t=new n4({definition:[]});a.definition.push(t),(0,nL.Z)(e,"IGNORE_AMBIGUITIES")?t.ignoreAmbiguities=e.IGNORE_AMBIGUITIES:(0,nL.Z)(e,"GATE")&&(t.ignoreAmbiguities=!0),this.recordingProdStack.push(t),e.ALT.call(this),this.recordingProdStack.pop()}),sD}function sK(e){return 0===e?"":`${e}`}function sj(e){if(e<0||e>255){let t=Error(`Invalid DSL Method idx value: <${e}>
	Idx value must be a none negative value smaller than 256`);throw t.KNOWN_RECORDER_ERROR=!0,t}}let sV=aU(aZ,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(sV);let sW=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:aF,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),sH=Object.freeze({recoveryValueFunc:()=>void 0,resyncEnabled:!0});function sz(e){return function(){return e}}(e5=re||(re={}))[e5.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",e5[e5.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",e5[e5.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",e5[e5.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",e5[e5.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",e5[e5.LEFT_RECURSION=5]="LEFT_RECURSION",e5[e5.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",e5[e5.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",e5[e5.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",e5[e5.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",e5[e5.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",e5[e5.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",e5[e5.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",e5[e5.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION";class sY{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{this.selfAnalysisDone=!0;let e=this.className;this.TRACE_INIT("toFastProps",()=>{nb(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),(0,nS.Z)(this.definedRulesNames,e=>{let t;let r=this[e],n=r.originalGrammarAction;this.TRACE_INIT(`${e} Rule`,()=>{t=this.topLevelRuleRecord(e,n)}),this.gastProductionsCache[e]=t})}finally{this.disableRecording()}});let t=[];if(this.TRACE_INIT("Grammar Resolving",()=>{t=function(e){let t=(0,iC.Z)(e,{errMsgProvider:aG}),r={};return(0,nS.Z)(e.rules,e=>{r[e.name]=e}),function(e,t){let r=new aj(e,t);return r.resolveRefs(),r.errors}(r,t.errMsgProvider)}({rules:(0,nN.Z)(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(t)}),this.TRACE_INIT("Grammar Validations",()=>{if((0,nC.Z)(t)&&!1===this.skipValidations){var r;let t=(r={rules:(0,nN.Z)(this.gastProductionsCache),tokenTypes:(0,nN.Z)(this.tokensMap),errMsgProvider:aB,grammarName:e},function(e,t,r,n){let i=aV(e,e=>(function(e,t){let r=new sh;e.accept(r);let n=r.allProductions,i=aq(n,sc),a=nY(i,e=>e.length>1),s=(0,n$.Z)((0,nN.Z)(a),r=>{let n=iz(r),i=t.buildDuplicateFoundError(e,r),a=ik(n),s={message:i,type:re.DUPLICATE_PRODUCTIONS,ruleName:e.name,dslName:a,occurrence:n.idx},o=sd(n);return o&&(s.parameter=o),s});return s})(e,r)),a=function(e,t,r){let n=[],i=(0,n$.Z)(t,e=>e.name);return(0,nS.Z)(e,e=>{let t=e.name;if(iT(i,t)){let i=r.buildNamespaceConflictError(e);n.push({message:i,type:re.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:t})}}),n}(e,t,r),s=aV(e,e=>(function(e,t){let r=new sf;e.accept(r);let n=r.alternations,i=aV(n,r=>r.definition.length>255?[{message:t.buildTooManyAlternativesError({topLevelRule:e,alternation:r}),type:re.TOO_MANY_ALTS,ruleName:e.name,occurrence:r.idx}]:[]);return i})(e,r)),o=aV(e,t=>(function(e,t,r,n){let i=[],a=(0,iM.Z)(t,(t,r)=>r.name===e.name?t+1:t,0);if(a>1){let t=n.buildDuplicateRuleNameError({topLevelRule:e,grammarName:r});i.push({message:t,type:re.DUPLICATE_RULE_NAME,ruleName:e.name})}return i})(t,e,n,r));return i.concat(a,s,o)}((r=(0,iC.Z)(r,{errMsgProvider:aB})).rules,r.tokenTypes,r.errMsgProvider,r.grammarName)),n=function(e){let t=e.lookaheadStrategy.validate({rules:e.rules,tokenTypes:e.tokenTypes,grammarName:e.grammarName});return(0,n$.Z)(t,e=>Object.assign({type:re.CUSTOM_LOOKAHEAD_VALIDATION},e))}({lookaheadStrategy:this.lookaheadStrategy,rules:(0,nN.Z)(this.gastProductionsCache),tokenTypes:(0,nN.Z)(this.tokensMap),grammarName:e});this.definitionErrors=this.definitionErrors.concat(t,n)}}),(0,nC.Z)(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{let e=function(e){let t={};return(0,nS.Z)(e,e=>{let r=new iS(e).startWalking();nj(t,r)}),t}((0,nN.Z)(this.gastProductionsCache));this.resyncFollows=e}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var e,t;null===(t=(e=this.lookaheadStrategy).initialize)||void 0===t||t.call(e,{rules:(0,nN.Z)(this.gastProductionsCache)}),this.preComputeLookaheadFunctions((0,nN.Z)(this.gastProductionsCache))})),!sY.DEFER_DEFINITION_ERRORS_HANDLING&&!(0,nC.Z)(this.definitionErrors))throw Error(`Parser Definition Errors detected:
 ${(0,n$.Z)(this.definitionErrors,e=>e.message).join("\n-------------------------------\n")}`)})}constructor(e,t){if(this.definitionErrors=[],this.selfAnalysisDone=!1,this.initErrorHandler(t),this.initLexerAdapter(),this.initLooksAhead(t),this.initRecognizerEngine(e,t),this.initRecoverable(t),this.initTreeBuilder(t),this.initContentAssist(),this.initGastRecorder(t),this.initPerformanceTracer(t),(0,nL.Z)(t,"ignoredIssues"))throw Error("The <ignoredIssues> IParserConfig property has been deprecated.\n	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.\n	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES\n	For further details.");this.skipValidations=(0,nL.Z)(t,"skipValidations")?t.skipValidations:sW.skipValidations}}sY.DEFER_DEFINITION_ERRORS_HANDLING=!1,function(e,t){t.forEach(t=>{let r=t.prototype;Object.getOwnPropertyNames(r).forEach(n=>{if("constructor"===n)return;let i=Object.getOwnPropertyDescriptor(r,n);i&&(i.get||i.set)?Object.defineProperty(e.prototype,n,i):e.prototype[n]=t.prototype[n]})})}(sY,[class{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=(0,nL.Z)(e,"recoveryEnabled")?e.recoveryEnabled:sW.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=s$)}getTokenToInsert(e){let t=aU(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,r,n){let i=this.findReSyncTokenType(),a=this.exportLexerState(),s=[],o=!1,l=this.LA(1),u=this.LA(1),c=()=>{let e=this.LA(0),t=this.errorMessageProvider.buildMismatchTokenMessage({expected:n,actual:l,previous:e,ruleName:this.getCurrRuleFullName()}),r=new sA(t,l,this.LA(0));r.resyncedTokens=aX(s),this.SAVE_ERROR(r)};for(;!o;){if(this.tokenMatcher(u,n)){c();return}if(r.call(this)){c(),e.apply(this,t);return}this.tokenMatcher(u,i)?o=!0:(u=this.SKIP_TOKEN(),this.addToResyncTokens(u,s))}this.importLexerState(a)}shouldInRepetitionRecoveryBeTried(e,t,r){return!(!1===r||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){let r=this.getCurrentGrammarPath(e,t),n=this.getNextPossibleTokenTypes(r);return n}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t)){let t=this.getTokenToInsert(e);return t}if(this.canRecoverWithSingleTokenDeletion(e)){let e=this.SKIP_TOKEN();return this.consumeToken(),e}throw new sC("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||(0,nC.Z)(t))return!1;let r=this.LA(1),n=void 0!==(0,iY.Z)(t,e=>this.tokenMatcher(r,e));return n}canRecoverWithSingleTokenDeletion(e){if(!this.canTokenTypeBeDeletedInRecovery(e))return!1;let t=this.tokenMatcher(this.LA(2),e);return t}isInCurrentRuleReSyncSet(e){let t=this.getCurrFollowKey(),r=this.getFollowSetFromFollowKey(t);return iT(r,e)}findReSyncTokenType(){let e=this.flattenFollowSet(),t=this.LA(1),r=2;for(;;){let n=(0,iY.Z)(e,e=>{let r=ay(t,e);return r});if(void 0!==n)return n;t=this.LA(r),r++}}getCurrFollowKey(){if(1===this.RULE_STACK.length)return sS;let e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),r=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(r)}}buildFullFollowKeyStack(){let e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return(0,n$.Z)(e,(r,n)=>0===n?sS:{ruleName:this.shortRuleNameToFullName(r),idxInCallingRule:t[n],inRule:this.shortRuleNameToFullName(e[n-1])})}flattenFollowSet(){let e=(0,n$.Z)(this.buildFullFollowKeyStack(),e=>this.getFollowSetFromFollowKey(e));return(0,il.Z)(e)}getFollowSetFromFollowKey(e){if(e===sS)return[aZ];let t=e.ruleName+e.idxInCallingRule+ix+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,aZ)||t.push(e),t}reSyncTo(e){let t=[],r=this.LA(1);for(;!1===this.tokenMatcher(r,e);)r=this.SKIP_TOKEN(),this.addToResyncTokens(r,t);return aX(t)}attemptInRepetitionRecovery(e,t,r,n,i,a,s){}getCurrentGrammarPath(e,t){let r=this.getHumanReadableRuleStack(),n=(0,nw.Z)(this.RULE_OCCURRENCE_STACK);return{ruleStack:r,occurrenceStack:n,lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return(0,n$.Z)(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}},class{initLooksAhead(e){this.dynamicTokensEnabled=(0,nL.Z)(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:sW.dynamicTokensEnabled,this.maxLookahead=(0,nL.Z)(e,"maxLookahead")?e.maxLookahead:sW.maxLookahead,this.lookaheadStrategy=(0,nL.Z)(e,"lookaheadStrategy")?e.lookaheadStrategy:new sL({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){(0,nS.Z)(e,e=>{this.TRACE_INIT(`${e.name} Rule Lookahead`,()=>{let{alternation:t,repetition:r,option:n,repetitionMandatory:i,repetitionMandatoryWithSeparator:a,repetitionWithSeparator:s}=function(e){sw.reset(),e.accept(sw);let t=sw.dslMethods;return sw.reset(),t}(e);(0,nS.Z)(t,t=>{let r=0===t.idx?"":t.idx;this.TRACE_INIT(`${ik(t)}${r}`,()=>{var r;let n=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:t.idx,rule:e,maxLookahead:t.maxLookahead||this.maxLookahead,hasPredicates:t.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),i=(r=this.fullRuleNameToShort[e.name],256|t.idx|r);this.setLaFuncCache(i,n)})}),(0,nS.Z)(r,t=>{this.computeLookaheadFunc(e,t.idx,768,"Repetition",t.maxLookahead,ik(t))}),(0,nS.Z)(n,t=>{this.computeLookaheadFunc(e,t.idx,512,"Option",t.maxLookahead,ik(t))}),(0,nS.Z)(i,t=>{this.computeLookaheadFunc(e,t.idx,1024,"RepetitionMandatory",t.maxLookahead,ik(t))}),(0,nS.Z)(a,t=>{this.computeLookaheadFunc(e,t.idx,1536,"RepetitionMandatoryWithSeparator",t.maxLookahead,ik(t))}),(0,nS.Z)(s,t=>{this.computeLookaheadFunc(e,t.idx,1280,"RepetitionWithSeparator",t.maxLookahead,ik(t))})})})}computeLookaheadFunc(e,t,r,n,i,a){this.TRACE_INIT(`${a}${0===t?"":t}`,()=>{let a=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:i||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:n}),s=t|r|this.fullRuleNameToShort[e.name];this.setLaFuncCache(s,a)})}getKeyForAutomaticLookahead(e,t){let r=this.getLastExplicitRuleShortName();return t|e|r}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}},class{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=(0,nL.Z)(e,"nodeLocationTracking")?e.nodeLocationTracking:sW.nodeLocationTracking,this.outputCst){if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=sO,this.setNodeLocationFromNode=sO,this.cstPostRule=ap.Z,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=ap.Z,this.setNodeLocationFromNode=ap.Z,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=sb,this.setNodeLocationFromNode=sb,this.cstPostRule=ap.Z,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=ap.Z,this.setNodeLocationFromNode=ap.Z,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=ap.Z,this.setNodeLocationFromNode=ap.Z,this.cstPostRule=ap.Z,this.setInitialNodeLocation=ap.Z;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}else this.cstInvocationStateUpdate=ap.Z,this.cstFinallyStateUpdate=ap.Z,this.cstPostTerminal=ap.Z,this.cstPostNonTerminal=ap.Z,this.cstPostRule=ap.Z}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){let t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){let t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){let t=this.LA(0),r=e.location;r.startOffset<=t.startOffset==!0?(r.endOffset=t.endOffset,r.endLine=t.endLine,r.endColumn=t.endColumn):(r.startOffset=NaN,r.startLine=NaN,r.startColumn=NaN)}cstPostRuleOnlyOffset(e){let t=this.LA(0),r=e.location;r.startOffset<=t.startOffset==!0?r.endOffset=t.endOffset:r.startOffset=NaN}cstPostTerminal(e,t){var r;let n=this.CST_STACK[this.CST_STACK.length-1];void 0===(r=n).children[e]?r.children[e]=[t]:r.children[e].push(t),this.setNodeLocationFromToken(n.location,t)}cstPostNonTerminal(e,t){var r;let n=this.CST_STACK[this.CST_STACK.length-1];void 0===(r=n).children[t]?r.children[t]=[e]:r.children[t].push(e),this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if((0,iN.Z)(this.baseCstVisitorConstructor)){let e=function(e,t){let r=function(){};return s_(r,e+"BaseSemantics"),r.prototype={visit:function(e,t){if((0,ih.Z)(e)&&(e=e[0]),!(0,iN.Z)(e))return this[e.name](e.children,t)},validateVisitor:function(){let e=function(e,t){let r=function(e,t){let r=(0,iD.Z)(t,t=>!1===(0,iO.Z)(e[t])),n=(0,n$.Z)(r,t=>({msg:`Missing visitor method: <${t}> on ${e.constructor.name} CST Visitor.`,type:t8.MISSING_METHOD,methodName:t}));return iH(n)}(e,t);return r}(this,t);if(!(0,nC.Z)(e)){let t=(0,n$.Z)(e,e=>e.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${t.join("\n\n").replace(/\n/g,"\n	")}`)}}},r.prototype.constructor=r,r._RULE_NAMES=t,r}(this.className,(0,nB.Z)(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if((0,iN.Z)(this.baseCstVisitorWithDefaultsConstructor)){let e=function(e,t,r){let n=function(){};s_(n,e+"BaseSemanticsWithDefaults");let i=Object.create(r.prototype);return(0,nS.Z)(t,e=>{i[e]=sP}),n.prototype=i,n.prototype.constructor=n,n}(this.className,(0,nB.Z)(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){let e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}},class{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(!0!==this.selfAnalysisDone)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):sV}LA(e){let t=this.currIdx+e;return t<0||this.tokVectorLength<=t?sV:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}},class{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=aT,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},(0,nL.Z)(t,"serializedGrammar"))throw Error("The Parser's configuration can no longer contain a <serializedGrammar> property.\n	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0\n	For Further details.");if((0,ih.Z)(e)){if((0,nC.Z)(e))throw Error("A Token Vocabulary cannot be empty.\n	Note that the first argument for the parser constructor\n	is no longer a Token vector (since v4.0).");if("number"==typeof e[0].startOffset)throw Error("The Parser constructor no longer accepts a token vector as the first argument.\n	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0\n	For Further details.")}if((0,ih.Z)(e))this.tokensMap=(0,iM.Z)(e,(e,t)=>(e[t.name]=t,e),{});else if((0,nL.Z)(e,"modes")&&iR((0,il.Z)((0,nN.Z)(e.modes)),aI)){let t=(0,il.Z)((0,nN.Z)(e.modes)),r=io(t);this.tokensMap=(0,iM.Z)(r,(e,t)=>(e[t.name]=t,e),{})}else if((0,sM.Z)(e))this.tokensMap=(0,nw.Z)(e);else throw Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=aZ;let r=(0,nL.Z)(e,"modes")?(0,il.Z)((0,nN.Z)(e.modes)):(0,nN.Z)(e),n=iR(r,e=>(0,nC.Z)(e.categoryMatches));this.tokenMatcher=n?aT:ay,aR((0,nN.Z)(this.tokensMap))}defineRule(e,t,r){let n;if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);let i=(0,nL.Z)(r,"resyncEnabled")?r.resyncEnabled:sH.resyncEnabled,a=(0,nL.Z)(r,"recoveryValueFunc")?r.recoveryValueFunc:sH.recoveryValueFunc,s=this.ruleShortNameIdx<<12;this.ruleShortNameIdx++,this.shortRuleNameToFull[s]=e,this.fullRuleNameToShort[e]=s,n=!0===this.outputCst?function(...r){try{this.ruleInvocationStateUpdate(s,e,this.subruleIdx),t.apply(this,r);let n=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(n),n}catch(e){return this.invokeRuleCatch(e,i,a)}finally{this.ruleFinallyStateUpdate()}}:function(...r){try{return this.ruleInvocationStateUpdate(s,e,this.subruleIdx),t.apply(this,r)}catch(e){return this.invokeRuleCatch(e,i,a)}finally{this.ruleFinallyStateUpdate()}};let o=Object.assign(n,{ruleName:e,originalGrammarAction:t});return o}invokeRuleCatch(e,t,r){let n=1===this.RULE_STACK.length,i=t&&!this.isBackTracking()&&this.recoveryEnabled;if(sE(e)){let t=e;if(i){let n=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(n)){if(t.resyncedTokens=this.reSyncTo(n),!this.outputCst)return r(e);{let e=this.CST_STACK[this.CST_STACK.length-1];return e.recoveredNode=!0,e}}if(this.outputCst){let e=this.CST_STACK[this.CST_STACK.length-1];e.recoveredNode=!0,t.partialCstResult=e}throw t}if(n)return this.moveToTerminatedState(),r(e);throw t}throw e}optionInternal(e,t){let r=this.getKeyForAutomaticLookahead(512,t);return this.optionInternalLogic(e,t,r)}optionInternalLogic(e,t,r){let n,i=this.getLaFuncFromCache(r);if("function"!=typeof e){n=e.DEF;let t=e.GATE;if(void 0!==t){let e=i;i=()=>t.call(this)&&e.call(this)}}else n=e;if(!0===i.call(this))return n.call(this)}atLeastOneInternal(e,t){let r=this.getKeyForAutomaticLookahead(1024,e);return this.atLeastOneInternalLogic(e,t,r)}atLeastOneInternalLogic(e,t,r){let n,i=this.getLaFuncFromCache(r);if("function"!=typeof t){n=t.DEF;let e=t.GATE;if(void 0!==e){let t=i;i=()=>e.call(this)&&t.call(this)}}else n=t;if(!0===i.call(this)){let e=this.doSingleRepetition(n);for(;!0===i.call(this)&&!0===e;)e=this.doSingleRepetition(n)}else throw this.raiseEarlyExitException(e,t9.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,1024,e,a3)}atLeastOneSepFirstInternal(e,t){let r=this.getKeyForAutomaticLookahead(1536,e);this.atLeastOneSepFirstInternalLogic(e,t,r)}atLeastOneSepFirstInternalLogic(e,t,r){let n=t.DEF,i=t.SEP,a=this.getLaFuncFromCache(r);if(!0===a.call(this)){n.call(this);let t=()=>this.tokenMatcher(this.LA(1),i);for(;!0===this.tokenMatcher(this.LA(1),i);)this.CONSUME(i),n.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,n,a7],t,1536,e,a7)}else throw this.raiseEarlyExitException(e,t9.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){let r=this.getKeyForAutomaticLookahead(768,e);return this.manyInternalLogic(e,t,r)}manyInternalLogic(e,t,r){let n,i=this.getLaFuncFromCache(r);if("function"!=typeof t){n=t.DEF;let e=t.GATE;if(void 0!==e){let t=i;i=()=>e.call(this)&&t.call(this)}}else n=t;let a=!0;for(;!0===i.call(this)&&!0===a;)a=this.doSingleRepetition(n);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,768,e,a1,a)}manySepFirstInternal(e,t){let r=this.getKeyForAutomaticLookahead(1280,e);this.manySepFirstInternalLogic(e,t,r)}manySepFirstInternalLogic(e,t,r){let n=t.DEF,i=t.SEP,a=this.getLaFuncFromCache(r);if(!0===a.call(this)){n.call(this);let t=()=>this.tokenMatcher(this.LA(1),i);for(;!0===this.tokenMatcher(this.LA(1),i);)this.CONSUME(i),n.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,n,a2],t,1280,e,a2)}}repetitionSepSecondInternal(e,t,r,n,i){for(;r();)this.CONSUME(t),n.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,r,n,i],r,1536,e,i)}doSingleRepetition(e){let t=this.getLexerPosition();e.call(this);let r=this.getLexerPosition();return r>t}orInternal(e,t){let r=this.getKeyForAutomaticLookahead(256,t),n=(0,ih.Z)(e)?e:e.DEF,i=this.getLaFuncFromCache(r),a=i.call(this,n);if(void 0!==a){let e=n[a];return e.ALT.call(this)}this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),0===this.RULE_STACK.length&&!1===this.isAtEndOfInput()){let e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new sI(t,e))}}subruleInternal(e,t,r){let n;try{let i=void 0!==r?r.ARGS:void 0;return this.subruleIdx=t,n=e.apply(this,i),this.cstPostNonTerminal(n,void 0!==r&&void 0!==r.LABEL?r.LABEL:e.ruleName),n}catch(t){throw this.subruleInternalError(t,r,e.ruleName)}}subruleInternalError(e,t,r){throw sE(e)&&void 0!==e.partialCstResult&&(this.cstPostNonTerminal(e.partialCstResult,void 0!==t&&void 0!==t.LABEL?t.LABEL:r),delete e.partialCstResult),e}consumeInternal(e,t,r){let n;try{let t=this.LA(1);!0===this.tokenMatcher(t,e)?(this.consumeToken(),n=t):this.consumeInternalError(e,t,r)}catch(r){n=this.consumeInternalRecovery(e,t,r)}return this.cstPostTerminal(void 0!==r&&void 0!==r.LABEL?r.LABEL:e.name,n),n}consumeInternalError(e,t,r){let n;let i=this.LA(0);throw n=void 0!==r&&r.ERR_MSG?r.ERR_MSG:this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:i,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new sA(n,t,i))}consumeInternalRecovery(e,t,r){if(this.recoveryEnabled&&"MismatchedTokenException"===r.name&&!this.isBackTracking()){let n=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,n)}catch(e){if(e.name===sN)throw r;throw e}}else throw r}saveRecogState(){let e=this.errors,t=(0,nw.Z)(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,r){this.RULE_OCCURRENCE_STACK.push(r),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return 0!==this.isBackTrackingStack.length}getCurrRuleFullName(){let e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),aZ)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}},class{ACTION(e){return e.call(this)}consume(e,t,r){return this.consumeInternal(t,e,r)}subrule(e,t,r){return this.subruleInternal(t,e,r)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,r=sH){if(iT(this.definedRulesNames,e)){let t=aB.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),r={message:t,type:re.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(r)}this.definedRulesNames.push(e);let n=this.defineRule(e,t,r);return this[e]=n,n}OVERRIDE_RULE(e,t,r=sH){let n=function(e,t,r){let n=[];return iT(t,e)||n.push({message:`Invalid rule override, rule: ->${e}<- cannot be overridden in the grammar: ->${r}<-as it is not defined in any of the super grammars `,type:re.INVALID_RULE_OVERRIDE,ruleName:e}),n}(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(n);let i=this.defineRule(e,t,r);return this[e]=i,i}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);let r=this.saveRecogState();try{return e.apply(this,t),!0}catch(e){if(sE(e))return!1;throw e}finally{this.reloadRecogState(r),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){var e;return e=(0,nN.Z)(this.gastProductionsCache),(0,n$.Z)(e,function e(t){function r(t){return(0,n$.Z)(t,e)}if(t instanceof n3){let e={type:"NonTerminal",name:t.nonTerminalName,idx:t.idx};return(0,nM.Z)(t.label)&&(e.label=t.label),e}if(t instanceof n4)return{type:"Alternative",definition:r(t.definition)};if(t instanceof n5)return{type:"Option",idx:t.idx,definition:r(t.definition)};if(t instanceof n6)return{type:"RepetitionMandatory",idx:t.idx,definition:r(t.definition)};if(t instanceof n9)return{type:"RepetitionMandatoryWithSeparator",idx:t.idx,separator:e(new ir({terminalType:t.separator})),definition:r(t.definition)};if(t instanceof ie)return{type:"RepetitionWithSeparator",idx:t.idx,separator:e(new ir({terminalType:t.separator})),definition:r(t.definition)};if(t instanceof n8)return{type:"Repetition",idx:t.idx,definition:r(t.definition)};else if(t instanceof it)return{type:"Alternation",idx:t.idx,definition:r(t.definition)};else if(t instanceof ir){var n,i;let e={type:"Terminal",name:t.terminalType.name,label:(i=n=t.terminalType,(0,nM.Z)(i.LABEL)&&""!==i.LABEL)?n.LABEL:n.name,idx:t.idx};(0,nM.Z)(t.label)&&(e.terminalLabel=t.label);let r=t.terminalType.PATTERN;return t.terminalType.PATTERN&&(e.pattern=n1(r)?r.source:r),e}else if(t instanceof n7)return{type:"Rule",name:t.name,orgText:t.orgText,definition:r(t.definition)};else throw Error("non exhaustive match")})}},class{initErrorHandler(e){this._errors=[],this.errorMessageProvider=(0,nL.Z)(e,"errorMessageProvider")?e.errorMessageProvider:sW.errorMessageProvider}SAVE_ERROR(e){if(sE(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:(0,nw.Z)(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return(0,nw.Z)(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,r){let n=this.getCurrRuleFullName(),i=this.getGAstProductions()[n],a=so(e,i,t,this.maxLookahead),s=a[0],o=[];for(let e=1;e<=this.maxLookahead;e++)o.push(this.LA(e));let l=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:s,actual:o,previous:this.LA(0),customUserDescription:r,ruleName:n});throw this.SAVE_ERROR(new sx(l,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){let r=this.getCurrRuleFullName(),n=this.getGAstProductions()[r],i=ss(e,n,this.maxLookahead),a=[];for(let e=1;e<=this.maxLookahead;e++)a.push(this.LA(e));let s=this.LA(0),o=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:i,actual:a,previous:s,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new sk(o,this.LA(1),s))}},class{initContentAssist(){}computeContentAssist(e,t){let r=this.gastProductionsCache[e];if((0,iN.Z)(r))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return a5([r],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){let t=iz(e.ruleStack),r=this.getGAstProductions(),n=r[t],i=new aJ(n,e).startWalking();return i}},class{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){let t=e>0?e:"";this[`CONSUME${t}`]=function(t,r){return this.consumeInternalRecord(t,e,r)},this[`SUBRULE${t}`]=function(t,r){return this.subruleInternalRecord(t,e,r)},this[`OPTION${t}`]=function(t){return this.optionInternalRecord(t,e)},this[`OR${t}`]=function(t){return this.orInternalRecord(t,e)},this[`MANY${t}`]=function(t){this.manyInternalRecord(e,t)},this[`MANY_SEP${t}`]=function(t){this.manySepFirstInternalRecord(e,t)},this[`AT_LEAST_ONE${t}`]=function(t){this.atLeastOneInternalRecord(e,t)},this[`AT_LEAST_ONE_SEP${t}`]=function(t){this.atLeastOneSepFirstInternalRecord(e,t)}}this.consume=function(e,t,r){return this.consumeInternalRecord(t,e,r)},this.subrule=function(e,t,r){return this.subruleInternalRecord(t,e,r)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{for(let e=0;e<10;e++){let t=e>0?e:"";delete this[`CONSUME${t}`],delete this[`SUBRULE${t}`],delete this[`OPTION${t}`],delete this[`OR${t}`],delete this[`MANY${t}`],delete this[`MANY_SEP${t}`],delete this[`AT_LEAST_ONE${t}`],delete this[`AT_LEAST_ONE_SEP${t}`]}delete this.consume,delete this.subrule,delete this.option,delete this.or,delete this.many,delete this.atLeastOne,delete this.ACTION,delete this.BACKTRACK,delete this.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return sV}topLevelRuleRecord(e,t){try{let r=new n7({definition:[],name:e});return r.name=e,this.recordingProdStack.push(r),t.call(this),this.recordingProdStack.pop(),r}catch(e){if(!0!==e.KNOWN_RECORDER_ERROR)try{e.message=e.message+'\n	 This error was thrown during the "grammar recording phase" For more info see:\n	https://chevrotain.io/docs/guide/internals.html#grammar-recording'}catch(t){throw e}throw e}}optionInternalRecord(e,t){return sG.call(this,n5,e,t)}atLeastOneInternalRecord(e,t){sG.call(this,n6,t,e)}atLeastOneSepFirstInternalRecord(e,t){sG.call(this,n9,t,e,!0)}manyInternalRecord(e,t){sG.call(this,n8,t,e)}manySepFirstInternalRecord(e,t){sG.call(this,ie,t,e,!0)}orInternalRecord(e,t){return sB.call(this,e,t)}subruleInternalRecord(e,t,r){if(sj(t),!e||!1===(0,nL.Z)(e,"ruleName")){let r=Error(`<SUBRULE${sK(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw r.KNOWN_RECORDER_ERROR=!0,r}let n=(0,am.Z)(this.recordingProdStack),i=e.ruleName,a=new n3({idx:t,nonTerminalName:i,label:null==r?void 0:r.LABEL,referencedRule:void 0});return n.definition.push(a),this.outputCst?sF:sD}consumeInternalRecord(e,t,r){if(sj(t),!aA(e)){let r=Error(`<CONSUME${sK(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw r.KNOWN_RECORDER_ERROR=!0,r}let n=(0,am.Z)(this.recordingProdStack),i=new ir({idx:t,terminalType:e,label:null==r?void 0:r.LABEL});return n.definition.push(i),sU}},class{initPerformanceTracer(e){if((0,nL.Z)(e,"traceInitPerf")){let t=e.traceInitPerf,r="number"==typeof t;this.traceInitMaxIdent=r?t:1/0,this.traceInitPerf=r?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=sW.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(!0!==this.traceInitPerf)return t();{this.traceInitIndent++;let r=Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${r}--> <${e}>`);let{time:n,value:i}=ag(t),a=n>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${n}ms`),this.traceInitIndent--,i}}}]);class sq extends sY{constructor(e,t=sW){let r=(0,nw.Z)(t);r.outputCst=!1,super(e,r)}}function sX(e,t,r){return`${e.name}_${t}_${r}`}class sQ{constructor(e){this.target=e}isEpsilon(){return!1}}class sJ extends sQ{constructor(e,t){super(e),this.tokenType=t}}class s0 extends sQ{constructor(e){super(e)}isEpsilon(){return!0}}class s1 extends sQ{constructor(e,t,r){super(e),this.rule=t,this.followState=r}isEpsilon(){return!0}}function s2(e,t,r,n,i){let a=n.left,s=n.right,o=s9(e,t,r,{type:11});s7(e,o);let l=s9(e,t,r,{type:12});return a.loopback=o,l.loopback=o,e.decisionMap[sX(t,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",r.idx)]=o,s6(s,o),void 0===i?(s6(o,a),s6(o,l)):(s6(o,l),s6(o,i.left),s6(i.right,a)),{left:a,right:l}}function s3(e,t,r,n,i){let a=n.left,s=n.right,o=s9(e,t,r,{type:10});s7(e,o);let l=s9(e,t,r,{type:12}),u=s9(e,t,r,{type:9});return o.loopback=u,l.loopback=u,s6(o,a),s6(o,l),s6(s,u),void 0!==i?(s6(u,l),s6(u,i.left),s6(i.right,a)):s6(u,o),e.decisionMap[sX(t,i?"RepetitionWithSeparator":"Repetition",r.idx)]=o,{left:o,right:l}}function s7(e,t){return e.decisionStates.push(t),t.decision=e.decisionStates.length-1,t.decision}function s4(e,t,r,n,...i){let a=s9(e,t,n,{type:8,start:r});for(let e of(r.end=a,i))void 0!==e?(s6(r,e.left),s6(e.right,a)):s6(r,a);return e.decisionMap[sX(t,function(e){if(e instanceof it)return"Alternation";if(e instanceof n5)return"Option";if(e instanceof n8)return"Repetition";if(e instanceof ie)return"RepetitionWithSeparator";if(e instanceof n6)return"RepetitionMandatory";if(e instanceof n9)return"RepetitionMandatoryWithSeparator";throw Error("Invalid production type encountered")}(n),n.idx)]=r,{left:r,right:a}}function s5(e,t,r,n){let i=s9(e,t,n,{type:1}),a=s9(e,t,n,{type:1});return s8(i,new sJ(a,r)),{left:i,right:a}}function s6(e,t){let r=new s0(t);s8(e,r)}function s9(e,t,r,n){let i=Object.assign({atn:e,production:r,epsilonOnlyTransitions:!1,rule:t,transitions:[],nextTokenWithinRule:[],stateNumber:e.states.length},n);return e.states.push(i),i}function s8(e,t){0===e.transitions.length&&(e.epsilonOnlyTransitions=t.isEpsilon()),e.transitions.push(t)}let oe={};class ot{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){let t=or(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return(0,n$.Z)(this.configs,e=>e.alt)}get key(){let e="";for(let t in this.map)e+=t+":";return e}}function or(e,t=!0){return`${t?`a${e.alt}`:""}s${e.state.stateNumber}:${e.stack.map(e=>e.stateNumber.toString()).join("_")}`}var on=r(3729);class oi{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="",t=this.predicates.length;for(let r=0;r<t;r++)e+=!0===this.predicates[r]?"1":"0";return e}}let oa=new oi;class os extends sL{constructor(e){var t;super(),this.logging=null!==(t=null==e?void 0:e.logging)&&void 0!==t?t:e=>console.log(e)}initialize(e){this.atn=function(e){let t={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};(function(e,t){let r=t.length;for(let n=0;n<r;n++){let r=t[n],i=s9(e,r,void 0,{type:2}),a=s9(e,r,void 0,{type:7});i.stop=a,e.ruleToStartState.set(r,i),e.ruleToStopState.set(r,a)}})(t,e);let r=e.length;for(let n=0;n<r;n++){let r=e[n],i=function e(t,r,n){let i=(0,iD.Z)((0,n$.Z)(n.definition,n=>(function t(r,n,i){if(i instanceof ir)return s5(r,n,i.terminalType,i);if(i instanceof n3)return function(e,t,r){let n=r.referencedRule,i=e.ruleToStartState.get(n),a=s9(e,t,r,{type:1}),s=s9(e,t,r,{type:1}),o=new s1(i,n,s);return s8(a,o),{left:a,right:s}}(r,n,i);if(i instanceof it)return function(e,r,n){let i=s9(e,r,n,{type:1});s7(e,i);let a=(0,n$.Z)(n.definition,n=>t(e,r,n)),s=s4(e,r,i,n,...a);return s}(r,n,i);if(i instanceof n5)return function(t,r,n){let i=s9(t,r,n,{type:1});s7(t,i);let a=s4(t,r,i,n,e(t,r,n));return function(e,t,r,n){let i=n.left,a=n.right;return s6(i,a),e.decisionMap[sX(t,"Option",r.idx)]=i,n}(t,r,n,a)}(r,n,i);if(i instanceof n8)return function(t,r,n){let i=s9(t,r,n,{type:5});s7(t,i);let a=s4(t,r,i,n,e(t,r,n));return s3(t,r,n,a)}(r,n,i);if(i instanceof ie)return function(t,r,n){let i=s9(t,r,n,{type:5});s7(t,i);let a=s4(t,r,i,n,e(t,r,n)),s=s5(t,r,n.separator,n);return s3(t,r,n,a,s)}(r,n,i);if(i instanceof n6)return function(t,r,n){let i=s9(t,r,n,{type:4});s7(t,i);let a=s4(t,r,i,n,e(t,r,n));return s2(t,r,n,a)}(r,n,i);else if(i instanceof n9)return function(t,r,n){let i=s9(t,r,n,{type:4});s7(t,i);let a=s4(t,r,i,n,e(t,r,n)),s=s5(t,r,n.separator,n);return s2(t,r,n,a,s)}(r,n,i);else return e(r,n,i)})(t,r,n)),e=>void 0!==e);return 1===i.length?i[0]:0===i.length?void 0:function(e,t){let r=t.length;for(let n=0;n<r-1;n++){let r;let i=t[n];1===i.left.transitions.length&&(r=i.left.transitions[0]);let a=r instanceof s1,s=r,o=t[n+1].left;1===i.left.type&&1===i.right.type&&void 0!==r&&(a&&s.followState===i.right||r.target===i.right)?(a?s.followState=o:r.target=o,function(e,t){e.states.splice(e.states.indexOf(t),1)}(e,i.right)):s6(i.right,o)}let n=t[0],i=t[r-1];return{left:n.left,right:i.right}}(t,i)}(t,r,r);void 0!==i&&function(e,t,r){let n=e.ruleToStartState.get(t);s6(n,r.left);let i=e.ruleToStopState.get(t);s6(r.right,i)}(t,r,i)}return t}(e.rules),this.dfas=function(e){let t=e.decisionStates.length,r=Array(t);for(let n=0;n<t;n++)r[n]=function(e,t){let r={};return n=>{let i=n.toString(),a=r[i];return void 0!==a||(a={atnStartState:e,decision:t,states:{}},r[i]=a),a}}(e.decisionStates[n],n);return r}(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){let{prodOccurrence:t,rule:r,hasPredicates:n,dynamicTokensEnabled:i}=e,a=this.dfas,s=this.logging,o=sX(r,"Alternation",t),l=this.atn.decisionMap[o],u=l.decision,c=(0,n$.Z)(a9({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:r}),e=>(0,n$.Z)(e,e=>e[0]));if(oo(c,!1)&&!i){let e=(0,iM.Z)(c,(e,t,r)=>((0,nS.Z)(t,t=>{t&&(e[t.tokenTypeIdx]=r,(0,nS.Z)(t.categoryMatches,t=>{e[t]=r}))}),e),{});return n?function(t){var r;let n=this.LA(1),i=e[n.tokenTypeIdx];if(void 0!==t&&void 0!==i){let e=null===(r=t[i])||void 0===r?void 0:r.GATE;if(void 0!==e&&!1===e.call(this))return}return i}:function(){let t=this.LA(1);return e[t.tokenTypeIdx]}}return n?function(e){let t=new oi,r=void 0===e?0:e.length;for(let n=0;n<r;n++){let r=null==e?void 0:e[n].GATE;t.set(n,void 0===r||r.call(this))}let n=ol.call(this,a,u,t,s);return"number"==typeof n?n:void 0}:function(){let e=ol.call(this,a,u,oa,s);return"number"==typeof e?e:void 0}}buildLookaheadForOptional(e){let{prodOccurrence:t,rule:r,prodType:n,dynamicTokensEnabled:i}=e,a=this.dfas,s=this.logging,o=sX(r,n,t),l=this.atn.decisionMap[o],u=l.decision,c=(0,n$.Z)(a9({maxLookahead:1,occurrence:t,prodType:n,rule:r}),e=>(0,n$.Z)(e,e=>e[0]));if(oo(c)&&c[0][0]&&!i){let e=c[0],t=(0,il.Z)(e);if(1===t.length&&(0,nC.Z)(t[0].categoryMatches)){let e=t[0],r=e.tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===r}}{let e=(0,iM.Z)(t,(e,t)=>(void 0!==t&&(e[t.tokenTypeIdx]=!0,(0,nS.Z)(t.categoryMatches,t=>{e[t]=!0})),e),{});return function(){let t=this.LA(1);return!0===e[t.tokenTypeIdx]}}}return function(){let e=ol.call(this,a,u,oa,s);return"object"!=typeof e&&0===e}}}function oo(e,t=!0){let r=new Set;for(let n of e){let e=new Set;for(let i of n){if(void 0===i){if(!t)return!1;break}let n=[i.tokenTypeIdx].concat(i.categoryMatches);for(let t of n)if(r.has(t)){if(!e.has(t))return!1}else r.add(t),e.add(t)}}return!0}function ol(e,t,r,n){let i=e[t](r),a=i.start;if(void 0===a){let e=function(e){let t=new ot,r=e.transitions.length;for(let n=0;n<r;n++){let r=e.transitions[n].target,i={state:r,alt:n,stack:[]};om(i,t)}return t}(i.atnStartState);a=op(i,oh(e)),i.start=a}let s=ou.apply(this,[i,a,r,n]);return s}function ou(e,t,r,n){let i=t,a=1,s=[],o=this.LA(a++);for(;;){var l,u;let t=(l=i,u=o,l.edges[u.tokenTypeIdx]);if(void 0===t&&(t=oc.apply(this,[e,i,o,a,r,n])),t===oe)return function(e,t,r){var n;let i=aV(t.configs.elements,e=>e.state.transitions),a=(n=i.filter(e=>e instanceof sJ).map(e=>e.tokenType))&&n.length?(0,is.Z)(n,(0,nW.Z)(e=>e.tokenTypeIdx,2)):[];return{actualToken:r,possibleTokenTypes:a,tokenPath:e}}(s,i,o);if(!0===t.isAcceptState)return t.prediction;i=t,s.push(o),o=this.LA(a++)}}function oc(e,t,r,n,i,a){let s=function(e,t,r){let n;let i=new ot,a=[];for(let n of e.elements){if(!1===r.is(n.alt))continue;if(7===n.state.type){a.push(n);continue}let e=n.state.transitions.length;for(let r=0;r<e;r++){let e=n.state.transitions[r],a=function(e,t){if(e instanceof sJ&&ay(t,e.tokenType))return e.target}(e,t);void 0!==a&&i.add({state:a,alt:n.alt,stack:n.stack})}}if(0===a.length&&1===i.size&&(n=i),void 0===n)for(let e of(n=new ot,i.elements))om(e,n);if(a.length>0&&!function(e){for(let t of e.elements)if(7===t.state.type)return!0;return!1}(n))for(let e of a)n.add(e);return n}(t.configs,r,i);if(0===s.size)return of(e,t,r,oe),oe;let o=oh(s),l=function(e,t){let r;for(let n of e.elements)if(!0===t.is(n.alt)){if(void 0===r)r=n.alt;else if(r!==n.alt)return}return r}(s,i);if(void 0!==l)o.isAcceptState=!0,o.prediction=l,o.configs.uniqueAlt=l;else if(function(e){if(function(e){for(let t of e.elements)if(7!==t.state.type)return!1;return!0}(e))return!0;let t=function(e){let t=new Map;for(let r of e){let e=or(r,!1),n=t.get(e);void 0===n&&(n={},t.set(e,n)),n[r.alt]=!0}return t}(e.elements),r=function(e){for(let t of Array.from(e.values()))if(Object.keys(t).length>1)return!0;return!1}(t)&&!function(e){for(let t of Array.from(e.values()))if(1===Object.keys(t).length)return!0;return!1}(t);return r}(s)){let t=(0,on.Z)(s.alts);o.isAcceptState=!0,o.prediction=t,o.configs.uniqueAlt=t,od.apply(this,[e,n,s.alts,a])}return of(e,t,r,o)}function od(e,t,r,n){let i=[];for(let e=1;e<=t;e++)i.push(this.LA(e).tokenType);let a=e.atnStartState,s=a.rule,o=a.production,l=function(e){let t=(0,n$.Z)(e.prefixPath,e=>aN(e)).join(", "),r=0===e.production.idx?"":e.production.idx;return`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(", ")}> in <${function(e){if(e instanceof n3)return"SUBRULE";if(e instanceof n5)return"OPTION";if(e instanceof it)return"OR";if(e instanceof n6)return"AT_LEAST_ONE";if(e instanceof n9)return"AT_LEAST_ONE_SEP";if(e instanceof ie)return"MANY_SEP";if(e instanceof n8)return"MANY";else if(e instanceof ir)return"CONSUME";else throw Error("non exhaustive match")}(e.production)}${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`}({topLevelRule:s,ambiguityIndices:r,production:o,prefixPath:i});n(l)}function oh(e){return{configs:e,edges:{},isAcceptState:!1,prediction:-1}}function of(e,t,r,n){return n=op(e,n),t.edges[r.tokenTypeIdx]=n,n}function op(e,t){if(t===oe)return t;let r=t.configs.key,n=e.states[r];return void 0!==n?n:(t.configs.finalize(),e.states[r]=t,t)}function om(e,t){let r=e.state;if(7===r.type){if(e.stack.length>0){let r=[...e.stack],n=r.pop(),i={state:n,alt:e.alt,stack:r};om(i,t)}else t.add(e);return}r.epsilonOnlyTransitions||t.add(e);let n=r.transitions.length;for(let i=0;i<n;i++){let n=r.transitions[i],a=function(e,t){if(t instanceof s0)return{state:t.target,alt:e.alt,stack:e.stack};if(t instanceof s1){let r=[...e.stack,t.followState];return{state:t.target,alt:e.alt,stack:r}}}(e,n);void 0!==a&&om(a,t)}}(rt||(rt={})).is=function(e){return"string"==typeof e},(rr||(rr={})).is=function(e){return"string"==typeof e},(e6=rn||(rn={})).MIN_VALUE=-2147483648,e6.MAX_VALUE=2147483647,e6.is=function(e){return"number"==typeof e&&e6.MIN_VALUE<=e&&e<=e6.MAX_VALUE},(e9=ri||(ri={})).MIN_VALUE=0,e9.MAX_VALUE=2147483647,e9.is=function(e){return"number"==typeof e&&e9.MIN_VALUE<=e&&e<=e9.MAX_VALUE},(e8=ra||(ra={})).create=function(e,t){return e===Number.MAX_VALUE&&(e=ri.MAX_VALUE),t===Number.MAX_VALUE&&(t=ri.MAX_VALUE),{line:e,character:t}},e8.is=function(e){return ng.objectLiteral(e)&&ng.uinteger(e.line)&&ng.uinteger(e.character)},(te=rs||(rs={})).create=function(e,t,r,n){if(ng.uinteger(e)&&ng.uinteger(t)&&ng.uinteger(r)&&ng.uinteger(n))return{start:ra.create(e,t),end:ra.create(r,n)};if(ra.is(e)&&ra.is(t))return{start:e,end:t};throw Error(`Range#create called with invalid arguments[${e}, ${t}, ${r}, ${n}]`)},te.is=function(e){return ng.objectLiteral(e)&&ra.is(e.start)&&ra.is(e.end)},(tt=ro||(ro={})).create=function(e,t){return{uri:e,range:t}},tt.is=function(e){return ng.objectLiteral(e)&&rs.is(e.range)&&(ng.string(e.uri)||ng.undefined(e.uri))},(tr=rl||(rl={})).create=function(e,t,r,n){return{targetUri:e,targetRange:t,targetSelectionRange:r,originSelectionRange:n}},tr.is=function(e){return ng.objectLiteral(e)&&rs.is(e.targetRange)&&ng.string(e.targetUri)&&rs.is(e.targetSelectionRange)&&(rs.is(e.originSelectionRange)||ng.undefined(e.originSelectionRange))},(tn=ru||(ru={})).create=function(e,t,r,n){return{red:e,green:t,blue:r,alpha:n}},tn.is=function(e){return ng.objectLiteral(e)&&ng.numberRange(e.red,0,1)&&ng.numberRange(e.green,0,1)&&ng.numberRange(e.blue,0,1)&&ng.numberRange(e.alpha,0,1)},(ti=rc||(rc={})).create=function(e,t){return{range:e,color:t}},ti.is=function(e){return ng.objectLiteral(e)&&rs.is(e.range)&&ru.is(e.color)},(ta=rd||(rd={})).create=function(e,t,r){return{label:e,textEdit:t,additionalTextEdits:r}},ta.is=function(e){return ng.objectLiteral(e)&&ng.string(e.label)&&(ng.undefined(e.textEdit)||rE.is(e))&&(ng.undefined(e.additionalTextEdits)||ng.typedArray(e.additionalTextEdits,rE.is))},(ts=rh||(rh={})).Comment="comment",ts.Imports="imports",ts.Region="region",(to=rf||(rf={})).create=function(e,t,r,n,i,a){let s={startLine:e,endLine:t};return ng.defined(r)&&(s.startCharacter=r),ng.defined(n)&&(s.endCharacter=n),ng.defined(i)&&(s.kind=i),ng.defined(a)&&(s.collapsedText=a),s},to.is=function(e){return ng.objectLiteral(e)&&ng.uinteger(e.startLine)&&ng.uinteger(e.startLine)&&(ng.undefined(e.startCharacter)||ng.uinteger(e.startCharacter))&&(ng.undefined(e.endCharacter)||ng.uinteger(e.endCharacter))&&(ng.undefined(e.kind)||ng.string(e.kind))},(tl=rp||(rp={})).create=function(e,t){return{location:e,message:t}},tl.is=function(e){return ng.defined(e)&&ro.is(e.location)&&ng.string(e.message)},(tu=rm||(rm={})).Error=1,tu.Warning=2,tu.Information=3,tu.Hint=4,(tc=rg||(rg={})).Unnecessary=1,tc.Deprecated=2,(ry||(ry={})).is=function(e){return ng.objectLiteral(e)&&ng.string(e.href)},(td=rT||(rT={})).create=function(e,t,r,n,i,a){let s={range:e,message:t};return ng.defined(r)&&(s.severity=r),ng.defined(n)&&(s.code=n),ng.defined(i)&&(s.source=i),ng.defined(a)&&(s.relatedInformation=a),s},td.is=function(e){var t;return ng.defined(e)&&rs.is(e.range)&&ng.string(e.message)&&(ng.number(e.severity)||ng.undefined(e.severity))&&(ng.integer(e.code)||ng.string(e.code)||ng.undefined(e.code))&&(ng.undefined(e.codeDescription)||ng.string(null===(t=e.codeDescription)||void 0===t?void 0:t.href))&&(ng.string(e.source)||ng.undefined(e.source))&&(ng.undefined(e.relatedInformation)||ng.typedArray(e.relatedInformation,rp.is))},(th=rv||(rv={})).create=function(e,t,...r){let n={title:e,command:t};return ng.defined(r)&&r.length>0&&(n.arguments=r),n},th.is=function(e){return ng.defined(e)&&ng.string(e.title)&&ng.string(e.command)},(tf=rE||(rE={})).replace=function(e,t){return{range:e,newText:t}},tf.insert=function(e,t){return{range:{start:e,end:e},newText:t}},tf.del=function(e){return{range:e,newText:""}},tf.is=function(e){return ng.objectLiteral(e)&&ng.string(e.newText)&&rs.is(e.range)},(tp=rR||(rR={})).create=function(e,t,r){let n={label:e};return void 0!==t&&(n.needsConfirmation=t),void 0!==r&&(n.description=r),n},tp.is=function(e){return ng.objectLiteral(e)&&ng.string(e.label)&&(ng.boolean(e.needsConfirmation)||void 0===e.needsConfirmation)&&(ng.string(e.description)||void 0===e.description)},(rA||(rA={})).is=function(e){return ng.string(e)},(tm=rk||(rk={})).replace=function(e,t,r){return{range:e,newText:t,annotationId:r}},tm.insert=function(e,t,r){return{range:{start:e,end:e},newText:t,annotationId:r}},tm.del=function(e,t){return{range:e,newText:"",annotationId:t}},tm.is=function(e){return rE.is(e)&&(rR.is(e.annotationId)||rA.is(e.annotationId))},(tg=rI||(rI={})).create=function(e,t){return{textDocument:e,edits:t}},tg.is=function(e){return ng.defined(e)&&rw.is(e.textDocument)&&Array.isArray(e.edits)},(ty=rx||(rx={})).create=function(e,t,r){let n={kind:"create",uri:e};return void 0!==t&&(void 0!==t.overwrite||void 0!==t.ignoreIfExists)&&(n.options=t),void 0!==r&&(n.annotationId=r),n},ty.is=function(e){return e&&"create"===e.kind&&ng.string(e.uri)&&(void 0===e.options||(void 0===e.options.overwrite||ng.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||ng.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||rA.is(e.annotationId))},(tT=rS||(rS={})).create=function(e,t,r,n){let i={kind:"rename",oldUri:e,newUri:t};return void 0!==r&&(void 0!==r.overwrite||void 0!==r.ignoreIfExists)&&(i.options=r),void 0!==n&&(i.annotationId=n),i},tT.is=function(e){return e&&"rename"===e.kind&&ng.string(e.oldUri)&&ng.string(e.newUri)&&(void 0===e.options||(void 0===e.options.overwrite||ng.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||ng.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||rA.is(e.annotationId))},(tv=rN||(rN={})).create=function(e,t,r){let n={kind:"delete",uri:e};return void 0!==t&&(void 0!==t.recursive||void 0!==t.ignoreIfNotExists)&&(n.options=t),void 0!==r&&(n.annotationId=r),n},tv.is=function(e){return e&&"delete"===e.kind&&ng.string(e.uri)&&(void 0===e.options||(void 0===e.options.recursive||ng.boolean(e.options.recursive))&&(void 0===e.options.ignoreIfNotExists||ng.boolean(e.options.ignoreIfNotExists)))&&(void 0===e.annotationId||rA.is(e.annotationId))},(rC||(rC={})).is=function(e){return e&&(void 0!==e.changes||void 0!==e.documentChanges)&&(void 0===e.documentChanges||e.documentChanges.every(e=>ng.string(e.kind)?rx.is(e)||rS.is(e)||rN.is(e):rI.is(e)))},(tE=r$||(r$={})).create=function(e){return{uri:e}},tE.is=function(e){return ng.defined(e)&&ng.string(e.uri)},(tR=rL||(rL={})).create=function(e,t){return{uri:e,version:t}},tR.is=function(e){return ng.defined(e)&&ng.string(e.uri)&&ng.integer(e.version)},(tA=rw||(rw={})).create=function(e,t){return{uri:e,version:t}},tA.is=function(e){return ng.defined(e)&&ng.string(e.uri)&&(null===e.version||ng.integer(e.version))},(tk=rb||(rb={})).create=function(e,t,r,n){return{uri:e,languageId:t,version:r,text:n}},tk.is=function(e){return ng.defined(e)&&ng.string(e.uri)&&ng.string(e.languageId)&&ng.integer(e.version)&&ng.string(e.text)},(tI=rO||(rO={})).PlainText="plaintext",tI.Markdown="markdown",tI.is=function(e){return e===tI.PlainText||e===tI.Markdown},(r_||(r_={})).is=function(e){return ng.objectLiteral(e)&&rO.is(e.kind)&&ng.string(e.value)},(tx=rP||(rP={})).Text=1,tx.Method=2,tx.Function=3,tx.Constructor=4,tx.Field=5,tx.Variable=6,tx.Class=7,tx.Interface=8,tx.Module=9,tx.Property=10,tx.Unit=11,tx.Value=12,tx.Enum=13,tx.Keyword=14,tx.Snippet=15,tx.Color=16,tx.File=17,tx.Reference=18,tx.Folder=19,tx.EnumMember=20,tx.Constant=21,tx.Struct=22,tx.Event=23,tx.Operator=24,tx.TypeParameter=25,(tS=rM||(rM={})).PlainText=1,tS.Snippet=2,(rD||(rD={})).Deprecated=1,(tN=rZ||(rZ={})).create=function(e,t,r){return{newText:e,insert:t,replace:r}},tN.is=function(e){return e&&ng.string(e.newText)&&rs.is(e.insert)&&rs.is(e.replace)},(tC=rU||(rU={})).asIs=1,tC.adjustIndentation=2,(rF||(rF={})).is=function(e){return e&&(ng.string(e.detail)||void 0===e.detail)&&(ng.string(e.description)||void 0===e.description)},(rG||(rG={})).create=function(e){return{label:e}},(rB||(rB={})).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(t$=rK||(rK={})).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},t$.is=function(e){return ng.string(e)||ng.objectLiteral(e)&&ng.string(e.language)&&ng.string(e.value)},(rj||(rj={})).is=function(e){return!!e&&ng.objectLiteral(e)&&(r_.is(e.contents)||rK.is(e.contents)||ng.typedArray(e.contents,rK.is))&&(void 0===e.range||rs.is(e.range))},(rV||(rV={})).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(rW||(rW={})).create=function(e,t,...r){let n={label:e};return ng.defined(t)&&(n.documentation=t),ng.defined(r)?n.parameters=r:n.parameters=[],n},(tL=rH||(rH={})).Text=1,tL.Read=2,tL.Write=3,(rz||(rz={})).create=function(e,t){let r={range:e};return ng.number(t)&&(r.kind=t),r},(tw=rY||(rY={})).File=1,tw.Module=2,tw.Namespace=3,tw.Package=4,tw.Class=5,tw.Method=6,tw.Property=7,tw.Field=8,tw.Constructor=9,tw.Enum=10,tw.Interface=11,tw.Function=12,tw.Variable=13,tw.Constant=14,tw.String=15,tw.Number=16,tw.Boolean=17,tw.Array=18,tw.Object=19,tw.Key=20,tw.Null=21,tw.EnumMember=22,tw.Struct=23,tw.Event=24,tw.Operator=25,tw.TypeParameter=26,(rq||(rq={})).Deprecated=1,(rX||(rX={})).create=function(e,t,r,n,i){let a={name:e,kind:t,location:{uri:n,range:r}};return i&&(a.containerName=i),a},(rQ||(rQ={})).create=function(e,t,r,n){return void 0!==n?{name:e,kind:t,location:{uri:r,range:n}}:{name:e,kind:t,location:{uri:r}}},(tb=rJ||(rJ={})).create=function(e,t,r,n,i,a){let s={name:e,detail:t,kind:r,range:n,selectionRange:i};return void 0!==a&&(s.children=a),s},tb.is=function(e){return e&&ng.string(e.name)&&ng.number(e.kind)&&rs.is(e.range)&&rs.is(e.selectionRange)&&(void 0===e.detail||ng.string(e.detail))&&(void 0===e.deprecated||ng.boolean(e.deprecated))&&(void 0===e.children||Array.isArray(e.children))&&(void 0===e.tags||Array.isArray(e.tags))},(tO=r0||(r0={})).Empty="",tO.QuickFix="quickfix",tO.Refactor="refactor",tO.RefactorExtract="refactor.extract",tO.RefactorInline="refactor.inline",tO.RefactorRewrite="refactor.rewrite",tO.Source="source",tO.SourceOrganizeImports="source.organizeImports",tO.SourceFixAll="source.fixAll",(t_=r1||(r1={})).Invoked=1,t_.Automatic=2,(tP=r2||(r2={})).create=function(e,t,r){let n={diagnostics:e};return null!=t&&(n.only=t),null!=r&&(n.triggerKind=r),n},tP.is=function(e){return ng.defined(e)&&ng.typedArray(e.diagnostics,rT.is)&&(void 0===e.only||ng.typedArray(e.only,ng.string))&&(void 0===e.triggerKind||e.triggerKind===r1.Invoked||e.triggerKind===r1.Automatic)},(tM=r3||(r3={})).create=function(e,t,r){let n={title:e},i=!0;return"string"==typeof t?(i=!1,n.kind=t):rv.is(t)?n.command=t:n.edit=t,i&&void 0!==r&&(n.kind=r),n},tM.is=function(e){return e&&ng.string(e.title)&&(void 0===e.diagnostics||ng.typedArray(e.diagnostics,rT.is))&&(void 0===e.kind||ng.string(e.kind))&&(void 0!==e.edit||void 0!==e.command)&&(void 0===e.command||rv.is(e.command))&&(void 0===e.isPreferred||ng.boolean(e.isPreferred))&&(void 0===e.edit||rC.is(e.edit))},(tD=r7||(r7={})).create=function(e,t){let r={range:e};return ng.defined(t)&&(r.data=t),r},tD.is=function(e){return ng.defined(e)&&rs.is(e.range)&&(ng.undefined(e.command)||rv.is(e.command))},(tZ=r4||(r4={})).create=function(e,t){return{tabSize:e,insertSpaces:t}},tZ.is=function(e){return ng.defined(e)&&ng.uinteger(e.tabSize)&&ng.boolean(e.insertSpaces)},(tU=r5||(r5={})).create=function(e,t,r){return{range:e,target:t,data:r}},tU.is=function(e){return ng.defined(e)&&rs.is(e.range)&&(ng.undefined(e.target)||ng.string(e.target))},(tF=r6||(r6={})).create=function(e,t){return{range:e,parent:t}},tF.is=function(e){return ng.objectLiteral(e)&&rs.is(e.range)&&(void 0===e.parent||tF.is(e.parent))},(tG=r9||(r9={})).namespace="namespace",tG.type="type",tG.class="class",tG.enum="enum",tG.interface="interface",tG.struct="struct",tG.typeParameter="typeParameter",tG.parameter="parameter",tG.variable="variable",tG.property="property",tG.enumMember="enumMember",tG.event="event",tG.function="function",tG.method="method",tG.macro="macro",tG.keyword="keyword",tG.modifier="modifier",tG.comment="comment",tG.string="string",tG.number="number",tG.regexp="regexp",tG.operator="operator",tG.decorator="decorator",(tB=r8||(r8={})).declaration="declaration",tB.definition="definition",tB.readonly="readonly",tB.static="static",tB.deprecated="deprecated",tB.abstract="abstract",tB.async="async",tB.modification="modification",tB.documentation="documentation",tB.defaultLibrary="defaultLibrary",(ne||(ne={})).is=function(e){return ng.objectLiteral(e)&&(void 0===e.resultId||"string"==typeof e.resultId)&&Array.isArray(e.data)&&(0===e.data.length||"number"==typeof e.data[0])},(tK=nt||(nt={})).create=function(e,t){return{range:e,text:t}},tK.is=function(e){return null!=e&&rs.is(e.range)&&ng.string(e.text)},(tj=nr||(nr={})).create=function(e,t,r){return{range:e,variableName:t,caseSensitiveLookup:r}},tj.is=function(e){return null!=e&&rs.is(e.range)&&ng.boolean(e.caseSensitiveLookup)&&(ng.string(e.variableName)||void 0===e.variableName)},(tV=nn||(nn={})).create=function(e,t){return{range:e,expression:t}},tV.is=function(e){return null!=e&&rs.is(e.range)&&(ng.string(e.expression)||void 0===e.expression)},(tW=ni||(ni={})).create=function(e,t){return{frameId:e,stoppedLocation:t}},tW.is=function(e){return ng.defined(e)&&rs.is(e.stoppedLocation)},(tH=na||(na={})).Type=1,tH.Parameter=2,tH.is=function(e){return 1===e||2===e},(tz=ns||(ns={})).create=function(e){return{value:e}},tz.is=function(e){return ng.objectLiteral(e)&&(void 0===e.tooltip||ng.string(e.tooltip)||r_.is(e.tooltip))&&(void 0===e.location||ro.is(e.location))&&(void 0===e.command||rv.is(e.command))},(tY=no||(no={})).create=function(e,t,r){let n={position:e,label:t};return void 0!==r&&(n.kind=r),n},tY.is=function(e){return ng.objectLiteral(e)&&ra.is(e.position)&&(ng.string(e.label)||ng.typedArray(e.label,ns.is))&&(void 0===e.kind||na.is(e.kind))&&void 0===e.textEdits||ng.typedArray(e.textEdits,rE.is)&&(void 0===e.tooltip||ng.string(e.tooltip)||r_.is(e.tooltip))&&(void 0===e.paddingLeft||ng.boolean(e.paddingLeft))&&(void 0===e.paddingRight||ng.boolean(e.paddingRight))},(nl||(nl={})).createSnippet=function(e){return{kind:"snippet",value:e}},(nu||(nu={})).create=function(e,t,r,n){return{insertText:e,filterText:t,range:r,command:n}},(nc||(nc={})).create=function(e){return{items:e}},(tq=nd||(nd={})).Invoked=0,tq.Automatic=1,(nh||(nh={})).create=function(e,t){return{range:e,text:t}},(nf||(nf={})).create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}},(np||(np={})).is=function(e){return ng.objectLiteral(e)&&rr.is(e.uri)&&ng.string(e.name)},(tX=nm||(nm={})).create=function(e,t,r,n){return new og(e,t,r,n)},tX.is=function(e){return!!(ng.defined(e)&&ng.string(e.uri)&&(ng.undefined(e.languageId)||ng.string(e.languageId))&&ng.uinteger(e.lineCount)&&ng.func(e.getText)&&ng.func(e.positionAt)&&ng.func(e.offsetAt))},tX.applyEdits=function(e,t){let r=e.getText(),n=function e(t,r){if(t.length<=1)return t;let n=t.length/2|0,i=t.slice(0,n),a=t.slice(n);e(i,r),e(a,r);let s=0,o=0,l=0;for(;s<i.length&&o<a.length;)0>=r(i[s],a[o])?t[l++]=i[s++]:t[l++]=a[o++];for(;s<i.length;)t[l++]=i[s++];for(;o<a.length;)t[l++]=a[o++];return t}(t,(e,t)=>{let r=e.range.start.line-t.range.start.line;return 0===r?e.range.start.character-t.range.start.character:r}),i=r.length;for(let t=n.length-1;t>=0;t--){let a=n[t],s=e.offsetAt(a.range.start),o=e.offsetAt(a.range.end);if(o<=i)r=r.substring(0,s)+a.newText+r.substring(o,r.length);else throw Error("Overlapping edit");i=s}return r};class og{constructor(e,t,r,n){this._uri=e,this._languageId=t,this._version=r,this._content=n,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){let e=[],t=this._content,r=!0;for(let n=0;n<t.length;n++){r&&(e.push(n),r=!1);let i=t.charAt(n);r="\r"===i||"\n"===i,"\r"===i&&n+1<t.length&&"\n"===t.charAt(n+1)&&n++}r&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,n=t.length;if(0===n)return ra.create(0,e);for(;r<n;){let i=Math.floor((r+n)/2);t[i]>e?n=i:r=i+1}let i=r-1;return ra.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line],n=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,n),r)}get lineCount(){return this.getLineOffsets().length}}!function(e){let t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,r,n){return"[object Number]"===t.call(e)&&r<=e&&e<=n},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(ng||(ng={}));class oy{constructor(){this.nodeStack=[]}get current(){return this.nodeStack[this.nodeStack.length-1]}buildRootNode(e){return this.rootNode=new oA(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){let t=new oE;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){let r=new ov(e.startOffset,e.image.length,y(e),e.tokenType,!1);return r.grammarSource=t,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){let t=e.container;if(t){let r=t.content.indexOf(e);r>=0&&t.content.splice(r,1)}}construct(e){let t=this.current;"string"==typeof e.$type&&(this.current.astNode=e),e.$cstNode=t;let r=this.nodeStack.pop();(null==r?void 0:r.content.length)===0&&this.removeNode(r)}addHiddenTokens(e){for(let t of e){let e=new ov(t.startOffset,t.image.length,y(t),t.tokenType,!0);e.root=this.rootNode,this.addHiddenToken(this.rootNode,e)}}addHiddenToken(e,t){let{offset:r,end:n}=t;for(let i=0;i<e.content.length;i++){let a=e.content[i],{offset:s,end:l}=a;if(o(a)&&r>s&&n<l){this.addHiddenToken(a,t);return}if(n<=s){e.content.splice(i,0,t);return}}e.content.push(t)}}class oT{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;let r="string"==typeof(null===(e=this._astNode)||void 0===e?void 0:e.$type)?this._astNode:null===(t=this.container)||void 0===t?void 0:t.astNode;if(!r)throw Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class ov extends oT{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,r,n,i=!1){super(),this._hidden=i,this._offset=e,this._tokenType=n,this._length=t,this._range=r}}class oE extends oT{constructor(){super(...arguments),this.content=new oR(this)}get children(){return this.content}get offset(){var e,t;return null!==(t=null===(e=this.firstNonHiddenNode)||void 0===e?void 0:e.offset)&&void 0!==t?t:0}get length(){return this.end-this.offset}get end(){var e,t;return null!==(t=null===(e=this.lastNonHiddenNode)||void 0===e?void 0:e.end)&&void 0!==t?t:0}get range(){let e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(!e||!t)return{start:ra.create(0,0),end:ra.create(0,0)};if(void 0===this._rangeCache){let{range:r}=e,{range:n}=t;this._rangeCache={start:r.start,end:n.end.line<r.start.line?r.start:n.end}}return this._rangeCache}get firstNonHiddenNode(){for(let e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){let t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class oR extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,oR.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...r){return this.addParents(r),super.splice(e,t,...r)}addParents(e){for(let t of e)t.container=this.parent}}class oA extends oE{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=null!=e?e:""}}let ok=Symbol("Datatype");function oI(e){return e.$type===ok}let ox=e=>e.endsWith("​")?e:e+"​";class oS{constructor(e){this._unorderedGroups=new Map,this.lexer=e.parser.Lexer;let t=this.lexer.definition;this.wrapper=new ob(t,Object.assign(Object.assign({},e.parser.ParserConfig),{errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class oN extends oS{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new oy,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){let r=e.fragment?void 0:ez(e)?ok:eq(e),n=this.wrapper.DEFINE_RULE(ox(e.name),this.startImplementation(r,t).bind(this));return e.entry&&(this.mainRule=n),n}parse(e){this.nodeBuilder.buildRootNode(e);let t=this.lexer.tokenize(e);this.wrapper.input=t.tokens;let r=this.mainRule.call(this.wrapper,{});return this.nodeBuilder.addHiddenTokens(t.hidden),this.unorderedGroups.clear(),{value:r,lexerErrors:t.errors,parserErrors:this.wrapper.errors}}startImplementation(e,t){return r=>{let n;if(!this.isRecording()){let t={$type:e};this.stack.push(t),e===ok&&(t.value="")}try{n=t(r)}catch(e){n=void 0}return this.isRecording()||void 0!==n||(n=this.construct()),n}}consume(e,t,r){let n=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(n)){let e=this.nodeBuilder.buildLeafNode(n,r),{assignment:t,isCrossRef:i}=this.getAssignment(r),a=this.current;if(t){let a=en(r)?n.image:this.converter.convert(n.image,e);this.assign(t.operator,t.feature,a,e,i)}else if(oI(a)){let t=n.image;en(r)||(t=this.converter.convert(t,e).toString()),a.value+=t}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&"number"==typeof e.endOffset&&!isNaN(e.endOffset)}subrule(e,t,r,n){let i;this.isRecording()||(i=this.nodeBuilder.buildCompositeNode(r));let a=this.wrapper.wrapSubrule(e,t,n);!this.isRecording()&&i&&i.length>0&&this.performSubruleAssignment(a,r,i)}performSubruleAssignment(e,t,r){let{assignment:n,isCrossRef:i}=this.getAssignment(t);if(n)this.assign(n.operator,n.feature,e,r,i);else if(!n){let t=this.current;if(oI(t))t.value+=e.toString();else if("object"==typeof e&&e){let r=e.$type,n=this.assignWithoutOverride(e,t);r&&(n.$type=r),this.stack.pop(),this.stack.push(n)}}}action(e,t){if(!this.isRecording()){let r=this.current;if(!r.$cstNode&&t.feature&&t.operator){r=this.construct(!1);let e=r.$cstNode.feature;this.nodeBuilder.buildCompositeNode(e)}this.stack.pop(),this.stack.push({$type:e}),t.feature&&t.operator&&this.assign(t.operator,t.feature,r,r.$cstNode,!1)}}construct(e=!0){if(this.isRecording())return;let t=this.current;return(!function(e){for(let[t,r]of Object.entries(e))!t.startsWith("$")&&(Array.isArray(r)?r.forEach((r,i)=>{n(r)&&(r.$container=e,r.$containerProperty=t,r.$containerIndex=i)}):n(r)&&(r.$container=e,r.$containerProperty=t))}(t),this.nodeBuilder.construct(t),e&&this.stack.pop(),oI(t))?this.converter.convert(t.value,t.$cstNode):(!function(e,t){let r=e.getTypeMetaData(t.$type),n=t;for(let e of r.properties)void 0!==e.defaultValue&&void 0===n[e.name]&&(n[e.name]=function e(t){return Array.isArray(t)?[...t.map(e)]:t}(e.defaultValue))}(this.astReflection,t),t)}getAssignment(e){if(!this.assignmentMap.has(e)){let t=eT(e,Y);this.assignmentMap.set(e,{assignment:t,isCrossRef:!!t&&Q(t.terminal)})}return this.assignmentMap.get(e)}assign(e,t,r,n,i){let a;let s=this.current;switch(a=i&&"string"==typeof r?this.linker.buildReference(s,t,n,r):r,e){case"=":s[t]=a;break;case"?=":s[t]=!0;break;case"+=":Array.isArray(s[t])||(s[t]=[]),s[t].push(a)}}assignWithoutOverride(e,t){for(let[r,n]of Object.entries(t)){let t=e[r];void 0===t?e[r]=n:Array.isArray(t)&&Array.isArray(n)&&(n.push(...t),e[r]=n)}return e}get definitionErrors(){return this.wrapper.definitionErrors}}class oC{buildMismatchTokenMessage(e){return aF.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return aF.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return aF.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return aF.buildEarlyExitMessage(e)}}class o$ extends oC{buildMismatchTokenMessage({expected:e,actual:t}){let r=e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`;return`Expecting ${r} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class oL extends oS{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();let t=this.lexer.tokenize(e);return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){let r=this.wrapper.DEFINE_RULE(ox(e.name),this.startImplementation(t).bind(this));return e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{let r=this.keepStackSize();try{e(t)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){let e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,r){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,r,n){this.before(r),this.wrapper.wrapSubrule(e,t,n),this.after(r)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){let t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}let ow={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new o$};class ob extends sq{constructor(e,t){super(e,Object.assign(Object.assign(Object.assign({},ow),{lookaheadStrategy:t&&"maxLookahead"in t?new sL({maxLookahead:t.maxLookahead}):new os}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,r){return this.subrule(e,t,{ARGS:[r]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}function oO(e,t,r){let n=new Map,i={parser:t,tokens:r,rules:n,ruleNames:new Map};return function(e,t){let r=ej(t,!1),n=p(t.rules).filter(Z).filter(e=>r.has(e));for(let t of n){let r=Object.assign(Object.assign({},e),{consume:1,optional:1,subrule:1,many:1,or:1});r.rules.set(t.name,e.parser.rule(t,function e(t,r,n=!1){let i;if(en(r))i=function(e,t){let r=e.consume++,n=e.tokens[t.value];if(!n)throw Error("Could not find token for keyword: "+t.value);return()=>e.parser.consume(r,n,t)}(t,r);else if(V(r))i=function(e,t){let r=eq(t);return()=>e.parser.action(r,t)}(t,r);else if(Y(r))i=e(t,r.terminal);else if(Q(r))i=function e(t,r,n=r.terminal){if(n){if(eo(n)&&Z(n.rule.ref)){let e=t.subrule++;return i=>t.parser.subrule(e,oD(t,n.rule.ref),r,i)}if(eo(n)&&G(n.rule.ref)){let e=t.consume++,i=oZ(t,n.rule.ref.name);return()=>t.parser.consume(e,i,r)}if(en(n)){let e=t.consume++,i=oZ(t,n.value);return()=>t.parser.consume(e,i,r)}else throw Error("Could not build cross reference parser")}{if(!r.type.ref)throw Error("Could not resolve reference to type: "+r.type.$refText);let n=eH(r.type.ref),i=null==n?void 0:n.terminal;if(!i)throw Error("Could not find name assignment for type: "+eq(r.type.ref));return e(t,r,i)}}(t,r);else if(eo(r))i=function(e,t){let r=t.rule.ref;if(Z(r)){let n=e.subrule++,i=t.arguments.length>0?function(e,t){let r=t.map(e=>o_(e.value));return t=>{let n={};for(let i=0;i<r.length;i++){let a=e.parameters[i],s=r[i];n[a.name]=s(t)}return n}}(r,t.arguments):()=>({});return a=>e.parser.subrule(n,oD(e,r),t,i(a))}if(G(r)){let n=e.consume++,i=oZ(e,r.name);return()=>e.parser.consume(n,i,t)}if(r)A(r);else throw new R(t.$cstNode,`Undefined rule type: ${t.$type}`)}(t,r);else if(H(r))i=function(t,r){if(1===r.elements.length)return e(t,r.elements[0]);{let n=[];for(let i of r.elements){let r={ALT:e(t,i,!0)},a=oP(i);a&&(r.GATE=o_(a)),n.push(r)}let i=t.or++;return e=>t.parser.alternatives(i,n.map(t=>{let r={ALT:()=>t.ALT(e)},n=t.GATE;return n&&(r.GATE=()=>n(e)),r}))}}(t,r);else if(ef(r))i=function(t,r){if(1===r.elements.length)return e(t,r.elements[0]);let n=[];for(let i of r.elements){let r={ALT:e(t,i,!0)},a=oP(i);a&&(r.GATE=o_(a)),n.push(r)}let i=t.or++,a=(e,t)=>{let r=t.getRuleStack().join("-");return`uGroup_${e}_${r}`},s=e=>t.parser.alternatives(i,n.map((r,n)=>{let s={ALT:()=>!0},o=t.parser;s.ALT=()=>{if(r.ALT(e),!o.isRecording()){let e=a(i,o);o.unorderedGroups.get(e)||o.unorderedGroups.set(e,[]);let t=o.unorderedGroups.get(e);void 0===(null==t?void 0:t[n])&&(t[n]=!0)}};let l=r.GATE;return l?s.GATE=()=>l(e):s.GATE=()=>{let e=o.unorderedGroups.get(a(i,o)),t=!(null==e?void 0:e[n]);return t},s})),o=oM(t,oP(r),s,"*");return e=>{o(e),t.parser.isRecording()||t.parser.unorderedGroups.delete(a(i,t.parser))}}(t,r);else if(et(r))i=function(t,r){let n=r.elements.map(r=>e(t,r));return e=>n.forEach(t=>t(e))}(t,r);else if(ey.isInstance(r,J)){let e=t.consume++;i=()=>t.parser.consume(e,aZ,r)}else throw new R(r.$cstNode,`Unexpected element type: ${r.$type}`);return oM(t,n?void 0:oP(r),i,r.cardinality)}(r,t.definition)))}}(i,e),t}function o_(e){if(ey.isInstance(e,L)){let t=o_(e.left),r=o_(e.right);return e=>t(e)||r(e)}if(ey.isInstance(e,$)){let t=o_(e.left),r=o_(e.right);return e=>t(e)&&r(e)}if(ey.isInstance(e,P)){let t=o_(e.value);return e=>!t(e)}if(ey.isInstance(e,M)){let t=e.parameter.ref.name;return e=>void 0!==e&&!0===e[t]}if(ey.isInstance(e,C)){let t=Boolean(e.true);return()=>t}A(e)}function oP(e){if(et(e))return e.guardCondition}function oM(e,t,r,n){let i=t&&o_(t);if(!n){if(!i)return r;{let t=e.or++;return n=>e.parser.alternatives(t,[{ALT:()=>r(n),GATE:()=>i(n)},{ALT:sz(),GATE:()=>!i(n)}])}}if("*"===n){let t=e.many++;return n=>e.parser.many(t,{DEF:()=>r(n),GATE:i?()=>i(n):void 0})}if("+"===n){let t=e.many++;if(!i)return n=>e.parser.atLeastOne(t,{DEF:()=>r(n)});{let n=e.or++;return a=>e.parser.alternatives(n,[{ALT:()=>e.parser.atLeastOne(t,{DEF:()=>r(a)}),GATE:()=>i(a)},{ALT:sz(),GATE:()=>!i(a)}])}}if("?"===n){let t=e.optional++;return n=>e.parser.optional(t,{DEF:()=>r(n),GATE:i?()=>i(n):void 0})}A(n)}function oD(e,t){let r=function(e,t){if(Z(t))return t.name;if(e.ruleNames.has(t))return e.ruleNames.get(t);{let r=t,n=r.$container,i=t.$type;for(;!Z(n);){if(et(n)||H(n)||ef(n)){let e=n.elements.indexOf(r);i=e.toString()+":"+i}r=n,n=n.$container}let a=n;return i=a.name+":"+i,e.ruleNames.set(t,i),i}}(e,t),n=e.rules.get(r);if(!n)throw Error(`Rule "${r}" not found."`);return n}function oZ(e,t){let r=e.tokens[t];if(!r)throw Error(`Token "${t}" not found."`);return r}class oU{buildTokens(e,t){let r=p(ej(e,!1)),n=this.buildTerminalTokens(r),i=this.buildKeywordTokens(r,n,t);return n.forEach(e=>{let t=e.PATTERN;"object"==typeof t&&t&&"test"in t&&eB(t)?i.unshift(e):i.push(e)}),i}buildTerminalTokens(e){return e.filter(G).filter(e=>!e.fragment).map(e=>this.buildTerminalToken(e)).toArray()}buildTerminalToken(e){let t=eX(e),r=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,n={name:e.name,PATTERN:r,LINE_BREAKS:!0};return e.hidden&&(n.GROUP=eB(t)?aS.SKIPPED:"hidden"),n}requiresCustomPattern(e){return!!e.flags.includes("u")||!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){let t=RegExp(e,e.flags+"y");return(e,r)=>{t.lastIndex=r;let n=t.exec(e);return n}}buildKeywordTokens(e,t,r){return e.filter(Z).flatMap(e=>eR(e).filter(en)).distinct(e=>e.value).toArray().sort((e,t)=>t.value.length-e.value.length).map(e=>this.buildKeywordToken(e,t,Boolean(null==r?void 0:r.caseInsensitive)))}buildKeywordToken(e,t,r){return{name:e.value,PATTERN:this.buildKeywordPattern(e,r),LONGER_ALT:this.findLongerAlt(e,t)}}buildKeywordPattern(e,t){var r;return t?RegExp((r=e.value,Array.prototype.map.call(r,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:eK(e)).join(""))):e.value}findLongerAlt(e,t){return t.reduce((t,r)=>{let n=null==r?void 0:r.PATTERN;return(null==n?void 0:n.source)&&function(e,t){let r=function(e){"string"==typeof e&&(e=RegExp(e));let t=e,r=e.source,n=0;return RegExp(function e(){let i="",a;function s(e){i+=r.substr(n,e),n+=e}function o(e){i+="(?:"+r.substr(n,e)+"|$)",n+=e}for(;n<r.length;)switch(r[n]){case"\\":switch(r[n+1]){case"c":o(3);break;case"x":o(4);break;case"u":o(t.unicode?"{"===r[n+2]?r.indexOf("}",n)-n+1:6:2);break;case"p":case"P":o(t.unicode?r.indexOf("}",n)-n+1:2);break;case"k":o(r.indexOf(">",n)-n+1);break;default:o(2)}break;case"[":(a=/\[(?:\\.|.)*?\]/g).lastIndex=n,o((a=a.exec(r)||[])[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":s(1);break;case"{":(a=/\{\d+,?\d*\}/g).lastIndex=n,(a=a.exec(r))?s(a[0].length):o(1);break;case"(":if("?"===r[n+1])switch(r[n+2]){case":":n+=3,i+="(?:"+e()+"|$)";break;case"=":n+=3,i+="(?="+e()+")";break;case"!":a=n,n+=3,e(),i+=r.substr(a,n-a);break;case"<":switch(r[n+3]){case"=":case"!":a=n,n+=4,e(),i+=r.substr(a,n-a);break;default:s(r.indexOf(">",n)-n+1),i+=e()+"|$)"}}else s(1),i+=e()+"|$)";break;case")":return++n,i;default:o(1)}return i}(),e.flags)}(e),n=t.match(r);return!!n&&n[0].length>0}("^"+n.source+"$",e.value)&&t.push(r),t},[])}}class oF{convert(e,t){let r=t.grammarSource;if(Q(r)&&(r=function(e){if(e.terminal)return e.terminal;if(e.type.ref){let t=eH(e.type.ref);return null==t?void 0:t.terminal}}(r)),eo(r)){let n=r.rule.ref;if(!n)throw Error("This cst node was not parsed by a rule.");return this.runConverter(n,e,t)}return e}runConverter(e,t,r){var n,i,a,s;switch(e.name.toUpperCase()){case"INT":return ny.convertInt(t);case"STRING":return ny.convertString(t);case"ID":return ny.convertID(t)}switch(null===(n=G(e)?null!==(a=null===(i=e.type)||void 0===i?void 0:i.name)&&void 0!==a?a:"string":ez(e)?e.name:null!==(s=eY(e))&&void 0!==s?s:e.name)||void 0===n?void 0:n.toLowerCase()){case"number":return ny.convertNumber(t);case"boolean":return ny.convertBoolean(t);case"bigint":return ny.convertBigint(t);case"date":return ny.convertDate(t);default:return t}}}(tQ=ny||(ny={})).convertString=function(e){let t="";for(let r=1;r<e.length-1;r++){let n=e.charAt(r);if("\\"===n){let n=e.charAt(++r);t+=function(e){switch(e){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\x00";default:return e}}(n)}else t+=n}return t},tQ.convertID=function(e){return"^"===e.charAt(0)?e.substring(1):e},tQ.convertInt=function(e){return parseInt(e)},tQ.convertBigint=function(e){return BigInt(e)},tQ.convertDate=function(e){return new Date(e)},tQ.convertNumber=function(e){return Number(e)},tQ.convertBoolean=function(e){return"true"===e.toLowerCase()};var oG=r(13881);let oB=0,oK=Symbol("OperationCancelled");async function oj(e){if(e===oG.Ts.None)return;let t=Date.now();if(t-oB>=10&&(oB=t,await new Promise(e=>{"undefined"==typeof setImmediate?setTimeout(e,0):setImmediate(e)})),e.isCancellationRequested)throw oK}class oV{constructor(){this.promise=new Promise((e,t)=>{this.resolve=t=>(e(t),this),this.reject=e=>(t(e),this)})}}class oW{constructor(e,t,r,n){this._uri=e,this._languageId=t,this._version=r,this._content=n,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){for(let t of e)if(oW.isIncremental(t)){let e=oY(t.range),r=this.offsetAt(e.start),n=this.offsetAt(e.end);this._content=this._content.substring(0,r)+t.text+this._content.substring(n,this._content.length);let i=Math.max(e.start.line,0),a=Math.max(e.end.line,0),s=this._lineOffsets,o=oH(t.text,!1,r);if(a-i===o.length)for(let e=0,t=o.length;e<t;e++)s[e+i+1]=o[e];else o.length<1e4?s.splice(i+1,a-i,...o):this._lineOffsets=s=s.slice(0,i+1).concat(o,s.slice(a+1));let l=t.text.length-(n-r);if(0!==l)for(let e=i+1+o.length,t=s.length;e<t;e++)s[e]=s[e]+l}else if(oW.isFull(t))this._content=t.text,this._lineOffsets=void 0;else throw Error("Unknown change event received");this._version=t}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=oH(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,n=t.length;if(0===n)return{line:0,character:e};for(;r<n;){let i=Math.floor((r+n)/2);t[i]>e?n=i:r=i+1}let i=r-1;return{line:i,character:(e=this.ensureBeforeEOL(e,t[i]))-t[i]}}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line];if(e.character<=0)return r;let n=e.line+1<t.length?t[e.line+1]:this._content.length,i=Math.min(r+e.character,n);return this.ensureBeforeEOL(i,r)}ensureBeforeEOL(e,t){for(;e>t&&oz(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){return null!=e&&"string"==typeof e.text&&void 0!==e.range&&(void 0===e.rangeLength||"number"==typeof e.rangeLength)}static isFull(e){return null!=e&&"string"==typeof e.text&&void 0===e.range&&void 0===e.rangeLength}}function oH(e,t,r=0){let n=t?[r]:[];for(let t=0;t<e.length;t++){let i=e.charCodeAt(t);oz(i)&&(13===i&&t+1<e.length&&10===e.charCodeAt(t+1)&&t++,n.push(r+t+1))}return n}function oz(e){return 13===e||10===e}function oY(e){let t=e.start,r=e.end;return t.line>r.line||t.line===r.line&&t.character>r.character?{start:r,end:t}:e}function oq(e){let t=oY(e.range);return t!==e.range?{newText:e.newText,range:t}:e}(tJ=nT||(nT={})).create=function(e,t,r,n){return new oW(e,t,r,n)},tJ.update=function(e,t,r){if(e instanceof oW)return e.update(t,r),e;throw Error("TextDocument.update: document must be created by TextDocument.create")},tJ.applyEdits=function(e,t){let r=e.getText(),n=function e(t,r){if(t.length<=1)return t;let n=t.length/2|0,i=t.slice(0,n),a=t.slice(n);e(i,r),e(a,r);let s=0,o=0,l=0;for(;s<i.length&&o<a.length;){let e=r(i[s],a[o]);e<=0?t[l++]=i[s++]:t[l++]=a[o++]}for(;s<i.length;)t[l++]=i[s++];for(;o<a.length;)t[l++]=a[o++];return t}(t.map(oq),(e,t)=>{let r=e.range.start.line-t.range.start.line;return 0===r?e.range.start.character-t.range.start.character:r}),i=0,a=[];for(let t of n){let n=e.offsetAt(t.range.start);if(n<i)throw Error("Overlapping edit");n>i&&a.push(r.substring(i,n)),t.newText.length&&a.push(t.newText),i=e.offsetAt(t.range.end)}return a.push(r.substr(i)),a.join("")};var oX=r(83454);(()=>{var e={470:e=>{function t(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",i=0,a=-1,s=0,o=0;o<=e.length;++o){if(o<e.length)r=e.charCodeAt(o);else{if(47===r)break;r=47}if(47===r){if(a===o-1||1===s);else if(a!==o-1&&2===s){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",i=0):i=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),a=o,s=0;continue}}else if(2===n.length||1===n.length){n="",i=0,a=o,s=0;continue}}t&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+e.slice(a+1,o):n=e.slice(a+1,o),i=o-a-1;a=o,s=0}else 46===r&&-1!==s?++s:s=-1}return n}var n={resolve:function(){for(var e,n,i="",a=!1,s=arguments.length-1;s>=-1&&!a;s--)s>=0?e=arguments[s]:(void 0===n&&(n=oX.cwd()),e=n),t(e),0!==e.length&&(i=e+"/"+i,a=47===e.charCodeAt(0));return i=r(i,!a),a?i.length>0?"/"+i:"/":i.length>0?i:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&i&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0==arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var i=arguments[r];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=n.resolve(e))===(r=n.resolve(r)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var a=e.length,s=a-i,o=1;o<r.length&&47===r.charCodeAt(o);++o);for(var l=r.length-o,u=s<l?s:l,c=-1,d=0;d<=u;++d){if(d===u){if(l>u){if(47===r.charCodeAt(o+d))return r.slice(o+d+1);if(0===d)return r.slice(o+d)}else s>u&&(47===e.charCodeAt(i+d)?c=d:0===d&&(c=0));break}var h=e.charCodeAt(i+d);if(h!==r.charCodeAt(o+d))break;47===h&&(c=d)}var f="";for(d=i+c+1;d<=a;++d)d!==a&&47!==e.charCodeAt(d)||(0===f.length?f+="..":f+="/..");return f.length>0?f+r.slice(o+c):(o+=c,47===r.charCodeAt(o)&&++o,r.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,i=-1,a=!0,s=e.length-1;s>=1;--s)if(47===(r=e.charCodeAt(s))){if(!a){i=s;break}}else a=!1;return -1===i?n?"/":".":n&&1===i?"//":e.slice(0,i)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw TypeError('"ext" argument must be a string');t(e);var n,i=0,a=-1,s=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var o=r.length-1,l=-1;for(n=e.length-1;n>=0;--n){var u=e.charCodeAt(n);if(47===u){if(!s){i=n+1;break}}else -1===l&&(s=!1,l=n+1),o>=0&&(u===r.charCodeAt(o)?-1==--o&&(a=n):(o=-1,a=l))}return i===a?a=l:-1===a&&(a=e.length),e.slice(i,a)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!s){i=n+1;break}}else -1===a&&(s=!1,a=n+1);return -1===a?"":e.slice(i,a)},extname:function(e){t(e);for(var r=-1,n=0,i=-1,a=!0,s=0,o=e.length-1;o>=0;--o){var l=e.charCodeAt(o);if(47!==l)-1===i&&(a=!1,i=o+1),46===l?-1===r?r=o:1!==s&&(s=1):-1!==r&&(s=-1);else if(!a){n=o+1;break}}return -1===r||-1===i||0===s||1===s&&r===i-1&&r===n+1?"":e.slice(r,i)},format:function(e){var t,r;if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r},parse:function(e){t(e);var r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;var n,i=e.charCodeAt(0),a=47===i;a?(r.root="/",n=1):n=0;for(var s=-1,o=0,l=-1,u=!0,c=e.length-1,d=0;c>=n;--c)if(47!==(i=e.charCodeAt(c)))-1===l&&(u=!1,l=c+1),46===i?-1===s?s=c:1!==d&&(d=1):-1!==s&&(d=-1);else if(!u){o=c+1;break}return -1===s||-1===l||0===d||1===d&&s===l-1&&s===o+1?-1!==l&&(r.base=r.name=0===o&&a?e.slice(1,l):e.slice(o,l)):(0===o&&a?(r.name=e.slice(1,s),r.base=e.slice(1,l)):(r.name=e.slice(o,s),r.base=e.slice(o,l)),r.ext=e.slice(s,l)),o>0?r.dir=e.slice(0,o-1):a&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{let e;(r.r(n),r.d(n,{URI:()=>l,Utils:()=>v}),"object"==typeof oX)?e="win32"===oX.platform:"object"==typeof navigator&&(e=navigator.userAgent.indexOf("Windows")>=0);let t=/^\w[\w\d+.-]*$/,i=/^\//,a=/^\/\//;function s(e,r){if(!e.scheme&&r)throw Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!i.test(e.path))throw Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(a.test(e.path))throw Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}let o=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class l{static isUri(e){return e instanceof l||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,r,n,i,a=!1){"object"==typeof e?(this.scheme=e.scheme||"",this.authority=e.authority||"",this.path=e.path||"",this.query=e.query||"",this.fragment=e.fragment||""):(this.scheme=e||a?e:"file",this.authority=t||"",this.path=function(e,t){switch(e){case"https":case"http":case"file":t?"/"!==t[0]&&(t="/"+t):t="/"}return t}(this.scheme,r||""),this.query=n||"",this.fragment=i||"",s(this,a))}get fsPath(){return p(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:r,path:n,query:i,fragment:a}=e;return void 0===t?t=this.scheme:null===t&&(t=""),void 0===r?r=this.authority:null===r&&(r=""),void 0===n?n=this.path:null===n&&(n=""),void 0===i?i=this.query:null===i&&(i=""),void 0===a?a=this.fragment:null===a&&(a=""),t===this.scheme&&r===this.authority&&n===this.path&&i===this.query&&a===this.fragment?this:new c(t,r,n,i,a)}static parse(e,t=!1){let r=o.exec(e);return r?new c(r[2]||"",y(r[4]||""),y(r[5]||""),y(r[7]||""),y(r[9]||""),t):new c("","","","","")}static file(t){let r="";if(e&&(t=t.replace(/\\/g,"/")),"/"===t[0]&&"/"===t[1]){let e=t.indexOf("/",2);-1===e?(r=t.substring(2),t="/"):(r=t.substring(2,e),t=t.substring(e)||"/")}return new c("file",r,t,"","")}static from(e){let t=new c(e.scheme,e.authority,e.path,e.query,e.fragment);return s(t,!0),t}toString(e=!1){return m(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof l)return e;{let t=new c(e);return t._formatted=e.external,t._fsPath=e._sep===u?e.fsPath:null,t}}return e}}let u=e?1:void 0;class c extends l{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=p(this,!1)),this._fsPath}toString(e=!1){return e?m(this,!0):(this._formatted||(this._formatted=m(this,!1)),this._formatted)}toJSON(){let e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=u),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}let d={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function h(e,t,r){let n,i=-1;for(let a=0;a<e.length;a++){let s=e.charCodeAt(a);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||t&&47===s||r&&91===s||r&&93===s||r&&58===s)-1!==i&&(n+=encodeURIComponent(e.substring(i,a)),i=-1),void 0!==n&&(n+=e.charAt(a));else{void 0===n&&(n=e.substr(0,a));let t=d[s];void 0!==t?(-1!==i&&(n+=encodeURIComponent(e.substring(i,a)),i=-1),n+=t):-1===i&&(i=a)}}return -1!==i&&(n+=encodeURIComponent(e.substring(i))),void 0!==n?n:e}function f(e){let t;for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);35===n||63===n?(void 0===t&&(t=e.substr(0,r)),t+=d[n]):void 0!==t&&(t+=e[r])}return void 0!==t?t:e}function p(t,r){let n;return n=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&90>=t.path.charCodeAt(1)||t.path.charCodeAt(1)>=97&&122>=t.path.charCodeAt(1))&&58===t.path.charCodeAt(2)?r?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(n=n.replace(/\//g,"\\")),n}function m(e,t){let r=t?f:h,n="",{scheme:i,authority:a,path:s,query:o,fragment:l}=e;if(i&&(n+=i+":"),(a||"file"===i)&&(n+="//"),a){let e=a.indexOf("@");if(-1!==e){let t=a.substr(0,e);a=a.substr(e+1),-1===(e=t.lastIndexOf(":"))?n+=r(t,!1,!1):n+=r(t.substr(0,e),!1,!1)+":"+r(t.substr(e+1),!1,!0),n+="@"}-1===(e=(a=a.toLowerCase()).lastIndexOf(":"))?n+=r(a,!1,!0):n+=r(a.substr(0,e),!1,!0)+a.substr(e)}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){let e=s.charCodeAt(1);e>=65&&e<=90&&(s=`/${String.fromCharCode(e+32)}:${s.substr(3)}`)}else if(s.length>=2&&58===s.charCodeAt(1)){let e=s.charCodeAt(0);e>=65&&e<=90&&(s=`${String.fromCharCode(e+32)}:${s.substr(2)}`)}n+=r(s,!0,!1)}return o&&(n+="?"+r(o,!1,!1)),l&&(n+="#"+(t?l:h(l,!1,!1))),n}let g=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function y(e){return e.match(g)?e.replace(g,e=>(function e(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substr(0,3)+e(t.substr(3)):t}})(e)):e}var T,v,E=r(470);let R=E.posix||E;(T=v||(v={})).joinPath=function(e,...t){return e.with({path:R.join(e.path,...t)})},T.resolvePath=function(e,...t){let r=e.path,n=!1;"/"!==r[0]&&(r="/"+r,n=!0);let i=R.resolve(r,...t);return n&&"/"===i[0]&&!e.authority&&(i=i.substring(1)),e.with({path:i})},T.dirname=function(e){if(0===e.path.length||"/"===e.path)return e;let t=R.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},T.basename=function(e){return R.basename(e.path)},T.extname=function(e){return R.extname(e.path)}})(),nv=n})();let{URI:oQ,Utils:oJ}=nv;(t0=nE||(nE={})).basename=oJ.basename,t0.dirname=oJ.dirname,t0.extname=oJ.extname,t0.joinPath=oJ.joinPath,t0.resolvePath=oJ.resolvePath,t0.equals=function(e,t){return(null==e?void 0:e.toString())===(null==t?void 0:t.toString())},t0.relative=function(e,t){let r="string"==typeof e?e:e.path,n="string"==typeof t?t:t.path,i=r.split("/").filter(e=>e.length>0),a=n.split("/").filter(e=>e.length>0),s=0;for(;s<i.length&&i[s]===a[s];s++);let o="../".repeat(i.length-s),l=a.slice(s).join("/");return o+l},(t1=nR||(nR={}))[t1.Changed=0]="Changed",t1[t1.Parsed=1]="Parsed",t1[t1.IndexedContent=2]="IndexedContent",t1[t1.ComputedScopes=3]="ComputedScopes",t1[t1.Linked=4]="Linked",t1[t1.IndexedReferences=5]="IndexedReferences",t1[t1.Validated=6]="Validated";class o0{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=oG.Ts.None){let r=await this.fileSystemProvider.readFile(e);return this.createAsync(e,r,t)}fromTextDocument(e,t,r){return(t=null!=t?t:oQ.parse(e.uri),r)?this.createAsync(t,e,r):this.create(t,e)}fromString(e,t,r){return r?this.createAsync(t,e,r):this.create(t,e)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t){if("string"==typeof t){let r=this.parse(e,t);return this.createLangiumDocument(r,e,void 0,t)}if("$model"in t){let r={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(r,e)}{let r=this.parse(e,t.getText());return this.createLangiumDocument(r,e,t)}}async createAsync(e,t,r){if("string"==typeof t){let n=await this.parseAsync(e,t,r);return this.createLangiumDocument(n,e,void 0,t)}{let n=await this.parseAsync(e,t.getText(),r);return this.createLangiumDocument(n,e,t)}}createLangiumDocument(e,t,r,n){let i;if(r)i={parseResult:e,uri:t,state:nR.Parsed,references:[],textDocument:r};else{let r=this.createTextDocumentGetter(t,n);i={parseResult:e,uri:t,state:nR.Parsed,references:[],get textDocument(){return r()}}}return e.value.$document=i,i}async update(e,t){var r,n;let i=null===(r=e.parseResult.value.$cstNode)||void 0===r?void 0:r.root.fullText,a=null===(n=this.textDocuments)||void 0===n?void 0:n.get(e.uri.toString()),s=a?a.getText():await this.fileSystemProvider.readFile(e.uri);if(a)Object.defineProperty(e,"textDocument",{value:a});else{let t=this.createTextDocumentGetter(e.uri,s);Object.defineProperty(e,"textDocument",{get:t})}return i!==s&&(e.parseResult=await this.parseAsync(e.uri,s,t),e.parseResult.value.$document=e),e.state=nR.Parsed,e}parse(e,t){let r=this.serviceRegistry.getServices(e);return r.parser.LangiumParser.parse(t)}parseAsync(e,t,r){let n=this.serviceRegistry.getServices(e);return n.parser.AsyncParser.parse(t,r)}createTextDocumentGetter(e,t){let r;let n=this.serviceRegistry;return()=>null!=r?r:r=nT.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,null!=t?t:"")}}class o1{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory}get all(){return p(this.documentMap.values())}addDocument(e){let t=e.uri.toString();if(this.documentMap.has(t))throw Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){let t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let r=this.getDocument(e);return r||(r=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(r)),r}createDocument(e,t,r){if(r)return this.langiumDocumentFactory.fromString(t,e,r).then(e=>(this.addDocument(e),e));{let r=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(r),r}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){let t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=nR.Changed,r.precomputedScopes=void 0,r.references=[],r.diagnostics=void 0),r}deleteDocument(e){let t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=nR.Changed,this.documentMap.delete(t)),r}}class o2{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=oG.Ts.None){for(let r of eA(e.parseResult.value))await oj(t),eI(r).forEach(t=>this.doLink(t,e))}doLink(e,t){let r=e.reference;if(void 0===r._ref)try{let t=this.getCandidate(e);if(a(t))r._ref=t;else if(r._nodeDescription=t,this.langiumDocuments().hasDocument(t.documentUri)){let n=this.loadAstNode(t);r._ref=null!=n?n:this.createLinkingError(e,t)}}catch(t){r._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${r.$refText}': ${t}`})}t.references.push(r)}unlink(e){for(let t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){let t=this.scopeProvider.getScope(e),r=t.getElement(e.reference.$refText);return null!=r?r:this.createLinkingError(e)}buildReference(e,t,r,i){let s=this,o={$refNode:r,$refText:i,get ref(){var l,u;if(n(this._ref))return this._ref;if("object"==typeof(u=this._nodeDescription)&&null!==u&&"string"==typeof u.name&&"string"==typeof u.type&&"string"==typeof u.path){let r=s.loadAstNode(this._nodeDescription);this._ref=null!=r?r:s.createLinkingError({reference:o,container:e,property:t},this._nodeDescription)}else if(void 0===this._ref){let r=s.getLinkedNode({reference:o,container:e,property:t});if(r.error&&ev(e).state<nR.ComputedScopes)return;this._ref=null!==(l=r.node)&&void 0!==l?l:r.error,this._nodeDescription=r.descr}return n(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return a(this._ref)?this._ref:void 0}};return o}getLinkedNode(e){try{let t=this.getCandidate(e);if(a(t))return{error:t};let r=this.loadAstNode(t);if(r)return{node:r,descr:t};return{descr:t,error:this.createLinkingError(e,t)}}catch(t){return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${t}`})}}}loadAstNode(e){if(e.node)return e.node;let t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){let r=ev(e.container);r.state<nR.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${r.uri}).`);let n=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${n} named '${e.reference.$refText}'.`,targetDescription:t})}}class o3{getName(e){if("string"==typeof e.name)return e.name}getNameNode(e){return eV(e.$cstNode,"name")}}class o7{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){let t=function(e){var t;let r=e.astNode;for(;r===(null===(t=e.container)||void 0===t?void 0:t.astNode);){let t=eT(e.grammarSource,Y);if(t)return t;e=e.container}}(e),r=e.astNode;if(t&&r){let n=r[t.feature];if(i(n))return n.ref;if(Array.isArray(n)){for(let t of n)if(i(t)&&t.$refNode&&t.$refNode.offset<=e.offset&&t.$refNode.end>=e.end)return t.ref}}if(r){let t=this.nameProvider.getNameNode(r);if(t&&(t===e||function(e,t){for(;e.container;)if((e=e.container)===t)return!0;return!1}(e,t)))return r}}}findDeclarationNode(e){let t=this.findDeclaration(e);if(null==t?void 0:t.$cstNode){let e=this.nameProvider.getNameNode(t);return null!=e?e:t.$cstNode}}findReferences(e,t){let r=[];if(t.includeDeclaration){let t=this.getReferenceToSelf(e);t&&r.push(t)}let n=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(n=n.filter(e=>nE.equals(e.sourceUri,t.documentUri))),r.push(...n),p(r)}getReferenceToSelf(e){let t=this.nameProvider.getNameNode(e);if(t){let r=ev(e),n=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:n,targetUri:r.uri,targetPath:n,segment:T(t),local:!0}}}}class o4{constructor(e){if(this.map=new Map,e)for(let[t,r]of e)this.add(t,r)}get size(){return t4.sum(p(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(void 0===t)return this.map.delete(e);{let r=this.map.get(e);if(r){let n=r.indexOf(t);if(n>=0)return 1===r.length?this.map.delete(e):r.splice(n,1),!0}return!1}}get(e){var t;return null!==(t=this.map.get(e))&&void 0!==t?t:[]}has(e,t){if(void 0===t)return this.map.has(e);{let r=this.map.get(e);return!!r&&r.indexOf(t)>=0}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,r)=>t.forEach(t=>e(t,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return p(this.map.entries()).flatMap(([e,t])=>t.map(t=>[e,t]))}keys(){return p(this.map.keys())}values(){return p(this.map.values()).flat()}entriesGroupedByKey(){return p(this.map.entries())}}class o5{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(let[t,r]of e)this.set(t,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){let t=this.map.get(e);return void 0!==t&&(this.map.delete(e),this.inverse.delete(t),!0)}}class o6{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=oG.Ts.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,r=eE,n=oG.Ts.None){let i=[];for(let a of(this.exportNode(e,i,t),r(e)))await oj(n),this.exportNode(a,i,t);return i}exportNode(e,t,r){let n=this.nameProvider.getName(e);n&&t.push(this.descriptions.createDescription(e,n,r))}async computeLocalScopes(e,t=oG.Ts.None){let r=e.parseResult.value,n=new o4;for(let i of eR(r))await oj(t),this.processNode(i,e,n);return n}processNode(e,t,r){let n=e.$container;if(n){let i=this.nameProvider.getName(e);i&&r.add(n,this.descriptions.createDescription(e,i,t))}}}class o9{constructor(e,t,r){var n;this.elements=e,this.outerScope=t,this.caseInsensitive=null!==(n=null==r?void 0:r.caseInsensitive)&&void 0!==n&&n}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){let t=this.caseInsensitive?this.elements.find(t=>t.name.toLowerCase()===e.toLowerCase()):this.elements.find(t=>t.name===e);return t||(this.outerScope?this.outerScope.getElement(e):void 0)}}class o8{constructor(e,t,r){var n;for(let t of(this.elements=new Map,this.caseInsensitive=null!==(n=null==r?void 0:r.caseInsensitive)&&void 0!==n&&n,e)){let e=this.caseInsensitive?t.name.toLowerCase():t.name;this.elements.set(e,t)}this.outerScope=t}getElement(e){let t=this.caseInsensitive?e.toLowerCase():e,r=this.elements.get(t);return r||(this.outerScope?this.outerScope.getElement(e):void 0)}getAllElements(){let e=p(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class le{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw Error("This cache has already been disposed")}}class lt extends le{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){let r=t();return this.cache.set(e,r),r}}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class lr extends le{constructor(e){super(),this.cache=new Map,this.converter=null!=e?e:e=>e}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,r){this.throwIfDisposed(),this.cacheForContext(e).set(t,r)}get(e,t,r){this.throwIfDisposed();let n=this.cacheForContext(e);if(n.has(t))return n.get(t);if(r){let e=r();return n.set(t,e),e}}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){let t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){let t=this.converter(e),r=this.cache.get(t);return r||(r=new Map,this.cache.set(t,r)),r}}class ln extends lt{constructor(e){super(),this.onDispose(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class li{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new ln(e.shared)}getScope(e){let t=[],r=this.reflection.getReferenceType(e),n=ev(e.container).precomputedScopes;if(n){let i=e.container;do{let e=n.get(i);e.length>0&&t.push(p(e).filter(e=>this.reflection.isSubtype(e.type,r))),i=i.$container}while(i)}let i=this.getGlobalScope(r,e);for(let e=t.length-1;e>=0;e--)i=this.createScope(t[e],i);return i}createScope(e,t,r){return new o9(p(e),t,r)}createScopeForNodes(e,t,r){let n=p(e).map(e=>{let t=this.nameProvider.getName(e);if(t)return this.descriptions.createDescription(e,t)}).nonNullable();return new o9(n,t,r)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new o8(this.indexManager.allElements(e)))}}function la(e){return"object"==typeof e&&!!e&&("$ref"in e||"$error"in e)}class ls{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t={}){let r=null==t?void 0:t.replacer,n=(e,r)=>this.replacer(e,r,t);try{return this.currentDocument=ev(e),JSON.stringify(e,r?(e,t)=>r(e,t,n):n,null==t?void 0:t.space)}finally{this.currentDocument=void 0}}deserialize(e,t={}){let r=JSON.parse(e);return this.linkNode(r,r,t),r}replacer(e,t,{refText:r,sourceText:a,textRegions:s,comments:o,uriConverter:l}){var u,c,d,h;if(!this.ignoreProperties.has(e)){if(i(t)){let e=t.ref,n=r?t.$refText:void 0;if(!e)return{$error:null!==(c=null===(u=t.error)||void 0===u?void 0:u.message)&&void 0!==c?c:"Could not resolve reference",$refText:n};{let r=ev(e),i="";this.currentDocument&&this.currentDocument!==r&&(i=l?l(r.uri,t):r.uri.toString());let a=this.astNodeLocator.getAstNodePath(e);return{$ref:`${i}#${a}`,$refText:n}}}if(!n(t))return t;{let r;if(s&&(r=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&(null==r?void 0:r.$textRegion)&&(r.$textRegion.documentURI=null===(d=this.currentDocument)||void 0===d?void 0:d.uri.toString())),a&&!e&&(null!=r||(r=Object.assign({},t)),r.$sourceText=null===(h=t.$cstNode)||void 0===h?void 0:h.text),o){null!=r||(r=Object.assign({},t));let e=this.commentProvider.getComment(t);e&&(r.$comment=e.replace(/\r/g,""))}return null!=r?r:t}}}addAstNodeRegionWithAssignmentsTo(e){let t=e=>({offset:e.offset,end:e.end,length:e.length,range:e.range});if(e.$cstNode){let r=e.$textRegion=t(e.$cstNode),n=r.assignments={};return Object.keys(e).filter(e=>!e.startsWith("$")).forEach(r=>{var i;let a=((i=e.$cstNode)&&r?eW(i,r,i.astNode,!0):[]).map(t);0!==a.length&&(n[r]=a)}),e}}linkNode(e,t,r,i,a,s){for(let[i,a]of Object.entries(e))if(Array.isArray(a))for(let s=0;s<a.length;s++){let o=a[s];la(o)?a[s]=this.reviveReference(e,i,t,o,r):n(o)&&this.linkNode(o,t,r,e,i,s)}else la(a)?e[i]=this.reviveReference(e,i,t,a,r):n(a)&&this.linkNode(a,t,r,e,i);let o=e;o.$container=i,o.$containerProperty=a,o.$containerIndex=s}reviveReference(e,t,r,i,a){let s=i.$refText,o=i.$error;if(i.$ref){let e=this.getRefNode(r,i.$ref,a.uriConverter);if(n(e))return s||(s=this.nameProvider.getName(e)),{$refText:null!=s?s:"",ref:e};o=e}if(o){let r={$refText:null!=s?s:""};return r.error={container:e,property:t,message:o,reference:r},r}}getRefNode(e,t,r){try{let n=t.indexOf("#");if(0===n){let r=this.astNodeLocator.getAstNode(e,t.substring(1));if(!r)return"Could not resolve path: "+t;return r}if(n<0){let e=r?r(t):oQ.parse(t),n=this.langiumDocuments.getDocument(e);if(!n)return"Could not find document for URI: "+t;return n.parseResult.value}let i=r?r(t.substring(0,n)):oQ.parse(t.substring(0,n)),a=this.langiumDocuments.getDocument(i);if(!a)return"Could not find document for URI: "+t;if(n===t.length-1)return a.parseResult.value;let s=this.astNodeLocator.getAstNode(a.parseResult.value,t.substring(n+1));if(!s)return"Could not resolve URI: "+t;return s}catch(e){return String(e)}}}class lo{register(e){if(!this.singleton&&!this.map){this.singleton=e;return}if(!this.map&&(this.map={},this.singleton)){for(let e of this.singleton.LanguageMetaData.fileExtensions)this.map[e]=this.singleton;this.singleton=void 0}for(let t of e.LanguageMetaData.fileExtensions)void 0!==this.map[t]&&this.map[t]!==e&&console.warn(`The file extension ${t} is used by multiple languages. It is now assigned to '${e.LanguageMetaData.languageId}'.`),this.map[t]=e}getServices(e){if(void 0!==this.singleton)return this.singleton;if(void 0===this.map)throw Error("The service registry is empty. Use `register` to register the services of a language.");let t=nE.extname(e),r=this.map[t];if(!r)throw Error(`The service registry contains no services for the extension '${t}'.`);return r}get all(){return void 0!==this.singleton?[this.singleton]:void 0!==this.map?Object.values(this.map):[]}}(nA||(nA={})).all=["fast","slow","built-in"];class ll{constructor(e){this.entries=new o4,this.reflection=e.shared.AstReflection}register(e,t=this,r="fast"){if("built-in"===r)throw Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(let[n,i]of Object.entries(e)){let e=i;if(Array.isArray(e))for(let i of e){let e={check:this.wrapValidationException(i,t),category:r};this.addEntry(n,e)}else if("function"==typeof e){let i={check:this.wrapValidationException(e,t),category:r};this.addEntry(n,i)}}}wrapValidationException(e,t){return async(r,n,i)=>{try{await e.call(t,r,n,i)}catch(t){if(t===oK)throw t;console.error("An error occurred during validation:",t);let e=t instanceof Error?t.message:String(t);t instanceof Error&&t.stack&&console.error(t.stack),n("error","An error occurred during validation: "+e,{node:r})}}}addEntry(e,t){if("AstNode"===e){this.entries.add("AstNode",t);return}for(let r of this.reflection.getAllSubTypes(e))this.entries.add(r,t)}getChecks(e,t){let r=p(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(r=r.filter(e=>t.includes(e.category))),r.map(e=>e.check)}}class lu{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},r=oG.Ts.None){let n=e.parseResult,i=[];if(await oj(r),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(n,i,t),t.stopAfterLexingErrors&&i.some(e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.code)===nk.LexingError})||(this.processParsingErrors(n,i,t),t.stopAfterParsingErrors&&i.some(e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.code)===nk.ParsingError}))||(this.processLinkingErrors(e,i,t),t.stopAfterLinkingErrors&&i.some(e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.code)===nk.LinkingError}))))return i;try{i.push(...await this.validateAst(n.value,t,r))}catch(e){if(e===oK)throw e;console.error("An error occurred during validation:",e)}return await oj(r),i}processLexingErrors(e,t,r){for(let r of e.lexerErrors){let e={severity:lc("error"),range:{start:{line:r.line-1,character:r.column-1},end:{line:r.line-1,character:r.column+r.length-1}},message:r.message,data:{code:nk.LexingError},source:this.getSource()};t.push(e)}}processParsingErrors(e,t,r){for(let r of e.parserErrors){let e;if(isNaN(r.token.startOffset)){if("previousToken"in r){let t=r.previousToken;if(isNaN(t.startOffset)){let t={line:0,character:0};e={start:t,end:t}}else{let r={line:t.endLine-1,character:t.endColumn};e={start:r,end:r}}}}else e=y(r.token);if(e){let n={severity:lc("error"),range:e,message:r.message,data:{code:nk.ParsingError},source:this.getSource()};t.push(n)}}}processLinkingErrors(e,t,r){for(let r of e.references){let e=r.error;if(e){let r={node:e.container,property:e.property,index:e.index,data:{code:nk.LinkingError,containerType:e.container.$type,property:e.property,refText:e.reference.$refText}};t.push(this.toDiagnostic("error",e.message,r))}}}async validateAst(e,t,r=oG.Ts.None){let n=[],i=(e,t,r)=>{n.push(this.toDiagnostic(e,t,r))};return await Promise.all(eA(e).map(async e=>{await oj(r);let n=this.validationRegistry.getChecks(e.$type,t.categories);for(let t of n)await t(e,i,r)})),n}toDiagnostic(e,t,r){let n;return{message:t,range:r.range?r.range:("string"==typeof r.property?n=eV(r.node.$cstNode,r.property,r.index):"string"==typeof r.keyword&&(n=function(e,t,r){if(!e)return;let n=function(e,t,r){let n;if(e.astNode!==r)return[];if(en(e.grammarSource)&&e.grammarSource.value===t)return[e];let i=g(e).iterator(),a=[];do if(!(n=i.next()).done){let e=n.value;e.astNode===r?en(e.grammarSource)&&e.grammarSource.value===t&&a.push(e):i.prune()}while(!n.done);return a}(e,t,null==e?void 0:e.astNode);if(0!==n.length)return r=void 0!==r?Math.max(0,Math.min(r,n.length-1)):0,n[r]}(r.node.$cstNode,r.keyword,r.index)),null!=n||(n=r.node.$cstNode),n)?n.range:{start:{line:0,character:0},end:{line:0,character:0}},severity:lc(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function lc(e){switch(e){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw Error("Invalid diagnostic severity: "+e)}}(t2=nk||(nk={})).LexingError="lexing-error",t2.ParsingError="parsing-error",t2.LinkingError="linking-error";class ld{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,r=ev(e)){let n;null!=t||(t=this.nameProvider.getName(e));let i=this.astNodeLocator.getAstNodePath(e);if(!t)throw Error(`Node at path ${i} has no name.`);let a=()=>{var t;return null!=n?n:n=T(null!==(t=this.nameProvider.getNameNode(e))&&void 0!==t?t:e.$cstNode)};return{node:e,name:t,get nameSegment(){return a()},selectionSegment:T(e.$cstNode),type:e.$type,documentUri:r.uri,path:i}}}class lh{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=oG.Ts.None){let r=[],n=e.parseResult.value;for(let e of eA(n))await oj(t),eI(e).filter(e=>!a(e)).forEach(e=>{let t=this.createDescription(e);t&&r.push(t)});return r}createDescription(e){let t=e.reference.$nodeDescription,r=e.reference.$refNode;if(!t||!r)return;let n=ev(e.container).uri;return{sourceUri:n,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:T(r),local:nE.equals(t.documentUri,n)}}}class lf{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){let t=this.getAstNodePath(e.$container),r=this.getPathSegment(e),n=t+this.segmentSeparator+r;return n}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw Error("Missing '$containerProperty' in AST node.");return void 0!==t?e+this.indexSeparator+t:e}getAstNode(e,t){let r=t.split(this.segmentSeparator);return r.reduce((e,t)=>{if(!e||0===t.length)return e;let r=t.indexOf(this.indexSeparator);if(r>0){let n=t.substring(0,r),i=parseInt(t.substring(r+1)),a=e[n];return null==a?void 0:a[i]}return e[t]},e)}}class lp{constructor(e){this._ready=new oV,this.settings={},this.workspaceConfig=!1,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,r;this.workspaceConfig=null!==(r=null===(t=e.capabilities.workspace)||void 0===t?void 0:t.configuration)&&void 0!==r&&r}async initialized(e){if(this.workspaceConfig){if(e.register){let t=this.serviceRegistry.all;e.register({section:t.map(e=>this.toSectionName(e.LanguageMetaData.languageId))})}if(e.fetchConfiguration){let t=this.serviceRegistry.all.map(e=>({section:this.toSectionName(e.LanguageMetaData.languageId)})),r=await e.fetchConfiguration(t);t.forEach((e,t)=>{this.updateSectionConfiguration(e.section,r[t])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{this.updateSectionConfiguration(t,e.settings[t])})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;let r=this.toSectionName(e);if(this.settings[r])return this.settings[r][t]}toSectionName(e){return`${e}`}}(nI||(nI={})).create=function(e){return{dispose:async()=>await e()}};class lm{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new o4,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=nR.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},r=oG.Ts.None){var n,i;for(let r of e){let e=r.uri.toString();if(r.state===nR.Validated){if("boolean"==typeof t.validation&&t.validation)r.state=nR.IndexedReferences,r.diagnostics=void 0,this.buildState.delete(e);else if("object"==typeof t.validation){let a=this.buildState.get(e),s=null===(n=null==a?void 0:a.result)||void 0===n?void 0:n.validationChecks;if(s){let n=null!==(i=t.validation.categories)&&void 0!==i?i:nA.all,o=n.filter(e=>!s.includes(e));o.length>0&&(this.buildState.set(e,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:o})},result:a.result}),r.state=nR.IndexedReferences)}}}else this.buildState.delete(e)}this.currentState=nR.Changed,await this.emitUpdate(e.map(e=>e.uri),[]),await this.buildDocuments(e,t,r)}async update(e,t,r=oG.Ts.None){for(let e of(this.currentState=nR.Changed,t))this.langiumDocuments.deleteDocument(e),this.buildState.delete(e.toString()),this.indexManager.remove(e);for(let t of e){let e=this.langiumDocuments.invalidateDocument(t);if(!e){let e=this.langiumDocumentFactory.fromModel({$type:"INVALID"},t);e.state=nR.Changed,this.langiumDocuments.addDocument(e)}this.buildState.delete(t.toString())}let n=p(e).concat(t).map(e=>e.toString()).toSet();this.langiumDocuments.all.filter(e=>!n.has(e.uri.toString())&&this.shouldRelink(e,n)).forEach(e=>{let t=this.serviceRegistry.getServices(e.uri).references.Linker;t.unlink(e),e.state=Math.min(e.state,nR.ComputedScopes),e.diagnostics=void 0}),await this.emitUpdate(e,t),await oj(r);let i=this.langiumDocuments.all.filter(e=>{var t;return e.state<nR.Linked||!(null===(t=this.buildState.get(e.uri.toString()))||void 0===t?void 0:t.completed)}).toArray();await this.buildDocuments(i,this.updateBuildOptions,r)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(r=>r(e,t)))}shouldRelink(e,t){return!!e.references.some(e=>void 0!==e.error)||this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),nI.create(()=>{let t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,r){this.prepareBuild(e,t),await this.runCancelable(e,nR.Parsed,r,e=>this.langiumDocumentFactory.update(e,r)),await this.runCancelable(e,nR.IndexedContent,r,e=>this.indexManager.updateContent(e,r)),await this.runCancelable(e,nR.ComputedScopes,r,async e=>{let t=this.serviceRegistry.getServices(e.uri).references.ScopeComputation;e.precomputedScopes=await t.computeLocalScopes(e,r)}),await this.runCancelable(e,nR.Linked,r,e=>{let t=this.serviceRegistry.getServices(e.uri).references.Linker;return t.link(e,r)}),await this.runCancelable(e,nR.IndexedReferences,r,e=>this.indexManager.updateReferences(e,r));let n=e.filter(e=>this.shouldValidate(e));for(let t of(await this.runCancelable(n,nR.Validated,r,e=>this.validate(e,r)),e)){let e=this.buildState.get(t.uri.toString());e&&(e.completed=!0)}}prepareBuild(e,t){for(let r of e){let e=r.uri.toString(),n=this.buildState.get(e);(!n||n.completed)&&this.buildState.set(e,{completed:!1,options:t,result:null==n?void 0:n.result})}}async runCancelable(e,t,r,n){let i=e.filter(e=>e.state<t);for(let e of i)await oj(r),await n(e),e.state=t;await this.notifyBuildPhase(i,t,r),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),nI.create(()=>{this.buildPhaseListeners.delete(e,t)})}waitUntil(e,t,r){let n;if(t&&"path"in t?n=t:r=t,null!=r||(r=oG.Ts.None),n){let t=this.langiumDocuments.getDocument(n);if(t&&t.state>e)return Promise.resolve(n)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(oK):new Promise((t,i)=>{let a=this.onBuildPhase(e,()=>{if(a.dispose(),s.dispose(),n){let e=this.langiumDocuments.getDocument(n);t(null==e?void 0:e.uri)}else t(void 0)}),s=r.onCancellationRequested(()=>{a.dispose(),s.dispose(),i(oK)})})}async notifyBuildPhase(e,t,r){if(0===e.length)return;let n=this.buildPhaseListeners.get(t);for(let t of n)await oj(r),await t(e,r)}shouldValidate(e){return Boolean(this.getBuildOptions(e).validation)}async validate(e,t){var r,n;let i=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,a=this.getBuildOptions(e).validation,s="object"==typeof a?a:void 0,o=await i.validateDocument(e,s,t);e.diagnostics?e.diagnostics.push(...o):e.diagnostics=o;let l=this.buildState.get(e.uri.toString());if(l){null!==(r=l.result)&&void 0!==r||(l.result={});let e=null!==(n=null==s?void 0:s.categories)&&void 0!==n?n:nA.all;l.result.validationChecks?l.result.validationChecks.push(...e):l.result.validationChecks=[...e]}}getBuildOptions(e){var t,r;return null!==(r=null===(t=this.buildState.get(e.uri.toString()))||void 0===t?void 0:t.options)&&void 0!==r?r:{}}}class lg{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new lr,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){let r=ev(e).uri,n=[];return this.referenceIndex.forEach(e=>{e.forEach(e=>{nE.equals(e.targetUri,r)&&e.targetPath===t&&n.push(e)})}),p(n)}allElements(e,t){let r=p(this.symbolIndex.keys());return t&&(r=r.filter(e=>!t||t.has(e))),r.map(t=>this.getFileDescriptions(t,e)).flat()}getFileDescriptions(e,t){var r;if(!t)return null!==(r=this.symbolIndex.get(e))&&void 0!==r?r:[];let n=this.symbolByTypeIndex.get(e,t,()=>{var r;let n=null!==(r=this.symbolIndex.get(e))&&void 0!==r?r:[];return n.filter(e=>this.astReflection.isSubtype(e.type,t))});return n}remove(e){let t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=oG.Ts.None){let r=this.serviceRegistry.getServices(e.uri),n=await r.references.ScopeComputation.computeExports(e,t),i=e.uri.toString();this.symbolIndex.set(i,n),this.symbolByTypeIndex.clear(i)}async updateReferences(e,t=oG.Ts.None){let r=this.serviceRegistry.getServices(e.uri),n=await r.workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),n)}isAffected(e,t){let r=this.referenceIndex.get(e.uri.toString());return!!r&&r.some(e=>!e.local&&t.has(e.targetUri.toString()))}}class ly{constructor(e){this.initialBuildOptions={},this._ready=new oV,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}initialize(e){var t;this.folders=null!==(t=e.workspaceFolders)&&void 0!==t?t:void 0}initialized(e){return this.mutex.write(e=>{var t;return this.initializeWorkspace(null!==(t=this.folders)&&void 0!==t?t:[],e)})}async initializeWorkspace(e,t=oG.Ts.None){let r=await this.performStartup(e);await oj(t),await this.documentBuilder.build(r,this.initialBuildOptions,t)}async performStartup(e){let t=this.serviceRegistry.all.flatMap(e=>e.LanguageMetaData.fileExtensions),r=[],n=e=>{r.push(e),this.langiumDocuments.hasDocument(e.uri)||this.langiumDocuments.addDocument(e)};return await this.loadAdditionalDocuments(e,n),await Promise.all(e.map(e=>[e,this.getRootFolder(e)]).map(async e=>this.traverseFolder(...e,t,n))),this._ready.resolve(),r}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return oQ.parse(e.uri)}async traverseFolder(e,t,r,n){let i=await this.fileSystemProvider.readDirectory(t);await Promise.all(i.map(async t=>{if(this.includeEntry(e,t,r)){if(t.isDirectory)await this.traverseFolder(e,t.uri,r,n);else if(t.isFile){let e=await this.langiumDocuments.getOrCreateDocument(t.uri);n(e)}}}))}includeEntry(e,t,r){let n=nE.basename(t.uri);if(n.startsWith("."))return!1;if(t.isDirectory)return"node_modules"!==n&&"out"!==n;if(t.isFile){let e=nE.extname(t.uri);return r.includes(e)}return!1}}class lT{constructor(e){let t=e.parser.TokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);let r=lE(t)?Object.values(t):t;this.chevrotainLexer=new aS(r,{positionTracking:"full"})}get definition(){return this.tokenTypes}tokenize(e){var t;let r=this.chevrotainLexer.tokenize(e);return{tokens:r.tokens,errors:r.errors,hidden:null!==(t=r.groups.hidden)&&void 0!==t?t:[]}}toTokenTypeDictionary(e){if(lE(e))return e;let t=lv(e)?Object.values(e.modes).flat():e,r={};return t.forEach(e=>r[e.name]=e),r}}function lv(e){return e&&"modes"in e&&"defaultMode"in e}function lE(e){return!(Array.isArray(e)&&(0===e.length||"name"in e[0]))&&!lv(e)}function lR(e){let t="";t="string"==typeof e?e:e.text;let r=t.split(eU);return r}let lA=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,lk=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu,lI=/\S/,lx=/\s*$/;function lS(e,t){let r=e.substring(t).match(lI);return r?t+r.index:e.length}function lN(e){let t=e.tokens[e.index],r=t,n=t,i=[];for(;t&&"break"!==t.type&&"tag"!==t.type;)i.push(function(e){let t=e.tokens[e.index];return"inline-tag"===t.type?lC(e,!0):l$(e)}(e)),n=t,t=e.tokens[e.index];return new l_(i,rs.create(r.range.start,n.range.end))}function lC(e,t){let r=e.tokens[e.index++],n=r.content.substring(1),i=e.tokens[e.index];if((null==i?void 0:i.type)==="text"){if(t){let i=l$(e);return new lO(n,new l_([i],i.range),t,rs.create(r.range.start,i.range.end))}{let i=lN(e);return new lO(n,i,t,rs.create(r.range.start,i.range.end))}}{let e=r.range;return new lO(n,new l_([],e),t,e)}}function l$(e){let t=e.tokens[e.index++];return new lP(t.content,t.range)}function lL(e){if(!e)return lL({start:"/**",end:"*/",line:"*"});let{start:t,end:r,line:n}=e;return{start:lw(t,!0),end:lw(r,!1),line:lw(n,!0)}}function lw(e,t){if("string"!=typeof e&&"object"!=typeof e)return e;{let r="string"==typeof e?eK(e):e.source;return t?RegExp(`^\\s*${r}`):RegExp(`\\s*${r}\\s*$`)}}class lb{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(let t of this.elements)if(0===e.length)e=t.toString();else{let r=t.toString();e+=lM(e)+r}return e.trim()}toMarkdown(e){let t="";for(let r of this.elements)if(0===t.length)t=r.toMarkdown(e);else{let n=r.toMarkdown(e);t+=lM(t)+n}return t.trim()}}class lO{constructor(e,t,r,n){this.name=e,this.content=t,this.inline=r,this.range=n}toString(){let e=`@${this.name}`,t=this.content.toString();return(1===this.content.inlines.length?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline)?`{${e}}`:e}toMarkdown(e){var t,r;return null!==(r=null===(t=null==e?void 0:e.renderTag)||void 0===t?void 0:t.call(e,this))&&void 0!==r?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){let t=this.content.toMarkdown(e);if(this.inline){let r=function(e,t,r){var n,i;if("linkplain"===e||"linkcode"===e||"link"===e){let a=t.indexOf(" "),s=t;if(a>0){let e=lS(t,a);s=t.substring(e),t=t.substring(0,a)}("linkcode"===e||"link"===e&&"code"===r.link)&&(s=`\`${s}\``);let o=null!==(i=null===(n=r.renderLink)||void 0===n?void 0:n.call(r,t,s))&&void 0!==i?i:function(e,t){try{return oQ.parse(e,!0),`[${t}](${e})`}catch(t){return e}}(t,s);return o}}(this.name,t,null!=e?e:{});if("string"==typeof r)return r}let r="";(null==e?void 0:e.tag)==="italic"||(null==e?void 0:e.tag)===void 0?r="*":(null==e?void 0:e.tag)==="bold"?r="**":(null==e?void 0:e.tag)==="bold-italic"&&(r="***");let n=`${r}@${this.name}${r}`;return(1===this.content.inlines.length?n=`${n} — ${t}`:this.content.inlines.length>1&&(n=`${n}
${t}`),this.inline)?`{${n}}`:n}}class l_{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){let r=this.inlines[t],n=this.inlines[t+1];e+=r.toString(),n&&n.range.start.line>r.range.start.line&&(e+="\n")}return e}toMarkdown(e){let t="";for(let r=0;r<this.inlines.length;r++){let n=this.inlines[r],i=this.inlines[r+1];t+=n.toMarkdown(e),i&&i.range.start.line>n.range.start.line&&(t+="\n")}return t}}class lP{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function lM(e){return e.endsWith("\n")?"\n":"\n\n"}class lD{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){let t=this.commentProvider.getComment(e);if(t&&function(e,t){let r=lL(void 0),n=lR(e);if(0===n.length)return!1;let i=n[0],a=n[n.length-1],s=r.start,o=r.end;return Boolean(null==s?void 0:s.exec(i))&&Boolean(null==o?void 0:o.exec(a))}(t)){let r=function(e,t,r){let n,i;"string"==typeof e?(i=t,n=void 0):(i=e.range.start,n=t),i||(i=ra.create(0,0));let a=lR(e),s=lL(n),o=function(e){var t,r,n;let i=[],a=e.position.line,s=e.position.character;for(let o=0;o<e.lines.length;o++){let l=0===o,u=o===e.lines.length-1,c=e.lines[o],d=0;if(l&&e.options.start){let r=null===(t=e.options.start)||void 0===t?void 0:t.exec(c);r&&(d=r.index+r[0].length)}else{let t=null===(r=e.options.line)||void 0===r?void 0:r.exec(c);t&&(d=t.index+t[0].length)}if(u){let t=null===(n=e.options.end)||void 0===n?void 0:n.exec(c);t&&(c=c.substring(0,t.index))}c=c.substring(0,function(e){let t=e.match(lx);if(t&&"number"==typeof t.index)return t.index}(c));let h=lS(c,d);if(h>=c.length){if(i.length>0){let e=ra.create(a,s);i.push({type:"break",content:"",range:rs.create(e,e)})}}else{lA.lastIndex=d;let e=lA.exec(c);if(e){let t=e[0],r=e[1],n=ra.create(a,s+d),o=ra.create(a,s+d+t.length);i.push({type:"tag",content:r,range:rs.create(n,o)}),d+=t.length,d=lS(c,d)}if(d<c.length){let e=c.substring(d),t=Array.from(e.matchAll(lk));i.push(...function(e,t,r,n){let i=[];if(0===e.length){let e=ra.create(r,n),a=ra.create(r,n+t.length);i.push({type:"text",content:t,range:rs.create(e,a)})}else{let a=0;for(let s of e){let e=s.index,o=t.substring(a,e);o.length>0&&i.push({type:"text",content:t.substring(a,e),range:rs.create(ra.create(r,a+n),ra.create(r,e+n))});let l=o.length+1,u=s[1];if(i.push({type:"inline-tag",content:u,range:rs.create(ra.create(r,a+l+n),ra.create(r,a+l+u.length+n))}),l+=u.length,4===s.length){l+=s[2].length;let e=s[3];i.push({type:"text",content:e,range:rs.create(ra.create(r,a+l+n),ra.create(r,a+l+e.length+n))})}else i.push({type:"text",content:"",range:rs.create(ra.create(r,a+l+n),ra.create(r,a+l+n))});a=e+s[0].length}let s=t.substring(a);s.length>0&&i.push({type:"text",content:s,range:rs.create(ra.create(r,a+n),ra.create(r,a+n+s.length))})}return i}(t,e,a,s+d))}}a++,s=0}return i.length>0&&"break"===i[i.length-1].type?i.slice(0,-1):i}({lines:a,position:i,options:s});return function(e){var t,r,n,i;let a=ra.create(e.position.line,e.position.character);if(0===e.tokens.length)return new lb([],rs.create(a,a));let s=[];for(;e.index<e.tokens.length;){let t=function(e,t){let r=e.tokens[e.index];return"tag"===r.type?lC(e,!1):"text"===r.type||"inline-tag"===r.type?lN(e):void(function(e,t){if(t){let r=new lP("",e.range);"inlines"in t?t.inlines.push(r):t.content.inlines.push(r)}}(r,t),e.index++)}(e,s[s.length-1]);t&&s.push(t)}let o=null!==(r=null===(t=s[0])||void 0===t?void 0:t.range.start)&&void 0!==r?r:a,l=null!==(i=null===(n=s[s.length-1])||void 0===n?void 0:n.range.end)&&void 0!==i?i:a;return new lb(s,rs.create(o,l))}({index:0,tokens:o,position:i})}(t);return r.toMarkdown({renderLink:(t,r)=>this.documentationLinkRenderer(e,t,r),renderTag:t=>this.documentationTagRenderer(e,t)})}}documentationLinkRenderer(e,t,r){var n;let i=null!==(n=this.findNameInPrecomputedScopes(e,t))&&void 0!==n?n:this.findNameInGlobalScope(e,t);if(i&&i.nameSegment){let e=i.nameSegment.range.start.line+1,t=i.nameSegment.range.start.character+1,n=i.documentUri.with({fragment:`L${e},${t}`});return`[${r}](${n.toString()})`}}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){let r=ev(e),n=r.precomputedScopes;if(!n)return;let i=e;do{let e=n.get(i),r=e.find(e=>e.name===t);if(r)return r;i=i.$container}while(i)}findNameInGlobalScope(e,t){let r=this.indexManager.allElements().find(e=>e.name===t);return r}}class lZ{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return"string"==typeof e.$comment?e.$comment:null===(t=function(e,t){if(e){let r=function(e,t=!0){for(;e.container;){let r=e.container,n=r.content.indexOf(e);for(;n>0;){n--;let e=r.content[n];if(t||!e.hidden)return e}e=r}}(e,!0);if(r&&E(r,t))return r;if(u(e)){let r=e.content.findIndex(e=>!e.hidden);for(let n=r-1;n>=0;n--){let r=e.content[n];if(E(r,t))return r}}}}(e.$cstNode,this.grammarConfig().multilineCommentRules))||void 0===t?void 0:t.text}}r(27135);class lU{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e){return Promise.resolve(this.syncParser.parse(e))}}class lF{constructor(){this.previousTokenSource=new oG.AU,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();let t=new oG.AU;return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,r){let n=new oV,i={action:t,deferred:n,cancellationToken:null!=r?r:oG.Ts.None};return e.push(i),this.performNextOperation(),n.promise}async performNextOperation(){if(!this.done)return;let e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else{if(!(this.readQueue.length>0))return;e.push(...this.readQueue.splice(0,this.readQueue.length))}this.done=!1,await Promise.all(e.map(async({action:e,deferred:t,cancellationToken:r})=>{try{let n=await Promise.resolve().then(()=>e(r));t.resolve(n)}catch(e){e===oK?t.resolve(void 0):t.reject(e)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class lG{constructor(e){this.grammarElementIdMap=new o5,this.tokenTypeIdMap=new o5,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors.map(e=>Object.assign({},e)),parserErrors:e.parserErrors.map(e=>Object.assign({},e)),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}createDehyrationContext(e){let t=new Map,r=new Map;for(let r of eA(e))t.set(r,{});if(e.$cstNode)for(let t of g(e.$cstNode))r.set(t,{});return{astNodes:t,cstNodes:r}}dehydrateAstNode(e,t){let r=t.astNodes.get(e);for(let[a,s]of(r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,void 0!==e.$cstNode&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,t)),Object.entries(e)))if(!a.startsWith("$")){if(Array.isArray(s)){let e=[];for(let o of(r[a]=e,s))n(o)?e.push(this.dehydrateAstNode(o,t)):i(o)?e.push(this.dehydrateReference(o,t)):e.push(o)}else n(s)?r[a]=this.dehydrateAstNode(s,t):i(s)?r[a]=this.dehydrateReference(s,t):void 0!==s&&(r[a]=s)}return r}dehydrateReference(e,t){let r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=t.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,t){let r=t.cstNodes.get(e);return u(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=t.astNodes.get(e.astNode),o(e)?r.content=e.content.map(e=>this.dehydrateCstNode(e,t)):l(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){let t=e.value,r=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,r),{lexerErrors:e.lexerErrors,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,r)}}createHydrationContext(e){let t;let r=new Map,n=new Map;for(let t of eA(e))r.set(t,{});if(e.$cstNode)for(let r of g(e.$cstNode)){let e;"fullText"in r?t=e=new oA(r.fullText):"content"in r?e=new oE:"tokenType"in r&&(e=this.hydrateCstLeafNode(r)),e&&(n.set(r,e),e.root=t)}return{astNodes:r,cstNodes:n}}hydrateAstNode(e,t){let r=t.astNodes.get(e);for(let[a,s]of(r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=t.cstNodes.get(e.$cstNode)),Object.entries(e)))if(!a.startsWith("$")){if(Array.isArray(s)){let e=[];for(let o of(r[a]=e,s))n(o)?e.push(this.setParent(this.hydrateAstNode(o,t),r)):i(o)?e.push(this.hydrateReference(o,r,a,t)):e.push(o)}else n(s)?r[a]=this.setParent(this.hydrateAstNode(s,t),r):i(s)?r[a]=this.hydrateReference(s,r,a,t):void 0!==s&&(r[a]=s)}return r}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,r,n){return this.linker.buildReference(t,r,n.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,r=0){let n=t.cstNodes.get(e);if("number"==typeof e.grammarSource&&(n.grammarSource=this.getGrammarElement(e.grammarSource)),n.astNode=t.astNodes.get(e.astNode),o(n))for(let i of e.content){let e=this.hydrateCstNode(i,t,r++);n.content.push(e)}return n}hydrateCstLeafNode(e){let t=this.getTokenType(e.tokenType),r=e.offset,n=e.length,i=e.startLine,a=e.startColumn,s=e.endLine,o=e.endColumn,l=e.hidden,u=new ov(r,n,{start:{line:i,character:a},end:{line:s,character:o}},t,l);return u}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){return 0===this.grammarElementIdMap.size&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){0===this.grammarElementIdMap.size&&this.createGrammarElementIdMap();let t=this.grammarElementIdMap.getKey(e);if(t)return t;throw Error("Invalid grammar element id: "+e)}createGrammarElementIdMap(){let e=0;for(let r of eA(this.grammar)){var t;t=r,ey.isInstance(t,N)&&this.grammarElementIdMap.set(r,e++)}}}function lB(e){return{documentation:{CommentProvider:e=>new lZ(e),DocumentationProvider:e=>new lD(e)},parser:{AsyncParser:e=>new lU(e),GrammarConfig:e=>(function(e){let t=[],r=e.Grammar;for(let e of r.rules){var n;G(e)&&(n=e).hidden&&!eX(n).test(" ")&&function(e){try{return"string"==typeof e&&(e=RegExp(e)),e=e.toString(),eG.reset(e),eG.visit(eF.pattern(e)),eG.multiline}catch(e){return!1}}(eX(e))&&t.push(e.name)}return{multilineCommentRules:t,nameRegexp:v}})(e),LangiumParser:e=>(function(e){let t=function(e){let t=e.Grammar,r=e.parser.Lexer,n=new oN(e);return oO(t,n,r.definition)}(e);return t.finalize(),t})(e),CompletionParser:e=>(function(e){let t=e.Grammar,r=e.parser.Lexer,n=new oL(e);return oO(t,n,r.definition),n.finalize(),n})(e),ValueConverter:()=>new oF,TokenBuilder:()=>new oU,Lexer:e=>new lT(e),ParserErrorMessageProvider:()=>new o$},workspace:{AstNodeLocator:()=>new lf,AstNodeDescriptionProvider:e=>new ld(e),ReferenceDescriptionProvider:e=>new lh(e)},references:{Linker:e=>new o2(e),NameProvider:()=>new o3,ScopeProvider:e=>new li(e),ScopeComputation:e=>new o6(e),References:e=>new o7(e)},serializer:{Hydrator:e=>new lG(e),JsonSerializer:e=>new ls(e)},validation:{DocumentValidator:e=>new lu(e),ValidationRegistry:e=>new ll(e)},shared:()=>e.shared}}function lK(e){return{ServiceRegistry:()=>new lo,workspace:{LangiumDocuments:e=>new o1(e),LangiumDocumentFactory:e=>new o0(e),DocumentBuilder:e=>new lm(e),IndexManager:e=>new lg(e),WorkspaceManager:e=>new ly(e),FileSystemProvider:t=>e.fileSystemProvider(t),WorkspaceLock:()=>new lF,ConfigurationProvider:e=>new lp(e)}}}function lj(e,t,r,n,i,a,s,o,l){let u=[e,t,r,n,i,a,s,o,l].reduce(lY,{});return lW(u)}(nx||(nx={})).merge=(e,t)=>lY(lY({},e),t);let lV=Symbol("isProxy");function lW(e,t){let r=new Proxy({},{deleteProperty:()=>!1,get:(n,i)=>lz(n,i,e,t||r),getOwnPropertyDescriptor:(n,i)=>(lz(n,i,e,t||r),Object.getOwnPropertyDescriptor(n,i)),has:(t,r)=>r in e,ownKeys:()=>[...Reflect.ownKeys(e),lV]});return r[lV]=!0,r}let lH=Symbol();function lz(e,t,r,n){if(t in e){if(e[t]instanceof Error)throw Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:e[t]});if(e[t]===lH)throw Error('Cycle detected. Please make "'+String(t)+'" lazy. See https://langium.org/docs/configuration-services/#resolving-cyclic-dependencies');return e[t]}if(t in r){let i=r[t];e[t]=lH;try{e[t]="function"==typeof i?i(n):lW(i,n)}catch(r){throw e[t]=r instanceof Error?r:void 0,r}return e[t]}}function lY(e,t){if(t){for(let[r,n]of Object.entries(t))if(void 0!==n){let t=e[r];null!==t&&null!==n&&"object"==typeof t&&"object"==typeof n?e[r]=lY(t,n):e[r]=n}}return e}class lq{readFile(){throw Error("No file system is available.")}async readDirectory(){return[]}}let lX={fileSystemProvider:()=>new lq},lQ={Grammar:()=>void 0,LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},lJ={AstReflection:()=>new eg};function l0(e){var t;let r=function(){let e=lj(lK(lX),lJ),t=lj(lB({shared:e}),lQ);return e.ServiceRegistry.register(t),t}(),n=r.serializer.JsonSerializer.deserialize(e);return r.shared.workspace.LangiumDocumentFactory.fromModel(n,oQ.parse(`memory://${null!==(t=n.name)&&void 0!==t?t:"grammar"}.langium`)),n}},58e3:function(e,t,r){var n=r(72714);t.Z=function(e,t,r){for(var i=-1,a=e.length;++i<a;){var s=e[i],o=t(s);if(null!=o&&(void 0===l?o==o&&!(0,n.Z)(o):r(o,l)))var l=o,u=s}return u}},40676:function(e,t){t.Z=function(e,t){return e<t}},87156:function(e,t,r){var n=r(49811),i=r(50585);t.Z=function(e,t){var r=-1,a=(0,i.Z)(e)?Array(e.length):[];return(0,n.Z)(e,function(e,n,i){a[++r]=t(e,n,i)}),a}},51795:function(e,t,r){r.d(t,{Z:function(){return c}});var n=r(13317),i=r(72954),a=r(22823),s=r(56009),o=r(77226),l=r(62281),u=function(e,t,r,n){if(!(0,o.Z)(e))return e;t=(0,a.Z)(t,e);for(var u=-1,c=t.length,d=c-1,h=e;null!=h&&++u<c;){var f=(0,l.Z)(t[u]),p=r;if("__proto__"===f||"constructor"===f||"prototype"===f)break;if(u!=d){var m=h[f];void 0===(p=n?n(m,f,h):void 0)&&(p=(0,o.Z)(m)?m:(0,s.Z)(t[u+1])?[]:{})}(0,i.Z)(h,f,p),h=h[f]}return e},c=function(e,t,r){for(var i=-1,s=t.length,o={};++i<s;){var l=t[i],c=(0,n.Z)(e,l);r(c,l)&&u(o,(0,a.Z)(l,e),c)}return o}},92346:function(e,t,r){var n=r(45401);t.Z=function(e){return(0,n.Z)(e,4)}},3688:function(e,t,r){var n=r(69581),i=r(79651),a=r(50439),s=r(32957),o=Object.prototype,l=o.hasOwnProperty,u=(0,n.Z)(function(e,t){e=Object(e);var r=-1,n=t.length,u=n>2?t[2]:void 0;for(u&&(0,a.Z)(t[0],t[1],u)&&(n=1);++r<n;)for(var c=t[r],d=(0,s.Z)(c),h=-1,f=d.length;++h<f;){var p=d[h],m=e[p];(void 0===m||(0,i.Z)(m,o[p])&&!l.call(e,p))&&(e[p]=c[p])}return e});t.Z=u},38656:function(e,t,r){r.d(t,{Z:function(){return c}});var n,i=r(2e3),a=r(50585),s=r(17179),o=r(21692),l=r(24930),u=Math.max,c=(n=function(e,t,r){var n=null==e?0:e.length;if(!n)return -1;var a=null==r?0:(0,l.Z)(r);return a<0&&(a=u(n+a,0)),(0,o.Z)(e,(0,i.Z)(t,3),a)},function(e,t,r){var o=Object(e);if(!(0,a.Z)(e)){var l=(0,i.Z)(t,3);e=(0,s.Z)(e),t=function(e){return l(o[e],e,o)}}var u=n(e,t,r);return u>-1?o[l?e[u]:u]:void 0})},27961:function(e,t,r){var n=r(10626);t.Z=function(e){return(null==e?0:e.length)?(0,n.Z)(e,1):[]}},17452:function(e,t,r){r.d(t,{Z:function(){return s}});var n=Object.prototype.hasOwnProperty,i=function(e,t){return null!=e&&n.call(e,t)},a=r(16174),s=function(e,t){return null!=e&&(0,a.Z)(e,t,i)}},36378:function(e,t,r){var n=r(93589),i=r(27771),a=r(18533);t.Z=function(e){return"string"==typeof e||!(0,i.Z)(e)&&(0,a.Z)(e)&&"[object String]"==(0,n.Z)(e)}},935:function(e,t){t.Z=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},43836:function(e,t,r){var n=r(74073),i=r(2e3),a=r(87156),s=r(27771);t.Z=function(e,t){return((0,s.Z)(e)?n.Z:a.Z)(e,(0,i.Z)(t,3))}},3729:function(e,t,r){var n=r(58e3),i=r(40676),a=r(69203);t.Z=function(e){return e&&e.length?(0,n.Z)(e,a.Z,i.Z):void 0}},94099:function(e,t,r){r.d(t,{Z:function(){return m}});var n=/\s/,i=function(e){for(var t=e.length;t--&&n.test(e.charAt(t)););return t},a=/^\s+/,s=r(77226),o=r(72714),l=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,d=/^0o[0-7]+$/i,h=parseInt,f=function(e){if("number"==typeof e)return e;if((0,o.Z)(e))return l;if((0,s.Z)(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=(0,s.Z)(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=(t=e)?t.slice(0,i(t)+1).replace(a,""):t;var n=c.test(e);return n||d.test(e)?h(e.slice(2),n?2:8):u.test(e)?l:+e},p=1/0,m=function(e){return e?(e=f(e))===p||e===-p?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},24930:function(e,t,r){var n=r(94099);t.Z=function(e){var t=(0,n.Z)(e),r=t%1;return t==t?r?t-r:t:0}}}]);