(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[320],{61946:function(n,e,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/dreamlens",function(){return s(39264)}])},39264:function(n,e,s){"use strict";s.r(e),s.d(e,{__N_SSG:function(){return u},default:function(){return c}});var r=s(85893),t=s(9008),i=s.n(t),o=s(70425);s(11752);var a=s(47426);s(63214);var u=!0;function c(){return(0,r.jsxs)(r.Frag<PERSON>,{children:[(0,r.jsxs)(i(),{children:[(0,r.jsx)("title",{children:"DreamLens: AI-Powered Dream Analysis & Interpretation | FunBlocks AI Tools"}),(0,r.jsx)("meta",{name:"description",content:"Unlock your subconscious with Dream<PERSON><PERSON>, an AI-powered dream analysis tool. Get personalized insights into your dreams using psychology and neuroscience perspectives."}),(0,r.jsx)("meta",{name:"keywords",content:"dream analysis, dream interpretation, AI dream analysis, subconscious, psychology, Freud, Jung, cognitive neuroscience, dream symbols, personal growth, mental health, self-understanding, insight, visualization, mind map, DreamLens"})]}),(0,r.jsx)(o.Z,{app:a.IF.dreamlens})]})}},63214:function(n,e,s){"use strict";s(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return n(n.s=61946)}),_N_E=n.O()}]);