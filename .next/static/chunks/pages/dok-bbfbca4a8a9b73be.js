(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4768],{35315:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/dok",function(){return t(44443)}])},44443:function(e,n,t){"use strict";t.r(n),t.d(n,{__N_SSG:function(){return u},default:function(){return c}});var r=t(85893),o=t(9008),s=t.n(o),i=t(70425);t(11752);var a=t(47426);t(63214);var u=!0;function c(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(s(),{children:[(0,r.jsx)("title",{children:"AI DOKBrain - Educational Course Designer Based on <PERSON>'s Depth of Knowledge (DOK) framework | FunBlocks AI"}),(0,r.jsx)("meta",{name:"description",content:"AI DOKBrain helps educators and learners create comprehensive educational plans using <PERSON>'s Depth of Knowledge (DOK) framework. Generate structured mind maps that break down complex subjects into progressive learning journeys."}),(0,r.jsx)("meta",{name:"keywords",content:"DOK, Webb DOK, Depth of Knowledge framework, SOLO Taxonomy, educational tool, learning objectives, cognitive levels, AI education, AI tools, AI mind map, AI Brainstorming, teaching strategies, curriculum planning, instructional design, Structure of Observed Learning Outcomes"})]}),(0,r.jsx)(i.Z,{app:a.IF.dok})]})}},63214:function(e,n,t){"use strict";t(47426)}},function(e){e.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return e(e.s=35315)}),_N_E=e.O()}]);