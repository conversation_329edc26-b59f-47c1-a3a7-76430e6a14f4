(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2888],{48711:function(e,t,r){"use strict";r.d(t,{Z:function(){return U}});var n=function(){function e(e){var t=this;this._insertTag=function(e){var r;r=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,r),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){if(this.ctr%(this.isSpeedy?65e3:1)==0){var t;this._insertTag(((t=document.createElement("style")).setAttribute("data-emotion",this.key),void 0!==this.nonce&&t.setAttribute("nonce",this.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t))}var r=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(r);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else r.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach(function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),this.tags=[],this.ctr=0},e}(),i=Math.abs,o=String.fromCharCode,a=Object.assign;function s(e,t,r){return e.replace(t,r)}function l(e,t){return e.indexOf(t)}function u(e,t){return 0|e.charCodeAt(t)}function c(e,t,r){return e.slice(t,r)}function d(e){return e.length}function h(e,t){return t.push(e),e}var p=1,f=1,g=0,m=0,b=0,y="";function v(e,t,r,n,i,o,a){return{value:e,root:t,parent:r,type:n,props:i,children:o,line:p,column:f,length:a,return:""}}function x(e,t){return a(v("",null,null,"",null,null,0),e,{length:-e.length},t)}function k(){return b=m<g?u(y,m++):0,f++,10===b&&(f=1,p++),b}function S(){return u(y,m)}function w(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function C(e){return p=f=1,g=d(y=e),m=0,[]}function P(e){var t,r;return(t=m-1,r=function e(t){for(;k();)switch(b){case t:return m;case 34:case 39:34!==t&&39!==t&&e(b);break;case 40:41===t&&e(t);break;case 92:k()}return m}(91===e?e+2:40===e?e+1:e),c(y,t,r)).trim()}var _="-ms-",A="-moz-",j="-webkit-",E="comm",$="rule",T="decl",R="@keyframes";function z(e,t){for(var r="",n=e.length,i=0;i<n;i++)r+=t(e[i],i,e,t)||"";return r}function O(e,t,r,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case T:return e.return=e.return||e.value;case E:return"";case R:return e.return=e.value+"{"+z(e.children,n)+"}";case $:e.value=e.props.join(",")}return d(r=z(e.children,n))?e.return=e.value+"{"+r+"}":""}function L(e,t,r,n,o,a,l,u,d,h,p){for(var f=o-1,g=0===o?a:[""],m=g.length,b=0,y=0,x=0;b<n;++b)for(var k=0,S=c(e,f+1,f=i(y=l[b])),w=e;k<m;++k)(w=(y>0?g[k]+" "+S:s(S,/&\f/g,g[k])).trim())&&(d[x++]=w);return v(e,t,r,0===o?$:u,d,h,p)}function B(e,t,r,n){return v(e,t,r,T,c(e,0,n),c(e,n+1,-1),n)}var M=function(e,t,r){for(var n=0,i=0;n=i,i=S(),38===n&&12===i&&(t[r]=1),!w(i);)k();return c(y,e,m)},I=function(e,t){var r=-1,n=44;do switch(w(n)){case 0:38===n&&12===S()&&(t[r]=1),e[r]+=M(m-1,t,r);break;case 2:e[r]+=P(n);break;case 4:if(44===n){e[++r]=58===S()?"&\f":"",t[r]=e[r].length;break}default:e[r]+=o(n)}while(n=k());return e},D=function(e,t){var r;return r=I(C(e),t),y="",r},F=new WeakMap,V=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,r=e.parent,n=e.column===r.column&&e.line===r.line;"rule"!==r.type;)if(!(r=r.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||F.get(r))&&!n){F.set(e,!0);for(var i=[],o=D(t,i),a=r.props,s=0,l=0;s<o.length;s++)for(var u=0;u<a.length;u++,l++)e.props[l]=i[s]?o[s].replace(/&\f/g,a[u]):a[u]+" "+o[s]}}},N=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}},W=[function(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case T:e.return=function e(t,r){switch(45^u(t,0)?(((r<<2^u(t,0))<<2^u(t,1))<<2^u(t,2))<<2^u(t,3):0){case 5103:return j+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return j+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return j+t+A+t+_+t+t;case 6828:case 4268:return j+t+_+t+t;case 6165:return j+t+_+"flex-"+t+t;case 5187:return j+t+s(t,/(\w+).+(:[^]+)/,j+"box-$1$2"+_+"flex-$1$2")+t;case 5443:return j+t+_+"flex-item-"+s(t,/flex-|-self/,"")+t;case 4675:return j+t+_+"flex-line-pack"+s(t,/align-content|flex-|-self/,"")+t;case 5548:return j+t+_+s(t,"shrink","negative")+t;case 5292:return j+t+_+s(t,"basis","preferred-size")+t;case 6060:return j+"box-"+s(t,"-grow","")+j+t+_+s(t,"grow","positive")+t;case 4554:return j+s(t,/([^-])(transform)/g,"$1"+j+"$2")+t;case 6187:return s(s(s(t,/(zoom-|grab)/,j+"$1"),/(image-set)/,j+"$1"),t,"")+t;case 5495:case 3959:return s(t,/(image-set\([^]*)/,j+"$1$`$1");case 4968:return s(s(t,/(.+:)(flex-)?(.*)/,j+"box-pack:$3"+_+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+j+t+t;case 4095:case 3583:case 4068:case 2532:return s(t,/(.+)-inline(.+)/,j+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(d(t)-1-r>6)switch(u(t,r+1)){case 109:if(45!==u(t,r+4))break;case 102:return s(t,/(.+:)(.+)-([^]+)/,"$1"+j+"$2-$3$1"+A+(108==u(t,r+3)?"$3":"$2-$3"))+t;case 115:return~l(t,"stretch")?e(s(t,"stretch","fill-available"),r)+t:t}break;case 4949:if(115!==u(t,r+1))break;case 6444:switch(u(t,d(t)-3-(~l(t,"!important")&&10))){case 107:return s(t,":",":"+j)+t;case 101:return s(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+j+(45===u(t,14)?"inline-":"")+"box$3$1"+j+"$2$3$1"+_+"$2box$3")+t}break;case 5936:switch(u(t,r+11)){case 114:return j+t+_+s(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return j+t+_+s(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return j+t+_+s(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return j+t+_+t+t}return t}(e.value,e.length);break;case R:return z([x(e,{value:s(e.value,"@","@"+j)})],n);case $:if(e.length)return e.props.map(function(t){var r;switch(r=t,(r=/(::plac\w+|:read-\w+)/.exec(r))?r[0]:r){case":read-only":case":read-write":return z([x(e,{props:[s(t,/:(read-\w+)/,":"+A+"$1")]})],n);case"::placeholder":return z([x(e,{props:[s(t,/:(plac\w+)/,":"+j+"input-$1")]}),x(e,{props:[s(t,/:(plac\w+)/,":"+A+"$1")]}),x(e,{props:[s(t,/:(plac\w+)/,_+"input-$1")]})],n)}return""}).join("")}}],U=function(e){var t,r,i,a,g,x=e.key;if("css"===x){var _=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(_,function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))})}var A=e.stylisPlugins||W,j={},$=[];a=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+x+' "]'),function(e){for(var t=e.getAttribute("data-emotion").split(" "),r=1;r<t.length;r++)j[t[r]]=!0;$.push(e)});var T=(r=(t=[V,N].concat(A,[O,(i=function(e){g.insert(e)},function(e){!e.root&&(e=e.return)&&i(e)})])).length,function(e,n,i,o){for(var a="",s=0;s<r;s++)a+=t[s](e,n,i,o)||"";return a}),R=function(e){var t,r;return z((r=function e(t,r,n,i,a,g,x,C,_){for(var A,j=0,$=0,T=x,R=0,z=0,O=0,M=1,I=1,D=1,F=0,V="",N=a,W=g,U=i,H=V;I;)switch(O=F,F=k()){case 40:if(108!=O&&58==u(H,T-1)){-1!=l(H+=s(P(F),"&","&\f"),"&\f")&&(D=-1);break}case 34:case 39:case 91:H+=P(F);break;case 9:case 10:case 13:case 32:H+=function(e){for(;b=S();)if(b<33)k();else break;return w(e)>2||w(b)>3?"":" "}(O);break;case 92:H+=function(e,t){for(var r;--t&&k()&&!(b<48)&&!(b>102)&&(!(b>57)||!(b<65))&&(!(b>70)||!(b<97)););return r=m+(t<6&&32==S()&&32==k()),c(y,e,r)}(m-1,7);continue;case 47:switch(S()){case 42:case 47:h(v(A=function(e,t){for(;k();)if(e+b===57)break;else if(e+b===84&&47===S())break;return"/*"+c(y,t,m-1)+"*"+o(47===e?e:k())}(k(),m),r,n,E,o(b),c(A,2,-2),0),_);break;default:H+="/"}break;case 123*M:C[j++]=d(H)*D;case 125*M:case 59:case 0:switch(F){case 0:case 125:I=0;case 59+$:-1==D&&(H=s(H,/\f/g,"")),z>0&&d(H)-T&&h(z>32?B(H+";",i,n,T-1):B(s(H," ","")+";",i,n,T-2),_);break;case 59:H+=";";default:if(h(U=L(H,r,n,j,$,a,C,V,N=[],W=[],T),g),123===F){if(0===$)e(H,r,U,U,N,g,T,C,W);else switch(99===R&&110===u(H,3)?100:R){case 100:case 108:case 109:case 115:e(t,U,U,i&&h(L(t,U,U,0,0,a,C,V,a,N=[],T),W),a,W,T,C,i?N:W);break;default:e(H,U,U,U,[""],W,0,C,W)}}}j=$=z=0,M=D=1,V=H="",T=x;break;case 58:T=1+d(H),z=O;default:if(M<1){if(123==F)--M;else if(125==F&&0==M++&&125==(b=m>0?u(y,--m):0,f--,10===b&&(f=1,p--),b))continue}switch(H+=o(F),F*M){case 38:D=$>0?1:(H+="\f",-1);break;case 44:C[j++]=(d(H)-1)*D,D=1;break;case 64:45===S()&&(H+=P(k())),R=S(),$=T=d(V=H+=function(e){for(;!w(S());)k();return c(y,e,m)}(m)),F++;break;case 45:45===O&&2==d(H)&&(M=0)}}return g}("",null,null,null,[""],t=C(t=e),0,[0],t),y="",r),T)},M={key:x,sheet:new n({key:x,container:a,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:j,registered:{},insert:function(e,t,r,n){g=r,R(e?e+"{"+t.styles+"}":t.styles),n&&(M.inserted[t.name]=!0)}};return M.sheet.hydrate($),M}},45042:function(e,t,r){"use strict";function n(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}r.d(t,{Z:function(){return n}})},20442:function(e,t,r){"use strict";r.d(t,{E:function(){return x},T:function(){return p},_:function(){return d},a:function(){return g},c:function(){return y},h:function(){return m},w:function(){return h}});var n=r(67294),i=r(48711),o=r(87462),a=function(e){var t=new WeakMap;return function(r){if(t.has(r))return t.get(r);var n=e(r);return t.set(r,n),n}},s=r(70444),l=r(85662),u=r(27278),c=n.createContext("undefined"!=typeof HTMLElement?(0,i.Z)({key:"css"}):null);c.Provider;var d=function(){return(0,n.useContext)(c)},h=function(e){return(0,n.forwardRef)(function(t,r){return e(t,(0,n.useContext)(c),r)})},p=n.createContext({}),f=a(function(e){return a(function(t){return"function"==typeof t?t(e):(0,o.Z)({},e,t)})}),g=function(e){var t=n.useContext(p);return e.theme!==t&&(t=f(t)(e.theme)),n.createElement(p.Provider,{value:t},e.children)},m={}.hasOwnProperty,b="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",y=function(e,t){var r={};for(var n in t)m.call(t,n)&&(r[n]=t[n]);return r[b]=e,r},v=function(e){var t=e.cache,r=e.serialized,n=e.isStringTag;return(0,s.hC)(t,r,n),(0,u.L)(function(){return(0,s.My)(t,r,n)}),null},x=h(function(e,t,r){var i=e.css;"string"==typeof i&&void 0!==t.registered[i]&&(i=t.registered[i]);var o=e[b],a=[i],u="";"string"==typeof e.className?u=(0,s.fp)(t.registered,a,e.className):null!=e.className&&(u=e.className+" ");var c=(0,l.O)(a,void 0,n.useContext(p));u+=t.key+"-"+c.name;var d={};for(var h in e)m.call(e,h)&&"css"!==h&&h!==b&&(d[h]=e[h]);return d.className=u,r&&(d.ref=r),n.createElement(n.Fragment,null,n.createElement(v,{cache:t,serialized:c,isStringTag:"string"==typeof o}),n.createElement(o,d))})},70917:function(e,t,r){"use strict";r.d(t,{F4:function(){return p},iv:function(){return h},xB:function(){return d}});var n,i,o=r(20442),a=r(67294),s=r(70444),l=r(27278),u=r(85662);r(48711),r(8679);var c=function(e,t){var r=arguments;if(null==t||!o.h.call(t,"css"))return a.createElement.apply(void 0,r);var n=r.length,i=Array(n);i[0]=o.E,i[1]=(0,o.c)(e,t);for(var s=2;s<n;s++)i[s]=r[s];return a.createElement.apply(null,i)};n=c||(c={}),i||(i=n.JSX||(n.JSX={}));var d=(0,o.w)(function(e,t){var r=e.styles,n=(0,u.O)([r],void 0,a.useContext(o.T)),i=a.useRef();return(0,l.j)(function(){var e=t.key+"-global",r=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),o=!1,a=document.querySelector('style[data-emotion="'+e+" "+n.name+'"]');return t.sheet.tags.length&&(r.before=t.sheet.tags[0]),null!==a&&(o=!0,a.setAttribute("data-emotion",e),r.hydrate([a])),i.current=[r,o],function(){r.flush()}},[t]),(0,l.j)(function(){var e=i.current,r=e[0];if(e[1]){e[1]=!1;return}if(void 0!==n.next&&(0,s.My)(t,n.next,!0),r.tags.length){var o=r.tags[r.tags.length-1].nextElementSibling;r.before=o,r.flush()}t.insert("",n,r,!1)},[t,n.name]),null});function h(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,u.O)(t)}function p(){var e=h.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},85662:function(e,t,r){"use strict";r.d(t,{O:function(){return f}});var n,i={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},o=r(45042),a=/[A-Z]|^ms/g,s=/_EMO_([^_]+?)_([^]*?)_EMO_/g,l=function(e){return 45===e.charCodeAt(1)},u=function(e){return null!=e&&"boolean"!=typeof e},c=(0,o.Z)(function(e){return l(e)?e:e.replace(a,"-$&").toLowerCase()}),d=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(s,function(e,t,r){return n={name:t,styles:r,next:n},t})}return 1===i[e]||l(e)||"number"!=typeof t||0===t?t:t+"px"};function h(e,t,r){if(null==r)return"";if(void 0!==r.__emotion_styles)return r;switch(typeof r){case"boolean":return"";case"object":if(1===r.anim)return n={name:r.name,styles:r.styles,next:n},r.name;if(void 0!==r.styles){var i=r.next;if(void 0!==i)for(;void 0!==i;)n={name:i.name,styles:i.styles,next:n},i=i.next;return r.styles+";"}return function(e,t,r){var n="";if(Array.isArray(r))for(var i=0;i<r.length;i++)n+=h(e,t,r[i])+";";else for(var o in r){var a=r[o];if("object"!=typeof a)null!=t&&void 0!==t[a]?n+=o+"{"+t[a]+"}":u(a)&&(n+=c(o)+":"+d(o,a)+";");else if(Array.isArray(a)&&"string"==typeof a[0]&&(null==t||void 0===t[a[0]]))for(var s=0;s<a.length;s++)u(a[s])&&(n+=c(o)+":"+d(o,a[s])+";");else{var l=h(e,t,a);switch(o){case"animation":case"animationName":n+=c(o)+":"+l+";";break;default:n+=o+"{"+l+"}"}}}return n}(e,t,r);case"function":if(void 0!==e){var o=n,a=r(e);return n=o,h(e,t,a)}}if(null==t)return r;var s=t[r];return void 0!==s?s:r}var p=/label:\s*([^\s;{]+)\s*(;|$)/g;function f(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var i,o=!0,a="";n=void 0;var s=e[0];null==s||void 0===s.raw?(o=!1,a+=h(r,t,s)):a+=s[0];for(var l=1;l<e.length;l++)a+=h(r,t,e[l]),o&&(a+=s[l]);p.lastIndex=0;for(var u="";null!==(i=p.exec(a));)u+="-"+i[1];return{name:function(e){for(var t,r=0,n=0,i=e.length;i>=4;++n,i-=4)t=(65535&(t=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))*1540483477+((t>>>16)*59797<<16),t^=t>>>24,r=(65535&t)*1540483477+((t>>>16)*59797<<16)^(65535&r)*1540483477+((r>>>16)*59797<<16);switch(i){case 3:r^=(255&e.charCodeAt(n+2))<<16;case 2:r^=(255&e.charCodeAt(n+1))<<8;case 1:r^=255&e.charCodeAt(n),r=(65535&r)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,(((r=(65535&r)*1540483477+((r>>>16)*59797<<16))^r>>>15)>>>0).toString(36)}(a)+u,styles:a,next:n}}},16829:function(e,t,r){"use strict";r.d(t,{Z:function(){return m}});var n=r(87462),i=r(20442),o=r(85662),a=r(27278),s=r(70444),l=r(67294),u=r(45042),c=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,d=(0,u.Z)(function(e){return c.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&91>e.charCodeAt(2)}),h=function(e){return"theme"!==e},p=function(e){return"string"==typeof e&&e.charCodeAt(0)>96?d:h},f=function(e,t,r){var n;if(t){var i=t.shouldForwardProp;n=e.__emotion_forwardProp&&i?function(t){return e.__emotion_forwardProp(t)&&i(t)}:i}return"function"!=typeof n&&r&&(n=e.__emotion_forwardProp),n},g=function(e){var t=e.cache,r=e.serialized,n=e.isStringTag;return(0,s.hC)(t,r,n),(0,a.L)(function(){return(0,s.My)(t,r,n)}),null},m=(function e(t,r){var a,u,c=t.__emotion_real===t,d=c&&t.__emotion_base||t;void 0!==r&&(a=r.label,u=r.target);var h=f(t,r,c),m=h||p(d),b=!m("as");return function(){var y=arguments,v=c&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==a&&v.push("label:"+a+";"),null==y[0]||void 0===y[0].raw)v.push.apply(v,y);else{var x=y[0];v.push(x[0]);for(var k=y.length,S=1;S<k;S++)v.push(y[S],x[S])}var w=(0,i.w)(function(e,t,r){var n=b&&e.as||d,a="",c=[],f=e;if(null==e.theme){for(var y in f={},e)f[y]=e[y];f.theme=l.useContext(i.T)}"string"==typeof e.className?a=(0,s.fp)(t.registered,c,e.className):null!=e.className&&(a=e.className+" ");var x=(0,o.O)(v.concat(c),t.registered,f);a+=t.key+"-"+x.name,void 0!==u&&(a+=" "+u);var k=b&&void 0===h?p(n):m,S={};for(var w in e)(!b||"as"!==w)&&k(w)&&(S[w]=e[w]);return S.className=a,r&&(S.ref=r),l.createElement(l.Fragment,null,l.createElement(g,{cache:t,serialized:x,isStringTag:"string"==typeof n}),l.createElement(n,S))});return w.displayName=void 0!==a?a:"Styled("+("string"==typeof d?d:d.displayName||d.name||"Component")+")",w.defaultProps=t.defaultProps,w.__emotion_real=w,w.__emotion_base=d,w.__emotion_styles=v,w.__emotion_forwardProp=h,Object.defineProperty(w,"toString",{value:function(){return"."+u}}),w.withComponent=function(t,i){return e(t,(0,n.Z)({},r,i,{shouldForwardProp:f(w,i,!0)})).apply(void 0,v)},w}}).bind(null);["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach(function(e){m[e]=m(e)})},27278:function(e,t,r){"use strict";r.d(t,{L:function(){return a},j:function(){return s}});var n,i=r(67294),o=!!(n||(n=r.t(i,2))).useInsertionEffect&&(n||(n=r.t(i,2))).useInsertionEffect,a=o||function(e){return e()},s=o||i.useLayoutEffect},70444:function(e,t,r){"use strict";function n(e,t,r){var n="";return r.split(" ").forEach(function(r){void 0!==e[r]?t.push(e[r]+";"):r&&(n+=r+" ")}),n}r.d(t,{My:function(){return o},fp:function(){return n},hC:function(){return i}});var i=function(e,t,r){var n=e.key+"-"+t.name;!1===r&&void 0===e.registered[n]&&(e.registered[n]=t.styles)},o=function(e,t,r){i(e,t,r);var n=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var o=t;do e.insert(t===o?"."+n:"",o,e.sheet,!0),o=o.next;while(void 0!==o)}}},8679:function(e,t,r){"use strict";var n=r(21296),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return n.isMemo(e)?a:s[e.$$typeof]||i}s[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[n.Memo]=a;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,h=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,f=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(f){var i=p(r);i&&i!==f&&e(t,i,n)}var a=c(r);d&&(a=a.concat(d(r)));for(var s=l(t),g=l(r),m=0;m<a.length;++m){var b=a[m];if(!o[b]&&!(n&&n[b])&&!(g&&g[b])&&!(s&&s[b])){var y=h(r,b);try{u(t,b,y)}catch(e){}}}}return t}},96103:function(e,t){"use strict";/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,s=r?Symbol.for("react.profiler"):60114,l=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,c=r?Symbol.for("react.async_mode"):60111,d=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,f=r?Symbol.for("react.suspense_list"):60120,g=r?Symbol.for("react.memo"):60115,m=r?Symbol.for("react.lazy"):60116,b=r?Symbol.for("react.block"):60121,y=r?Symbol.for("react.fundamental"):60117,v=r?Symbol.for("react.responder"):60118,x=r?Symbol.for("react.scope"):60119;function k(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case c:case d:case o:case s:case a:case p:return e;default:switch(e=e&&e.$$typeof){case u:case h:case m:case g:case l:return e;default:return t}}case i:return t}}}function S(e){return k(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=l,t.Element=n,t.ForwardRef=h,t.Fragment=o,t.Lazy=m,t.Memo=g,t.Portal=i,t.Profiler=s,t.StrictMode=a,t.Suspense=p,t.isAsyncMode=function(e){return S(e)||k(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return k(e)===u},t.isContextProvider=function(e){return k(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return k(e)===h},t.isFragment=function(e){return k(e)===o},t.isLazy=function(e){return k(e)===m},t.isMemo=function(e){return k(e)===g},t.isPortal=function(e){return k(e)===i},t.isProfiler=function(e){return k(e)===s},t.isStrictMode=function(e){return k(e)===a},t.isSuspense=function(e){return k(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===d||e===s||e===a||e===p||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===g||e.$$typeof===l||e.$$typeof===u||e.$$typeof===h||e.$$typeof===y||e.$$typeof===v||e.$$typeof===x||e.$$typeof===b)},t.typeOf=k},21296:function(e,t,r){"use strict";e.exports=r(96103)},38554:function(e,t,r){e=r.nmd(e);var n,i,o,a,s,l,u,c,d,h,p="__lodash_hash_undefined__",f="[object Arguments]",g="[object Function]",m="[object Object]",b=/^\[object .+?Constructor\]$/,y=/^(?:0|[1-9]\d*)$/,v={};v["[object Float32Array]"]=v["[object Float64Array]"]=v["[object Int8Array]"]=v["[object Int16Array]"]=v["[object Int32Array]"]=v["[object Uint8Array]"]=v["[object Uint8ClampedArray]"]=v["[object Uint16Array]"]=v["[object Uint32Array]"]=!0,v[f]=v["[object Array]"]=v["[object ArrayBuffer]"]=v["[object Boolean]"]=v["[object DataView]"]=v["[object Date]"]=v["[object Error]"]=v[g]=v["[object Map]"]=v["[object Number]"]=v[m]=v["[object RegExp]"]=v["[object Set]"]=v["[object String]"]=v["[object WeakMap]"]=!1;var x="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,k="object"==typeof self&&self&&self.Object===Object&&self,S=x||k||Function("return this")(),w=t&&!t.nodeType&&t,C=w&&e&&!e.nodeType&&e,P=C&&C.exports===w,_=P&&x.process,A=function(){try{var e=C&&C.require&&C.require("util").types;if(e)return e;return _&&_.binding&&_.binding("util")}catch(e){}}(),j=A&&A.isTypedArray,E=Array.prototype,$=Function.prototype,T=Object.prototype,R=S["__core-js_shared__"],z=$.toString,O=T.hasOwnProperty,L=(l=/[^.]+$/.exec(R&&R.keys&&R.keys.IE_PROTO||""))?"Symbol(src)_1."+l:"",B=T.toString,M=z.call(Object),I=RegExp("^"+z.call(O).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),D=P?S.Buffer:void 0,F=S.Symbol,V=S.Uint8Array,N=D?D.allocUnsafe:void 0,W=(u=Object.getPrototypeOf,c=Object,function(e){return u(c(e))}),U=Object.create,H=T.propertyIsEnumerable,J=E.splice,q=F?F.toStringTag:void 0,Z=function(){try{var e=eh(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),G=D?D.isBuffer:void 0,Y=Math.max,K=Date.now,X=eh(S,"Map"),Q=eh(Object,"create"),ee=function(){function e(){}return function(t){if(!eC(t))return{};if(U)return U(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function et(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function er(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function en(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function ei(e){var t=this.__data__=new er(e);this.size=t.size}function eo(e,t,r){(void 0===r||eb(e[t],r))&&(void 0!==r||t in e)||es(e,t,r)}function ea(e,t){for(var r=e.length;r--;)if(eb(e[r][0],t))return r;return -1}function es(e,t,r){"__proto__"==t&&Z?Z(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}et.prototype.clear=function(){this.__data__=Q?Q(null):{},this.size=0},et.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},et.prototype.get=function(e){var t=this.__data__;if(Q){var r=t[e];return r===p?void 0:r}return O.call(t,e)?t[e]:void 0},et.prototype.has=function(e){var t=this.__data__;return Q?void 0!==t[e]:O.call(t,e)},et.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Q&&void 0===t?p:t,this},er.prototype.clear=function(){this.__data__=[],this.size=0},er.prototype.delete=function(e){var t=this.__data__,r=ea(t,e);return!(r<0)&&(r==t.length-1?t.pop():J.call(t,r,1),--this.size,!0)},er.prototype.get=function(e){var t=this.__data__,r=ea(t,e);return r<0?void 0:t[r][1]},er.prototype.has=function(e){return ea(this.__data__,e)>-1},er.prototype.set=function(e,t){var r=this.__data__,n=ea(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this},en.prototype.clear=function(){this.size=0,this.__data__={hash:new et,map:new(X||er),string:new et}},en.prototype.delete=function(e){var t=ed(this,e).delete(e);return this.size-=t?1:0,t},en.prototype.get=function(e){return ed(this,e).get(e)},en.prototype.has=function(e){return ed(this,e).has(e)},en.prototype.set=function(e,t){var r=ed(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this},ei.prototype.clear=function(){this.__data__=new er,this.size=0},ei.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},ei.prototype.get=function(e){return this.__data__.get(e)},ei.prototype.has=function(e){return this.__data__.has(e)},ei.prototype.set=function(e,t){var r=this.__data__;if(r instanceof er){var n=r.__data__;if(!X||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new en(n)}return r.set(e,t),this.size=r.size,this};var el=function(e,t,r){for(var n=-1,i=Object(e),o=r(e),a=o.length;a--;){var s=o[++n];if(!1===t(i[s],s,i))break}return e};function eu(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":q&&q in Object(e)?function(e){var t=O.call(e,q),r=e[q];try{e[q]=void 0;var n=!0}catch(e){}var i=B.call(e);return n&&(t?e[q]=r:delete e[q]),i}(e):B.call(e)}function ec(e){return eP(e)&&eu(e)==f}function ed(e,t){var r,n=e.__data__;return("string"==(r=typeof t)||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==t:null===t)?n["string"==typeof t?"string":"hash"]:n.map}function eh(e,t){var r=null==e?void 0:e[t];return!(!eC(r)||L&&L in r)&&(eS(r)?I:b).test(function(e){if(null!=e){try{return z.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(r))?r:void 0}function ep(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&y.test(e))&&e>-1&&e%1==0&&e<t}function ef(e){var t=e&&e.constructor,r="function"==typeof t&&t.prototype||T;return e===r}function eg(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var em=(n=Z?function(e,t){return Z(e,"toString",{configurable:!0,enumerable:!1,value:function(){return t},writable:!0})}:eE,i=0,o=0,function(){var e=K(),t=16-(e-o);if(o=e,t>0){if(++i>=800)return arguments[0]}else i=0;return n.apply(void 0,arguments)});function eb(e,t){return e===t||e!=e&&t!=t}var ey=ec(function(){return arguments}())?ec:function(e){return eP(e)&&O.call(e,"callee")&&!H.call(e,"callee")},ev=Array.isArray;function ex(e){return null!=e&&ew(e.length)&&!eS(e)}var ek=G||function(){return!1};function eS(e){if(!eC(e))return!1;var t=eu(e);return t==g||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function ew(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function eC(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function eP(e){return null!=e&&"object"==typeof e}var e_=j?function(e){return j(e)}:function(e){return eP(e)&&ew(e.length)&&!!v[eu(e)]};function eA(e){return ex(e)?function(e,t){var r=ev(e),n=!r&&ey(e),i=!r&&!n&&ek(e),o=!r&&!n&&!i&&e_(e),a=r||n||i||o,s=a?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],l=s.length;for(var u in e)(t||O.call(e,u))&&!(a&&("length"==u||i&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||ep(u,l)))&&s.push(u);return s}(e,!0):function(e){if(!eC(e))return function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}(e);var t=ef(e),r=[];for(var n in e)"constructor"==n&&(t||!O.call(e,n))||r.push(n);return r}(e)}var ej=(d=function(e,t,r,n){!function e(t,r,n,i,o){t!==r&&el(r,function(a,s){if(o||(o=new ei),eC(a))(function(e,t,r,n,i,o,a){var s=eg(e,r),l=eg(t,r),u=a.get(l);if(u){eo(e,r,u);return}var c=o?o(s,l,r+"",e,t,a):void 0,d=void 0===c;if(d){var h,p,f=ev(l),g=!f&&ek(l),b=!f&&!g&&e_(l);c=l,f||g||b?ev(s)?c=s:eP(s)&&ex(s)?c=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}(s):g?(d=!1,c=function(e,t){if(t)return e.slice();var r=e.length,n=N?N(r):new e.constructor(r);return e.copy(n),n}(l,!0)):b?(d=!1,p=new(h=l.buffer).constructor(h.byteLength),new V(p).set(new V(h)),c=new l.constructor(p,l.byteOffset,l.length)):c=[]:function(e){if(!eP(e)||eu(e)!=m)return!1;var t=W(e);if(null===t)return!0;var r=O.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&z.call(r)==M}(l)||ey(l)?(c=s,ey(s)?c=function(e,t,r,n){var i=!r;r||(r={});for(var o=-1,a=t.length;++o<a;){var s=t[o],l=n?n(r[s],e[s],s,r,e):void 0;void 0===l&&(l=e[s]),i?es(r,s,l):function(e,t,r){var n=e[t];O.call(e,t)&&eb(n,r)&&(void 0!==r||t in e)||es(e,t,r)}(r,s,l)}return r}(s,eA(s)):(!eC(s)||eS(s))&&(c="function"!=typeof l.constructor||ef(l)?{}:ee(W(l)))):d=!1}d&&(a.set(l,c),i(c,l,n,o,a),a.delete(l)),eo(e,r,c)})(t,r,s,n,e,i,o);else{var l=i?i(eg(t,s),a,s+"",t,r,o):void 0;void 0===l&&(l=a),eo(t,s,l)}},eA)}(e,t,r,n)},em((a=h=function(e,t){var r=-1,n=t.length,i=n>1?t[n-1]:void 0,o=n>2?t[2]:void 0;for(i=d.length>3&&"function"==typeof i?(n--,i):void 0,o&&function(e,t,r){if(!eC(r))return!1;var n=typeof t;return("number"==n?!!(ex(r)&&ep(t,r.length)):"string"==n&&(t in r))&&eb(r[t],e)}(t[0],t[1],o)&&(i=n<3?void 0:i,n=1),e=Object(e);++r<n;){var a=t[r];a&&d(e,a,r,i)}return e},s=Y((s=void 0,a.length-1),0),function(){for(var e=arguments,t=-1,r=Y(e.length-s,0),n=Array(r);++t<r;)n[t]=e[s+t];t=-1;for(var i=Array(s+1);++t<s;)i[t]=e[t];return i[s]=eE(n),function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}(a,this,i)}),h+""));function eE(e){return e}e.exports=ej},31837:function(e,t,r){"use strict";r.d(t,{Jc:function(){return eP},$G:function(){return n.$G}});var n=r(67421),i=r(87462),o=r(86656),a=r(67294),s=r(8679),l=r.n(s);function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function c(e,t){if(e){if("string"==typeof e)return u(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}var d=r(71002);function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],l=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,i=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||c(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var p=r(45987),f={defaultNS:"common",errorStackTraceLimit:0,i18n:{defaultLocale:"en",locales:["en"]},get initImmediate(){return"undefined"!=typeof window},get initAsync(){return"undefined"!=typeof window},interpolation:{escapeValue:!1},load:"currentOnly",localeExtension:"json",localePath:"./public/locales",localeStructure:"{{lng}}/{{ns}}",react:{useSuspense:!1},reloadOnPrerender:!1,serializeConfig:!0,use:[]},g="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,m=["i18n"],b=["i18n"];function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){(0,o.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var x=["backend","detection"],k=function(e){if("string"!=typeof(null==e?void 0:e.lng))throw Error("config.lng was not passed into createConfig");var t,r,n,i=e.i18n,o=(0,p.Z)(e,m),a=f.i18n,s=v(v(v(v({},(0,p.Z)(f,b)),o),a),i),l=s.defaultNS,g=s.lng,y=s.localeExtension,k=s.localePath,S=s.nonExplicitSupportedLngs,w=s.locales.filter(function(e){return"default"!==e});if("cimode"===g)return s;if(void 0===s.fallbackLng&&(s.fallbackLng=s.defaultLocale,"default"===s.fallbackLng)){var C=h(w,1);s.fallbackLng=C[0]}var P=null==e||null===(t=e.interpolation)||void 0===t?void 0:t.prefix,_=null==e||null===(r=e.interpolation)||void 0===r?void 0:r.suffix,A=null!=P?P:"{{",j=null!=_?_:"}}";"string"!=typeof(null==e?void 0:e.localeStructure)&&(P||_)&&(s.localeStructure="".concat(A,"lng").concat(j,"/").concat(A,"ns").concat(j));var E=s.fallbackLng,$=s.localeStructure;if(S){var T=function(e,t){var r=h(t.split("-"),1)[0];return e[t]=[r],e};if("string"==typeof E)s.fallbackLng=s.locales.filter(function(e){return e.includes("-")}).reduce(T,{default:[E]});else if(Array.isArray(E))s.fallbackLng=s.locales.filter(function(e){return e.includes("-")}).reduce(T,{default:E});else if("object"===(0,d.Z)(E))s.fallbackLng=Object.entries(s.fallbackLng).reduce(function(e,t){var r,n=h(t,2),i=n[0],o=n[1];return e[i]=i.includes("-")?(r=[i.split("-")[0]].concat(function(e){if(Array.isArray(e))return u(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||c(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),Array.from(new Set(r))):o,e},E);else if("function"==typeof E)throw Error("If nonExplicitSupportedLngs is true, no functions are allowed for fallbackLng")}return(null==e||null===(n=e.use)||void 0===n?void 0:n.some(function(e){return"backend"===e.type}))||("string"==typeof k?s.backend={addPath:"".concat(k,"/").concat($,".missing.").concat(y),loadPath:"".concat(k,"/").concat($,".").concat(y)}:"function"!=typeof k||(s.backend={addPath:function(e,t){return k(e,t,!0)},loadPath:function(e,t){return k(e,t,!1)}})),"string"==typeof s.ns||Array.isArray(s.ns)||(s.ns=[l]),x.forEach(function(t){e[t]&&(s[t]=v(v({},s[t]),e[t]))}),s};let S=e=>"string"==typeof e,w=()=>{let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.resolve=e,r.reject=t,r},C=e=>null==e?"":""+e,P=(e,t,r)=>{e.forEach(e=>{t[e]&&(r[e]=t[e])})},_=/###/g,A=e=>e&&e.indexOf("###")>-1?e.replace(_,"."):e,j=e=>!e||S(e),E=(e,t,r)=>{let n=S(t)?t.split("."):t,i=0;for(;i<n.length-1;){if(j(e))return{};let t=A(n[i]);!e[t]&&r&&(e[t]=new r),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++i}return j(e)?{}:{obj:e,k:A(n[i])}},$=(e,t,r)=>{let{obj:n,k:i}=E(e,t,Object);if(void 0!==n||1===t.length){n[i]=r;return}let o=t[t.length-1],a=t.slice(0,t.length-1),s=E(e,a,Object);for(;void 0===s.obj&&a.length;)o=`${a[a.length-1]}.${o}`,(s=E(e,a=a.slice(0,a.length-1),Object))&&s.obj&&void 0!==s.obj[`${s.k}.${o}`]&&(s.obj=void 0);s.obj[`${s.k}.${o}`]=r},T=(e,t,r,n)=>{let{obj:i,k:o}=E(e,t,Object);i[o]=i[o]||[],i[o].push(r)},R=(e,t)=>{let{obj:r,k:n}=E(e,t);if(r)return r[n]},z=(e,t,r)=>{let n=R(e,r);return void 0!==n?n:R(t,r)},O=(e,t,r)=>{for(let n in t)"__proto__"!==n&&"constructor"!==n&&(n in e?S(e[n])||e[n]instanceof String||S(t[n])||t[n]instanceof String?r&&(e[n]=t[n]):O(e[n],t[n],r):e[n]=t[n]);return e},L=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var B={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let M=e=>S(e)?e.replace(/[&<>"'\/]/g,e=>B[e]):e,I=[" ",",","?","!",";"],D=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let r=RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}(20),F=(e,t,r)=>{t=t||"",r=r||"";let n=I.filter(e=>0>t.indexOf(e)&&0>r.indexOf(e));if(0===n.length)return!0;let i=D.getRegExp(`(${n.map(e=>"?"===e?"\\?":e).join("|")})`),o=!i.test(e);if(!o){let t=e.indexOf(r);t>0&&!i.test(e.substring(0,t))&&(o=!0)}return o},V=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];let n=t.split(r),i=e;for(let e=0;e<n.length;){let t;if(!i||"object"!=typeof i)return;let o="";for(let a=e;a<n.length;++a)if(a!==e&&(o+=r),o+=n[a],void 0!==(t=i[o])){if(["string","number","boolean"].indexOf(typeof t)>-1&&a<n.length-1)continue;e+=a-e+1;break}i=t}return i},N=e=>e&&e.replace("_","-"),W={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class U{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||W,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,n){return n&&!this.debug?null:(S(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new U(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new U(this.logger,e)}}var H=new U;class J{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let r=this.observers[e].get(t)||0;this.observers[e].set(t,r+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];if(this.observers[e]){let t=Array.from(this.observers[e].entries());t.forEach(e=>{let[t,n]=e;for(let e=0;e<n;e++)t(...r)})}if(this.observers["*"]){let t=Array.from(this.observers["*"].entries());t.forEach(t=>{let[n,i]=t;for(let t=0;t<i;t++)n.apply(n,[e,...r])})}}}class q extends J{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,r){let n,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=void 0!==i.keySeparator?i.keySeparator:this.options.keySeparator,a=void 0!==i.ignoreJSONStructure?i.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?n=e.split("."):(n=[e,t],r&&(Array.isArray(r)?n.push(...r):S(r)&&o?n.push(...r.split(o)):n.push(r)));let s=R(this.data,n);return(!s&&!t&&!r&&e.indexOf(".")>-1&&(e=n[0],t=n[1],r=n.slice(2).join(".")),!s&&a&&S(r))?V(this.data&&this.data[e]&&this.data[e][t],r,o):s}addResource(e,t,r,n){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},o=void 0!==i.keySeparator?i.keySeparator:this.options.keySeparator,a=[e,t];r&&(a=a.concat(o?r.split(o):r)),e.indexOf(".")>-1&&(a=e.split("."),n=t,t=a[1]),this.addNamespaces(t),$(this.data,a,n),i.silent||this.emit("added",e,t,r,n)}addResources(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let n in r)(S(r[n])||Array.isArray(r[n]))&&this.addResource(e,t,n,r[n],{silent:!0});n.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,n,i){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),n=r,r=t,t=a[1]),this.addNamespaces(t);let s=R(this.data,a)||{};o.skipCopy||(r=JSON.parse(JSON.stringify(r))),n?O(s,r,i):s={...s,...r},$(this.data,a,s),o.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return(t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI)?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e),r=t&&Object.keys(t)||[];return!!r.find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var Z={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,r,n,i){return e.forEach(e=>{this.processors[e]&&(t=this.processors[e].process(t,r,n,i))}),t}};let G={};class Y extends J{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),P(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=H.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let r=this.resolve(e,t);return r&&void 0!==r.res}extractFromKey(e,t){let r=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===r&&(r=":");let n=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,i=t.ns||this.options.defaultNS||[],o=r&&e.indexOf(r)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!F(e,r,n);if(o&&!a){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:S(i)?[i]:i};let o=e.split(r);(r!==n||r===n&&this.options.ns.indexOf(o[0])>-1)&&(i=o.shift()),e=o.join(n)}return{key:e,namespaces:S(i)?[i]:i}}translate(e,t,r){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let n=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,i=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:o,namespaces:a}=this.extractFromKey(e[e.length-1],t),s=a[a.length-1],l=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(l&&"cimode"===l.toLowerCase()){if(u){let e=t.nsSeparator||this.options.nsSeparator;return n?{res:`${s}${e}${o}`,usedKey:o,exactUsedKey:o,usedLng:l,usedNS:s,usedParams:this.getUsedParamsDetails(t)}:`${s}${e}${o}`}return n?{res:o,usedKey:o,exactUsedKey:o,usedLng:l,usedNS:s,usedParams:this.getUsedParamsDetails(t)}:o}let c=this.resolve(e,t),d=c&&c.res,h=c&&c.usedKey||o,p=c&&c.exactUsedKey||o,f=Object.prototype.toString.apply(d),g=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,m=!this.i18nFormat||this.i18nFormat.handleAsObject,b=!S(d)&&"boolean"!=typeof d&&"number"!=typeof d;if(m&&d&&b&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(f)&&!(S(g)&&Array.isArray(d))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(h,d,{...t,ns:a}):`key '${o} (${this.language})' returned an object instead of string.`;return n?(c.res=e,c.usedParams=this.getUsedParamsDetails(t),c):e}if(i){let e=Array.isArray(d),r=e?[]:{},n=e?p:h;for(let e in d)if(Object.prototype.hasOwnProperty.call(d,e)){let o=`${n}${i}${e}`;r[e]=this.translate(o,{...t,joinArrays:!1,ns:a}),r[e]===o&&(r[e]=d[e])}d=r}}else if(m&&S(g)&&Array.isArray(d))(d=d.join(g))&&(d=this.extendTranslation(d,e,t,r));else{let n=!1,a=!1,u=void 0!==t.count&&!S(t.count),h=Y.hasDefaultValue(t),p=u?this.pluralResolver.getSuffix(l,t.count,t):"",f=t.ordinal&&u?this.pluralResolver.getSuffix(l,t.count,{ordinal:!1}):"",g=u&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),m=g&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${p}`]||t[`defaultValue${f}`]||t.defaultValue;!this.isValidLookup(d)&&h&&(n=!0,d=m),this.isValidLookup(d)||(a=!0,d=o);let b=t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey,y=b&&a?void 0:d,v=h&&m!==d&&this.options.updateMissing;if(a||n||v){if(this.logger.log(v?"updateKey":"missingKey",l,s,o,v?m:d),i){let e=this.resolve(o,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],r=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&r&&r[0])for(let t=0;t<r.length;t++)e.push(r[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let n=(e,r,n)=>{let i=h&&n!==d?n:y;this.options.missingKeyHandler?this.options.missingKeyHandler(e,s,r,i,v,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,s,r,i,v,t),this.emit("missingKey",e,s,r,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&u?e.forEach(e=>{let r=this.pluralResolver.getSuffixes(e,t);g&&t[`defaultValue${this.options.pluralSeparator}zero`]&&0>r.indexOf(`${this.options.pluralSeparator}zero`)&&r.push(`${this.options.pluralSeparator}zero`),r.forEach(r=>{n([e],o+r,t[`defaultValue${r}`]||m)})}):n(e,o,m))}d=this.extendTranslation(d,e,t,c,r),a&&d===o&&this.options.appendNamespaceToMissingKey&&(d=`${s}:${o}`),(a||n)&&this.options.parseMissingKeyHandler&&(d="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${s}:${o}`:o,n?d:void 0):this.options.parseMissingKeyHandler(d))}return n?(c.res=d,c.usedParams=this.getUsedParamsDetails(t),c):d}extendTranslation(e,t,r,n,i){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!r.skipInterpolation){let a;r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});let s=S(e)&&(r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(s){let t=e.match(this.interpolator.nestingRegexp);a=t&&t.length}let l=r.replace&&!S(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(l={...this.options.interpolation.defaultVariables,...l}),e=this.interpolator.interpolate(e,l,r.lng||this.language||n.usedLng,r),s){let t=e.match(this.interpolator.nestingRegexp),n=t&&t.length;a<n&&(r.nest=!1)}!r.lng&&"v1"!==this.options.compatibilityAPI&&n&&n.res&&(r.lng=this.language||n.usedLng),!1!==r.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];return i&&i[0]===n[0]&&!r.context?(o.logger.warn(`It seems you are nesting recursively key: ${n[0]} in key: ${t[0]}`),null):o.translate(...n,t)},r)),r.interpolation&&this.interpolator.reset()}let a=r.postProcess||this.options.postProcess,s=S(a)?[a]:a;return null!=e&&s&&s.length&&!1!==r.applyPostProcessor&&(e=Z.handle(s,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e){let t,r,n,i,o,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return S(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let s=this.extractFromKey(e,a),l=s.key;r=l;let u=s.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));let c=void 0!==a.count&&!S(a.count),d=c&&!a.ordinal&&0===a.count&&this.pluralResolver.shouldUseIntlApi(),h=void 0!==a.context&&(S(a.context)||"number"==typeof a.context)&&""!==a.context,p=a.lngs?a.lngs:this.languageUtils.toResolveHierarchy(a.lng||this.language,a.fallbackLng);u.forEach(e=>{this.isValidLookup(t)||(o=e,!G[`${p[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(G[`${p[0]}-${e}`]=!0,this.logger.warn(`key "${r}" for languages "${p.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(r=>{let o;if(this.isValidLookup(t))return;i=r;let s=[l];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(s,l,r,e,a);else{let e;c&&(e=this.pluralResolver.getSuffix(r,a.count,a));let t=`${this.options.pluralSeparator}zero`,n=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(s.push(l+e),a.ordinal&&0===e.indexOf(n)&&s.push(l+e.replace(n,this.options.pluralSeparator)),d&&s.push(l+t)),h){let r=`${l}${this.options.contextSeparator}${a.context}`;s.push(r),c&&(s.push(r+e),a.ordinal&&0===e.indexOf(n)&&s.push(r+e.replace(n,this.options.pluralSeparator)),d&&s.push(r+t))}}for(;o=s.pop();)this.isValidLookup(t)||(n=o,t=this.getResource(r,e,o,a))}))})}),{res:t,usedKey:r,exactUsedKey:n,usedLng:i,usedNS:o}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,r,n):this.resourceStore.getResource(e,t,r,n)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!S(e.replace),r=t?e.replace:e;if(t&&void 0!==e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!t)for(let e of(r={...r},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete r[e];return r}static hasDefaultValue(e){let t="defaultValue";for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&void 0!==e[r])return!0;return!1}}let K=e=>e.charAt(0).toUpperCase()+e.slice(1);class X{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=H.create("languageUtils")}getScriptPartFromCode(e){if(!(e=N(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=N(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(S(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(e){}let t=["hans","hant","latn","cyrl","cans","mong","arab"],r=e.split("-");return this.options.lowerCaseLng?r=r.map(e=>e.toLowerCase()):2===r.length?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),t.indexOf(r[1].toLowerCase())>-1&&(r[1]=K(r[1].toLowerCase()))):3===r.length&&(r[0]=r[0].toLowerCase(),2===r[1].length&&(r[1]=r[1].toUpperCase()),"sgn"!==r[0]&&2===r[2].length&&(r[2]=r[2].toUpperCase()),t.indexOf(r[1].toLowerCase())>-1&&(r[1]=K(r[1].toLowerCase())),t.indexOf(r[2].toLowerCase())>-1&&(r[2]=K(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let r=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(r))&&(t=r)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return t=r;t=this.options.supportedLngs.find(e=>{if(e===r||!(0>e.indexOf("-")&&0>r.indexOf("-"))&&(e.indexOf("-")>0&&0>r.indexOf("-")&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&r.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),S(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return r||(r=e[this.getScriptPartFromCode(t)]),r||(r=e[this.formatLanguageCode(t)]),r||(r=e[this.getLanguagePartFromCode(t)]),r||(r=e.default),r||[]}toResolveHierarchy(e,t){let r=this.getFallbackCodes(t||this.options.fallbackLng||[],e),n=[],i=e=>{e&&(this.isSupportedCode(e)?n.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return S(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&i(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&i(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&i(this.getLanguagePartFromCode(e))):S(e)&&i(this.formatLanguageCode(e)),r.forEach(e=>{0>n.indexOf(e)&&i(this.formatLanguageCode(e))}),n}}let Q=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],ee={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)},et=["v1","v2","v3"],er=["v4"],en={zero:0,one:1,two:2,few:3,many:4,other:5},ei=()=>{let e={};return Q.forEach(t=>{t.lngs.forEach(r=>{e[r]={numbers:t.nr,plurals:ee[t.fc]}})}),e};class eo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=H.create("pluralResolver"),(!this.options.compatibilityJSON||er.includes(this.options.compatibilityJSON))&&("undefined"==typeof Intl||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=ei(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){let r;let n=N("dev"===e?"en":e),i=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:n,type:i});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];try{r=new Intl.PluralRules(n,{type:i})}catch(i){if(!e.match(/-|_/))return;let n=this.languageUtils.getLanguagePartFromCode(e);r=this.getRule(n,t)}return this.pluralRulesCache[o]=r,r}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.getRule(e,t);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,r).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.getRule(e,t);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((e,t)=>en[e]-en[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):r.numbers.map(r=>this.getSuffix(e,r,t)):[]}getSuffix(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=this.getRule(e,r);return n?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${n.select(t)}`:this.getSuffixRetroCompatible(n,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){let r=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),n=e.numbers[r];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===n?n="plural":1===n&&(n=""));let i=()=>this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString();return"v1"===this.options.compatibilityJSON?1===n?"":"number"==typeof n?`_plural_${n.toString()}`:i():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?i():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!et.includes(this.options.compatibilityJSON)}}let ea=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=z(e,t,r);return!o&&i&&S(r)&&void 0===(o=V(e,r,n))&&(o=V(t,r,n)),o},es=e=>e.replace(/\$/g,"$$$$");class el{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=H.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:r,useRawValueToEscape:n,prefix:i,prefixEscaped:o,suffix:a,suffixEscaped:s,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:h,nestingSuffix:p,nestingSuffixEscaped:f,nestingOptionsSeparator:g,maxReplaces:m,alwaysFormat:b}=e.interpolation;this.escape=void 0!==t?t:M,this.escapeValue=void 0===r||r,this.useRawValueToEscape=void 0!==n&&n,this.prefix=i?L(i):o||"{{",this.suffix=a?L(a):s||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?L(d):h||L("$t("),this.nestingSuffix=p?L(p):f||L(")"),this.nestingOptionsSeparator=g||",",this.maxReplaces=m||1e3,this.alwaysFormat=void 0!==b&&b,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,r,n){let i,o,a;let s=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=e=>{if(0>e.indexOf(this.formatSeparator)){let i=ea(t,s,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(i,void 0,r,{...n,...t,interpolationkey:e}):i}let i=e.split(this.formatSeparator),o=i.shift().trim(),a=i.join(this.formatSeparator).trim();return this.format(ea(t,s,o,this.options.keySeparator,this.options.ignoreJSONStructure),a,r,{...n,...t,interpolationkey:o})};this.resetRegExp();let u=n&&n.missingInterpolationHandler||this.options.missingInterpolationHandler,c=n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables,d=[{regex:this.regexpUnescape,safeValue:e=>es(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?es(this.escape(e)):es(e)}];return d.forEach(t=>{for(a=0;i=t.regex.exec(e);){let r=i[1].trim();if(void 0===(o=l(r))){if("function"==typeof u){let t=u(e,i,n);o=S(t)?t:""}else if(n&&Object.prototype.hasOwnProperty.call(n,r))o="";else if(c){o=i[0];continue}else this.logger.warn(`missed to pass in variable ${r} for interpolating ${e}`),o=""}else S(o)||this.useRawValueToEscape||(o=C(o));let s=t.safeValue(o);if(e=e.replace(i[0],s),c?(t.regex.lastIndex+=o.length,t.regex.lastIndex-=i[0].length):t.regex.lastIndex=0,++a>=this.maxReplaces)break}}),e}nest(e,t){let r,n,i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=(e,t)=>{let r=this.nestingOptionsSeparator;if(0>e.indexOf(r))return e;let n=e.split(RegExp(`${r}[ ]*{`)),o=`{${n[1]}`;e=n[0],o=this.interpolate(o,i);let a=o.match(/'/g),s=o.match(/"/g);(a&&a.length%2==0&&!s||s.length%2!=0)&&(o=o.replace(/'/g,'"'));try{i=JSON.parse(o),t&&(i={...t,...i})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${r}${o}`}return i.defaultValue&&i.defaultValue.indexOf(this.prefix)>-1&&delete i.defaultValue,e};for(;r=this.nestingRegexp.exec(e);){let s=[];(i=(i={...o}).replace&&!S(i.replace)?i.replace:i).applyPostProcessor=!1,delete i.defaultValue;let l=!1;if(-1!==r[0].indexOf(this.formatSeparator)&&!/{.*}/.test(r[1])){let e=r[1].split(this.formatSeparator).map(e=>e.trim());r[1]=e.shift(),s=e,l=!0}if((n=t(a.call(this,r[1].trim(),i),i))&&r[0]===e&&!S(n))return n;S(n)||(n=C(n)),n||(this.logger.warn(`missed to resolve ${r[1]} for nesting ${e}`),n=""),l&&(n=s.reduce((e,t)=>this.format(e,t,o.lng,{...o,interpolationkey:r[1].trim()}),n.trim())),e=e.replace(r[0],n),this.regexp.lastIndex=0}return e}}let eu=e=>{let t=e.toLowerCase().trim(),r={};if(e.indexOf("(")>-1){let n=e.split("(");t=n[0].toLowerCase().trim();let i=n[1].substring(0,n[1].length-1);if("currency"===t&&0>i.indexOf(":"))r.currency||(r.currency=i.trim());else if("relativetime"===t&&0>i.indexOf(":"))r.range||(r.range=i.trim());else{let e=i.split(";");e.forEach(e=>{if(e){let[t,...n]=e.split(":"),i=n.join(":").trim().replace(/^'+|'+$/g,""),o=t.trim();r[o]||(r[o]=i),"false"===i&&(r[o]=!1),"true"===i&&(r[o]=!0),isNaN(i)||(r[o]=parseInt(i,10))}})}}return{formatName:t,formatOptions:r}},ec=e=>{let t={};return(r,n,i)=>{let o=i;i&&i.interpolationkey&&i.formatParams&&i.formatParams[i.interpolationkey]&&i[i.interpolationkey]&&(o={...o,[i.interpolationkey]:void 0});let a=n+JSON.stringify(o),s=t[a];return s||(s=e(N(n),i),t[a]=s),s(r)}};class ed{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=H.create("formatter"),this.options=e,this.formats={number:ec((e,t)=>{let r=new Intl.NumberFormat(e,{...t});return e=>r.format(e)}),currency:ec((e,t)=>{let r=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>r.format(e)}),datetime:ec((e,t)=>{let r=new Intl.DateTimeFormat(e,{...t});return e=>r.format(e)}),relativetime:ec((e,t)=>{let r=new Intl.RelativeTimeFormat(e,{...t});return e=>r.format(e,t.range||"day")}),list:ec((e,t)=>{let r=new Intl.ListFormat(e,{...t});return e=>r.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=ec(t)}format(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&0>i[0].indexOf(")")&&i.find(e=>e.indexOf(")")>-1)){let e=i.findIndex(e=>e.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,e)].join(this.formatSeparator)}let o=i.reduce((e,t)=>{let{formatName:i,formatOptions:o}=eu(t);if(this.formats[i]){let t=e;try{let a=n&&n.formatParams&&n.formatParams[n.interpolationkey]||{},s=a.locale||a.lng||n.locale||n.lng||r;t=this.formats[i](e,s,{...o,...n,...a})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${i}`),e},e);return o}}let eh=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class ep extends J{constructor(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=n,this.logger=H.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=n.maxParallelReads||10,this.readingCalls=0,this.maxRetries=n.maxRetries>=0?n.maxRetries:5,this.retryTimeout=n.retryTimeout>=1?n.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,n.backend,n)}queueLoad(e,t,r,n){let i={},o={},a={},s={};return e.forEach(e=>{let n=!0;t.forEach(t=>{let a=`${e}|${t}`;!r.reload&&this.store.hasResourceBundle(e,t)?this.state[a]=2:this.state[a]<0||(1===this.state[a]?void 0===o[a]&&(o[a]=!0):(this.state[a]=1,n=!1,void 0===o[a]&&(o[a]=!0),void 0===i[a]&&(i[a]=!0),void 0===s[t]&&(s[t]=!0)))}),n||(a[e]=!0)}),(Object.keys(i).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:n}),{toLoad:Object.keys(i),pending:Object.keys(o),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(s)}}loaded(e,t,r){let n=e.split("|"),i=n[0],o=n[1];t&&this.emit("failedLoading",i,o,t),!t&&r&&this.store.addResourceBundle(i,o,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0);let a={};this.queue.forEach(r=>{T(r.loaded,[i],o),eh(r,e),t&&r.errors.push(t),0!==r.pendingCount||r.done||(Object.keys(r.loaded).forEach(e=>{a[e]||(a[e]={});let t=r.loaded[e];t.length&&t.forEach(t=>{void 0===a[e][t]&&(a[e][t]=!0)})}),r.done=!0,r.errors.length?r.callback(r.errors):r.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(e=>!e.done)}read(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,o=arguments.length>5?arguments[5]:void 0;if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:r,tried:n,wait:i,callback:o});return}this.readingCalls++;let a=(a,s)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(a&&s&&n<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,r,n+1,2*i,o)},i);return}o(a,s)},s=this.backend[r].bind(this.backend);if(2===s.length){try{let r=s(e,t);r&&"function"==typeof r.then?r.then(e=>a(null,e)).catch(a):a(null,r)}catch(e){a(e)}return}return s(e,t,a)}prepareLoading(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();S(e)&&(e=this.languageUtils.toResolveHierarchy(e)),S(t)&&(t=[t]);let i=this.queueLoad(e,t,r,n);if(!i.toLoad.length)return i.pending.length||n(),null;i.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e.split("|"),n=r[0],i=r[1];this.read(n,i,"read",void 0,void 0,(r,o)=>{r&&this.logger.warn(`${t}loading namespace ${i} for language ${n} failed`,r),!r&&o&&this.logger.log(`${t}loaded namespace ${i} for language ${n}`,o),this.loaded(e,r,o)})}saveMissing(e,t,r,n,i){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${r}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=r&&""!==r){if(this.backend&&this.backend.create){let s={...o,isUpdate:i},l=this.backend.create.bind(this.backend);if(l.length<6)try{let i;(i=5===l.length?l(e,t,r,n,s):l(e,t,r,n))&&"function"==typeof i.then?i.then(e=>a(null,e)).catch(a):a(null,i)}catch(e){a(e)}else l(e,t,r,n,a,s)}e&&e[0]&&this.store.addResource(e[0],t,r,n)}}}let ef=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),S(e[1])&&(t.defaultValue=e[1]),S(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let r=e[3]||e[2];Object.keys(r).forEach(e=>{t[e]=r[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),eg=e=>(S(e.ns)&&(e.ns=[e.ns]),S(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),S(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&0>e.supportedLngs.indexOf("cimode")&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),em=()=>{},eb=e=>{let t=Object.getOwnPropertyNames(Object.getPrototypeOf(e));t.forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class ey extends J{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=eg(e),this.services={},this.logger=H,this.modules={external:[]},eb(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(r=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(S(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let n=ef();this.options={...n,...this.options,...eg(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...n.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let i=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?H.init(i(this.modules.logger),this.options):H.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=ed);let r=new X(this.options);this.store=new q(this.options.resources,this.options);let o=this.services;o.logger=H,o.resourceStore=this.store,o.languageUtils=r,o.pluralResolver=new eo(r,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===n.interpolation.format)&&(o.formatter=i(t),o.formatter.init(o,this.options),this.options.interpolation.format=o.formatter.format.bind(o.formatter)),o.interpolator=new el(this.options),o.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},o.backendConnector=new ep(i(this.modules.backend),o.resourceStore,o,this.options),o.backendConnector.on("*",function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];e.emit(t,...n)}),this.modules.languageDetector&&(o.languageDetector=i(this.modules.languageDetector),o.languageDetector.init&&o.languageDetector.init(o,this.options.detection,this.options)),this.modules.i18nFormat&&(o.i18nFormat=i(this.modules.i18nFormat),o.i18nFormat.init&&o.i18nFormat.init(this)),this.translator=new Y(this.services,this.options),this.translator.on("*",function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];e.emit(t,...n)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,r||(r=em),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let o=w(),a=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(t),r(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?a():setTimeout(a,0),o}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:em,r=t,n=S(e)?e:this.language;if("function"==typeof e&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if(n&&"cimode"===n.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return r();let e=[],t=t=>{if(!t||"cimode"===t)return;let r=this.services.languageUtils.toResolveHierarchy(t);r.forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};if(n)t(n);else{let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.forEach(e=>t(e))}this.options.preload&&this.options.preload.forEach(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),r(e)})}else r(null)}reloadResources(e,t,r){let n=w();return"function"==typeof e&&(r=e,e=void 0),"function"==typeof t&&(r=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),r||(r=em),this.services.backendConnector.reload(e,t,e=>{n.resolve(),r(e)}),n}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&Z.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(e,t){var r=this;this.isLanguageChangingTo=e;let n=w();this.emit("languageChanging",e);let i=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},o=(e,o)=>{o?(i(o),this.translator.changeLanguage(o),this.isLanguageChangingTo=void 0,this.emit("languageChanged",o),this.logger.log("languageChanged",o)):this.isLanguageChangingTo=void 0,n.resolve(function(){return r.t(...arguments)}),t&&t(e,function(){return r.t(...arguments)})},a=t=>{e||t||!this.services.languageDetector||(t=[]);let r=S(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);r&&(this.language||i(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(r)),this.loadResources(r,e=>{o(e,r)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(e):a(this.services.languageDetector.detect()),n}getFixedT(e,t,r){var n=this;let i=function(e,t){let o,a;if("object"!=typeof t){for(var s=arguments.length,l=Array(s>2?s-2:0),u=2;u<s;u++)l[u-2]=arguments[u];o=n.options.overloadTranslationOptionHandler([e,t].concat(l))}else o={...t};o.lng=o.lng||i.lng,o.lngs=o.lngs||i.lngs,o.ns=o.ns||i.ns,""!==o.keyPrefix&&(o.keyPrefix=o.keyPrefix||r||i.keyPrefix);let c=n.options.keySeparator||".";return a=o.keyPrefix&&Array.isArray(e)?e.map(e=>`${o.keyPrefix}${c}${e}`):o.keyPrefix?`${o.keyPrefix}${c}${e}`:e,n.t(a,o)};return S(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=r,i}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let r=t.lng||this.resolvedLanguage||this.languages[0],n=!!this.options&&this.options.fallbackLng,i=this.languages[this.languages.length-1];if("cimode"===r.toLowerCase())return!0;let o=(e,t)=>{let r=this.services.backendConnector.state[`${e}|${t}`];return -1===r||0===r||2===r};if(t.precheck){let e=t.precheck(this,o);if(void 0!==e)return e}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(r,e)&&(!n||o(i,e)))}loadNamespaces(e,t){let r=w();return this.options.ns?(S(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){let r=w();S(e)&&(e=[e]);let n=this.options.preload||[],i=e.filter(e=>0>n.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return i.length?(this.options.preload=n.concat(i),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";let t=this.services&&this.services.languageUtils||new X(ef());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new ey(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:em,r=e.forkResourceStore;r&&delete e.forkResourceStore;let n={...this.options,...e,isClone:!0},i=new ey(n);return(void 0!==e.debug||void 0!==e.prefix)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(e=>{i[e]=this[e]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},r&&(i.store=new q(this.store.data,n),i.services.resourceStore=i.store),i.translator=new Y(i.services,n),i.translator.on("*",function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];i.emit(e,...r)}),i.init(n,t),i.translator.options=n,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let ev=ey.createInstance();ev.createInstance=ey.createInstance,ev.createInstance,ev.dir,ev.init,ev.loadResources,ev.reloadResources,ev.use,ev.changeLanguage,ev.getFixedT,ev.t,ev.exists,ev.setDefaultNamespace,ev.hasLoadedNamespace,ev.loadNamespaces,ev.loadLanguages;var ex=function(e){void 0===e.ns&&(e.ns=[]);var t,r,n=ev.createInstance(e);return n.isInitialized?t=Promise.resolve(ev.t):(null==e||null===(r=e.use)||void 0===r||r.forEach(function(e){return n.use(e)}),"function"==typeof e.onPreInitI18next&&e.onPreInitI18next(n),t=n.init(e)),{i18n:n,initPromise:t}},ek=a.createElement;function eS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ew(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eS(Object(r),!0).forEach(function(t){(0,o.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eC=function(e,t){if(t&&e.isInitialized)for(var r=0,n=Object.keys(t);r<n.length;r++)for(var i=n[r],o=0,a=Object.keys(t[i]);o<a.length;o++){var s,l=a[o];null!=e&&null!==(s=e.store)&&void 0!==s&&s.data&&e.store.data[i]&&e.store.data[i][l]||e.addResourceBundle(i,l,t[i][l],!0,!0)}},eP=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return l()(function(r){var o,s,l=(r.pageProps||{})._nextI18Next,u=null!==(o=null==l?void 0:l.initialLocale)&&void 0!==o?o:null==r||null===(s=r.router)||void 0===s?void 0:s.locale,c=null==l?void 0:l.ns,d=(0,a.useRef)(null),h=(0,a.useMemo)(function(){if(!l&&!t)return null;var e,r=null!=t?t:null==l?void 0:l.userConfig;if(!r)throw Error("appWithTranslation was called without a next-i18next config");if(!(null!=r&&r.i18n))throw Error("appWithTranslation was called without config.i18n");if(!(null!=r&&null!==(e=r.i18n)&&void 0!==e&&e.defaultLocale))throw Error("config.i18n does not include a defaultLocale property");var n=(l||{}).initialI18nStore,i=null!=t&&t.resources?t.resources:n;u||(u=r.i18n.defaultLocale);var o=d.current;return o?eC(o,i):(eC(o=ex(ew(ew(ew({},k(ew(ew({},r),{},{lng:u}))),{},{lng:u},c&&{ns:c}),{},{resources:i})).i18n,i),d.current=o),o},[l,u,c]);return g(function(){h&&u&&h.changeLanguage(u)},[h,u]),null!==h?ek(n.a3,{i18n:h},ek(e,r)):ek(e,(0,i.Z)({key:u},r))},e)}},11752:function(e,t,r){e.exports=r(88027)},91118:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return r(67856)}])},61951:function(e,t,r){"use strict";r.d(t,{H:function(){return a},a:function(){return s}});var n=r(85893),i=r(67294);let o=(0,i.createContext)(),a=e=>{let{children:t}=e,[r,a]=(0,i.useState)(!1),[s,l]=(0,i.useState)(!1),[u,c]=(0,i.useState)(null),d=()=>l(!0),h=()=>l(!1);(0,i.useEffect)(()=>{let e=localStorage.getItem("isLoggedIn");a("true"===e);let t=localStorage.getItem("user");c(t&&"undefined"!==t?JSON.parse(t):null)},[]);let p=e=>{localStorage.setItem("isLoggedIn","true"),localStorage.setItem("user",JSON.stringify(e)),c(e),a(!0),l(!1)},f=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("user"),c(null),a(!1)};return(0,n.jsx)(o.Provider,{value:{isLoggedIn:r,logon:p,logout:f,isLoginModalOpen:s,openLoginModal:d,closeLoginModal:h,user:u},children:t})},s=()=>(0,i.useContext)(o)},67856:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return ak}});var n=r(85893);function i(e,t={}){let r=!1;function n(t){let r=["container","root"].includes(t??"")?[e]:[e,t],n=r.filter(Boolean).join("__"),i=`chakra-${n}`,o={className:i,selector:`.${i}`,toString:()=>t};return o}return{parts:function(...o){for(let e of(!function(){if(!r){r=!0;return}throw Error("[anatomy] .part(...) should only be called once. Did you mean to use .extend(...) ?")}(),o))t[e]=n(e);return i(e,t)},toPart:n,extend:function(...r){for(let e of r)e in t||(t[e]=n(e));return i(e,t)},selectors:function(){let e=Object.fromEntries(Object.entries(t).map(([e,t])=>[e,t.selector]));return e},classnames:function(){let e=Object.fromEntries(Object.entries(t).map(([e,t])=>[e,t.className]));return e},get keys(){return Object.keys(t)},__type:{}}}let o=i("accordion").parts("root","container","button","panel","icon"),a=i("alert").parts("title","description","container","icon","spinner"),s=i("avatar").parts("label","badge","container","excessLabel","group"),l=i("breadcrumb").parts("link","item","container","separator");i("button").parts();let u=i("checkbox").parts("control","icon","container","label");i("progress").parts("track","filledTrack","label");let c=i("drawer").parts("overlay","dialogContainer","dialog","header","closeButton","body","footer"),d=i("editable").parts("preview","input","textarea"),h=i("form").parts("container","requiredIndicator","helperText"),p=i("formError").parts("text","icon"),f=i("input").parts("addon","field","element","group"),g=i("list").parts("container","item","icon"),m=i("menu").parts("button","list","item","groupTitle","icon","command","divider"),b=i("modal").parts("overlay","dialogContainer","dialog","header","closeButton","body","footer"),y=i("numberinput").parts("root","field","stepperGroup","stepper");i("pininput").parts("field");let v=i("popover").parts("content","header","body","footer","popper","arrow","closeButton"),x=i("progress").parts("label","filledTrack","track"),k=i("radio").parts("container","control","label"),S=i("select").parts("field","icon"),w=i("slider").parts("container","track","thumb","filledTrack","mark"),C=i("stat").parts("container","label","helpText","number","icon"),P=i("switch").parts("container","track","thumb","label"),_=i("table").parts("table","thead","tbody","tr","th","td","tfoot","caption"),A=i("tabs").parts("root","tab","tablist","tabpanel","tabpanels","indicator"),j=i("tag").parts("container","label","closeButton"),E=i("card").parts("container","header","body","footer");i("stepper").parts("stepper","step","title","description","indicator","separator","icon","number");var $=r(83695);let{definePartsStyle:T,defineMultiStyleConfig:R}=(0,$.D)(o.keys),z=(0,$.k0)({borderTopWidth:"1px",borderColor:"inherit",_last:{borderBottomWidth:"1px"}}),O=(0,$.k0)({transitionProperty:"common",transitionDuration:"normal",fontSize:"md",_focusVisible:{boxShadow:"outline"},_hover:{bg:"blackAlpha.50"},_disabled:{opacity:.4,cursor:"not-allowed"},px:"4",py:"2"}),L=(0,$.k0)({pt:"2",px:"4",pb:"5"}),B=(0,$.k0)({fontSize:"1.25em"}),M=T({container:z,button:O,panel:L,icon:B}),I=R({baseStyle:M});var D=r(43289);function F(e,t,r){return Math.min(Math.max(e,r),t)}var V=class extends Error{constructor(e){super(`Failed to parse color: "${e}"`)}};function N(e){if("string"!=typeof e)throw new V(e);if("transparent"===e.trim().toLowerCase())return[0,0,0,0];let t=e.trim();t=Y.test(e)?function(e){let t=e.toLowerCase().trim(),r=U[function(e){let t=5381,r=e.length;for(;r;)t=33*t^e.charCodeAt(--r);return(t>>>0)%2341}(t)];if(!r)throw new V(e);return`#${r}`}(e):e;let r=J.exec(t);if(r){let e=Array.from(r).slice(1);return[...e.slice(0,3).map(e=>parseInt(H(e,2),16)),parseInt(H(e[3]||"f",2),16)/255]}let n=q.exec(t);if(n){let e=Array.from(n).slice(1);return[...e.slice(0,3).map(e=>parseInt(e,16)),parseInt(e[3]||"ff",16)/255]}let i=Z.exec(t);if(i){let e=Array.from(i).slice(1);return[...e.slice(0,3).map(e=>parseInt(e,10)),parseFloat(e[3]||"1")]}let o=G.exec(t);if(o){let[t,r,n,i]=Array.from(o).slice(1).map(parseFloat);if(F(0,100,r)!==r||F(0,100,n)!==n)throw new V(e);return[...X(t,r,n),Number.isNaN(i)?1:i]}throw new V(e)}let W=e=>parseInt(e.replace(/_/g,""),36),U="1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm".split(" ").reduce((e,t)=>{let r=W(t.substring(0,3)),n=W(t.substring(3)).toString(16),i="";for(let e=0;e<6-n.length;e++)i+="0";return e[r]=`${i}${n}`,e},{}),H=(e,t)=>Array.from(Array(t)).map(()=>e).join(""),J=RegExp(`^#${H("([a-f0-9])",3)}([a-f0-9])?$`,"i"),q=RegExp(`^#${H("([a-f0-9]{2})",3)}([a-f0-9]{2})?$`,"i"),Z=RegExp(`^rgba?\\(\\s*(\\d+)\\s*${H(",\\s*(\\d+)\\s*",2)}(?:,\\s*([\\d.]+))?\\s*\\)$`,"i"),G=/^hsla?\(\s*([\d.]+)\s*,\s*([\d.]+)%\s*,\s*([\d.]+)%(?:\s*,\s*([\d.]+))?\s*\)$/i,Y=/^[a-z]+$/i,K=e=>Math.round(255*e),X=(e,t,r)=>{let n=r/100;if(0===t)return[n,n,n].map(K);let i=(e%360+360)%360/60,o=(1-Math.abs(2*n-1))*(t/100),a=o*(1-Math.abs(i%2-1)),s=0,l=0,u=0;i>=0&&i<1?(s=o,l=a):i>=1&&i<2?(s=a,l=o):i>=2&&i<3?(l=o,u=a):i>=3&&i<4?(l=a,u=o):i>=4&&i<5?(s=a,u=o):i>=5&&i<6&&(s=o,u=a);let c=n-o/2,d=s+c,h=l+c,p=u+c;return[d,h,p].map(K)},Q=e=>0===Object.keys(e).length,ee=(e,t,r)=>{let n=function(e,t,r,n,i){for(n=0,t=t.split?t.split("."):t;n<t.length;n++)e=e?e[t[n]]:i;return e===i?r:e}(e,`colors.${t}`,t);try{return!function(e){let[t,r,n,i]=N(e),o=e=>{let t=F(0,255,e).toString(16);return 1===t.length?`0${t}`:t};o(t),o(r),o(n),i<1&&o(Math.round(255*i))}(n),n}catch{return r??"#000000"}},et=e=>{let[t,r,n]=N(e);return(299*t+587*r+114*n)/1e3},er=e=>t=>{let r=ee(t,e),n=et(r);return n<128?"dark":"light"},en=e=>t=>"dark"===er(e)(t),ei=(e,t)=>r=>{let n=ee(r,e);return function(e,t){let[r,n,i,o]=N(e);return`rgba(${F(0,255,r).toFixed()}, ${F(0,255,n).toFixed()}, ${F(0,255,i).toFixed()}, ${parseFloat(F(0,1,o-t).toFixed(3))})`}(n,1-t)};function eo(e="1rem",t="rgba(255, 255, 255, 0.15)"){return{backgroundImage:`linear-gradient(
    45deg,
    ${t} 25%,
    transparent 25%,
    transparent 50%,
    ${t} 50%,
    ${t} 75%,
    transparent 75%,
    transparent
  )`,backgroundSize:`${e} ${e}`}}let ea=()=>`#${Math.floor(16777215*Math.random()).toString(16).padEnd(6,"0")}`,{definePartsStyle:es,defineMultiStyleConfig:el}=(0,$.D)(a.keys),eu=(0,D.gJ)("alert-fg"),ec=(0,D.gJ)("alert-bg"),ed=es({container:{bg:ec.reference,px:"4",py:"3"},title:{fontWeight:"bold",lineHeight:"6",marginEnd:"2"},description:{lineHeight:"6"},icon:{color:eu.reference,flexShrink:0,marginEnd:"3",w:"5",h:"6"},spinner:{color:eu.reference,flexShrink:0,marginEnd:"3",w:"5",h:"5"}});function eh(e){let{theme:t,colorScheme:r}=e,n=ei(`${r}.200`,.16)(t);return{light:`colors.${r}.100`,dark:n}}let ep=es(e=>{let{colorScheme:t}=e,r=eh(e);return{container:{[eu.variable]:`colors.${t}.600`,[ec.variable]:r.light,_dark:{[eu.variable]:`colors.${t}.200`,[ec.variable]:r.dark}}}}),ef=es(e=>{let{colorScheme:t}=e,r=eh(e);return{container:{[eu.variable]:`colors.${t}.600`,[ec.variable]:r.light,_dark:{[eu.variable]:`colors.${t}.200`,[ec.variable]:r.dark},paddingStart:"3",borderStartWidth:"4px",borderStartColor:eu.reference}}}),eg=es(e=>{let{colorScheme:t}=e,r=eh(e);return{container:{[eu.variable]:`colors.${t}.600`,[ec.variable]:r.light,_dark:{[eu.variable]:`colors.${t}.200`,[ec.variable]:r.dark},pt:"2",borderTopWidth:"4px",borderTopColor:eu.reference}}}),em=es(e=>{let{colorScheme:t}=e;return{container:{[eu.variable]:"colors.white",[ec.variable]:`colors.${t}.600`,_dark:{[eu.variable]:"colors.gray.900",[ec.variable]:`colors.${t}.200`},color:eu.reference}}}),eb=el({baseStyle:ed,variants:{subtle:ep,"left-accent":ef,"top-accent":eg,solid:em},defaultProps:{variant:"subtle",colorScheme:"blue"}}),ey={px:"1px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},ev={...ey,max:"max-content",min:"min-content",full:"100%","3xs":"14rem","2xs":"16rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem","8xl":"90rem",prose:"60ch",container:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px"}},ex=e=>"function"==typeof e;function ek(e,...t){return ex(e)?e(...t):e}let{definePartsStyle:eS,defineMultiStyleConfig:ew}=(0,$.D)(s.keys),eC=(0,D.gJ)("avatar-border-color"),eP=(0,D.gJ)("avatar-bg"),e_=(0,D.gJ)("avatar-font-size"),eA=(0,D.gJ)("avatar-size"),ej=(0,$.k0)({borderRadius:"full",border:"0.2em solid",borderColor:eC.reference,[eC.variable]:"white",_dark:{[eC.variable]:"colors.gray.800"}}),eE=(0,$.k0)({bg:eP.reference,fontSize:e_.reference,width:eA.reference,height:eA.reference,lineHeight:"1",[eP.variable]:"colors.gray.200",_dark:{[eP.variable]:"colors.whiteAlpha.400"}}),e$=(0,$.k0)(e=>{let{name:t,theme:r}=e,n=t?function(e){var t;let r=ea();return!e||Q(e)?r:e.string&&e.colors?function(e,t){let r=0;if(0===e.length)return t[0];for(let t=0;t<e.length;t+=1)r=e.charCodeAt(t)+((r<<5)-r),r&=r;return r=(r%t.length+t.length)%t.length,t[r]}(e.string,e.colors):e.string&&!e.colors?function(e){let t=0;if(0===e.length)return t.toString();for(let r=0;r<e.length;r+=1)t=e.charCodeAt(r)+((t<<5)-t),t&=t;let r="#";for(let e=0;e<3;e+=1){let n=t>>8*e&255;r+=`00${n.toString(16)}`.substr(-2)}return r}(e.string):e.colors&&!e.string?(t=e.colors)[Math.floor(Math.random()*t.length)]:r}({string:t}):"colors.gray.400",i=en(n)(r),o="white";return i||(o="gray.800"),{bg:eP.reference,fontSize:e_.reference,color:o,borderColor:eC.reference,verticalAlign:"top",width:eA.reference,height:eA.reference,"&:not([data-loaded])":{[eP.variable]:n},[eC.variable]:"colors.white",_dark:{[eC.variable]:"colors.gray.800"}}}),eT=(0,$.k0)({fontSize:e_.reference,lineHeight:"1"}),eR=eS(e=>({badge:ek(ej,e),excessLabel:ek(eE,e),container:ek(e$,e),label:eT}));function ez(e){let t="100%"!==e?ev[e]:void 0;return eS({container:{[eA.variable]:t??e,[e_.variable]:`calc(${t??e} / 2.5)`},excessLabel:{[eA.variable]:t??e,[e_.variable]:`calc(${t??e} / 2.5)`}})}let eO={"2xs":ez(4),xs:ez(6),sm:ez(8),md:ez(12),lg:ez(16),xl:ez(24),"2xl":ez(32),full:ez("100%")},eL=ew({baseStyle:eR,sizes:eO,defaultProps:{size:"md"}}),eB=(0,D._6)("badge",["bg","color","shadow"]),eM=(0,$.k0)({px:1,textTransform:"uppercase",fontSize:"xs",borderRadius:"sm",fontWeight:"bold",bg:eB.bg.reference,color:eB.color.reference,boxShadow:eB.shadow.reference}),eI=(0,$.k0)(e=>{let{colorScheme:t,theme:r}=e,n=ei(`${t}.500`,.6)(r);return{[eB.bg.variable]:`colors.${t}.500`,[eB.color.variable]:"colors.white",_dark:{[eB.bg.variable]:n,[eB.color.variable]:"colors.whiteAlpha.800"}}}),eD=(0,$.k0)(e=>{let{colorScheme:t,theme:r}=e,n=ei(`${t}.200`,.16)(r);return{[eB.bg.variable]:`colors.${t}.100`,[eB.color.variable]:`colors.${t}.800`,_dark:{[eB.bg.variable]:n,[eB.color.variable]:`colors.${t}.200`}}}),eF=(0,$.k0)(e=>{let{colorScheme:t,theme:r}=e,n=ei(`${t}.200`,.8)(r);return{[eB.color.variable]:`colors.${t}.500`,_dark:{[eB.color.variable]:n},[eB.shadow.variable]:`inset 0 0 0px 1px ${eB.color.reference}`}}),eV=(0,$.fj)({baseStyle:eM,variants:{solid:eI,subtle:eD,outline:eF},defaultProps:{variant:"subtle",colorScheme:"gray"}}),{defineMultiStyleConfig:eN,definePartsStyle:eW}=(0,$.D)(l.keys),eU=(0,D.gJ)("breadcrumb-link-decor"),eH=(0,$.k0)({transitionProperty:"common",transitionDuration:"fast",transitionTimingFunction:"ease-out",outline:"none",color:"inherit",textDecoration:eU.reference,[eU.variable]:"none","&:not([aria-current=page])":{cursor:"pointer",_hover:{[eU.variable]:"underline"},_focusVisible:{boxShadow:"outline"}}}),eJ=eW({link:eH}),eq=eN({baseStyle:eJ});function eZ(e,t){return r=>"dark"===r.colorMode?t:e}function eG(e){let{orientation:t,vertical:r,horizontal:n}=e;return t?"vertical"===t?r:n:{}}let eY=(0,$.k0)({lineHeight:"1.2",borderRadius:"md",fontWeight:"semibold",transitionProperty:"common",transitionDuration:"normal",_focusVisible:{boxShadow:"outline"},_disabled:{opacity:.4,cursor:"not-allowed",boxShadow:"none"},_hover:{_disabled:{bg:"initial"}}}),eK=(0,$.k0)(e=>{let{colorScheme:t,theme:r}=e;if("gray"===t)return{color:eZ("gray.800","whiteAlpha.900")(e),_hover:{bg:eZ("gray.100","whiteAlpha.200")(e)},_active:{bg:eZ("gray.200","whiteAlpha.300")(e)}};let n=ei(`${t}.200`,.12)(r),i=ei(`${t}.200`,.24)(r);return{color:eZ(`${t}.600`,`${t}.200`)(e),bg:"transparent",_hover:{bg:eZ(`${t}.50`,n)(e)},_active:{bg:eZ(`${t}.100`,i)(e)}}}),eX=(0,$.k0)(e=>{let{colorScheme:t}=e,r=eZ("gray.200","whiteAlpha.300")(e);return{border:"1px solid",borderColor:"gray"===t?r:"currentColor",".chakra-button__group[data-attached][data-orientation=horizontal] > &:not(:last-of-type)":{marginEnd:"-1px"},".chakra-button__group[data-attached][data-orientation=vertical] > &:not(:last-of-type)":{marginBottom:"-1px"},...ek(eK,e)}}),eQ={yellow:{bg:"yellow.400",color:"black",hoverBg:"yellow.500",activeBg:"yellow.600"},cyan:{bg:"cyan.400",color:"black",hoverBg:"cyan.500",activeBg:"cyan.600"}},e0=(0,$.k0)(e=>{let{colorScheme:t}=e;if("gray"===t){let t=eZ("gray.100","whiteAlpha.200")(e);return{bg:t,color:eZ("gray.800","whiteAlpha.900")(e),_hover:{bg:eZ("gray.200","whiteAlpha.300")(e),_disabled:{bg:t}},_active:{bg:eZ("gray.300","whiteAlpha.400")(e)}}}let{bg:r=`${t}.500`,color:n="white",hoverBg:i=`${t}.600`,activeBg:o=`${t}.700`}=eQ[t]??{},a=eZ(r,`${t}.200`)(e);return{bg:a,color:eZ(n,"gray.800")(e),_hover:{bg:eZ(i,`${t}.300`)(e),_disabled:{bg:a}},_active:{bg:eZ(o,`${t}.400`)(e)}}}),e1=(0,$.k0)(e=>{let{colorScheme:t}=e;return{padding:0,height:"auto",lineHeight:"normal",verticalAlign:"baseline",color:eZ(`${t}.500`,`${t}.200`)(e),_hover:{textDecoration:"underline",_disabled:{textDecoration:"none"}},_active:{color:eZ(`${t}.700`,`${t}.500`)(e)}}}),e2=(0,$.k0)({bg:"none",color:"inherit",display:"inline",lineHeight:"inherit",m:"0",p:"0"}),e5={lg:(0,$.k0)({h:"12",minW:"12",fontSize:"lg",px:"6"}),md:(0,$.k0)({h:"10",minW:"10",fontSize:"md",px:"4"}),sm:(0,$.k0)({h:"8",minW:"8",fontSize:"sm",px:"3"}),xs:(0,$.k0)({h:"6",minW:"6",fontSize:"xs",px:"2"})},e4=(0,$.fj)({baseStyle:eY,variants:{ghost:eK,outline:eX,solid:e0,link:e1,unstyled:e2},sizes:e5,defaultProps:{variant:"solid",size:"md",colorScheme:"gray"}}),{definePartsStyle:e3,defineMultiStyleConfig:e6}=(0,$.D)(E.keys),e8=(0,D.gJ)("card-bg"),e9=(0,D.gJ)("card-padding"),e7=(0,D.gJ)("card-shadow"),te=(0,D.gJ)("card-radius"),tt=(0,D.gJ)("card-border-width","0"),tr=(0,D.gJ)("card-border-color"),tn=e3({container:{[e8.variable]:"colors.chakra-body-bg",backgroundColor:e8.reference,boxShadow:e7.reference,borderRadius:te.reference,color:"chakra-body-text",borderWidth:tt.reference,borderColor:tr.reference},body:{padding:e9.reference,flex:"1 1 0%"},header:{padding:e9.reference},footer:{padding:e9.reference}}),ti={sm:e3({container:{[te.variable]:"radii.base",[e9.variable]:"space.3"}}),md:e3({container:{[te.variable]:"radii.md",[e9.variable]:"space.5"}}),lg:e3({container:{[te.variable]:"radii.xl",[e9.variable]:"space.7"}})},to={elevated:e3({container:{[e7.variable]:"shadows.base",_dark:{[e8.variable]:"colors.gray.700"}}}),outline:e3({container:{[tt.variable]:"1px",[tr.variable]:"colors.chakra-border-color"}}),filled:e3({container:{[e8.variable]:"colors.chakra-subtle-bg"}}),unstyled:{body:{[e9.variable]:0},header:{[e9.variable]:0},footer:{[e9.variable]:0}}},ta=e6({baseStyle:tn,variants:to,sizes:ti,defaultProps:{variant:"elevated",size:"md"}}),{definePartsStyle:ts,defineMultiStyleConfig:tl}=(0,$.D)(u.keys),tu=(0,D.gJ)("checkbox-size"),tc=(0,$.k0)(e=>{let{colorScheme:t}=e;return{w:tu.reference,h:tu.reference,transitionProperty:"box-shadow",transitionDuration:"normal",border:"2px solid",borderRadius:"sm",borderColor:"inherit",color:"white",_checked:{bg:eZ(`${t}.500`,`${t}.200`)(e),borderColor:eZ(`${t}.500`,`${t}.200`)(e),color:eZ("white","gray.900")(e),_hover:{bg:eZ(`${t}.600`,`${t}.300`)(e),borderColor:eZ(`${t}.600`,`${t}.300`)(e)},_disabled:{borderColor:eZ("gray.200","transparent")(e),bg:eZ("gray.200","whiteAlpha.300")(e),color:eZ("gray.500","whiteAlpha.500")(e)}},_indeterminate:{bg:eZ(`${t}.500`,`${t}.200`)(e),borderColor:eZ(`${t}.500`,`${t}.200`)(e),color:eZ("white","gray.900")(e)},_disabled:{bg:eZ("gray.100","whiteAlpha.100")(e),borderColor:eZ("gray.100","transparent")(e)},_focusVisible:{boxShadow:"outline"},_invalid:{borderColor:eZ("red.500","red.300")(e)}}}),td=(0,$.k0)({_disabled:{cursor:"not-allowed"}}),th=(0,$.k0)({userSelect:"none",_disabled:{opacity:.4}}),tp=(0,$.k0)({transitionProperty:"transform",transitionDuration:"normal"}),tf=ts(e=>({icon:tp,container:td,control:ek(tc,e),label:th})),tg={sm:ts({control:{[tu.variable]:"sizes.3"},label:{fontSize:"sm"},icon:{fontSize:"3xs"}}),md:ts({control:{[tu.variable]:"sizes.4"},label:{fontSize:"md"},icon:{fontSize:"2xs"}}),lg:ts({control:{[tu.variable]:"sizes.5"},label:{fontSize:"lg"},icon:{fontSize:"2xs"}})},tm=tl({baseStyle:tf,sizes:tg,defaultProps:{size:"md",colorScheme:"blue"}});function tb(e){let t=function(e,t="-"){return e.replace(/\s+/g,t)}(e.toString());return t.includes("\\.")?e:Number.isInteger(parseFloat(e.toString()))?e:t.replace(".","\\.")}function ty(e,t){var r,n;let i=function(e,t=""){return`--${function(e,t=""){return[t,tb(e)].filter(Boolean).join("-")}(e,t)}`}(e,t?.prefix);return{variable:i,reference:(r="string"==typeof(n=t?.fallback)?n:n?.reference,`var(${tb(i)}${r?`, ${r}`:""})`)}}let tv=ty("close-button-size"),tx=ty("close-button-bg"),tk=(0,$.k0)({w:[tv.reference],h:[tv.reference],borderRadius:"md",transitionProperty:"common",transitionDuration:"normal",_disabled:{opacity:.4,cursor:"not-allowed",boxShadow:"none"},_hover:{[tx.variable]:"colors.blackAlpha.100",_dark:{[tx.variable]:"colors.whiteAlpha.100"}},_active:{[tx.variable]:"colors.blackAlpha.200",_dark:{[tx.variable]:"colors.whiteAlpha.200"}},_focusVisible:{boxShadow:"outline"},bg:tx.reference}),tS={lg:(0,$.k0)({[tv.variable]:"sizes.10",fontSize:"md"}),md:(0,$.k0)({[tv.variable]:"sizes.8",fontSize:"xs"}),sm:(0,$.k0)({[tv.variable]:"sizes.6",fontSize:"2xs"})},tw=(0,$.fj)({baseStyle:tk,sizes:tS,defaultProps:{size:"md"}}),{variants:tC,defaultProps:tP}=eV,t_=(0,$.k0)({fontFamily:"mono",fontSize:"sm",px:"0.2em",borderRadius:"sm",bg:eB.bg.reference,color:eB.color.reference,boxShadow:eB.shadow.reference}),tA=(0,$.fj)({baseStyle:t_,variants:tC,defaultProps:tP}),tj=(0,$.k0)({w:"100%",mx:"auto",maxW:"prose",px:"4"}),tE=(0,$.fj)({baseStyle:tj}),t$=(0,$.k0)({opacity:.6,borderColor:"inherit"}),tT=(0,$.k0)({borderStyle:"solid"}),tR=(0,$.k0)({borderStyle:"dashed"}),tz=(0,$.fj)({baseStyle:t$,variants:{solid:tT,dashed:tR},defaultProps:{variant:"solid"}}),{definePartsStyle:tO,defineMultiStyleConfig:tL}=(0,$.D)(c.keys),tB=(0,D.gJ)("drawer-bg"),tM=(0,D.gJ)("drawer-box-shadow");function tI(e){return"full"===e?tO({dialog:{maxW:"100vw",h:"100vh"}}):tO({dialog:{maxW:e}})}let tD=(0,$.k0)({bg:"blackAlpha.600",zIndex:"modal"}),tF=(0,$.k0)({display:"flex",zIndex:"modal",justifyContent:"center"}),tV=(0,$.k0)(e=>{let{isFullHeight:t}=e;return{...t&&{height:"100vh"},zIndex:"modal",maxH:"100vh",color:"inherit",[tB.variable]:"colors.white",[tM.variable]:"shadows.lg",_dark:{[tB.variable]:"colors.gray.700",[tM.variable]:"shadows.dark-lg"},bg:tB.reference,boxShadow:tM.reference}}),tN=(0,$.k0)({px:"6",py:"4",fontSize:"xl",fontWeight:"semibold"}),tW=(0,$.k0)({position:"absolute",top:"2",insetEnd:"3"}),tU=(0,$.k0)({px:"6",py:"2",flex:"1",overflow:"auto"}),tH=(0,$.k0)({px:"6",py:"4"}),tJ=tO(e=>({overlay:tD,dialogContainer:tF,dialog:ek(tV,e),header:tN,closeButton:tW,body:tU,footer:tH})),tq={xs:tI("xs"),sm:tI("md"),md:tI("lg"),lg:tI("2xl"),xl:tI("4xl"),full:tI("full")},tZ=tL({baseStyle:tJ,sizes:tq,defaultProps:{size:"xs"}}),{definePartsStyle:tG,defineMultiStyleConfig:tY}=(0,$.D)(d.keys),tK=(0,$.k0)({borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal"}),tX=(0,$.k0)({borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal",width:"full",_focusVisible:{boxShadow:"outline"},_placeholder:{opacity:.6}}),tQ=(0,$.k0)({borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal",width:"full",_focusVisible:{boxShadow:"outline"},_placeholder:{opacity:.6}}),t0=tG({preview:tK,input:tX,textarea:tQ}),t1=tY({baseStyle:t0}),{definePartsStyle:t2,defineMultiStyleConfig:t5}=(0,$.D)(h.keys),t4=(0,D.gJ)("form-control-color"),t3=(0,$.k0)({marginStart:"1",[t4.variable]:"colors.red.500",_dark:{[t4.variable]:"colors.red.300"},color:t4.reference}),t6=(0,$.k0)({mt:"2",[t4.variable]:"colors.gray.600",_dark:{[t4.variable]:"colors.whiteAlpha.600"},color:t4.reference,lineHeight:"normal",fontSize:"sm"}),t8=t2({container:{width:"100%",position:"relative"},requiredIndicator:t3,helperText:t6}),t9=t5({baseStyle:t8}),{definePartsStyle:t7,defineMultiStyleConfig:re}=(0,$.D)(p.keys),rt=(0,D.gJ)("form-error-color"),rr=(0,$.k0)({[rt.variable]:"colors.red.500",_dark:{[rt.variable]:"colors.red.300"},color:rt.reference,mt:"2",fontSize:"sm",lineHeight:"normal"}),rn=(0,$.k0)({marginEnd:"0.5em",[rt.variable]:"colors.red.500",_dark:{[rt.variable]:"colors.red.300"},color:rt.reference}),ri=t7({text:rr,icon:rn}),ro=re({baseStyle:ri}),ra=(0,$.k0)({fontSize:"md",marginEnd:"3",mb:"2",fontWeight:"medium",transitionProperty:"common",transitionDuration:"normal",opacity:1,_disabled:{opacity:.4}}),rs=(0,$.fj)({baseStyle:ra}),rl=(0,$.k0)({fontFamily:"heading",fontWeight:"bold"}),ru={"4xl":(0,$.k0)({fontSize:["6xl",null,"7xl"],lineHeight:1}),"3xl":(0,$.k0)({fontSize:["5xl",null,"6xl"],lineHeight:1}),"2xl":(0,$.k0)({fontSize:["4xl",null,"5xl"],lineHeight:[1.2,null,1]}),xl:(0,$.k0)({fontSize:["3xl",null,"4xl"],lineHeight:[1.33,null,1.2]}),lg:(0,$.k0)({fontSize:["2xl",null,"3xl"],lineHeight:[1.33,null,1.2]}),md:(0,$.k0)({fontSize:"xl",lineHeight:1.2}),sm:(0,$.k0)({fontSize:"md",lineHeight:1.2}),xs:(0,$.k0)({fontSize:"sm",lineHeight:1.2})},rc=(0,$.fj)({baseStyle:rl,sizes:ru,defaultProps:{size:"xl"}}),{definePartsStyle:rd,defineMultiStyleConfig:rh}=(0,$.D)(f.keys),rp=(0,D.gJ)("input-height"),rf=(0,D.gJ)("input-font-size"),rg=(0,D.gJ)("input-padding"),rm=(0,D.gJ)("input-border-radius"),rb=rd({addon:{height:rp.reference,fontSize:rf.reference,px:rg.reference,borderRadius:rm.reference},field:{width:"100%",height:rp.reference,fontSize:rf.reference,px:rg.reference,borderRadius:rm.reference,minWidth:0,outline:0,position:"relative",appearance:"none",transitionProperty:"common",transitionDuration:"normal",_disabled:{opacity:.4,cursor:"not-allowed"}}}),ry={lg:(0,$.k0)({[rf.variable]:"fontSizes.lg",[rg.variable]:"space.4",[rm.variable]:"radii.md",[rp.variable]:"sizes.12"}),md:(0,$.k0)({[rf.variable]:"fontSizes.md",[rg.variable]:"space.4",[rm.variable]:"radii.md",[rp.variable]:"sizes.10"}),sm:(0,$.k0)({[rf.variable]:"fontSizes.sm",[rg.variable]:"space.3",[rm.variable]:"radii.sm",[rp.variable]:"sizes.8"}),xs:(0,$.k0)({[rf.variable]:"fontSizes.xs",[rg.variable]:"space.2",[rm.variable]:"radii.sm",[rp.variable]:"sizes.6"})},rv={lg:rd({field:ry.lg,group:ry.lg}),md:rd({field:ry.md,group:ry.md}),sm:rd({field:ry.sm,group:ry.sm}),xs:rd({field:ry.xs,group:ry.xs})};function rx(e){let{focusBorderColor:t,errorBorderColor:r}=e;return{focusBorderColor:t||eZ("blue.500","blue.300")(e),errorBorderColor:r||eZ("red.500","red.300")(e)}}let rk=rd(e=>{let{theme:t}=e,{focusBorderColor:r,errorBorderColor:n}=rx(e);return{field:{border:"1px solid",borderColor:"inherit",bg:"inherit",_hover:{borderColor:eZ("gray.300","whiteAlpha.400")(e)},_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:ee(t,n),boxShadow:`0 0 0 1px ${ee(t,n)}`},_focusVisible:{zIndex:1,borderColor:ee(t,r),boxShadow:`0 0 0 1px ${ee(t,r)}`}},addon:{border:"1px solid",borderColor:eZ("inherit","whiteAlpha.50")(e),bg:eZ("gray.100","whiteAlpha.300")(e)}}}),rS=rd(e=>{let{theme:t}=e,{focusBorderColor:r,errorBorderColor:n}=rx(e);return{field:{border:"2px solid",borderColor:"transparent",bg:eZ("gray.100","whiteAlpha.50")(e),_hover:{bg:eZ("gray.200","whiteAlpha.100")(e)},_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:ee(t,n)},_focusVisible:{bg:"transparent",borderColor:ee(t,r)}},addon:{border:"2px solid",borderColor:"transparent",bg:eZ("gray.100","whiteAlpha.50")(e)}}}),rw=rd(e=>{let{theme:t}=e,{focusBorderColor:r,errorBorderColor:n}=rx(e);return{field:{borderBottom:"1px solid",borderColor:"inherit",borderRadius:"0",px:"0",bg:"transparent",_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:ee(t,n),boxShadow:`0px 1px 0px 0px ${ee(t,n)}`},_focusVisible:{borderColor:ee(t,r),boxShadow:`0px 1px 0px 0px ${ee(t,r)}`}},addon:{borderBottom:"2px solid",borderColor:"inherit",borderRadius:"0",px:"0",bg:"transparent"}}}),rC=rd({field:{bg:"transparent",px:"0",height:"auto"},addon:{bg:"transparent",px:"0",height:"auto"}}),rP=rh({baseStyle:rb,sizes:rv,variants:{outline:rk,filled:rS,flushed:rw,unstyled:rC},defaultProps:{size:"md",variant:"outline"}}),r_=(0,D.gJ)("kbd-bg"),rA=(0,$.k0)({[r_.variable]:"colors.gray.100",_dark:{[r_.variable]:"colors.whiteAlpha.100"},bg:r_.reference,borderRadius:"md",borderWidth:"1px",borderBottomWidth:"3px",fontSize:"0.8em",fontWeight:"bold",lineHeight:"normal",px:"0.4em",whiteSpace:"nowrap"}),rj=(0,$.fj)({baseStyle:rA}),rE=(0,$.k0)({transitionProperty:"common",transitionDuration:"fast",transitionTimingFunction:"ease-out",cursor:"pointer",textDecoration:"none",outline:"none",color:"inherit",_hover:{textDecoration:"underline"},_focusVisible:{boxShadow:"outline"}}),r$=(0,$.fj)({baseStyle:rE}),{defineMultiStyleConfig:rT,definePartsStyle:rR}=(0,$.D)(g.keys),rz=(0,$.k0)({marginEnd:"2",display:"inline",verticalAlign:"text-bottom"}),rO=rR({icon:rz}),rL=rT({baseStyle:rO}),{defineMultiStyleConfig:rB,definePartsStyle:rM}=(0,$.D)(m.keys),rI=(0,D.gJ)("menu-bg"),rD=(0,D.gJ)("menu-shadow"),rF=(0,$.k0)({[rI.variable]:"#fff",[rD.variable]:"shadows.sm",_dark:{[rI.variable]:"colors.gray.700",[rD.variable]:"shadows.dark-lg"},color:"inherit",minW:"3xs",py:"2",zIndex:"dropdown",borderRadius:"md",borderWidth:"1px",bg:rI.reference,boxShadow:rD.reference}),rV=(0,$.k0)({py:"1.5",px:"3",transitionProperty:"background",transitionDuration:"ultra-fast",transitionTimingFunction:"ease-in",_focus:{[rI.variable]:"colors.gray.100",_dark:{[rI.variable]:"colors.whiteAlpha.100"}},_active:{[rI.variable]:"colors.gray.200",_dark:{[rI.variable]:"colors.whiteAlpha.200"}},_expanded:{[rI.variable]:"colors.gray.100",_dark:{[rI.variable]:"colors.whiteAlpha.100"}},_disabled:{opacity:.4,cursor:"not-allowed"},bg:rI.reference}),rN=(0,$.k0)({mx:4,my:2,fontWeight:"semibold",fontSize:"sm"}),rW=(0,$.k0)({display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:0}),rU=(0,$.k0)({opacity:.6}),rH=(0,$.k0)({border:0,borderBottom:"1px solid",borderColor:"inherit",my:"2",opacity:.6}),rJ=(0,$.k0)({transitionProperty:"common",transitionDuration:"normal"}),rq=rM({button:rJ,list:rF,item:rV,groupTitle:rN,icon:rW,command:rU,divider:rH}),rZ=rB({baseStyle:rq}),{defineMultiStyleConfig:rG,definePartsStyle:rY}=(0,$.D)(b.keys),rK=(0,D.gJ)("modal-bg"),rX=(0,D.gJ)("modal-shadow"),rQ=(0,$.k0)({bg:"blackAlpha.600",zIndex:"modal"}),r0=(0,$.k0)(e=>{let{isCentered:t,scrollBehavior:r}=e;return{display:"flex",zIndex:"modal",justifyContent:"center",alignItems:t?"center":"flex-start",overflow:"inside"===r?"hidden":"auto",overscrollBehaviorY:"none"}}),r1=(0,$.k0)(e=>{let{isCentered:t,scrollBehavior:r}=e;return{borderRadius:"md",color:"inherit",my:t?"auto":"16",mx:t?"auto":void 0,zIndex:"modal",maxH:"inside"===r?"calc(100% - 7.5rem)":void 0,[rK.variable]:"colors.white",[rX.variable]:"shadows.lg",_dark:{[rK.variable]:"colors.gray.700",[rX.variable]:"shadows.dark-lg"},bg:rK.reference,boxShadow:rX.reference}}),r2=(0,$.k0)({px:"6",py:"4",fontSize:"xl",fontWeight:"semibold"}),r5=(0,$.k0)({position:"absolute",top:"2",insetEnd:"3"}),r4=(0,$.k0)(e=>{let{scrollBehavior:t}=e;return{px:"6",py:"2",flex:"1",overflow:"inside"===t?"auto":void 0}}),r3=(0,$.k0)({px:"6",py:"4"}),r6=rY(e=>({overlay:rQ,dialogContainer:ek(r0,e),dialog:ek(r1,e),header:r2,closeButton:r5,body:ek(r4,e),footer:r3}));function r8(e){return"full"===e?rY({dialog:{maxW:"100vw",minH:"$100vh",my:"0",borderRadius:"0"}}):rY({dialog:{maxW:e}})}let r9={xs:r8("xs"),sm:r8("sm"),md:r8("md"),lg:r8("lg"),xl:r8("xl"),"2xl":r8("2xl"),"3xl":r8("3xl"),"4xl":r8("4xl"),"5xl":r8("5xl"),"6xl":r8("6xl"),full:r8("full")},r7=rG({baseStyle:r6,sizes:r9,defaultProps:{size:"md"}});var ne=r(79115);function nt(e){return(0,ne.Kn)(e)&&e.reference?e.reference:String(e)}let nr=(e,...t)=>t.map(nt).join(` ${e} `).replace(/calc/g,""),nn=(...e)=>`calc(${nr("+",...e)})`,ni=(...e)=>`calc(${nr("-",...e)})`,no=(...e)=>`calc(${nr("*",...e)})`,na=(...e)=>`calc(${nr("/",...e)})`,ns=e=>{let t=nt(e);return null==t||Number.isNaN(parseFloat(t))?no(t,-1):String(t).startsWith("-")?String(t).slice(1):`-${t}`},nl=Object.assign(e=>({add:(...t)=>nl(nn(e,...t)),subtract:(...t)=>nl(ni(e,...t)),multiply:(...t)=>nl(no(e,...t)),divide:(...t)=>nl(na(e,...t)),negate:()=>nl(ns(e)),toString:()=>e.toString()}),{add:nn,subtract:ni,multiply:no,divide:na,negate:ns}),nu={letterSpacings:{tighter:"-0.05em",tight:"-0.025em",normal:"0",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeights:{normal:"normal",none:1,shorter:1.25,short:1.375,base:1.5,tall:1.625,taller:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},fontWeights:{hairline:100,thin:200,light:300,normal:400,medium:500,semibold:600,bold:700,extrabold:800,black:900},fonts:{heading:'-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',body:'-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',mono:'SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace'},fontSizes:{"3xs":"0.45rem","2xs":"0.625rem",xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"}},{defineMultiStyleConfig:nc,definePartsStyle:nd}=(0,$.D)(y.keys),nh=ty("number-input-stepper-width"),np=ty("number-input-input-padding"),nf=nl(nh).add("0.5rem").toString(),ng=ty("number-input-bg"),nm=ty("number-input-color"),nb=ty("number-input-border-color"),ny=(0,$.k0)({[nh.variable]:"sizes.6",[np.variable]:nf}),nv=(0,$.k0)(e=>ek(rP.baseStyle,e)?.field??{}),nx=(0,$.k0)({width:nh.reference}),nk=(0,$.k0)({borderStart:"1px solid",borderStartColor:nb.reference,color:nm.reference,bg:ng.reference,[nm.variable]:"colors.chakra-body-text",[nb.variable]:"colors.chakra-border-color",_dark:{[nm.variable]:"colors.whiteAlpha.800",[nb.variable]:"colors.whiteAlpha.300"},_active:{[ng.variable]:"colors.gray.200",_dark:{[ng.variable]:"colors.whiteAlpha.300"}},_disabled:{opacity:.4,cursor:"not-allowed"}}),nS=nd(e=>({root:ny,field:ek(nv,e)??{},stepperGroup:nx,stepper:nk}));function nw(e){let t=rP.sizes?.[e],r={lg:"md",md:"md",sm:"sm",xs:"sm"},n=t.field?.fontSize??"md",i=nu.fontSizes[n];return nd({field:{...t.field,paddingInlineEnd:np.reference,verticalAlign:"top"},stepper:{fontSize:nl(i).multiply(.75).toString(),_first:{borderTopEndRadius:r[e]},_last:{borderBottomEndRadius:r[e],mt:"-1px",borderTopWidth:1}}})}let nC={xs:nw("xs"),sm:nw("sm"),md:nw("md"),lg:nw("lg")},nP=nc({baseStyle:nS,sizes:nC,variants:rP.variants,defaultProps:rP.defaultProps}),n_=(0,$.k0)({...rP.baseStyle?.field,textAlign:"center"}),nA={lg:(0,$.k0)({fontSize:"lg",w:12,h:12,borderRadius:"md"}),md:(0,$.k0)({fontSize:"md",w:10,h:10,borderRadius:"md"}),sm:(0,$.k0)({fontSize:"sm",w:8,h:8,borderRadius:"sm"}),xs:(0,$.k0)({fontSize:"xs",w:6,h:6,borderRadius:"sm"})},nj={outline:(0,$.k0)(e=>ek(rP.variants?.outline,e)?.field??{}),flushed:(0,$.k0)(e=>ek(rP.variants?.flushed,e)?.field??{}),filled:(0,$.k0)(e=>ek(rP.variants?.filled,e)?.field??{}),unstyled:rP.variants?.unstyled.field??{}},nE=(0,$.fj)({baseStyle:n_,sizes:nA,variants:nj,defaultProps:rP.defaultProps}),{defineMultiStyleConfig:n$,definePartsStyle:nT}=(0,$.D)(v.keys),nR=ty("popper-bg"),nz=ty("popper-arrow-bg"),nO=ty("popper-arrow-shadow-color"),nL=(0,$.k0)({zIndex:"popover"}),nB=(0,$.k0)({[nR.variable]:"colors.white",bg:nR.reference,[nz.variable]:nR.reference,[nO.variable]:"colors.gray.200",_dark:{[nR.variable]:"colors.gray.700",[nO.variable]:"colors.whiteAlpha.300"},width:"xs",border:"1px solid",borderColor:"inherit",borderRadius:"md",boxShadow:"sm",zIndex:"inherit",_focusVisible:{outline:0,boxShadow:"outline"}}),nM=(0,$.k0)({px:3,py:2,borderBottomWidth:"1px"}),nI=(0,$.k0)({px:3,py:2}),nD=(0,$.k0)({px:3,py:2,borderTopWidth:"1px"}),nF=(0,$.k0)({position:"absolute",borderRadius:"md",top:1,insetEnd:2,padding:2}),nV=nT({popper:nL,content:nB,header:nM,body:nI,footer:nD,closeButton:nF}),nN=n$({baseStyle:nV}),{defineMultiStyleConfig:nW,definePartsStyle:nU}=(0,$.D)(x.keys),nH=(0,$.k0)(e=>{let{colorScheme:t,theme:r,isIndeterminate:n,hasStripe:i}=e,o=eZ(eo(),eo("1rem","rgba(0,0,0,0.1)"))(e),a=eZ(`${t}.500`,`${t}.200`)(e),s=`linear-gradient(
    to right,
    transparent 0%,
    ${ee(r,a)} 50%,
    transparent 100%
  )`;return{...!n&&i&&o,...n?{bgImage:s}:{bgColor:a}}}),nJ=(0,$.k0)({lineHeight:"1",fontSize:"0.25em",fontWeight:"bold",color:"white"}),nq=(0,$.k0)(e=>({bg:eZ("gray.100","whiteAlpha.300")(e)})),nZ=(0,$.k0)(e=>({transitionProperty:"common",transitionDuration:"slow",...nH(e)})),nG=nU(e=>({label:nJ,filledTrack:nZ(e),track:nq(e)})),nY={xs:nU({track:{h:"1"}}),sm:nU({track:{h:"2"}}),md:nU({track:{h:"3"}}),lg:nU({track:{h:"4"}})},nK=nW({sizes:nY,baseStyle:nG,defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:nX,definePartsStyle:nQ}=(0,$.D)(k.keys),n0=(0,$.k0)(e=>{let t=ek(tm.baseStyle,e)?.control;return{...t,borderRadius:"full",_checked:{...t?._checked,_before:{content:'""',display:"inline-block",pos:"relative",w:"50%",h:"50%",borderRadius:"50%",bg:"currentColor"}}}}),n1=nQ(e=>({label:tm.baseStyle?.(e).label,container:tm.baseStyle?.(e).container,control:n0(e)})),n2={md:nQ({control:{w:"4",h:"4"},label:{fontSize:"md"}}),lg:nQ({control:{w:"5",h:"5"},label:{fontSize:"lg"}}),sm:nQ({control:{width:"3",height:"3"},label:{fontSize:"sm"}})},n5=nX({baseStyle:n1,sizes:n2,defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:n4,definePartsStyle:n3}=(0,$.D)(S.keys),n6=(0,D.gJ)("select-bg"),n8=(0,$.k0)({...rP.baseStyle?.field,appearance:"none",paddingBottom:"1px",lineHeight:"normal",bg:n6.reference,[n6.variable]:"colors.white",_dark:{[n6.variable]:"colors.gray.700"},"> option, > optgroup":{bg:n6.reference}}),n9=(0,$.k0)({width:"6",height:"100%",insetEnd:"2",position:"relative",color:"currentColor",fontSize:"xl",_disabled:{opacity:.5}}),n7=n3({field:n8,icon:n9}),ie=(0,$.k0)({paddingInlineEnd:"8"}),it={lg:{...rP.sizes?.lg,field:{...rP.sizes?.lg.field,...ie}},md:{...rP.sizes?.md,field:{...rP.sizes?.md.field,...ie}},sm:{...rP.sizes?.sm,field:{...rP.sizes?.sm.field,...ie}},xs:{...rP.sizes?.xs,field:{...rP.sizes?.xs.field,...ie},icon:{insetEnd:"1"}}},ir=n4({baseStyle:n7,sizes:it,variants:rP.variants,defaultProps:rP.defaultProps}),ii=(0,D.gJ)("skeleton-start-color"),io=(0,D.gJ)("skeleton-end-color"),ia=(0,$.k0)({[ii.variable]:"colors.gray.100",[io.variable]:"colors.gray.400",_dark:{[ii.variable]:"colors.gray.800",[io.variable]:"colors.gray.600"},background:ii.reference,borderColor:io.reference,opacity:.7,borderRadius:"sm"}),is=(0,$.fj)({baseStyle:ia}),il=(0,D.gJ)("skip-link-bg"),iu=(0,$.k0)({borderRadius:"md",fontWeight:"semibold",_focusVisible:{boxShadow:"outline",padding:"4",position:"fixed",top:"6",insetStart:"6",[il.variable]:"colors.white",_dark:{[il.variable]:"colors.gray.700"},bg:il.reference}}),ic=(0,$.fj)({baseStyle:iu});var id=r(33829);let{defineMultiStyleConfig:ih,definePartsStyle:ip}=(0,$.D)(w.keys),ig=(0,D.gJ)("slider-thumb-size"),im=(0,D.gJ)("slider-track-size"),ib=(0,D.gJ)("slider-bg"),iy=(0,$.k0)(e=>{let{orientation:t}=e;return{display:"inline-block",position:"relative",cursor:"pointer",_disabled:{opacity:.6,cursor:"default",pointerEvents:"none"},...eG({orientation:t,vertical:{h:"100%",px:(0,id.y)(ig.reference).divide(2).toString()},horizontal:{w:"100%",py:(0,id.y)(ig.reference).divide(2).toString()}})}}),iv=(0,$.k0)(e=>{let t=eG({orientation:e.orientation,horizontal:{h:im.reference},vertical:{w:im.reference}});return{...t,overflow:"hidden",borderRadius:"sm",[ib.variable]:"colors.gray.200",_dark:{[ib.variable]:"colors.whiteAlpha.200"},_disabled:{[ib.variable]:"colors.gray.300",_dark:{[ib.variable]:"colors.whiteAlpha.300"}},bg:ib.reference}}),ix=(0,$.k0)(e=>{let{orientation:t}=e,r=eG({orientation:t,vertical:{left:"50%",transform:"translateX(-50%)",_active:{transform:"translateX(-50%) scale(1.15)"}},horizontal:{top:"50%",transform:"translateY(-50%)",_active:{transform:"translateY(-50%) scale(1.15)"}}});return{...r,w:ig.reference,h:ig.reference,display:"flex",alignItems:"center",justifyContent:"center",position:"absolute",outline:0,zIndex:1,borderRadius:"full",bg:"white",boxShadow:"base",border:"1px solid",borderColor:"transparent",transitionProperty:"transform",transitionDuration:"normal",_focusVisible:{boxShadow:"outline"},_disabled:{bg:"gray.300"}}}),ik=(0,$.k0)(e=>{let{colorScheme:t}=e;return{width:"inherit",height:"inherit",[ib.variable]:`colors.${t}.500`,_dark:{[ib.variable]:`colors.${t}.200`},bg:ib.reference}}),iS=ip(e=>({container:iy(e),track:iv(e),thumb:ix(e),filledTrack:ik(e)})),iw=ip({container:{[ig.variable]:"sizes.4",[im.variable]:"sizes.1"}}),iC=ip({container:{[ig.variable]:"sizes.3.5",[im.variable]:"sizes.1"}}),iP=ip({container:{[ig.variable]:"sizes.2.5",[im.variable]:"sizes.0.5"}}),i_=ih({baseStyle:iS,sizes:{lg:iw,md:iC,sm:iP},defaultProps:{size:"md",colorScheme:"blue"}}),iA=ty("spinner-size"),ij=(0,$.k0)({width:[iA.reference],height:[iA.reference]}),iE={xs:(0,$.k0)({[iA.variable]:"sizes.3"}),sm:(0,$.k0)({[iA.variable]:"sizes.4"}),md:(0,$.k0)({[iA.variable]:"sizes.6"}),lg:(0,$.k0)({[iA.variable]:"sizes.8"}),xl:(0,$.k0)({[iA.variable]:"sizes.12"})},i$=(0,$.fj)({baseStyle:ij,sizes:iE,defaultProps:{size:"md"}}),{defineMultiStyleConfig:iT,definePartsStyle:iR}=(0,$.D)(C.keys),iz=(0,$.k0)({fontWeight:"medium"}),iO=(0,$.k0)({opacity:.8,marginBottom:"2"}),iL=(0,$.k0)({verticalAlign:"baseline",fontWeight:"semibold"}),iB=(0,$.k0)({marginEnd:1,w:"3.5",h:"3.5",verticalAlign:"middle"}),iM=iR({container:{},label:iz,helpText:iO,number:iL,icon:iB}),iI={md:iR({label:{fontSize:"sm"},helpText:{fontSize:"sm"},number:{fontSize:"2xl"}})},iD=iT({baseStyle:iM,sizes:iI,defaultProps:{size:"md"}}),{defineMultiStyleConfig:iF,definePartsStyle:iV}=(0,$.D)(["stepper","step","title","description","indicator","separator","icon","number"]),iN=(0,D.gJ)("stepper-indicator-size"),iW=(0,D.gJ)("stepper-icon-size"),iU=(0,D.gJ)("stepper-title-font-size"),iH=(0,D.gJ)("stepper-description-font-size"),iJ=(0,D.gJ)("stepper-accent-color"),iq=iV(({colorScheme:e})=>({stepper:{display:"flex",justifyContent:"space-between",gap:"4","&[data-orientation=vertical]":{flexDirection:"column",alignItems:"flex-start"},"&[data-orientation=horizontal]":{flexDirection:"row",alignItems:"center"},[iJ.variable]:`colors.${e}.500`,_dark:{[iJ.variable]:`colors.${e}.200`}},title:{fontSize:iU.reference,fontWeight:"medium"},description:{fontSize:iH.reference,color:"chakra-subtle-text"},number:{fontSize:iU.reference},step:{flexShrink:0,position:"relative",display:"flex",gap:"2","&[data-orientation=horizontal]":{alignItems:"center"},flex:"1","&:last-of-type:not([data-stretch])":{flex:"initial"}},icon:{flexShrink:0,width:iW.reference,height:iW.reference},indicator:{flexShrink:0,borderRadius:"full",width:iN.reference,height:iN.reference,display:"flex",justifyContent:"center",alignItems:"center","&[data-status=active]":{borderWidth:"2px",borderColor:iJ.reference},"&[data-status=complete]":{bg:iJ.reference,color:"chakra-inverse-text"},"&[data-status=incomplete]":{borderWidth:"2px"}},separator:{bg:"chakra-border-color",flex:"1","&[data-status=complete]":{bg:iJ.reference},"&[data-orientation=horizontal]":{width:"100%",height:"2px",marginStart:"2"},"&[data-orientation=vertical]":{width:"2px",position:"absolute",height:"100%",maxHeight:`calc(100% - ${iN.reference} - 8px)`,top:`calc(${iN.reference} + 4px)`,insetStart:`calc(${iN.reference} / 2 - 1px)`}}})),iZ=iF({baseStyle:iq,sizes:{xs:iV({stepper:{[iN.variable]:"sizes.4",[iW.variable]:"sizes.3",[iU.variable]:"fontSizes.xs",[iH.variable]:"fontSizes.xs"}}),sm:iV({stepper:{[iN.variable]:"sizes.6",[iW.variable]:"sizes.4",[iU.variable]:"fontSizes.sm",[iH.variable]:"fontSizes.xs"}}),md:iV({stepper:{[iN.variable]:"sizes.8",[iW.variable]:"sizes.5",[iU.variable]:"fontSizes.md",[iH.variable]:"fontSizes.sm"}}),lg:iV({stepper:{[iN.variable]:"sizes.10",[iW.variable]:"sizes.6",[iU.variable]:"fontSizes.lg",[iH.variable]:"fontSizes.md"}})},defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:iG,definePartsStyle:iY}=(0,$.D)(P.keys),iK=ty("switch-track-width"),iX=ty("switch-track-height"),iQ=ty("switch-track-diff"),i0=nl.subtract(iK,iX),i1=ty("switch-thumb-x"),i2=ty("switch-bg"),i5=(0,$.k0)(e=>{let{colorScheme:t}=e;return{borderRadius:"full",p:"0.5",width:[iK.reference],height:[iX.reference],transitionProperty:"common",transitionDuration:"fast",[i2.variable]:"colors.gray.300",_dark:{[i2.variable]:"colors.whiteAlpha.400"},_focusVisible:{boxShadow:"outline"},_disabled:{opacity:.4,cursor:"not-allowed"},_checked:{[i2.variable]:`colors.${t}.500`,_dark:{[i2.variable]:`colors.${t}.200`}},bg:i2.reference}}),i4=(0,$.k0)({bg:"white",transitionProperty:"transform",transitionDuration:"normal",borderRadius:"inherit",width:[iX.reference],height:[iX.reference],_checked:{transform:`translateX(${i1.reference})`}}),i3=iY(e=>({container:{[iQ.variable]:i0,[i1.variable]:iQ.reference,_rtl:{[i1.variable]:nl(iQ).negate().toString()}},track:i5(e),thumb:i4})),i6={sm:iY({container:{[iK.variable]:"1.375rem",[iX.variable]:"sizes.3"}}),md:iY({container:{[iK.variable]:"1.875rem",[iX.variable]:"sizes.4"}}),lg:iY({container:{[iK.variable]:"2.875rem",[iX.variable]:"sizes.6"}})},i8=iG({baseStyle:i3,sizes:i6,defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:i9,definePartsStyle:i7}=(0,$.D)(_.keys),oe=i7({table:{fontVariantNumeric:"lining-nums tabular-nums",borderCollapse:"collapse",width:"full"},th:{fontFamily:"heading",fontWeight:"bold",textTransform:"uppercase",letterSpacing:"wider",textAlign:"start"},td:{textAlign:"start"},caption:{mt:4,fontFamily:"heading",textAlign:"center",fontWeight:"medium"}}),ot=(0,$.k0)({"&[data-is-numeric=true]":{textAlign:"end"}}),or=i7(e=>{let{colorScheme:t}=e;return{th:{color:eZ("gray.600","gray.400")(e),borderBottom:"1px",borderColor:eZ(`${t}.100`,`${t}.700`)(e),...ot},td:{borderBottom:"1px",borderColor:eZ(`${t}.100`,`${t}.700`)(e),...ot},caption:{color:eZ("gray.600","gray.100")(e)},tfoot:{tr:{"&:last-of-type":{th:{borderBottomWidth:0}}}}}}),on=i7(e=>{let{colorScheme:t}=e;return{th:{color:eZ("gray.600","gray.400")(e),borderBottom:"1px",borderColor:eZ(`${t}.100`,`${t}.700`)(e),...ot},td:{borderBottom:"1px",borderColor:eZ(`${t}.100`,`${t}.700`)(e),...ot},caption:{color:eZ("gray.600","gray.100")(e)},tbody:{tr:{"&:nth-of-type(odd)":{"th, td":{borderBottomWidth:"1px",borderColor:eZ(`${t}.100`,`${t}.700`)(e)},td:{background:eZ(`${t}.100`,`${t}.700`)(e)}}}},tfoot:{tr:{"&:last-of-type":{th:{borderBottomWidth:0}}}}}}),oi={simple:or,striped:on,unstyled:(0,$.k0)({})},oo={sm:i7({th:{px:"4",py:"1",lineHeight:"4",fontSize:"xs"},td:{px:"4",py:"2",fontSize:"sm",lineHeight:"4"},caption:{px:"4",py:"2",fontSize:"xs"}}),md:i7({th:{px:"6",py:"3",lineHeight:"4",fontSize:"xs"},td:{px:"6",py:"4",lineHeight:"5"},caption:{px:"6",py:"2",fontSize:"sm"}}),lg:i7({th:{px:"8",py:"4",lineHeight:"5",fontSize:"sm"},td:{px:"8",py:"5",lineHeight:"6"},caption:{px:"6",py:"2",fontSize:"md"}})},oa=i9({baseStyle:oe,variants:oi,sizes:oo,defaultProps:{variant:"simple",size:"md",colorScheme:"gray"}}),os=(0,D.gJ)("tabs-color"),ol=(0,D.gJ)("tabs-bg"),ou=(0,D.gJ)("tabs-border-color"),{defineMultiStyleConfig:oc,definePartsStyle:od}=(0,$.D)(A.keys),oh=(0,$.k0)(e=>{let{orientation:t}=e;return{display:"vertical"===t?"flex":"block"}}),op=(0,$.k0)(e=>{let{isFitted:t}=e;return{flex:t?1:void 0,transitionProperty:"common",transitionDuration:"normal",_focusVisible:{zIndex:1,boxShadow:"outline"},_disabled:{cursor:"not-allowed",opacity:.4}}}),of=(0,$.k0)(e=>{let{align:t="start",orientation:r}=e;return{justifyContent:({end:"flex-end",center:"center",start:"flex-start"})[t],flexDirection:"vertical"===r?"column":"row"}}),og=(0,$.k0)({p:4}),om=od(e=>({root:oh(e),tab:op(e),tablist:of(e),tabpanel:og})),ob={sm:od({tab:{py:1,px:4,fontSize:"sm"}}),md:od({tab:{fontSize:"md",py:2,px:4}}),lg:od({tab:{fontSize:"lg",py:3,px:4}})},oy=od(e=>{let{colorScheme:t,orientation:r}=e,n="vertical"===r,i=n?"borderStart":"borderBottom";return{tablist:{[i]:"2px solid",borderColor:"inherit"},tab:{[i]:"2px solid",borderColor:"transparent",[n?"marginStart":"marginBottom"]:"-2px",_selected:{[os.variable]:`colors.${t}.600`,_dark:{[os.variable]:`colors.${t}.300`},borderColor:"currentColor"},_active:{[ol.variable]:"colors.gray.200",_dark:{[ol.variable]:"colors.whiteAlpha.300"}},_disabled:{_active:{bg:"none"}},color:os.reference,bg:ol.reference}}}),ov=od(e=>{let{colorScheme:t}=e;return{tab:{borderTopRadius:"md",border:"1px solid",borderColor:"transparent",mb:"-1px",[ou.variable]:"transparent",_selected:{[os.variable]:`colors.${t}.600`,[ou.variable]:"colors.white",_dark:{[os.variable]:`colors.${t}.300`,[ou.variable]:"colors.gray.800"},borderColor:"inherit",borderBottomColor:ou.reference},color:os.reference},tablist:{mb:"-1px",borderBottom:"1px solid",borderColor:"inherit"}}}),ox=od(e=>{let{colorScheme:t}=e;return{tab:{border:"1px solid",borderColor:"inherit",[ol.variable]:"colors.gray.50",_dark:{[ol.variable]:"colors.whiteAlpha.50"},mb:"-1px",_notLast:{marginEnd:"-1px"},_selected:{[ol.variable]:"colors.white",[os.variable]:`colors.${t}.600`,_dark:{[ol.variable]:"colors.gray.800",[os.variable]:`colors.${t}.300`},borderColor:"inherit",borderTopColor:"currentColor",borderBottomColor:"transparent"},color:os.reference,bg:ol.reference},tablist:{mb:"-1px",borderBottom:"1px solid",borderColor:"inherit"}}}),ok=od(e=>{let{colorScheme:t,theme:r}=e;return{tab:{borderRadius:"full",fontWeight:"semibold",color:"gray.600",_selected:{color:ee(r,`${t}.700`),bg:ee(r,`${t}.100`)}}}}),oS=od(e=>{let{colorScheme:t}=e;return{tab:{borderRadius:"full",fontWeight:"semibold",[os.variable]:"colors.gray.600",_dark:{[os.variable]:"inherit"},_selected:{[os.variable]:"colors.white",[ol.variable]:`colors.${t}.600`,_dark:{[os.variable]:"colors.gray.800",[ol.variable]:`colors.${t}.300`}},color:os.reference,bg:ol.reference}}}),ow=od({}),oC=oc({baseStyle:om,sizes:ob,variants:{line:oy,enclosed:ov,"enclosed-colored":ox,"soft-rounded":ok,"solid-rounded":oS,unstyled:ow},defaultProps:{size:"md",variant:"line",colorScheme:"blue"}}),{defineMultiStyleConfig:oP,definePartsStyle:o_}=(0,$.D)(j.keys),oA=(0,D.gJ)("tag-bg"),oj=(0,D.gJ)("tag-color"),oE=(0,D.gJ)("tag-shadow"),o$=(0,D.gJ)("tag-min-height"),oT=(0,D.gJ)("tag-min-width"),oR=(0,D.gJ)("tag-font-size"),oz=(0,D.gJ)("tag-padding-inline"),oO=(0,$.k0)({fontWeight:"medium",lineHeight:1.2,outline:0,[oj.variable]:eB.color.reference,[oA.variable]:eB.bg.reference,[oE.variable]:eB.shadow.reference,color:oj.reference,bg:oA.reference,boxShadow:oE.reference,borderRadius:"md",minH:o$.reference,minW:oT.reference,fontSize:oR.reference,px:oz.reference,_focusVisible:{[oE.variable]:"shadows.outline"}}),oL=(0,$.k0)({lineHeight:1.2,overflow:"visible"}),oB=(0,$.k0)({fontSize:"lg",w:"5",h:"5",transitionProperty:"common",transitionDuration:"normal",borderRadius:"full",marginStart:"1.5",marginEnd:"-1",opacity:.5,_disabled:{opacity:.4},_focusVisible:{boxShadow:"outline",bg:"rgba(0, 0, 0, 0.14)"},_hover:{opacity:.8},_active:{opacity:1}}),oM=o_({container:oO,label:oL,closeButton:oB}),oI={sm:o_({container:{[o$.variable]:"sizes.5",[oT.variable]:"sizes.5",[oR.variable]:"fontSizes.xs",[oz.variable]:"space.2"},closeButton:{marginEnd:"-2px",marginStart:"0.35rem"}}),md:o_({container:{[o$.variable]:"sizes.6",[oT.variable]:"sizes.6",[oR.variable]:"fontSizes.sm",[oz.variable]:"space.2"}}),lg:o_({container:{[o$.variable]:"sizes.8",[oT.variable]:"sizes.8",[oR.variable]:"fontSizes.md",[oz.variable]:"space.3"}})},oD={subtle:o_(e=>({container:eV.variants?.subtle(e)})),solid:o_(e=>({container:eV.variants?.solid(e)})),outline:o_(e=>({container:eV.variants?.outline(e)}))},oF=oP({variants:oD,baseStyle:oM,sizes:oI,defaultProps:{size:"md",variant:"subtle",colorScheme:"gray"}}),oV=(0,$.k0)({...rP.baseStyle?.field,paddingY:"2",minHeight:"20",lineHeight:"short",verticalAlign:"top"}),oN={outline:(0,$.k0)(e=>rP.variants?.outline(e).field??{}),flushed:(0,$.k0)(e=>rP.variants?.flushed(e).field??{}),filled:(0,$.k0)(e=>rP.variants?.filled(e).field??{}),unstyled:rP.variants?.unstyled.field??{}},oW={xs:rP.sizes?.xs.field??{},sm:rP.sizes?.sm.field??{},md:rP.sizes?.md.field??{},lg:rP.sizes?.lg.field??{}},oU=(0,$.fj)({baseStyle:oV,sizes:oW,variants:oN,defaultProps:{size:"md",variant:"outline"}}),oH=ty("tooltip-bg"),oJ=ty("tooltip-fg"),oq=ty("popper-arrow-bg"),oZ=(0,$.k0)({bg:oH.reference,color:oJ.reference,[oH.variable]:"colors.gray.700",[oJ.variable]:"colors.whiteAlpha.900",_dark:{[oH.variable]:"colors.gray.300",[oJ.variable]:"colors.gray.900"},[oq.variable]:oH.reference,px:"2",py:"0.5",borderRadius:"sm",fontWeight:"medium",fontSize:"sm",boxShadow:"md",maxW:"xs",zIndex:"tooltip"}),oG=(0,$.fj)({baseStyle:oZ}),oY={breakpoints:{base:"0em",sm:"30em",md:"48em",lg:"62em",xl:"80em","2xl":"96em"},zIndices:{hide:-1,auto:"auto",base:0,docked:10,dropdown:1e3,sticky:1100,banner:1200,overlay:1300,modal:1400,popover:1500,skipLink:1600,toast:1700,tooltip:1800},radii:{none:"0",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},blur:{none:0,sm:"4px",base:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},colors:{transparent:"transparent",current:"currentColor",black:"#000000",white:"#FFFFFF",whiteAlpha:{50:"rgba(255, 255, 255, 0.04)",100:"rgba(255, 255, 255, 0.06)",200:"rgba(255, 255, 255, 0.08)",300:"rgba(255, 255, 255, 0.16)",400:"rgba(255, 255, 255, 0.24)",500:"rgba(255, 255, 255, 0.36)",600:"rgba(255, 255, 255, 0.48)",700:"rgba(255, 255, 255, 0.64)",800:"rgba(255, 255, 255, 0.80)",900:"rgba(255, 255, 255, 0.92)"},blackAlpha:{50:"rgba(0, 0, 0, 0.04)",100:"rgba(0, 0, 0, 0.06)",200:"rgba(0, 0, 0, 0.08)",300:"rgba(0, 0, 0, 0.16)",400:"rgba(0, 0, 0, 0.24)",500:"rgba(0, 0, 0, 0.36)",600:"rgba(0, 0, 0, 0.48)",700:"rgba(0, 0, 0, 0.64)",800:"rgba(0, 0, 0, 0.80)",900:"rgba(0, 0, 0, 0.92)"},gray:{50:"#F7FAFC",100:"#EDF2F7",200:"#E2E8F0",300:"#CBD5E0",400:"#A0AEC0",500:"#718096",600:"#4A5568",700:"#2D3748",800:"#1A202C",900:"#171923"},red:{50:"#FFF5F5",100:"#FED7D7",200:"#FEB2B2",300:"#FC8181",400:"#F56565",500:"#E53E3E",600:"#C53030",700:"#9B2C2C",800:"#822727",900:"#63171B"},orange:{50:"#FFFAF0",100:"#FEEBC8",200:"#FBD38D",300:"#F6AD55",400:"#ED8936",500:"#DD6B20",600:"#C05621",700:"#9C4221",800:"#7B341E",900:"#652B19"},yellow:{50:"#FFFFF0",100:"#FEFCBF",200:"#FAF089",300:"#F6E05E",400:"#ECC94B",500:"#D69E2E",600:"#B7791F",700:"#975A16",800:"#744210",900:"#5F370E"},green:{50:"#F0FFF4",100:"#C6F6D5",200:"#9AE6B4",300:"#68D391",400:"#48BB78",500:"#38A169",600:"#2F855A",700:"#276749",800:"#22543D",900:"#1C4532"},teal:{50:"#E6FFFA",100:"#B2F5EA",200:"#81E6D9",300:"#4FD1C5",400:"#38B2AC",500:"#319795",600:"#2C7A7B",700:"#285E61",800:"#234E52",900:"#1D4044"},blue:{50:"#ebf8ff",100:"#bee3f8",200:"#90cdf4",300:"#63b3ed",400:"#4299e1",500:"#3182ce",600:"#2b6cb0",700:"#2c5282",800:"#2a4365",900:"#1A365D"},cyan:{50:"#EDFDFD",100:"#C4F1F9",200:"#9DECF9",300:"#76E4F7",400:"#0BC5EA",500:"#00B5D8",600:"#00A3C4",700:"#0987A0",800:"#086F83",900:"#065666"},purple:{50:"#FAF5FF",100:"#E9D8FD",200:"#D6BCFA",300:"#B794F4",400:"#9F7AEA",500:"#805AD5",600:"#6B46C1",700:"#553C9A",800:"#44337A",900:"#322659"},pink:{50:"#FFF5F7",100:"#FED7E2",200:"#FBB6CE",300:"#F687B3",400:"#ED64A6",500:"#D53F8C",600:"#B83280",700:"#97266D",800:"#702459",900:"#521B41"}},...nu,sizes:ev,shadows:{xs:"0 0 0 1px rgba(0, 0, 0, 0.05)",sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",base:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)",outline:"0 0 0 3px rgba(66, 153, 225, 0.6)",inner:"inset 0 2px 4px 0 rgba(0,0,0,0.06)",none:"none","dark-lg":"rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px, rgba(0, 0, 0, 0.4) 0px 15px 40px"},space:ey,borders:{none:0,"1px":"1px solid","2px":"2px solid","4px":"4px solid","8px":"8px solid"},transition:{property:{common:"background-color, border-color, color, fill, stroke, opacity, box-shadow, transform",colors:"background-color, border-color, color, fill, stroke",dimensions:"width, height",position:"left, right, top, bottom",background:"background-color, background-image, background-position"},easing:{"ease-in":"cubic-bezier(0.4, 0, 1, 1)","ease-out":"cubic-bezier(0, 0, 0.2, 1)","ease-in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},duration:{"ultra-fast":"50ms",faster:"100ms",fast:"150ms",normal:"200ms",slow:"300ms",slower:"400ms","ultra-slow":"500ms"}}},oK={semanticTokens:{colors:{"chakra-body-text":{_light:"gray.800",_dark:"whiteAlpha.900"},"chakra-body-bg":{_light:"white",_dark:"gray.800"},"chakra-border-color":{_light:"gray.200",_dark:"whiteAlpha.300"},"chakra-inverse-text":{_light:"white",_dark:"gray.800"},"chakra-subtle-bg":{_light:"gray.100",_dark:"gray.700"},"chakra-subtle-text":{_light:"gray.600",_dark:"gray.400"},"chakra-placeholder-color":{_light:"gray.500",_dark:"whiteAlpha.400"}}},direction:"ltr",...oY,components:{Accordion:I,Alert:eb,Avatar:eL,Badge:eV,Breadcrumb:eq,Button:e4,Checkbox:tm,CloseButton:tw,Code:tA,Container:tE,Divider:tz,Drawer:tZ,Editable:t1,Form:t9,FormError:ro,FormLabel:rs,Heading:rc,Input:rP,Kbd:rj,Link:r$,List:rL,Menu:rZ,Modal:r7,NumberInput:nP,PinInput:nE,Popover:nN,Progress:nK,Radio:n5,Select:ir,Skeleton:is,SkipLink:ic,Slider:i_,Spinner:i$,Stat:iD,Switch:i8,Table:oa,Tabs:oC,Tag:oF,Textarea:oU,Tooltip:oG,Card:ta,Stepper:iZ},styles:{global:{body:{fontFamily:"body",color:"chakra-body-text",bg:"chakra-body-bg",transitionProperty:"background-color",transitionDuration:"normal",lineHeight:"base"},"*::placeholder":{color:"chakra-placeholder-color"},"*, *::before, &::after":{borderColor:"chakra-border-color"}}},config:{useSystemColorMode:!1,initialColorMode:"light",cssVarPrefix:"chakra"}};({...oY});var oX=r(21326),oQ=r(20442),o0=r(67294),o1=r(31046);let o2={light:"chakra-ui-light",dark:"chakra-ui-dark"},o5="chakra-ui-color-mode",o4={ssr:!1,type:"localStorage",get(e){let t;if(!globalThis?.document)return e;try{t=localStorage.getItem(o5)||e}catch(e){}return t||e},set(e){try{localStorage.setItem(o5,e)}catch(e){}}},o3=()=>{},o6=(0,oX.jU)()?o0.useLayoutEffect:o0.useEffect;function o8(e,t){return"cookie"===e.type&&e.ssr?e.get(t):t}let o9=function(e){let{value:t,children:r,options:{useSystemColorMode:i,initialColorMode:o,disableTransitionOnChange:a}={},colorModeManager:s=o4}=e,l=(0,oQ._)(),u="dark"===o?"dark":"light",[c,d]=(0,o0.useState)(()=>o8(s,u)),[h,p]=(0,o0.useState)(()=>o8(s)),{getSystemTheme:f,setClassName:g,setDataset:m,addListener:b}=(0,o0.useMemo)(()=>(function(e={}){let{preventTransition:t=!0,nonce:r}=e,n={setDataset:e=>{let r=t?n.preventTransition():void 0;document.documentElement.dataset.theme=e,document.documentElement.style.colorScheme=e,r?.()},setClassName(e){document.body.classList.add(e?o2.dark:o2.light),document.body.classList.remove(e?o2.light:o2.dark)},query:()=>window.matchMedia("(prefers-color-scheme: dark)"),getSystemTheme(e){let t=n.query().matches??"dark"===e;return t?"dark":"light"},addListener(e){let t=n.query(),r=t=>{e(t.matches?"dark":"light")};return"function"==typeof t.addListener?t.addListener(r):t.addEventListener("change",r),()=>{"function"==typeof t.removeListener?t.removeListener(r):t.removeEventListener("change",r)}},preventTransition(){let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),void 0!==r&&(e.nonce=r),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),requestAnimationFrame(()=>{requestAnimationFrame(()=>{document.head.removeChild(e)})})}}};return n})({preventTransition:a,nonce:l?.nonce}),[a,l?.nonce]),y="system"!==o||c?c:h,v=(0,o0.useCallback)(e=>{let t="system"===e?f():e;d(t),g("dark"===t),m(t),s.set(t)},[s,f,g,m]);o6(()=>{"system"===o&&p(f())},[]),(0,o0.useEffect)(()=>{let e=s.get();if(e){v(e);return}if("system"===o){v("system");return}v(u)},[s,u,o,v]);let x=(0,o0.useCallback)(()=>{v("dark"===y?"light":"dark")},[y,v]);(0,o0.useEffect)(()=>{if(i)return b(v)},[i,b,v]);let k=(0,o0.useMemo)(()=>({colorMode:t??y,toggleColorMode:t?o3:x,setColorMode:t?o3:v,forced:void 0!==t}),[y,x,v,t]);return(0,n.jsx)(o1.kc.Provider,{value:k,children:r})};o9.displayName="ColorModeProvider";var o7=r(70917);let ae=String.raw,at=ae`
  :root,
  :host {
    --chakra-vh: 100vh;
  }

  @supports (height: -webkit-fill-available) {
    :root,
    :host {
      --chakra-vh: -webkit-fill-available;
    }
  }

  @supports (height: -moz-fill-available) {
    :root,
    :host {
      --chakra-vh: -moz-fill-available;
    }
  }

  @supports (height: 100dvh) {
    :root,
    :host {
      --chakra-vh: 100dvh;
    }
  }
`,ar=()=>(0,n.jsx)(o7.xB,{styles:at}),an=({scope:e=""})=>(0,n.jsx)(o7.xB,{styles:ae`
      html {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        font-family: system-ui, sans-serif;
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
        touch-action: manipulation;
      }

      body {
        position: relative;
        min-height: 100%;
        margin: 0;
        font-feature-settings: "kern";
      }

      ${e} :where(*, *::before, *::after) {
        border-width: 0;
        border-style: solid;
        box-sizing: border-box;
        word-wrap: break-word;
      }

      main {
        display: block;
      }

      ${e} hr {
        border-top-width: 1px;
        box-sizing: content-box;
        height: 0;
        overflow: visible;
      }

      ${e} :where(pre, code, kbd,samp) {
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
        font-size: 1em;
      }

      ${e} a {
        background-color: transparent;
        color: inherit;
        text-decoration: inherit;
      }

      ${e} abbr[title] {
        border-bottom: none;
        text-decoration: underline;
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
      }

      ${e} :where(b, strong) {
        font-weight: bold;
      }

      ${e} small {
        font-size: 80%;
      }

      ${e} :where(sub,sup) {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }

      ${e} sub {
        bottom: -0.25em;
      }

      ${e} sup {
        top: -0.5em;
      }

      ${e} img {
        border-style: none;
      }

      ${e} :where(button, input, optgroup, select, textarea) {
        font-family: inherit;
        font-size: 100%;
        line-height: 1.15;
        margin: 0;
      }

      ${e} :where(button, input) {
        overflow: visible;
      }

      ${e} :where(button, select) {
        text-transform: none;
      }

      ${e} :where(
          button::-moz-focus-inner,
          [type="button"]::-moz-focus-inner,
          [type="reset"]::-moz-focus-inner,
          [type="submit"]::-moz-focus-inner
        ) {
        border-style: none;
        padding: 0;
      }

      ${e} fieldset {
        padding: 0.35em 0.75em 0.625em;
      }

      ${e} legend {
        box-sizing: border-box;
        color: inherit;
        display: table;
        max-width: 100%;
        padding: 0;
        white-space: normal;
      }

      ${e} progress {
        vertical-align: baseline;
      }

      ${e} textarea {
        overflow: auto;
      }

      ${e} :where([type="checkbox"], [type="radio"]) {
        box-sizing: border-box;
        padding: 0;
      }

      ${e} input[type="number"]::-webkit-inner-spin-button,
      ${e} input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none !important;
      }

      ${e} input[type="number"] {
        -moz-appearance: textfield;
      }

      ${e} input[type="search"] {
        -webkit-appearance: textfield;
        outline-offset: -2px;
      }

      ${e} input[type="search"]::-webkit-search-decoration {
        -webkit-appearance: none !important;
      }

      ${e} ::-webkit-file-upload-button {
        -webkit-appearance: button;
        font: inherit;
      }

      ${e} details {
        display: block;
      }

      ${e} summary {
        display: list-item;
      }

      template {
        display: none;
      }

      [hidden] {
        display: none !important;
      }

      ${e} :where(
          blockquote,
          dl,
          dd,
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          hr,
          figure,
          p,
          pre
        ) {
        margin: 0;
      }

      ${e} button {
        background: transparent;
        padding: 0;
      }

      ${e} fieldset {
        margin: 0;
        padding: 0;
      }

      ${e} :where(ol, ul) {
        margin: 0;
        padding: 0;
      }

      ${e} textarea {
        resize: vertical;
      }

      ${e} :where(button, [role="button"]) {
        cursor: pointer;
      }

      ${e} button::-moz-focus-inner {
        border: 0 !important;
      }

      ${e} table {
        border-collapse: collapse;
      }

      ${e} :where(h1, h2, h3, h4, h5, h6) {
        font-size: inherit;
        font-weight: inherit;
      }

      ${e} :where(button, input, optgroup, select, textarea) {
        padding: 0;
        line-height: inherit;
        color: inherit;
      }

      ${e} :where(img, svg, video, canvas, audio, iframe, embed, object) {
        display: block;
      }

      ${e} :where(img, video) {
        max-width: 100%;
        height: auto;
      }

      [data-js-focus-visible]
        :focus:not([data-focus-visible-added]):not(
          [data-focus-visible-disabled]
        ) {
        outline: none;
        box-shadow: none;
      }

      ${e} select::-ms-expand {
        display: none;
      }

      ${at}
    `});var ai=r(66140),ao=r(28215),aa=r(28497);let as=e=>{let{children:t,colorModeManager:r,portalZIndex:i,resetScope:o,resetCSS:a=!0,theme:s={},environment:l,cssVarsRoot:u,disableEnvironment:c,disableGlobalStyle:d}=e,h=(0,n.jsx)(aa.u,{environment:l,disabled:c,children:t});return(0,n.jsx)(ai.f6,{theme:s,cssVarsRoot:u,children:(0,n.jsxs)(o9,{colorModeManager:r,options:s.config,children:[a?(0,n.jsx)(an,{scope:o}):(0,n.jsx)(ar,{}),!d&&(0,n.jsx)(ai.ZL,{}),i?(0,n.jsx)(ao.h,{zIndex:i,children:h}):h]})})};var al=r(88559);let au=function({children:e,theme:t=oK,toastOptions:r,...i}){return(0,n.jsxs)(as,{theme:t,...i,children:[(0,n.jsx)(al.Qi,{value:r?.defaultOptions,children:e}),(0,n.jsx)(al.VW,{...r})]})};var ac=r(61951);r(5759),r(96888),r(17505),r(41198),r(90508);var ad=r(31837),ah=r(9008),ap=r.n(ah),af=r(11752),ag=r.n(af),am=r(11163),ab=r(4298),ay=r.n(ab);let av="G-RYTCZEQK0W",ax=e=>{window.gtag("config",av,{page_path:e})};var ak=(0,ad.Jc)(function(e){let{Component:t,pageProps:r}=e,{t:i}=(0,ad.$G)("common"),{basePath:o}=ag()().publicRuntimeConfig,a=(0,am.useRouter)(),s=r.metaTitle||i("meta.title"),l=r.metaDescription||i("meta.description"),u=r.metaImage||"https://www.funblocks.net/assets/img/portfolio/fullsize/ai_insights.png",c=r.metaUrl||"https://www.funblocks.net".concat(o);return(0,o0.useEffect)(()=>{let e=e=>{ax(e)};return a.events.on("routeChangeComplete",e),()=>{a.events.off("routeChangeComplete",e)}},[a.events]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(ay(),{strategy:"afterInteractive",src:"https://www.googletagmanager.com/gtag/js?id=".concat(av)}),(0,n.jsx)(ay(),{id:"gtag-init",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:"\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', '".concat(av,"', {\n              page_path: window.location.pathname,\n            });\n          ")}}),(0,n.jsxs)(ap(),{children:[(0,n.jsx)("meta",{charSet:"utf-8"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("link",{rel:"icon",href:"".concat(o,"/icon.png")}),(0,n.jsx)("meta",{name:"keywords",content:i("meta.keywords")}),(0,n.jsx)("title",{children:s},"title"),(0,n.jsx)("meta",{name:"description",content:l},"description"),(0,n.jsx)("meta",{property:"og:title",content:s},"og:title"),(0,n.jsx)("meta",{property:"og:description",content:l},"og:description"),(0,n.jsx)("meta",{property:"og:image",content:u},"og:image"),(0,n.jsx)("meta",{property:"og:url",content:c},"og:url"),(0,n.jsx)("meta",{property:"og:type",content:"website"}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary_large_image"},"twitter:card"),(0,n.jsx)("meta",{name:"twitter:title",content:s},"twitter:title"),(0,n.jsx)("meta",{name:"twitter:description",content:l},"twitter:description"),(0,n.jsx)("meta",{name:"twitter:image",content:u},"twitter:image"),(0,n.jsx)("link",{rel:"canonical",href:c},"canonical")]}),(0,n.jsx)(au,{children:(0,n.jsx)(ac.H,{children:(0,n.jsx)(t,{...r})})})]})})},17505:function(){},5759:function(){},96888:function(){},41198:function(){},90508:function(){},9008:function(e,t,r){e.exports=r(5443)},11163:function(e,t,r){e.exports=r(90387)},4298:function(e,t,r){e.exports=r(20699)},69590:function(e){var t="undefined"!=typeof Element,r="function"==typeof Map,n="function"==typeof Set,i="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;e.exports=function(e,o){try{return function e(o,a){if(o===a)return!0;if(o&&a&&"object"==typeof o&&"object"==typeof a){var s,l,u,c;if(o.constructor!==a.constructor)return!1;if(Array.isArray(o)){if((s=o.length)!=a.length)return!1;for(l=s;0!=l--;)if(!e(o[l],a[l]))return!1;return!0}if(r&&o instanceof Map&&a instanceof Map){if(o.size!==a.size)return!1;for(c=o.entries();!(l=c.next()).done;)if(!a.has(l.value[0]))return!1;for(c=o.entries();!(l=c.next()).done;)if(!e(l.value[1],a.get(l.value[0])))return!1;return!0}if(n&&o instanceof Set&&a instanceof Set){if(o.size!==a.size)return!1;for(c=o.entries();!(l=c.next()).done;)if(!a.has(l.value[0]))return!1;return!0}if(i&&ArrayBuffer.isView(o)&&ArrayBuffer.isView(a)){if((s=o.length)!=a.length)return!1;for(l=s;0!=l--;)if(o[l]!==a[l])return!1;return!0}if(o.constructor===RegExp)return o.source===a.source&&o.flags===a.flags;if(o.valueOf!==Object.prototype.valueOf&&"function"==typeof o.valueOf&&"function"==typeof a.valueOf)return o.valueOf()===a.valueOf();if(o.toString!==Object.prototype.toString&&"function"==typeof o.toString&&"function"==typeof a.toString)return o.toString()===a.toString();if((s=(u=Object.keys(o)).length)!==Object.keys(a).length)return!1;for(l=s;0!=l--;)if(!Object.prototype.hasOwnProperty.call(a,u[l]))return!1;if(t&&o instanceof Element)return!1;for(l=s;0!=l--;)if(("_owner"!==u[l]&&"__v"!==u[l]&&"__o"!==u[l]||!o.$$typeof)&&!e(o[u[l]],a[u[l]]))return!1;return!0}return o!=o&&a!=a}(e,o)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}}},71739:function(e){e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},86656:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(71002);function i(e,t,r){var i;return(i=function(e,t){if("object"!=(0,n.Z)(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=(0,n.Z)(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==(0,n.Z)(i)?i:i+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},87462:function(e,t,r){"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{Z:function(){return n}})},45987:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(63366);function i(e,t){if(null==e)return{};var r,i,o=(0,n.Z)(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)r=a[i],t.includes(r)||({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}},63366:function(e,t,r){"use strict";function n(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}r.d(t,{Z:function(){return n}})},71002:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.d(t,{Z:function(){return n}})},95372:function(e,t,r){"use strict";r.d(t,{W:function(){return i}});var n=r(67294);function i(e,t=[]){let r=(0,n.useRef)(e);return(0,n.useEffect)(()=>{r.current=e}),(0,n.useCallback)((...e)=>r.current?.(...e),t)}},12408:function(e,t,r){"use strict";r.d(t,{G:function(){return i}});var n=r(67294);let i=Boolean(globalThis?.document)?n.useLayoutEffect:n.useEffect},4840:function(e,t,r){"use strict";r.d(t,{r:function(){return i}});var n=r(67294);let i=(e,t)=>{let r=(0,n.useRef)(!1),i=(0,n.useRef)(!1);(0,n.useEffect)(()=>{let t=r.current,n=t&&i.current;if(n)return e();i.current=!0},t),(0,n.useEffect)(()=>(r.current=!0,()=>{r.current=!1}),[])}},71856:function(e,t,r){"use strict";r.d(t,{P:function(){return c}});var n=r(85893),i=r(65544),o=r(12553),a=r(49381),s=r(73035),l=r(64993);function u(e){return(0,n.jsx)(o.J,{focusable:"false","aria-hidden":!0,...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M.439,21.44a1.5,1.5,0,0,0,2.122,2.121L11.823,14.3a.25.25,0,0,1,.354,0l9.262,9.263a1.5,1.5,0,1,0,2.122-2.121L14.3,12.177a.25.25,0,0,1,0-.354l9.263-9.262A1.5,1.5,0,0,0,21.439.44L12.177,9.7a.25.25,0,0,1-.354,0L2.561.44A1.5,1.5,0,0,0,.439,2.561L9.7,11.823a.25.25,0,0,1,0,.354Z"})})}let c=(0,a.G)(function(e,t){let r=(0,s.m)("CloseButton",e),{children:o,isDisabled:a,__css:c,...d}=(0,i.L)(e);return(0,n.jsx)(l.m.button,{type:"button","aria-label":"Close",ref:t,disabled:a,__css:{outline:0,display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,...r,...c},...d,children:o||(0,n.jsx)(u,{width:"1em",height:"1em"})})});c.displayName="CloseButton"},31046:function(e,t,r){"use strict";r.d(t,{If:function(){return o},ff:function(){return a},kc:function(){return i}});var n=r(67294);let i=(0,n.createContext)({});function o(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useColorMode must be used within a ColorModeProvider");return e}function a(e,t){let{colorMode:r}=o();return"dark"===r?t:e}i.displayName="ColorModeContext"},28497:function(e,t,r){"use strict";r.d(t,{O:function(){return s},u:function(){return l}});var n=r(85893),i=r(12408),o=r(67294);let a=(0,o.createContext)({getDocument:()=>document,getWindow:()=>window});function s({defer:e}={}){let[,t]=(0,o.useReducer)(e=>e+1,0);return(0,i.G)(()=>{e&&t()},[e]),(0,o.useContext)(a)}function l(e){let{children:t,environment:r,disabled:i}=e,s=(0,o.useRef)(null),l=(0,o.useMemo)(()=>r||{getDocument:()=>s.current?.ownerDocument??document,getWindow:()=>s.current?.ownerDocument.defaultView??window},[r]);return(0,n.jsxs)(a.Provider,{value:l,children:[t,(!i||!r)&&(0,n.jsx)("span",{id:"__chakra_env",hidden:!0,ref:s})]})}a.displayName="EnvironmentContext",l.displayName="EnvironmentProvider"},12553:function(e,t,r){"use strict";r.d(t,{J:function(){return u}});var n=r(85893),i=r(34926),o=r(49381),a=r(73035),s=r(64993);let l={path:(0,n.jsxs)("g",{stroke:"currentColor",strokeWidth:"1.5",children:[(0,n.jsx)("path",{strokeLinecap:"round",fill:"none",d:"M9,9a3,3,0,1,1,4,2.829,1.5,1.5,0,0,0-1,1.415V14.25"}),(0,n.jsx)("path",{fill:"currentColor",strokeLinecap:"round",d:"M12,17.25a.375.375,0,1,0,.375.375A.375.375,0,0,0,12,17.25h0"}),(0,n.jsx)("circle",{fill:"none",strokeMiterlimit:"10",cx:"12",cy:"12",r:"11.25"})]}),viewBox:"0 0 24 24"},u=(0,o.G)((e,t)=>{let{as:r,viewBox:o,color:u="currentColor",focusable:c=!1,children:d,className:h,__css:p,...f}=e,g=(0,i.cx)("chakra-icon",h),m=(0,a.m)("Icon",e),b={w:"1em",h:"1em",display:"inline-block",lineHeight:"1em",flexShrink:0,color:u,...p,...m},y={ref:t,focusable:c,className:g,__css:b},v=o??l.viewBox;if(r&&"string"!=typeof r)return(0,n.jsx)(s.m.svg,{as:r,...y,...f});let x=d??l.path;return(0,n.jsx)(s.m.svg,{verticalAlign:"middle",viewBox:v,...y,...f,children:x})});u.displayName="Icon"},28215:function(e,t,r){"use strict";r.d(t,{L:function(){return a},h:function(){return s}});var n=r(85893),i=r(52110);let[o,a]=(0,i.k)({strict:!1,name:"PortalManagerContext"});function s(e){let{children:t,zIndex:r}=e;return(0,n.jsx)(o,{value:{zIndex:r},children:t})}s.displayName="PortalManager"},93977:function(e,t,r){"use strict";r.d(t,{h:function(){return g}});var n=r(85893),i=r(12408),o=r(52110),a=r(67294),s=r(73935),l=r(28215);let[u,c]=(0,o.k)({strict:!1,name:"PortalContext"}),d="chakra-portal",h=e=>(0,n.jsx)("div",{className:"chakra-portal-zIndex",style:{position:"absolute",zIndex:e.zIndex,top:0,left:0,right:0},children:e.children}),p=e=>{let{appendToParentPortal:t,children:r}=e,[o,p]=(0,a.useState)(null),f=(0,a.useRef)(null),[,g]=(0,a.useState)({});(0,a.useEffect)(()=>g({}),[]);let m=c(),b=(0,l.L)();(0,i.G)(()=>{if(!o)return;let e=o.ownerDocument,r=t?m??e.body:e.body;if(!r)return;f.current=e.createElement("div"),f.current.className=d,r.appendChild(f.current),g({});let n=f.current;return()=>{r.contains(n)&&r.removeChild(n)}},[o]);let y=b?.zIndex?(0,n.jsx)(h,{zIndex:b?.zIndex,children:r}):r;return f.current?(0,s.createPortal)((0,n.jsx)(u,{value:f.current,children:y}),f.current):(0,n.jsx)("span",{ref:e=>{e&&p(e)}})},f=e=>{let{children:t,containerRef:r,appendToParentPortal:o}=e,l=r.current,c=l??("undefined"!=typeof window?document.body:void 0),h=(0,a.useMemo)(()=>{let e=l?.ownerDocument.createElement("div");return e&&(e.className=d),e},[l]),[,p]=(0,a.useState)({});return((0,i.G)(()=>p({}),[]),(0,i.G)(()=>{if(h&&c)return c.appendChild(h),()=>{c.removeChild(h)}},[h,c]),c&&h)?(0,s.createPortal)((0,n.jsx)(u,{value:o?h:null,children:t}),h):null};function g(e){let t={appendToParentPortal:!0,...e},{containerRef:r,...i}=t;return r?(0,n.jsx)(f,{containerRef:r,...i}):(0,n.jsx)(p,{...i})}g.className=d,g.selector=".chakra-portal",g.displayName="Portal"},48899:function(e,t,r){"use strict";r.d(t,{$:function(){return d}});var n=r(85893),i=r(65544),o=r(34926),a=r(70917),s=r(49381),l=r(73035),u=r(64993);let c=(0,a.F4)({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}),d=(0,s.G)((e,t)=>{let r=(0,l.m)("Spinner",e),{label:a="Loading...",thickness:s="2px",speed:d="0.45s",emptyColor:h="transparent",className:p,...f}=(0,i.L)(e),g=(0,o.cx)("chakra-spinner",p),m={display:"inline-block",borderColor:"currentColor",borderStyle:"solid",borderRadius:"99999px",borderWidth:s,borderBottomColor:h,borderLeftColor:h,animation:`${c} ${d} linear infinite`,...r};return(0,n.jsx)(u.m.div,{ref:t,__css:m,className:g,...f,children:a&&(0,n.jsx)(u.m.span,{srOnly:!0,children:a})})});d.displayName="Spinner"},64993:function(e,t,r){"use strict";r.d(t,{m:function(){return b}});var n,i=r(35831),o=r(82203),a=r(2847),s=r(87155),l=r(16829),u=r(67294);let c=new Set([...i.cC,"textStyle","layerStyle","apply","noOfLines","focusBorderColor","errorBorderColor","as","__css","css","sx"]),d=new Set(["htmlWidth","htmlHeight","htmlSize","htmlTranslate"]);function h(e){return(d.has(e)||!c.has(e))&&"_"!==e[0]}var p=r(31046);let f=(n=l.Z).default||n,g=({baseStyle:e})=>t=>{let{theme:r,css:n,__css:l,sx:u,...c}=t,[d]=function(e,...t){let r=Object.getOwnPropertyDescriptors(e),n=Object.keys(r),i=e=>{let t={};for(let n=0;n<e.length;n++){let i=e[n];r[i]&&(Object.defineProperty(t,i,r[i]),delete r[i])}return t},o=e=>i(Array.isArray(e)?e:n.filter(e));return t.map(o).concat(i(n))}(c,i.ZR),h=(0,a.P)(e,t),p=function(e,...t){if(null==e)throw TypeError("Cannot convert undefined or null to object");let r={...e};for(let e of t)if(null!=e)for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(t in r&&delete r[t],r[t]=e[t]);return r}({},l,h,(0,s.o)(d),u),f=(0,o.i)(p)(t.theme);return n?[f,n]:f};function m(e,t){let{baseStyle:r,...n}=t??{};n.shouldForwardProp||(n.shouldForwardProp=h);let i=g({baseStyle:r}),o=f(e,n)(i),a=(0,u.forwardRef)(function(e,t){let{children:r,...n}=e,{colorMode:i,forced:a}=(0,p.If)();return(0,u.createElement)(o,{ref:t,"data-theme":a?i:void 0,...n},r)});return a}let b=function(){let e=new Map;return new Proxy(m,{apply:(e,t,r)=>m(...r),get:(t,r)=>(e.has(r)||e.set(r,m(r)),e.get(r))})}()},49381:function(e,t,r){"use strict";r.d(t,{G:function(){return i}});var n=r(67294);function i(e){return(0,n.forwardRef)(e)}},70052:function(e,t,r){"use strict";r.d(t,{LP:function(){return a},uP:function(){return o}});var n=r(37984),i=r(31046);function o(){let e=(0,i.If)(),t=(0,n.F)();return{...e,theme:t}}function a(e,t,r){let n=Array.isArray(t)?t:[t],i=Array.isArray(r)?r:[r];return r=>{let o=i.filter(Boolean),a=n.map((t,n)=>{if("breakpoints"===e)return function(e,t,r){if(null==t)return t;let n=t=>e.__breakpoints?.asArray?.[t];return n(t)??n(r)??r}(r,t,o[n]??t);let i=`${e}.${t}`;return function(e,t,r){if(null==t)return t;let n=t=>e.__cssMap?.[t]?.value;return n(t)??n(r)??r}(r,i,o[n]??t)});return Array.isArray(t)?a:a[0]}}},66140:function(e,t,r){"use strict";r.d(t,{ZL:function(){return A},f6:function(){return S},eC:function(){return _}});var n=r(85893),i=r(15292),o=r(79115),a=r(38554),s=r(33829),l=r(43289);function u(e,t,r={}){let{stop:n,getKey:i}=r;return function e(r,a=[]){if((0,o.Kn)(r)||Array.isArray(r)){let o={};for(let[s,l]of Object.entries(r)){let u=i?.(s)??s,c=[...a,u];if(n?.(r,c))return t(r,a);o[u]=e(l,c)}return o}return t(r,a)}(e)}var c=r(57474),d=r(70562);let h=["colors","borders","borderWidths","borderStyles","fonts","fontSizes","fontWeights","gradients","letterSpacings","lineHeights","radii","space","shadows","sizes","zIndices","transition","blur","breakpoints"];function p(e,t){return(0,l.gJ)(String(e).replace(/\./g,"-"),void 0,t)}var f=r(82203),g=r(52110),m=r(4839),b=r(2847),y=r(20442),v=r(70917),x=r(67294),k=r(31046);function S(e){let{cssVarsRoot:t,theme:r,children:l}=e,f=(0,x.useMemo)(()=>(function(e){let t=function(e){let{__cssMap:t,__cssVars:r,__breakpoints:n,...i}=e;return i}(e),{cssMap:r,cssVars:n}=function(e){let t=function(e){let t=(0,d.e)(e,h),r=e.semanticTokens,n=e=>c._.includes(e)||"default"===e,i={};return u(t,(e,t)=>{null!=e&&(i[t.join(".")]={isSemantic:!1,value:e})}),u(r,(e,t)=>{null!=e&&(i[t.join(".")]={isSemantic:!0,value:e})},{stop:e=>Object.keys(e).every(n)}),i}(e),r=e.config?.cssVarPrefix,n={},i={};for(let[e,l]of Object.entries(t)){let{isSemantic:u,value:d}=l,{variable:h,reference:f}=p(e,r);if(!u){if(e.startsWith("space")){let t=e.split("."),[r,...n]=t,o=`${r}.-${n.join(".")}`,a=s.y.negate(d),l=s.y.negate(f);i[o]={value:a,var:h,varRef:l}}n[h]=d,i[e]={value:d,var:h,varRef:f};continue}let g=(0,o.Kn)(d)?d:{default:d};n=a(n,Object.entries(g).reduce((n,[i,o])=>{if(!o)return n;let a=function(e,n){let i=String(e).split(".")[0],o=[i,n].join("."),a=t[o];if(!a)return n;let{reference:s}=p(o,r);return s}(e,`${o}`);if("default"===i)return n[h]=a,n;let s=c.v?.[i]??i;return n[s]={[h]:a},n},{})),i[e]={value:f,var:h,varRef:f}}return{cssVars:n,cssMap:i}}(t);return Object.assign(t,{__cssVars:{"--chakra-ring-inset":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-ring-offset-width":"0px","--chakra-ring-offset-color":"#fff","--chakra-ring-color":"rgba(66, 153, 225, 0.6)","--chakra-ring-offset-shadow":"0 0 #0000","--chakra-ring-shadow":"0 0 #0000","--chakra-space-x-reverse":"0","--chakra-space-y-reverse":"0",...n},__cssMap:r,__breakpoints:(0,i.y)(t.breakpoints)}),t})(r),[r]);return(0,n.jsxs)(y.a,{theme:f,children:[(0,n.jsx)(w,{root:t}),l]})}function w({root:e=":host, :root"}){let t=[e,"[data-theme]"].join(",");return(0,n.jsx)(v.xB,{styles:e=>({[t]:e.__cssVars})})}let[C,P]=(0,g.k)({name:"StylesContext",errorMessage:"useStyles: `styles` is undefined. Seems you forgot to wrap the components in `<StylesProvider />` "});function _(e){return(0,g.k)({name:`${e}StylesContext`,errorMessage:`useStyles: "styles" is undefined. Seems you forgot to wrap the components in "<${e} />" `})}function A(){let{colorMode:e}=(0,k.If)();return(0,n.jsx)(v.xB,{styles:t=>{let r=(0,m.W)(t,"styles.global"),n=(0,b.P)(r,{theme:t,colorMode:e});if(!n)return;let i=(0,f.i)(n)(t);return i}})}},73035:function(e,t,r){"use strict";r.d(t,{j:function(){return g},m:function(){return f}});var n=r(79115),i=r(15292),o=r(2847),a=r(38554),s=r(4839),l=r(87155),u=r(8297),c=r(67294),d=r(69590),h=r(70052);function p(e,t={}){let{styleConfig:r,...p}=t,{theme:f,colorMode:g}=(0,h.uP)(),m=e?(0,s.W)(f,`components.${e}`):void 0,b=r||m,y=a({theme:f,colorMode:g},b?.defaultProps??{},(0,l.o)((0,u.C)(p,["children"])),(e,t)=>e?void 0:t),v=(0,c.useRef)({});if(b){let e=(e=>{let{variant:t,size:r,theme:s}=e,l=function(e){let t=e.__breakpoints;return function(e,r,s,l){var u;if(!t)return;let c={},d=(u=t.toArrayValue,Array.isArray(s)?s:(0,n.Kn)(s)?u(s):null!=s?[s]:void 0);if(!d)return c;let h=d.length,p=1===h,f=!!e.parts;for(let n=0;n<h;n++){let s=t.details[n],u=t.details[function(e,t){for(let r=t+1;r<e.length;r++)if(null!=e[r])return r;return -1}(d,n)],h=(0,i.Y)(s.minW,u?._minW),g=(0,o.P)(e[r]?.[d[n]],l);if(g){if(f){e.parts?.forEach(e=>{a(c,{[e]:p?g[e]:{[h]:g[e]}})});continue}if(!f){p?a(c,g):c[h]=g;continue}c[h]=g}}return c}}(s);return a({},(0,o.P)(b.baseStyle??{},e),l(b,"sizes",r,e),l(b,"variants",t,e))})(y),t=d(v.current,e);t||(v.current=e)}return v.current}function f(e,t={}){return p(e,t)}function g(e,t={}){return p(e,t)}},37984:function(e,t,r){"use strict";r.d(t,{F:function(){return o}});var n=r(20442),i=r(67294);function o(){let e=(0,i.useContext)(n.T);if(!e)throw Error("useTheme: `theme` is undefined. Seems you forgot to wrap your app in `<ChakraProvider />` or `<ThemeProvider />`");return e}},60975:function(e,t,r){"use strict";r.d(t,{C:function(){return P}});var n=r(85893),i=r(65544),o=r(83695),a=r(34926),s=r(52110),l=r(12553);function u(e){return(0,n.jsx)(l.J,{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M11.983,0a12.206,12.206,0,0,0-8.51,3.653A11.8,11.8,0,0,0,0,12.207,11.779,11.779,0,0,0,11.8,24h.214A12.111,12.111,0,0,0,24,11.791h0A11.766,11.766,0,0,0,11.983,0ZM10.5,16.542a1.476,1.476,0,0,1,1.449-1.53h.027a1.527,1.527,0,0,1,1.523,1.47,1.475,1.475,0,0,1-1.449,1.53h-.027A1.529,1.529,0,0,1,10.5,16.542ZM11,12.5v-6a1,1,0,0,1,2,0v6a1,1,0,1,1-2,0Z"})})}var c=r(48899);let[d,h]=(0,s.k)({name:"AlertContext",hookName:"useAlertContext",providerName:"<Alert />"}),[p,f]=(0,s.k)({name:"AlertStylesContext",hookName:"useAlertStyles",providerName:"<Alert />"}),g={info:{icon:function(e){return(0,n.jsx)(l.J,{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm.25,5a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,12.25,5ZM14.5,18.5h-4a1,1,0,0,1,0-2h.75a.25.25,0,0,0,.25-.25v-4.5a.25.25,0,0,0-.25-.25H10.5a1,1,0,0,1,0-2h1a2,2,0,0,1,2,2v4.75a.25.25,0,0,0,.25.25h.75a1,1,0,1,1,0,2Z"})})},colorScheme:"blue"},warning:{icon:u,colorScheme:"orange"},success:{icon:function(e){return(0,n.jsx)(l.J,{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z"})})},colorScheme:"green"},error:{icon:u,colorScheme:"red"},loading:{icon:c.$,colorScheme:"blue"}};var m=r(73035),b=r(49381),y=r(64993);let v=(0,b.G)(function(e,t){let{status:r="info",addRole:s=!0,...l}=(0,i.L)(e),u=e.colorScheme??g[r].colorScheme,c=(0,m.j)("Alert",{...e,colorScheme:u}),h=(0,o.k0)({width:"100%",display:"flex",alignItems:"center",position:"relative",overflow:"hidden",...c.container});return(0,n.jsx)(d,{value:{status:r},children:(0,n.jsx)(p,{value:c,children:(0,n.jsx)(y.m.div,{"data-status":r,role:s?"alert":void 0,ref:t,...l,className:(0,a.cx)("chakra-alert",e.className),__css:h})})})});function x(e){let{status:t}=h(),r=g[t].icon,i=f(),o="loading"===t?i.spinner:i.icon;return(0,n.jsx)(y.m.span,{display:"inherit","data-status":t,...e,className:(0,a.cx)("chakra-alert__icon",e.className),__css:o,children:e.children||(0,n.jsx)(r,{h:"100%",w:"100%"})})}v.displayName="Alert",x.displayName="AlertIcon";let k=(0,b.G)(function(e,t){let r=f(),{status:i}=h();return(0,n.jsx)(y.m.div,{ref:t,"data-status":i,...e,className:(0,a.cx)("chakra-alert__title",e.className),__css:r.title})});k.displayName="AlertTitle";let S=(0,b.G)(function(e,t){let{status:r}=h(),i=f(),s=(0,o.k0)({display:"inline",...i.description});return(0,n.jsx)(y.m.div,{ref:t,"data-status":r,...e,className:(0,a.cx)("chakra-alert__desc",e.className),__css:s})});S.displayName="AlertDescription";var w=r(71856);let C=e=>{let{status:t,variant:r="solid",id:i,title:o,isClosable:a,onClose:s,description:l,colorScheme:u,icon:c}=e,d=i?{root:`toast-${i}`,title:`toast-${i}-title`,description:`toast-${i}-description`}:void 0;return(0,n.jsxs)(v,{addRole:!1,status:t,variant:r,id:d?.root,alignItems:"start",borderRadius:"md",boxShadow:"lg",paddingEnd:8,textAlign:"start",width:"auto",colorScheme:u,children:[(0,n.jsx)(x,{children:c}),(0,n.jsxs)(y.m.div,{flex:"1",maxWidth:"100%",children:[o&&(0,n.jsx)(k,{id:d?.title,children:o}),l&&(0,n.jsx)(S,{id:d?.description,display:"block",children:l})]}),a&&(0,n.jsx)(w.P,{size:"sm",onClick:s,position:"absolute",insetEnd:1,top:1})]})};function P(e={}){let{render:t,toastComponent:r=C}=e,i=i=>"function"==typeof t?t({...i,...e}):(0,n.jsx)(r,{...i,...e});return i}},88559:function(e,t,r){"use strict";r.d(t,{Qi:function(){return y},VW:function(){return x},OX:function(){return v}});var n=r(85893),i=r(52110),o=r(51526),a=r(67294),s=r(4840),l=r(95372),u=r(2847),c=r(15947),d=r(97340),h=r(57535),p=r(64993);let f={initial:e=>{let{position:t}=e,r=["top","bottom"].includes(t)?"y":"x",n=["top-right","bottom-right"].includes(t)?1:-1;return"bottom"===t&&(n=1),{opacity:0,[r]:24*n}},animate:{opacity:1,y:0,x:0,scale:1,transition:{duration:.4,ease:[.4,0,.2,1]}},exit:{opacity:0,scale:.85,transition:{duration:.2,ease:[.4,0,1,1]}}},g=(0,a.memo)(e=>{let{id:t,message:r,onCloseComplete:i,onRequestRemove:o,requestClose:g=!1,position:m="bottom",duration:b=5e3,containerStyle:y,motionVariants:v=f,toastSpacing:x="0.5rem"}=e,[k,S]=(0,a.useState)(b),w=(0,c.hO)();(0,s.r)(()=>{w||i?.()},[w]),(0,s.r)(()=>{S(b)},[b]);let C=()=>S(null),P=()=>S(b),_=()=>{w&&o()};(0,a.useEffect)(()=>{w&&g&&o()},[w,g,o]),function(e,t){let r=(0,l.W)(e);(0,a.useEffect)(()=>{if(null==t)return;let e=null;return e=window.setTimeout(()=>{r()},t),()=>{e&&window.clearTimeout(e)}},[t,r])}(_,k);let A=(0,a.useMemo)(()=>({pointerEvents:"auto",maxWidth:560,minWidth:300,margin:x,...y}),[y,x]),j=(0,a.useMemo)(()=>(0,h.sv)(m),[m]);return(0,n.jsx)(d.E.div,{layout:!0,className:"chakra-toast",variants:v,initial:"initial",animate:"animate",exit:"exit",onHoverStart:C,onHoverEnd:P,custom:{position:m},style:j,children:(0,n.jsx)(p.m.div,{role:"status","aria-atomic":"true",className:"chakra-toast__inner",__css:A,children:(0,u.P)(r,{id:t,onClose:_})})})});g.displayName="ToastComponent";var m=r(11065),b=r(93977);let[y,v]=(0,i.k)({name:"ToastOptionsContext",strict:!1}),x=e=>{let t=(0,a.useSyncExternalStore)(m.f.subscribe,m.f.getState,m.f.getState),{motionVariants:r,component:i=g,portalProps:s,animatePresenceProps:l}=e,u=Object.keys(t),c=u.map(e=>{let a=t[e];return(0,n.jsx)("div",{role:"region","aria-live":"polite","aria-label":`Notifications-${e}`,id:`chakra-toast-manager-${e}`,style:(0,h.IW)(e),children:(0,n.jsx)(o.M,{...l,initial:!1,children:a.map(e=>(0,n.jsx)(i,{motionVariants:r,...e},e.id))})},e)});return(0,n.jsx)(b.h,{...s,children:c})}},11065:function(e,t,r){"use strict";r.d(t,{f:function(){return o}});var n=r(60975),i=r(57535);let o=function(e){let t=e,r=new Set,s=e=>{t=e(t),r.forEach(e=>e())};return{getState:()=>t,subscribe:t=>(r.add(t),()=>{s(()=>e),r.delete(t)}),removeToast:(e,t)=>{s(r=>({...r,[t]:r[t].filter(t=>t.id!=e)}))},notify:(e,t)=>{let r=function(e,t={}){a+=1;let r=t.id??a,n=t.position??"bottom";return{id:r,message:e,position:n,duration:t.duration,onCloseComplete:t.onCloseComplete,onRequestRemove:()=>o.removeToast(String(r),n),status:t.status,requestClose:!1,containerStyle:t.containerStyle}}(e,t),{position:n,id:i}=r;return s(e=>{let t=n.includes("top"),i=t?[r,...e[n]??[]]:[...e[n]??[],r];return{...e,[n]:i}}),i},update:(e,t)=>{e&&s(r=>{let o={...r},{position:a,index:s}=(0,i.Dn)(o,e);return a&&-1!==s&&(o[a][s]={...o[a][s],...t,message:(0,n.C)(t)}),o})},closeAll:({positions:e}={})=>{s(t=>(e??["bottom","bottom-right","bottom-left","top","top-left","top-right"]).reduce((e,r)=>(e[r]=t[r].map(e=>({...e,requestClose:!0})),e),{...t}))},close:e=>{s(t=>{let r=(0,i.ym)(t,e);return r?{...t,[r]:t[r].map(t=>t.id==e?{...t,requestClose:!0}:t)}:t})},isActive:e=>Boolean((0,i.Dn)(o.getState(),e).position)}}({top:[],"top-left":[],"top-right":[],"bottom-left":[],bottom:[],"bottom-right":[]}),a=0},57535:function(e,t,r){"use strict";r.d(t,{Dn:function(){return i},IW:function(){return s},sv:function(){return a},ym:function(){return o}});let n=(e,t)=>e.find(e=>e.id===t);function i(e,t){let r=o(e,t),n=r?e[r].findIndex(e=>e.id===t):-1;return{position:r,index:n}}function o(e,t){for(let[r,i]of Object.entries(e))if(n(i,t))return r}function a(e){let t=e.includes("right"),r=e.includes("left"),n="center";return t&&(n="flex-end"),r&&(n="flex-start"),{display:"flex",flexDirection:"column",alignItems:n}}function s(e){let t=e.includes("top")?"env(safe-area-inset-top, 0px)":void 0,r=e.includes("bottom")?"env(safe-area-inset-bottom, 0px)":void 0,n=e.includes("left")?void 0:"env(safe-area-inset-right, 0px)",i=e.includes("right")?void 0:"env(safe-area-inset-left, 0px)";return{position:"fixed",zIndex:"var(--toast-z-index, 5500)",pointerEvents:"none",display:"flex",flexDirection:"column",margin:"top"===e||"bottom"===e?"0 auto":void 0,top:t,bottom:r,right:n,left:i}}},33829:function(e,t,r){"use strict";r.d(t,{y:function(){return d}});var n=r(79115);function i(e){return(0,n.Kn)(e)&&e.reference?e.reference:String(e)}let o=(e,...t)=>t.map(i).join(` ${e} `).replace(/calc/g,""),a=(...e)=>`calc(${o("+",...e)})`,s=(...e)=>`calc(${o("-",...e)})`,l=(...e)=>`calc(${o("*",...e)})`,u=(...e)=>`calc(${o("/",...e)})`,c=e=>{let t=i(e);return null==t||Number.isNaN(parseFloat(t))?l(t,-1):String(t).startsWith("-")?String(t).slice(1):`-${t}`},d=Object.assign(e=>({add:(...t)=>d(a(e,...t)),subtract:(...t)=>d(s(e,...t)),multiply:(...t)=>d(l(e,...t)),divide:(...t)=>d(u(e,...t)),negate:()=>d(c(e)),toString:()=>e.toString()}),{add:a,subtract:s,multiply:l,divide:u,negate:c})},43289:function(e,t,r){"use strict";function n(e,t,r){let n=function(e,t=""){return function(e){let t=function(e,t="-"){return e.replace(/\s+/g,t)}(e.toString());return(function(e){if(e.includes("\\."))return e;let t=!Number.isInteger(parseFloat(e.toString()));return t?e.replace(".","\\."):e})(t).replace(/[!-,/:-@[-^`{-~]/g,"\\$&")}(`--${function(e,t=""){return[t,e].filter(Boolean).join("-")}(e,t)}`)}(e,r);return{variable:n,reference:`var(${n}${t?`, ${t}`:""})`}}function i(e,t){let r={};for(let i of t){if(Array.isArray(i)){let[t,o]=i;r[t]=n(`${e}-${t}`,o);continue}r[i]=n(`${e}-${i}`)}return r}r.d(t,{_6:function(){return i},gJ:function(){return n}})},82203:function(e,t,r){"use strict";r.d(t,{i:function(){return d}});var n=r(2847),i=r(79115),o=r(38554),a=r(57474),s=r(35831);let l=e=>t=>{if(!t.__breakpoints)return e;let{isResponsive:r,toArrayValue:o,media:a}=t.__breakpoints,s={};for(let l in e){let u=(0,n.P)(e[l],t);if(null==u)continue;if(!Array.isArray(u=(0,i.Kn)(u)&&r(u)?o(u):u)){s[l]=u;continue}let c=u.slice(0,a.length).length;for(let e=0;e<c;e+=1){let t=a?.[e];if(!t){s[l]=u[e];continue}s[t]=s[t]||{},null!=u[e]&&(s[t][l]=u[e])}}return s},u=(e,t)=>e.startsWith("--")&&"string"==typeof t&&!/^var\(--.+\)$/.test(t),c=(e,t)=>{if(null==t)return t;let r=t=>e.__cssMap?.[t]?.varRef,n=e=>r(e)??e,[i,o]=function(e){let t=[],r="",n=!1;for(let i=0;i<e.length;i++){let o=e[i];"("===o?(n=!0,r+=o):")"===o?(n=!1,r+=o):","!==o||n?r+=o:(t.push(r),r="")}return(r=r.trim())&&t.push(r),t}(t);return t=r(i)??n(o)??n(t)},d=e=>t=>{let r=function(e){let{configs:t={},pseudos:r={},theme:a}=e,s=(e,d=!1)=>{let h=(0,n.P)(e,a),p=l(h)(a),f={};for(let e in p){let l=p[e],g=(0,n.P)(l,a);e in r&&(e=r[e]),u(e,g)&&(g=c(a,g));let m=t[e];if(!0===m&&(m={property:e}),(0,i.Kn)(g)){f[e]=f[e]??{},f[e]=o({},f[e],s(g,!0));continue}let b=m?.transform?.(g,a,h)??g;b=m?.processResult?s(b,!0):b;let y=(0,n.P)(m?.property,a);if(!d&&m?.static){let e=(0,n.P)(m.static,a);f=o({},f,e)}if(y&&Array.isArray(y)){for(let e of y)f[e]=b;continue}if(y){"&"===y&&(0,i.Kn)(b)?f=o({},f,b):f[y]=b;continue}if((0,i.Kn)(b)){f=o({},f,b);continue}f[e]=b}return f};return s}({theme:t,pseudos:a.v,configs:s.Ul});return r(e)}},83695:function(e,t,r){"use strict";function n(e){return e}function i(e){return e}function o(e){return{definePartsStyle:e=>e,defineMultiStyleConfig:t=>({parts:e,...t})}}r.d(t,{D:function(){return o},fj:function(){return i},k0:function(){return n}})},57474:function(e,t,r){"use strict";r.d(t,{_:function(){return l},v:function(){return s}});let n={open:(e,t)=>`${e}[data-open], ${e}[open], ${e}[data-state=open] ${t}`,closed:(e,t)=>`${e}[data-closed], ${e}[data-state=closed] ${t}`,hover:(e,t)=>`${e}:hover ${t}, ${e}[data-hover] ${t}`,focus:(e,t)=>`${e}:focus ${t}, ${e}[data-focus] ${t}`,focusVisible:(e,t)=>`${e}:focus-visible ${t}`,focusWithin:(e,t)=>`${e}:focus-within ${t}`,active:(e,t)=>`${e}:active ${t}, ${e}[data-active] ${t}`,disabled:(e,t)=>`${e}:disabled ${t}, ${e}[data-disabled] ${t}`,invalid:(e,t)=>`${e}:invalid ${t}, ${e}[data-invalid] ${t}`,checked:(e,t)=>`${e}:checked ${t}, ${e}[data-checked] ${t}`,indeterminate:(e,t)=>`${e}:indeterminate ${t}, ${e}[aria-checked=mixed] ${t}, ${e}[data-indeterminate] ${t}`,readOnly:(e,t)=>`${e}:read-only ${t}, ${e}[readonly] ${t}, ${e}[data-read-only] ${t}`,expanded:(e,t)=>`${e}:read-only ${t}, ${e}[aria-expanded=true] ${t}, ${e}[data-expanded] ${t}`,placeholderShown:(e,t)=>`${e}:placeholder-shown ${t}`},i=e=>a(t=>e(t,"&"),"[role=group]","[data-group]",".group"),o=e=>a(t=>e(t,"~ &"),"[data-peer]",".peer"),a=(e,...t)=>t.map(e).join(", "),s={_hover:"&:hover, &[data-hover]",_active:"&:active, &[data-active]",_focus:"&:focus, &[data-focus]",_highlighted:"&[data-highlighted]",_focusWithin:"&:focus-within, &[data-focus-within]",_focusVisible:"&:focus-visible, &[data-focus-visible]",_disabled:"&:disabled, &[disabled], &[aria-disabled=true], &[data-disabled]",_readOnly:"&[aria-readonly=true], &[readonly], &[data-readonly]",_before:"&::before",_after:"&::after",_empty:"&:empty, &[data-empty]",_expanded:"&[aria-expanded=true], &[data-expanded], &[data-state=expanded]",_checked:"&[aria-checked=true], &[data-checked], &[data-state=checked]",_grabbed:"&[aria-grabbed=true], &[data-grabbed]",_pressed:"&[aria-pressed=true], &[data-pressed]",_invalid:"&[aria-invalid=true], &[data-invalid]",_valid:"&[data-valid], &[data-state=valid]",_loading:"&[data-loading], &[aria-busy=true]",_selected:"&[aria-selected=true], &[data-selected]",_hidden:"&[hidden], &[data-hidden]",_autofill:"&:-webkit-autofill",_even:"&:nth-of-type(even)",_odd:"&:nth-of-type(odd)",_first:"&:first-of-type",_firstLetter:"&::first-letter",_last:"&:last-of-type",_notFirst:"&:not(:first-of-type)",_notLast:"&:not(:last-of-type)",_visited:"&:visited",_activeLink:"&[aria-current=page]",_activeStep:"&[aria-current=step]",_indeterminate:"&:indeterminate, &[aria-checked=mixed], &[data-indeterminate], &[data-state=indeterminate]",_groupOpen:i(n.open),_groupClosed:i(n.closed),_groupHover:i(n.hover),_peerHover:o(n.hover),_groupFocus:i(n.focus),_peerFocus:o(n.focus),_groupFocusVisible:i(n.focusVisible),_peerFocusVisible:o(n.focusVisible),_groupActive:i(n.active),_peerActive:o(n.active),_groupDisabled:i(n.disabled),_peerDisabled:o(n.disabled),_groupInvalid:i(n.invalid),_peerInvalid:o(n.invalid),_groupChecked:i(n.checked),_peerChecked:o(n.checked),_groupFocusWithin:i(n.focusWithin),_peerFocusWithin:o(n.focusWithin),_peerPlaceholderShown:o(n.placeholderShown),_placeholder:"&::placeholder, &[data-placeholder]",_placeholderShown:"&:placeholder-shown, &[data-placeholder-shown]",_fullScreen:"&:fullscreen, &[data-fullscreen]",_selection:"&::selection",_rtl:"[dir=rtl] &, &[dir=rtl]",_ltr:"[dir=ltr] &, &[dir=ltr]",_mediaDark:"@media (prefers-color-scheme: dark)",_mediaReduceMotion:"@media (prefers-reduced-motion: reduce)",_dark:".chakra-ui-dark &:not([data-theme]),[data-theme=dark] &:not([data-theme]),&[data-theme=dark]",_light:".chakra-ui-light &:not([data-theme]),[data-theme=light] &:not([data-theme]),&[data-theme=light]",_horizontal:"&[data-orientation=horizontal]",_vertical:"&[data-orientation=vertical]",_open:"&[data-open], &[open], &[data-state=open]",_closed:"&[data-closed], &[data-state=closed]",_complete:"&[data-complete]",_incomplete:"&[data-incomplete]",_current:"&[data-current]"},l=Object.keys(s)},35831:function(e,t,r){"use strict";r.d(t,{ZR:function(){return ee},oE:function(){return K},cC:function(){return X},Ul:function(){return G}});var n=r(38554),i=r(57474),o=r(79115);let a=e=>/!(important)?$/.test(e),s=e=>"string"==typeof e?e.replace(/!(important)?$/,"").trim():e,l=(e,t)=>r=>{let n=String(t),i=a(n),l=s(n),u=e?`${e}.${l}`:l,c=(0,o.Kn)(r.__cssMap)&&u in r.__cssMap?r.__cssMap[u].varRef:t;return c=s(c),i?`${c} !important`:c};function u(e){let{scale:t,transform:r,compose:n}=e,i=(e,i)=>{let o=l(t,e)(i),a=r?.(o,i)??o;return n&&(a=n(a,i)),a};return i}let c=(...e)=>t=>e.reduce((e,t)=>t(e),t);function d(e,t){return r=>{let n={property:r,scale:e};return n.transform=u({scale:e,transform:t}),n}}let h=({rtl:e,ltr:t})=>r=>"rtl"===r.direction?e:t,p=["rotate(var(--chakra-rotate, 0))","scaleX(var(--chakra-scale-x, 1))","scaleY(var(--chakra-scale-y, 1))","skewX(var(--chakra-skew-x, 0))","skewY(var(--chakra-skew-y, 0))"],f={"--chakra-blur":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-brightness":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-contrast":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-grayscale":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-hue-rotate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-invert":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-saturate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-sepia":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-drop-shadow":"var(--chakra-empty,/*!*/ /*!*/)",filter:"var(--chakra-blur) var(--chakra-brightness) var(--chakra-contrast) var(--chakra-grayscale) var(--chakra-hue-rotate) var(--chakra-invert) var(--chakra-saturate) var(--chakra-sepia) var(--chakra-drop-shadow)"},g={backdropFilter:"var(--chakra-backdrop-blur) var(--chakra-backdrop-brightness) var(--chakra-backdrop-contrast) var(--chakra-backdrop-grayscale) var(--chakra-backdrop-hue-rotate) var(--chakra-backdrop-invert) var(--chakra-backdrop-opacity) var(--chakra-backdrop-saturate) var(--chakra-backdrop-sepia)","--chakra-backdrop-blur":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-brightness":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-contrast":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-grayscale":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-hue-rotate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-invert":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-opacity":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-saturate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-sepia":"var(--chakra-empty,/*!*/ /*!*/)"},m={"row-reverse":{space:"--chakra-space-x-reverse",divide:"--chakra-divide-x-reverse"},"column-reverse":{space:"--chakra-space-y-reverse",divide:"--chakra-divide-y-reverse"}},b={"to-t":"to top","to-tr":"to top right","to-r":"to right","to-br":"to bottom right","to-b":"to bottom","to-bl":"to bottom left","to-l":"to left","to-tl":"to top left"},y=new Set(Object.values(b)),v=new Set(["none","-moz-initial","inherit","initial","revert","unset"]),x=e=>e.trim(),k=e=>"string"==typeof e&&e.includes("(")&&e.includes(")"),S=(e,t)=>(function(e,t){if(null==e||v.has(e))return e;let r=k(e)||v.has(e);if(!r)return`url('${e}')`;let n=/(^[a-z-A-Z]+)\((.*)\)/g.exec(e),i=n?.[1],o=n?.[2];if(!i||!o)return e;let a=i.includes("-gradient")?i:`${i}-gradient`,[s,...l]=o.split(",").map(x).filter(Boolean);if(l?.length===0)return e;let u=s in b?b[s]:s;l.unshift(u);let c=l.map(e=>{if(y.has(e))return e;let r=e.indexOf(" "),[n,i]=-1!==r?[e.substr(0,r),e.substr(r+1)]:[e],o=k(i)?i:i&&i.split(" "),a=`colors.${n}`,s=a in t.__cssMap?t.__cssMap[a].varRef:n;return o?[s,...Array.isArray(o)?o:[o]].join(" "):s});return`${a}(${c.join(", ")})`})(e,t??{}),w=e=>{let t=parseFloat(e.toString()),r=e.toString().replace(String(t),"");return{unitless:!r,value:t,unit:r}},C=e=>t=>`${e}(${t})`,P={filter:e=>"auto"!==e?e:f,backdropFilter:e=>"auto"!==e?e:g,ring:e=>({"--chakra-ring-offset-shadow":"var(--chakra-ring-inset) 0 0 0 var(--chakra-ring-offset-width) var(--chakra-ring-offset-color)","--chakra-ring-shadow":"var(--chakra-ring-inset) 0 0 0 calc(var(--chakra-ring-width) + var(--chakra-ring-offset-width)) var(--chakra-ring-color)","--chakra-ring-width":P.px(e),boxShadow:"var(--chakra-ring-offset-shadow), var(--chakra-ring-shadow), var(--chakra-shadow, 0 0 #0000)"}),bgClip:e=>"text"===e?{color:"transparent",backgroundClip:"text"}:{backgroundClip:e},transform:e=>"auto"===e?["translateX(var(--chakra-translate-x, 0))","translateY(var(--chakra-translate-y, 0))",...p].join(" "):"auto-gpu"===e?["translate3d(var(--chakra-translate-x, 0), var(--chakra-translate-y, 0), 0)",...p].join(" "):e,vh:e=>"$100vh"===e?"var(--chakra-vh)":e,px(e){if(null==e)return e;let{unitless:t}=w(e);return t||"number"==typeof e?`${e}px`:e},fraction:e=>"number"!=typeof e||e>1?e:`${100*e}%`,float:(e,t)=>"rtl"===t.direction?({left:"right",right:"left"})[e]:e,degree(e){if(/^var\(--.+\)$/.test(e)||null==e)return e;let t="string"==typeof e&&!e.endsWith("deg");return"number"==typeof e||t?`${e}deg`:e},gradient:S,blur:C("blur"),opacity:C("opacity"),brightness:C("brightness"),contrast:C("contrast"),dropShadow:C("drop-shadow"),grayscale:C("grayscale"),hueRotate:e=>C("hue-rotate")(P.degree(e)),invert:C("invert"),saturate:C("saturate"),sepia:C("sepia"),bgImage(e){if(null==e)return e;let t=k(e)||v.has(e);return t?e:`url(${e})`},outline(e){let t="0"===String(e)||"none"===String(e);return null!==e&&t?{outline:"2px solid transparent",outlineOffset:"2px"}:{outline:e}},flexDirection(e){let{space:t,divide:r}=m[e]??{},n={flexDirection:e};return t&&(n[t]=1),r&&(n[r]=1),n}},_={borderWidths:d("borderWidths"),borderStyles:d("borderStyles"),colors:d("colors"),borders:d("borders"),gradients:d("gradients",P.gradient),radii:d("radii",P.px),space:d("space",c(P.vh,P.px)),spaceT:d("space",c(P.vh,P.px)),degreeT:e=>({property:e,transform:P.degree}),prop:(e,t,r)=>({property:e,scale:t,...t&&{transform:u({scale:t,transform:r})}}),propT:(e,t)=>({property:e,transform:t}),sizes:d("sizes",c(P.vh,P.px)),sizesT:d("sizes",c(P.vh,P.fraction)),shadows:d("shadows"),logical:function(e){let{property:t,scale:r,transform:n}=e;return{scale:r,property:h(t),transform:r?u({scale:r,compose:n}):n}},blur:d("blur",P.blur)},A={background:_.colors("background"),backgroundColor:_.colors("backgroundColor"),backgroundImage:_.gradients("backgroundImage"),backgroundSize:!0,backgroundPosition:!0,backgroundRepeat:!0,backgroundAttachment:!0,backgroundClip:{transform:P.bgClip},bgSize:_.prop("backgroundSize"),bgPosition:_.prop("backgroundPosition"),bg:_.colors("background"),bgColor:_.colors("backgroundColor"),bgPos:_.prop("backgroundPosition"),bgRepeat:_.prop("backgroundRepeat"),bgAttachment:_.prop("backgroundAttachment"),bgGradient:_.gradients("backgroundImage"),bgClip:{transform:P.bgClip}};Object.assign(A,{bgImage:A.backgroundImage,bgImg:A.backgroundImage});let j={border:_.borders("border"),borderWidth:_.borderWidths("borderWidth"),borderStyle:_.borderStyles("borderStyle"),borderColor:_.colors("borderColor"),borderRadius:_.radii("borderRadius"),borderTop:_.borders("borderTop"),borderBlockStart:_.borders("borderBlockStart"),borderTopLeftRadius:_.radii("borderTopLeftRadius"),borderStartStartRadius:_.logical({scale:"radii",property:{ltr:"borderTopLeftRadius",rtl:"borderTopRightRadius"}}),borderEndStartRadius:_.logical({scale:"radii",property:{ltr:"borderBottomLeftRadius",rtl:"borderBottomRightRadius"}}),borderTopRightRadius:_.radii("borderTopRightRadius"),borderStartEndRadius:_.logical({scale:"radii",property:{ltr:"borderTopRightRadius",rtl:"borderTopLeftRadius"}}),borderEndEndRadius:_.logical({scale:"radii",property:{ltr:"borderBottomRightRadius",rtl:"borderBottomLeftRadius"}}),borderRight:_.borders("borderRight"),borderInlineEnd:_.borders("borderInlineEnd"),borderBottom:_.borders("borderBottom"),borderBlockEnd:_.borders("borderBlockEnd"),borderBottomLeftRadius:_.radii("borderBottomLeftRadius"),borderBottomRightRadius:_.radii("borderBottomRightRadius"),borderLeft:_.borders("borderLeft"),borderInlineStart:{property:"borderInlineStart",scale:"borders"},borderInlineStartRadius:_.logical({scale:"radii",property:{ltr:["borderTopLeftRadius","borderBottomLeftRadius"],rtl:["borderTopRightRadius","borderBottomRightRadius"]}}),borderInlineEndRadius:_.logical({scale:"radii",property:{ltr:["borderTopRightRadius","borderBottomRightRadius"],rtl:["borderTopLeftRadius","borderBottomLeftRadius"]}}),borderX:_.borders(["borderLeft","borderRight"]),borderInline:_.borders("borderInline"),borderY:_.borders(["borderTop","borderBottom"]),borderBlock:_.borders("borderBlock"),borderTopWidth:_.borderWidths("borderTopWidth"),borderBlockStartWidth:_.borderWidths("borderBlockStartWidth"),borderTopColor:_.colors("borderTopColor"),borderBlockStartColor:_.colors("borderBlockStartColor"),borderTopStyle:_.borderStyles("borderTopStyle"),borderBlockStartStyle:_.borderStyles("borderBlockStartStyle"),borderBottomWidth:_.borderWidths("borderBottomWidth"),borderBlockEndWidth:_.borderWidths("borderBlockEndWidth"),borderBottomColor:_.colors("borderBottomColor"),borderBlockEndColor:_.colors("borderBlockEndColor"),borderBottomStyle:_.borderStyles("borderBottomStyle"),borderBlockEndStyle:_.borderStyles("borderBlockEndStyle"),borderLeftWidth:_.borderWidths("borderLeftWidth"),borderInlineStartWidth:_.borderWidths("borderInlineStartWidth"),borderLeftColor:_.colors("borderLeftColor"),borderInlineStartColor:_.colors("borderInlineStartColor"),borderLeftStyle:_.borderStyles("borderLeftStyle"),borderInlineStartStyle:_.borderStyles("borderInlineStartStyle"),borderRightWidth:_.borderWidths("borderRightWidth"),borderInlineEndWidth:_.borderWidths("borderInlineEndWidth"),borderRightColor:_.colors("borderRightColor"),borderInlineEndColor:_.colors("borderInlineEndColor"),borderRightStyle:_.borderStyles("borderRightStyle"),borderInlineEndStyle:_.borderStyles("borderInlineEndStyle"),borderTopRadius:_.radii(["borderTopLeftRadius","borderTopRightRadius"]),borderBottomRadius:_.radii(["borderBottomLeftRadius","borderBottomRightRadius"]),borderLeftRadius:_.radii(["borderTopLeftRadius","borderBottomLeftRadius"]),borderRightRadius:_.radii(["borderTopRightRadius","borderBottomRightRadius"])};Object.assign(j,{rounded:j.borderRadius,roundedTop:j.borderTopRadius,roundedTopLeft:j.borderTopLeftRadius,roundedTopRight:j.borderTopRightRadius,roundedTopStart:j.borderStartStartRadius,roundedTopEnd:j.borderStartEndRadius,roundedBottom:j.borderBottomRadius,roundedBottomLeft:j.borderBottomLeftRadius,roundedBottomRight:j.borderBottomRightRadius,roundedBottomStart:j.borderEndStartRadius,roundedBottomEnd:j.borderEndEndRadius,roundedLeft:j.borderLeftRadius,roundedRight:j.borderRightRadius,roundedStart:j.borderInlineStartRadius,roundedEnd:j.borderInlineEndRadius,borderStart:j.borderInlineStart,borderEnd:j.borderInlineEnd,borderTopStartRadius:j.borderStartStartRadius,borderTopEndRadius:j.borderStartEndRadius,borderBottomStartRadius:j.borderEndStartRadius,borderBottomEndRadius:j.borderEndEndRadius,borderStartRadius:j.borderInlineStartRadius,borderEndRadius:j.borderInlineEndRadius,borderStartWidth:j.borderInlineStartWidth,borderEndWidth:j.borderInlineEndWidth,borderStartColor:j.borderInlineStartColor,borderEndColor:j.borderInlineEndColor,borderStartStyle:j.borderInlineStartStyle,borderEndStyle:j.borderInlineEndStyle});let E={color:_.colors("color"),textColor:_.colors("color"),fill:_.colors("fill"),stroke:_.colors("stroke"),accentColor:_.colors("accentColor"),textFillColor:_.colors("textFillColor")},$={alignItems:!0,alignContent:!0,justifyItems:!0,justifyContent:!0,flexWrap:!0,flexDirection:{transform:P.flexDirection},flex:!0,flexFlow:!0,flexGrow:!0,flexShrink:!0,flexBasis:_.sizes("flexBasis"),justifySelf:!0,alignSelf:!0,order:!0,placeItems:!0,placeContent:!0,placeSelf:!0,gap:_.space("gap"),rowGap:_.space("rowGap"),columnGap:_.space("columnGap")};Object.assign($,{flexDir:$.flexDirection});let T={width:_.sizesT("width"),inlineSize:_.sizesT("inlineSize"),height:_.sizes("height"),blockSize:_.sizes("blockSize"),boxSize:_.sizes(["width","height"]),minWidth:_.sizes("minWidth"),minInlineSize:_.sizes("minInlineSize"),minHeight:_.sizes("minHeight"),minBlockSize:_.sizes("minBlockSize"),maxWidth:_.sizes("maxWidth"),maxInlineSize:_.sizes("maxInlineSize"),maxHeight:_.sizes("maxHeight"),maxBlockSize:_.sizes("maxBlockSize"),overflow:!0,overflowX:!0,overflowY:!0,overscrollBehavior:!0,overscrollBehaviorX:!0,overscrollBehaviorY:!0,display:!0,aspectRatio:!0,hideFrom:{scale:"breakpoints",transform:(e,t)=>{let r=t.__breakpoints?.get(e)?.minW??e,n=`@media screen and (min-width: ${r})`;return{[n]:{display:"none"}}}},hideBelow:{scale:"breakpoints",transform:(e,t)=>{let r=t.__breakpoints?.get(e)?._minW??e,n=`@media screen and (max-width: ${r})`;return{[n]:{display:"none"}}}},verticalAlign:!0,boxSizing:!0,boxDecorationBreak:!0,float:_.propT("float",P.float),objectFit:!0,objectPosition:!0,visibility:!0,isolation:!0};Object.assign(T,{w:T.width,h:T.height,minW:T.minWidth,maxW:T.maxWidth,minH:T.minHeight,maxH:T.maxHeight,overscroll:T.overscrollBehavior,overscrollX:T.overscrollBehaviorX,overscrollY:T.overscrollBehaviorY});let R={filter:{transform:P.filter},blur:_.blur("--chakra-blur"),brightness:_.propT("--chakra-brightness",P.brightness),contrast:_.propT("--chakra-contrast",P.contrast),hueRotate:_.propT("--chakra-hue-rotate",P.hueRotate),invert:_.propT("--chakra-invert",P.invert),saturate:_.propT("--chakra-saturate",P.saturate),dropShadow:_.propT("--chakra-drop-shadow",P.dropShadow),backdropFilter:{transform:P.backdropFilter},backdropBlur:_.blur("--chakra-backdrop-blur"),backdropBrightness:_.propT("--chakra-backdrop-brightness",P.brightness),backdropContrast:_.propT("--chakra-backdrop-contrast",P.contrast),backdropHueRotate:_.propT("--chakra-backdrop-hue-rotate",P.hueRotate),backdropInvert:_.propT("--chakra-backdrop-invert",P.invert),backdropSaturate:_.propT("--chakra-backdrop-saturate",P.saturate)},z={ring:{transform:P.ring},ringColor:_.colors("--chakra-ring-color"),ringOffset:_.prop("--chakra-ring-offset-width"),ringOffsetColor:_.colors("--chakra-ring-offset-color"),ringInset:_.prop("--chakra-ring-inset")},O={appearance:!0,cursor:!0,resize:!0,userSelect:!0,pointerEvents:!0,outline:{transform:P.outline},outlineOffset:!0,outlineColor:_.colors("outlineColor")},L={gridGap:_.space("gridGap"),gridColumnGap:_.space("gridColumnGap"),gridRowGap:_.space("gridRowGap"),gridColumn:!0,gridRow:!0,gridAutoFlow:!0,gridAutoColumns:!0,gridColumnStart:!0,gridColumnEnd:!0,gridRowStart:!0,gridRowEnd:!0,gridAutoRows:!0,gridTemplate:!0,gridTemplateColumns:!0,gridTemplateRows:!0,gridTemplateAreas:!0,gridArea:!0},B=(e=>{let t=new WeakMap,r=(r,n,i,o)=>{if(void 0===r)return e(r,n,i);t.has(r)||t.set(r,new Map);let a=t.get(r);if(a.has(n))return a.get(n);let s=e(r,n,i,o);return a.set(n,s),s};return r})(function(e,t,r,n){let i="string"==typeof t?t.split("."):[t];for(n=0;n<i.length&&e;n+=1)e=e[i[n]];return void 0===e?r:e}),M={border:"0px",clip:"rect(0, 0, 0, 0)",width:"1px",height:"1px",margin:"-1px",padding:"0px",overflow:"hidden",whiteSpace:"nowrap",position:"absolute"},I={position:"static",width:"auto",height:"auto",clip:"auto",padding:"0",margin:"0",overflow:"visible",whiteSpace:"normal"},D=(e,t,r)=>{let n={},i=B(e,t,{});for(let e in i){let t=e in r&&null!=r[e];t||(n[e]=i[e])}return n},F={position:!0,pos:_.prop("position"),zIndex:_.prop("zIndex","zIndices"),inset:_.spaceT("inset"),insetX:_.spaceT(["left","right"]),insetInline:_.spaceT("insetInline"),insetY:_.spaceT(["top","bottom"]),insetBlock:_.spaceT("insetBlock"),top:_.spaceT("top"),insetBlockStart:_.spaceT("insetBlockStart"),bottom:_.spaceT("bottom"),insetBlockEnd:_.spaceT("insetBlockEnd"),left:_.spaceT("left"),insetInlineStart:_.logical({scale:"space",property:{ltr:"left",rtl:"right"}}),right:_.spaceT("right"),insetInlineEnd:_.logical({scale:"space",property:{ltr:"right",rtl:"left"}})};Object.assign(F,{insetStart:F.insetInlineStart,insetEnd:F.insetInlineEnd});let V={boxShadow:_.shadows("boxShadow"),mixBlendMode:!0,blendMode:_.prop("mixBlendMode"),backgroundBlendMode:!0,bgBlendMode:_.prop("backgroundBlendMode"),opacity:!0};Object.assign(V,{shadow:V.boxShadow});let N={margin:_.spaceT("margin"),marginTop:_.spaceT("marginTop"),marginBlockStart:_.spaceT("marginBlockStart"),marginRight:_.spaceT("marginRight"),marginInlineEnd:_.spaceT("marginInlineEnd"),marginBottom:_.spaceT("marginBottom"),marginBlockEnd:_.spaceT("marginBlockEnd"),marginLeft:_.spaceT("marginLeft"),marginInlineStart:_.spaceT("marginInlineStart"),marginX:_.spaceT(["marginInlineStart","marginInlineEnd"]),marginInline:_.spaceT("marginInline"),marginY:_.spaceT(["marginTop","marginBottom"]),marginBlock:_.spaceT("marginBlock"),padding:_.space("padding"),paddingTop:_.space("paddingTop"),paddingBlockStart:_.space("paddingBlockStart"),paddingRight:_.space("paddingRight"),paddingBottom:_.space("paddingBottom"),paddingBlockEnd:_.space("paddingBlockEnd"),paddingLeft:_.space("paddingLeft"),paddingInlineStart:_.space("paddingInlineStart"),paddingInlineEnd:_.space("paddingInlineEnd"),paddingX:_.space(["paddingInlineStart","paddingInlineEnd"]),paddingInline:_.space("paddingInline"),paddingY:_.space(["paddingTop","paddingBottom"]),paddingBlock:_.space("paddingBlock")};Object.assign(N,{m:N.margin,mt:N.marginTop,mr:N.marginRight,me:N.marginInlineEnd,marginEnd:N.marginInlineEnd,mb:N.marginBottom,ml:N.marginLeft,ms:N.marginInlineStart,marginStart:N.marginInlineStart,mx:N.marginX,my:N.marginY,p:N.padding,pt:N.paddingTop,py:N.paddingY,px:N.paddingX,pb:N.paddingBottom,pl:N.paddingLeft,ps:N.paddingInlineStart,paddingStart:N.paddingInlineStart,pr:N.paddingRight,pe:N.paddingInlineEnd,paddingEnd:N.paddingInlineEnd});let W={scrollBehavior:!0,scrollSnapAlign:!0,scrollSnapStop:!0,scrollSnapType:!0,scrollMargin:_.spaceT("scrollMargin"),scrollMarginTop:_.spaceT("scrollMarginTop"),scrollMarginBottom:_.spaceT("scrollMarginBottom"),scrollMarginLeft:_.spaceT("scrollMarginLeft"),scrollMarginRight:_.spaceT("scrollMarginRight"),scrollMarginX:_.spaceT(["scrollMarginLeft","scrollMarginRight"]),scrollMarginY:_.spaceT(["scrollMarginTop","scrollMarginBottom"]),scrollPadding:_.spaceT("scrollPadding"),scrollPaddingTop:_.spaceT("scrollPaddingTop"),scrollPaddingBottom:_.spaceT("scrollPaddingBottom"),scrollPaddingLeft:_.spaceT("scrollPaddingLeft"),scrollPaddingRight:_.spaceT("scrollPaddingRight"),scrollPaddingX:_.spaceT(["scrollPaddingLeft","scrollPaddingRight"]),scrollPaddingY:_.spaceT(["scrollPaddingTop","scrollPaddingBottom"])},U={fontFamily:_.prop("fontFamily","fonts"),fontSize:_.prop("fontSize","fontSizes",P.px),fontWeight:_.prop("fontWeight","fontWeights"),lineHeight:_.prop("lineHeight","lineHeights"),letterSpacing:_.prop("letterSpacing","letterSpacings"),textAlign:!0,fontStyle:!0,textIndent:!0,wordBreak:!0,overflowWrap:!0,textOverflow:!0,textTransform:!0,whiteSpace:!0,isTruncated:{transform(e){if(!0===e)return{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}}},noOfLines:{static:{overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitBoxOrient:"vertical",WebkitLineClamp:"var(--chakra-line-clamp)"},property:"--chakra-line-clamp"}},H={textDecorationColor:_.colors("textDecorationColor"),textDecoration:!0,textDecor:{property:"textDecoration"},textDecorationLine:!0,textDecorationStyle:!0,textDecorationThickness:!0,textUnderlineOffset:!0,textShadow:_.shadows("textShadow")},J={clipPath:!0,transform:_.propT("transform",P.transform),transformOrigin:!0,translateX:_.spaceT("--chakra-translate-x"),translateY:_.spaceT("--chakra-translate-y"),skewX:_.degreeT("--chakra-skew-x"),skewY:_.degreeT("--chakra-skew-y"),scaleX:_.prop("--chakra-scale-x"),scaleY:_.prop("--chakra-scale-y"),scale:_.prop(["--chakra-scale-x","--chakra-scale-y"]),rotate:_.degreeT("--chakra-rotate")},q={listStyleType:!0,listStylePosition:!0,listStylePos:_.prop("listStylePosition"),listStyleImage:!0,listStyleImg:_.prop("listStyleImage")},Z={transition:!0,transitionDelay:!0,animation:!0,willChange:!0,transitionDuration:_.prop("transitionDuration","transition.duration"),transitionProperty:_.prop("transitionProperty","transition.property"),transitionTimingFunction:_.prop("transitionTimingFunction","transition.easing")},G=n({},A,j,E,$,T,R,z,O,L,{srOnly:{transform:e=>!0===e?M:"focusable"===e?I:{}},layerStyle:{processResult:!0,transform:(e,t,r)=>D(t,`layerStyles.${e}`,r)},textStyle:{processResult:!0,transform:(e,t,r)=>D(t,`textStyles.${e}`,r)},apply:{processResult:!0,transform:(e,t,r)=>D(t,e,r)}},F,V,N,W,U,H,J,q,Z),Y=Object.assign({},N,T,$,L,F),K=Object.keys(Y),X=[...Object.keys(G),...i._],Q={...G,...i.v},ee=e=>e in Q},65544:function(e,t,r){"use strict";r.d(t,{L:function(){return i}});var n=r(8297);function i(e){return(0,n.C)(e,["styleConfig","size","variant","colorScheme"])}},15292:function(e,t,r){"use strict";r.d(t,{Y:function(){return u},y:function(){return c}});var n=r(79115);function i(e){if(null==e)return e;let{unitless:t}=function(e){let t=parseFloat(e.toString()),r=e.toString().replace(String(t),"");return{unitless:!r,value:t,unit:r}}(e);return t||"number"==typeof e?`${e}px`:e}let o=(e,t)=>parseInt(e[1],10)>parseInt(t[1],10)?1:-1,a=e=>Object.fromEntries(Object.entries(e).sort(o));function s(e){let t=a(e);return Object.assign(Object.values(t),t)}function l(e){return e?"number"==typeof(e=i(e)??e)?`${e+-.02}`:e.replace(/(\d+\.?\d*)/u,e=>`${parseFloat(e)+-.02}`):e}function u(e,t){let r=["@media screen"];return e&&r.push("and",`(min-width: ${i(e)})`),t&&r.push("and",`(max-width: ${i(t)})`),r.join(" ")}function c(e){if(!e)return null;e.base=e.base??"0px";let t=s(e),r=Object.entries(e).sort(o).map(([e,t],r,n)=>{let[,i]=n[r+1]??[];return i=parseFloat(i)>0?l(i):void 0,{_minW:l(t),breakpoint:e,minW:t,maxW:i,maxWQuery:u(null,i),minWQuery:u(t),minMaxQuery:u(t,i)}}),i=function(e){let t=Object.keys(a(e));return new Set(t)}(e),c=Array.from(i.values());return{keys:i,normalized:t,isResponsive(e){let t=Object.keys(e);return t.length>0&&t.every(e=>i.has(e))},asObject:a(e),asArray:s(e),details:r,get:e=>r.find(t=>t.breakpoint===e),media:[null,...t.map(e=>u(e)).slice(1)],toArrayValue(e){if(!(0,n.Kn)(e))throw Error("toArrayValue: value must be an object");let t=c.map(t=>e[t]??null);for(;null===function(e){let t=null==e?0:e.length;return t?e[t-1]:void 0}(t);)t.pop();return t},toObjectValue(e){if(!Array.isArray(e))throw Error("toObjectValue: value must be an array");return e.reduce((e,t,r)=>{let n=c[r];return null!=n&&null!=t&&(e[n]=t),e},{})}}}},87155:function(e,t,r){"use strict";function n(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}r.d(t,{o:function(){return n}})},52110:function(e,t,r){"use strict";r.d(t,{k:function(){return i}});var n=r(67294);function i(e={}){let{name:t,strict:r=!0,hookName:i="useContext",providerName:o="Provider",errorMessage:a,defaultValue:s}=e,l=(0,n.createContext)(s);return l.displayName=t,[l.Provider,function e(){let t=(0,n.useContext)(l);if(!t&&r){let t=Error(a??`${i} returned \`undefined\`. Seems you forgot to wrap component within ${o}`);throw t.name="ContextError",Error.captureStackTrace?.(t,e),t}return t},l]}},34926:function(e,t,r){"use strict";r.d(t,{cx:function(){return n}});let n=(...e)=>e.filter(Boolean).join(" ")},4839:function(e,t,r){"use strict";r.d(t,{W:function(){return n}});let n=(e=>{let t=new WeakMap,r=(r,n,i,o)=>{if(void 0===r)return e(r,n,i);t.has(r)||t.set(r,new Map);let a=t.get(r);if(a.has(n))return a.get(n);let s=e(r,n,i,o);return a.set(n,s),s};return r})(function(e,t,r,n){let i="string"==typeof t?t.split("."):[t];for(n=0;n<i.length&&e;n+=1)e=e[i[n]];return void 0===e?r:e})},21326:function(e,t,r){"use strict";function n(e){return null!=e&&"object"==typeof e&&"nodeType"in e&&e.nodeType===Node.ELEMENT_NODE}function i(){return Boolean(globalThis?.document)}function o(e){let t=e.getAttribute("contenteditable");return"false"!==t&&null!=t}function a(e){return!0===Boolean(e.getAttribute("disabled"))||!0===Boolean(e.getAttribute("aria-disabled"))}r.d(t,{Re:function(){return n},iU:function(){return o},jU:function(){return i},nV:function(){return a},oI:function(){return function e(t){return!!(t.parentElement&&e(t.parentElement))||t.hidden}}})},79115:function(e,t,r){"use strict";function n(e){let t=typeof e;return null!=e&&("object"===t||"function"===t)&&!Array.isArray(e)}r.d(t,{Kn:function(){return n}})},8297:function(e,t,r){"use strict";function n(e,t=[]){let r=Object.assign({},e);for(let e of t)e in r&&delete r[e];return r}r.d(t,{C:function(){return n}})},70562:function(e,t,r){"use strict";function n(e,t){let r={};for(let n of t)n in e&&(r[n]=e[n]);return r}r.d(t,{e:function(){return n}})},2847:function(e,t,r){"use strict";r.d(t,{P:function(){return i}});let n=e=>"function"==typeof e;function i(e,...t){return n(e)?e(...t):e}},51526:function(e,t,r){"use strict";r.d(t,{M:function(){return m}});var n=r(67294),i=r(58868);function o(){let e=(0,n.useRef)(!1);return(0,i.L)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var a=r(2074),s=r(240),l=r(96681);class u extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:e,isPresent:t}){let r=(0,n.useId)(),i=(0,n.useRef)(null),o=(0,n.useRef)({width:0,height:0,top:0,left:0});return(0,n.useInsertionEffect)(()=>{let{width:e,height:n,top:a,left:s}=o.current;if(t||!i.current||!e||!n)return;i.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            top: ${a}px !important;
            left: ${s}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),n.createElement(u,{isPresent:t,childRef:i,sizeRef:o},n.cloneElement(e,{ref:i}))}let d=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:o,presenceAffectsLayout:a,mode:u})=>{let d=(0,l.h)(h),p=(0,n.useId)(),f=(0,n.useMemo)(()=>({id:p,initial:t,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;i&&i()},register:e=>(d.set(e,!1),()=>d.delete(e))}),a?void 0:[r]);return(0,n.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[r]),n.useEffect(()=>{r||d.size||!i||i()},[r]),"popLayout"===u&&(e=n.createElement(c,{isPresent:r},e)),n.createElement(s.O.Provider,{value:f},e)};function h(){return new Map}var p=r(25364),f=r(45487);let g=e=>e.key||"",m=({children:e,custom:t,initial:r=!0,onExitComplete:s,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:c="sync"})=>{var h;(0,f.k)(!l,"Replace exitBeforeEnter with mode='wait'");let m=(0,n.useContext)(p.p).forceRender||function(){let e=o(),[t,r]=(0,n.useState)(0),i=(0,n.useCallback)(()=>{e.current&&r(t+1)},[t]),s=(0,n.useCallback)(()=>a.Wi.postRender(i),[i]);return[s,t]}()[0],b=o(),y=function(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}(e),v=y,x=(0,n.useRef)(new Map).current,k=(0,n.useRef)(v),S=(0,n.useRef)(new Map).current,w=(0,n.useRef)(!0);if((0,i.L)(()=>{w.current=!1,function(e,t){e.forEach(e=>{let r=g(e);t.set(r,e)})}(y,S),k.current=v}),h=()=>{w.current=!0,S.clear(),x.clear()},(0,n.useEffect)(()=>()=>h(),[]),w.current)return n.createElement(n.Fragment,null,v.map(e=>n.createElement(d,{key:g(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:u,mode:c},e)));v=[...v];let C=k.current.map(g),P=y.map(g),_=C.length;for(let e=0;e<_;e++){let t=C[e];-1!==P.indexOf(t)||x.has(t)||x.set(t,void 0)}return"wait"===c&&x.size&&(v=[]),x.forEach((e,r)=>{if(-1!==P.indexOf(r))return;let i=S.get(r);if(!i)return;let o=C.indexOf(r),a=e;if(!a){let e=()=>{x.delete(r);let e=Array.from(S.keys()).filter(e=>!P.includes(e));if(e.forEach(e=>S.delete(e)),k.current=y.filter(t=>{let n=g(t);return n===r||e.includes(n)}),!x.size){if(!1===b.current)return;m(),s&&s()}};a=n.createElement(d,{key:g(i),isPresent:!1,onExitComplete:e,custom:t,presenceAffectsLayout:u,mode:c},i),x.set(r,a)}v.splice(o,0,a)}),v=v.map(e=>{let t=e.key;return x.has(t)?e:n.createElement(d,{key:g(e),isPresent:!0,presenceAffectsLayout:u,mode:c},e)}),n.createElement(n.Fragment,null,x.size?v:v.map(e=>(0,n.cloneElement)(e)))}},15947:function(e,t,r){"use strict";r.d(t,{hO:function(){return a},oO:function(){return o}});var n=r(67294),i=r(240);function o(){let e=(0,n.useContext)(i.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:r,register:o}=e,a=(0,n.useId)();(0,n.useEffect)(()=>o(a),[]);let s=()=>r&&r(a);return!t&&r?[!1,s]:[!0]}function a(){var e;return null===(e=(0,n.useContext)(i.O))||e.isPresent}},25364:function(e,t,r){"use strict";r.d(t,{p:function(){return i}});var n=r(67294);let i=(0,n.createContext)({})},240:function(e,t,r){"use strict";r.d(t,{O:function(){return i}});var n=r(67294);let i=(0,n.createContext)(null)},2074:function(e,t,r){"use strict";r.d(t,{Pn:function(){return s},Wi:function(){return a},w0:function(){return l},S6:function(){return u}});var n=r(81662);class i{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let o=["prepare","read","update","preRender","render","postRender"],{schedule:a,cancel:s,state:l,steps:u}=function(e,t){let r=!1,n=!0,a={delta:0,timestamp:0,isProcessing:!1},s=o.reduce((e,t)=>(e[t]=function(e){let t=new i,r=new i,n=0,o=!1,a=!1,s=new WeakSet,l={schedule:(e,i=!1,a=!1)=>{let l=a&&o,u=l?t:r;return i&&s.add(e),u.add(e)&&l&&o&&(n=t.order.length),e},cancel:e=>{r.remove(e),s.delete(e)},process:i=>{if(o){a=!0;return}if(o=!0,[t,r]=[r,t],r.clear(),n=t.order.length)for(let r=0;r<n;r++){let n=t.order[r];n(i),s.has(n)&&(l.schedule(n),e())}o=!1,a&&(a=!1,l.process(i))}};return l}(()=>r=!0),e),{}),l=e=>s[e].process(a),u=()=>{let i=performance.now();r=!1,a.delta=n?1e3/60:Math.max(Math.min(i-a.timestamp,40),1),a.timestamp=i,a.isProcessing=!0,o.forEach(l),a.isProcessing=!1,r&&t&&(n=!1,e(u))},c=()=>{r=!0,n=!0,a.isProcessing||e(u)},d=o.reduce((e,t)=>{let n=s[t];return e[t]=(e,t=!1,i=!1)=>(r||c(),n.schedule(e,t,i)),e},{}),h=e=>o.forEach(t=>s[t].cancel(e));return{schedule:d,cancel:h,state:a,steps:s}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},97340:function(e,t,r){"use strict";let n;r.d(t,{E:function(){return iV}});var i,o,a=r(67294);let s=(0,a.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),l=(0,a.createContext)({});var u=r(240),c=r(58868);let d=(0,a.createContext)({strict:!1}),h=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+h("framerAppearId");function f(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function g(e){return"string"==typeof e||Array.isArray(e)}function m(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let b=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],y=["initial",...b];function v(e){return m(e.animate)||y.some(t=>g(e[t]))}function x(e){return Boolean(v(e)||e.variants)}function k(e){return Array.isArray(e)?e.join(" "):e}let S={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},w={};for(let e in S)w[e]={isEnabled:t=>S[e].some(e=>!!t[e])};var C=r(11741),P=r(25364);let _=(0,a.createContext)({}),A=Symbol.for("motionComponentSymbol"),j=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function E(e){if("string"!=typeof e||e.includes("-"));else if(j.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let $={},T=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],R=new Set(T);function z(e,{layout:t,layoutId:r}){return R.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!$[e]||"opacity"===e)}let O=e=>Boolean(e&&e.getVelocity),L={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},B=T.length,M=e=>t=>"string"==typeof t&&t.startsWith(e),I=M("--"),D=M("var(--"),F=(e,t)=>t&&"number"==typeof e?t.transform(e):e,V=(e,t,r)=>Math.min(Math.max(r,e),t),N={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},W={...N,transform:e=>V(0,1,e)},U={...N,default:1},H=e=>Math.round(1e5*e)/1e5,J=/(-)?([\d]*\.?[\d])+/g,q=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Z=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function G(e){return"string"==typeof e}let Y=e=>({test:t=>G(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),K=Y("deg"),X=Y("%"),Q=Y("px"),ee=Y("vh"),et=Y("vw"),er={...X,parse:e=>X.parse(e)/100,transform:e=>X.transform(100*e)},en={...N,transform:Math.round},ei={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:K,rotateX:K,rotateY:K,rotateZ:K,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:K,skewX:K,skewY:K,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:W,originX:er,originY:er,originZ:Q,zIndex:en,fillOpacity:W,strokeOpacity:W,numOctaves:en};function eo(e,t,r,n){let{style:i,vars:o,transform:a,transformOrigin:s}=e,l=!1,u=!1,c=!0;for(let e in t){let r=t[e];if(I(e)){o[e]=r;continue}let n=ei[e],d=F(r,n);if(R.has(e)){if(l=!0,a[e]=d,!c)continue;r!==(n.default||0)&&(c=!1)}else e.startsWith("origin")?(u=!0,s[e]=d):i[e]=d}if(!t.transform&&(l||n?i.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},n,i){let o="";for(let t=0;t<B;t++){let r=T[t];if(void 0!==e[r]){let t=L[r]||r;o+=`${t}(${e[r]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,n?"":o):r&&n&&(o="none"),o}(e.transform,r,c,n):i.transform&&(i.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:r=0}=s;i.transformOrigin=`${e} ${t} ${r}`}}let ea=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function es(e,t,r){for(let n in t)O(t[n])||z(n,r)||(e[n]=t[n])}function el(e,t,r){let n={},i=function(e,t,r){let n=e.style||{},i={};return es(i,n,e),Object.assign(i,function({transformTemplate:e},t,r){return(0,a.useMemo)(()=>{let n=ea();return eo(n,t,{enableHardwareAcceleration:!r},e),Object.assign({},n.vars,n.style)},[t])}(e,t,r)),e.transformValues?e.transformValues(i):i}(e,t,r);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=i,n}let eu=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ec(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||eu.has(e)}let ed=e=>!ec(e);try{(i=require("@emotion/is-prop-valid").default)&&(ed=e=>e.startsWith("on")?!ec(e):i(e))}catch(e){}function eh(e,t,r){return"string"==typeof e?e:Q.transform(t+r*e)}let ep={offset:"stroke-dashoffset",array:"stroke-dasharray"},ef={offset:"strokeDashoffset",array:"strokeDasharray"};function eg(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:o,pathLength:a,pathSpacing:s=1,pathOffset:l=0,...u},c,d,h){if(eo(e,u,c,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:f,dimensions:g}=e;p.transform&&(g&&(f.transform=p.transform),delete p.transform),g&&(void 0!==i||void 0!==o||f.transform)&&(f.transformOrigin=function(e,t,r){let n=eh(t,e.x,e.width),i=eh(r,e.y,e.height);return`${n} ${i}`}(g,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(p.x=t),void 0!==r&&(p.y=r),void 0!==n&&(p.scale=n),void 0!==a&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?ep:ef;e[o.offset]=Q.transform(-n);let a=Q.transform(t),s=Q.transform(r);e[o.array]=`${a} ${s}`}(p,a,s,l,!1)}let em=()=>({...ea(),attrs:{}}),eb=e=>"string"==typeof e&&"svg"===e.toLowerCase();function ey(e,t,r,n){let i=(0,a.useMemo)(()=>{let r=em();return eg(r,t,{enableHardwareAcceleration:!1},eb(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};es(t,e.style,e),i.style={...t,...i.style}}return i}function ev(e,{style:t,vars:r},n,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(o,r[o])}let ex=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ek(e,t,r,n){for(let r in ev(e,t,void 0,n),t.attrs)e.setAttribute(ex.has(r)?r:h(r),t.attrs[r])}function eS(e,t){let{style:r}=e,n={};for(let i in r)(O(r[i])||t.style&&O(t.style[i])||z(i,e))&&(n[i]=r[i]);return n}function ew(e,t){let r=eS(e,t);for(let n in e)if(O(e[n])||O(t[n])){let t=-1!==T.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n;r[t]=e[n]}return r}function eC(e,t,r,n={},i={}){return"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,i)),t}var eP=r(96681);let e_=e=>Array.isArray(e),eA=e=>Boolean(e&&"object"==typeof e&&e.mix&&e.toValue),ej=e=>e_(e)?e[e.length-1]||0:e;function eE(e){let t=O(e)?e.get():e;return eA(t)?t.toValue():t}let e$=e=>(t,r)=>{let n=(0,a.useContext)(l),i=(0,a.useContext)(u.O),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,i,o){let a={latestValues:function(e,t,r,n){let i={},o=n(e,{});for(let e in o)i[e]=eE(o[e]);let{initial:a,animate:s}=e,l=v(e),u=x(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===s&&(s=t.animate));let c=!!r&&!1===r.initial;c=c||!1===a;let d=c?s:a;if(d&&"boolean"!=typeof d&&!m(d)){let t=Array.isArray(d)?d:[d];t.forEach(t=>{let r=eC(e,t);if(!r)return;let{transitionEnd:n,transition:o,...a}=r;for(let e in a){let t=a[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let e in n)i[e]=n[e]})}return i}(n,i,o,e),renderState:t()};return r&&(a.mount=e=>r(n,e,a)),a})(e,t,n,i);return r?o():(0,eP.h)(o)};var eT=r(2074);let eR={useVisualState:e$({scrapeMotionValuesFromProps:ew,createRenderState:em,onMount:(e,t,{renderState:r,latestValues:n})=>{eT.Wi.read(()=>{try{r.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),eT.Wi.render(()=>{eg(r,n,{enableHardwareAcceleration:!1},eb(t.tagName),e.transformTemplate),ek(t,r)})}})},ez={useVisualState:e$({scrapeMotionValuesFromProps:eS,createRenderState:ea})};function eO(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let eL=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eB(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let eM=e=>t=>eL(t)&&e(t,eB(t));function eI(e,t,r,n){return eO(e,t,eM(r),n)}let eD=(e,t)=>r=>t(e(r)),eF=(...e)=>e.reduce(eD);function eV(e){let t=null;return()=>{let r=()=>{t=null};return null===t&&(t=e,r)}}let eN=eV("dragHorizontal"),eW=eV("dragVertical");function eU(e){let t=!1;if("y"===e)t=eW();else if("x"===e)t=eN();else{let e=eN(),r=eW();e&&r?t=()=>{e(),r()}:(e&&e(),r&&r())}return t}function eH(){let e=eU(!0);return!e||(e(),!1)}class eJ{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eq(e,t){let r="onHover"+(t?"Start":"End"),n=(n,i)=>{if("touch"===n.pointerType||eH())return;let o=e.getProps();e.animationState&&o.whileHover&&e.animationState.setActive("whileHover",t),o[r]&&eT.Wi.update(()=>o[r](n,i))};return eI(e.current,"pointer"+(t?"enter":"leave"),n,{passive:!e.getProps()[r]})}let eZ=(e,t)=>!!t&&(e===t||eZ(e,t.parentElement));var eG=r(81662);function eY(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,eB(r))}let eK=new WeakMap,eX=new WeakMap,eQ=e=>{let t=eK.get(e.target);t&&t(e)},e0=e=>{e.forEach(eQ)},e1={some:0,all:1};function e2(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function e5(e,t,r){let n=e.getProps();return eC(n,t,void 0!==r?r:n.custom,function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.getVelocity()),t}(e))}var e4=r(45487);let e3=e=>1e3*e,e6=e=>e/1e3,e8={current:!1},e9=e=>Array.isArray(e)&&"number"==typeof e[0],e7=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,te={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e7([0,.65,.55,1]),circOut:e7([.55,0,1,.45]),backIn:e7([.31,.01,.66,-.59]),backOut:e7([.33,1.53,.69,.99])},tt=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function tr(e,t,r,n){if(e===t&&r===n)return eG.Z;let i=t=>(function(e,t,r,n,i){let o,a;let s=0;do(o=tt(a=t+(r-t)/2,n,i)-e)>0?r=a:t=a;while(Math.abs(o)>1e-7&&++s<12);return a})(t,0,1,e,r);return e=>0===e||1===e?e:tt(i(e),t,n)}let tn=tr(.42,0,1,1),ti=tr(0,0,.58,1),to=tr(.42,0,.58,1),ta=e=>Array.isArray(e)&&"number"!=typeof e[0],ts=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,tl=e=>t=>1-e(1-t),tu=e=>1-Math.sin(Math.acos(e)),tc=tl(tu),td=ts(tu),th=tr(.33,1.53,.69,.99),tp=tl(th),tf=ts(tp),tg=e=>(e*=2)<1?.5*tp(e):.5*(2-Math.pow(2,-10*(e-1))),tm={linear:eG.Z,easeIn:tn,easeInOut:to,easeOut:ti,circIn:tu,circInOut:td,circOut:tc,backIn:tp,backInOut:tf,backOut:th,anticipate:tg},tb=e=>{if(Array.isArray(e)){(0,e4.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return tr(t,r,n,i)}return"string"==typeof e?((0,e4.k)(void 0!==tm[e],`Invalid easing type '${e}'`),tm[e]):e},ty=(e,t)=>r=>Boolean(G(r)&&Z.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),tv=(e,t,r)=>n=>{if(!G(n))return n;let[i,o,a,s]=n.match(J);return{[e]:parseFloat(i),[t]:parseFloat(o),[r]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},tx=e=>V(0,255,e),tk={...N,transform:e=>Math.round(tx(e))},tS={test:ty("rgb","red"),parse:tv("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+tk.transform(e)+", "+tk.transform(t)+", "+tk.transform(r)+", "+H(W.transform(n))+")"},tw={test:ty("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:tS.transform},tC={test:ty("hsl","hue"),parse:tv("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+X.transform(H(t))+", "+X.transform(H(r))+", "+H(W.transform(n))+")"},tP={test:e=>tS.test(e)||tw.test(e)||tC.test(e),parse:e=>tS.test(e)?tS.parse(e):tC.test(e)?tC.parse(e):tw.parse(e),transform:e=>G(e)?e:e.hasOwnProperty("red")?tS.transform(e):tC.transform(e)},t_=(e,t,r)=>-r*e+r*t+e;function tA(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}let tj=(e,t,r)=>{let n=e*e;return Math.sqrt(Math.max(0,r*(t*t-n)+n))},tE=[tw,tS,tC],t$=e=>tE.find(t=>t.test(e));function tT(e){let t=t$(e);(0,e4.k)(Boolean(t),`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===tC&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,a=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,s=2*r-n;i=tA(s,n,e+1/3),o=tA(s,n,e),a=tA(s,n,e-1/3)}else i=o=a=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:n}}(r)),r}let tR=(e,t)=>{let r=tT(e),n=tT(t),i={...r};return e=>(i.red=tj(r.red,n.red,e),i.green=tj(r.green,n.green,e),i.blue=tj(r.blue,n.blue,e),i.alpha=t_(r.alpha,n.alpha,e),tS.transform(i))},tz={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:eG.Z},tO={regex:q,countKey:"Colors",token:"${c}",parse:tP.parse},tL={regex:J,countKey:"Numbers",token:"${n}",parse:N.parse};function tB(e,{regex:t,countKey:r,token:n,parse:i}){let o=e.tokenised.match(t);o&&(e["num"+r]=o.length,e.tokenised=e.tokenised.replace(t,n),e.values.push(...o.map(i)))}function tM(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&tB(r,tz),tB(r,tO),tB(r,tL),r}function tI(e){return tM(e).values}function tD(e){let{values:t,numColors:r,numVars:n,tokenised:i}=tM(e),o=t.length;return e=>{let t=i;for(let i=0;i<o;i++)t=i<n?t.replace(tz.token,e[i]):i<n+r?t.replace(tO.token,tP.transform(e[i])):t.replace(tL.token,H(e[i]));return t}}let tF=e=>"number"==typeof e?0:e,tV={test:function(e){var t,r;return isNaN(e)&&G(e)&&((null===(t=e.match(J))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(q))||void 0===r?void 0:r.length)||0)>0},parse:tI,createTransformer:tD,getAnimatableNone:function(e){let t=tI(e),r=tD(e);return r(t.map(tF))}},tN=(e,t)=>r=>`${r>0?t:e}`;function tW(e,t){return"number"==typeof e?r=>t_(e,t,r):tP.test(e)?tR(e,t):e.startsWith("var(")?tN(e,t):tJ(e,t)}let tU=(e,t)=>{let r=[...e],n=r.length,i=e.map((e,r)=>tW(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}},tH=(e,t)=>{let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=tW(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}},tJ=(e,t)=>{let r=tV.createTransformer(t),n=tM(e),i=tM(t),o=n.numVars===i.numVars&&n.numColors===i.numColors&&n.numNumbers>=i.numNumbers;return o?eF(tU(n.values,i.values),r):((0,e4.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tN(e,t))},tq=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n},tZ=(e,t)=>r=>t_(e,t,r);function tG(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let o=e.length;if((0,e4.k)(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,r){let n=[],i=r||function(e){if("number"==typeof e);else if("string"==typeof e)return tP.test(e)?tR:tJ;else if(Array.isArray(e))return tU;else if("object"==typeof e)return tH;return tZ}(e[0]),o=e.length-1;for(let r=0;r<o;r++){let o=i(e[r],e[r+1]);if(t){let e=Array.isArray(t)?t[r]||eG.Z:t;o=eF(e,o)}n.push(o)}return n}(t,n,i),s=a.length,l=t=>{let r=0;if(s>1)for(;r<e.length-2&&!(t<e[r+1]);r++);let n=tq(e[r],e[r+1],t);return a[r](n)};return r?t=>l(V(e[0],e[o-1],t)):l}function tY({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=ta(n)?n.map(tb):tb(n),o={done:!1,value:t[0]},a=(r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=tq(0,t,n);e.push(t_(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),s=tG(a,t,{ease:Array.isArray(i)?i:t.map(()=>i||to).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=s(t),o.done=t>=e,o)}}function tK(e,t,r){var n,i;let o=Math.max(t-5,0);return n=r-e(o),(i=t-o)?n*(1e3/i):0}function tX(e,t){return e*Math.sqrt(1-t*t)}let tQ=["duration","bounce"],t0=["stiffness","damping","mass"];function t1(e,t){return t.some(t=>void 0!==e[t])}function t2({keyframes:e,restDelta:t,restSpeed:r,...n}){let i;let o=e[0],a=e[e.length-1],s={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:d,velocity:h,isResolvedFromDuration:p}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!t1(e,t0)&&t1(e,tQ)){let r=function({duration:e=800,bounce:t=.25,velocity:r=0,mass:n=1}){let i,o;(0,e4.K)(e<=e3(10),"Spring duration must be 10 seconds or less");let a=1-t;a=V(.05,1,a),e=V(.01,10,e6(e)),a<1?(i=t=>{let n=t*a,i=n*e,o=tX(t,a);return .001-(n-r)/o*Math.exp(-i)},o=t=>{let n=t*a,o=n*e,s=Math.pow(a,2)*Math.pow(t,2)*e,l=tX(Math.pow(t,2),a),u=-i(t)+.001>0?-1:1;return u*((o*r+r-s)*Math.exp(-o))/l}):(i=t=>{let n=Math.exp(-t*e),i=(t-r)*e+1;return -.001+n*i},o=t=>{let n=Math.exp(-t*e),i=(r-t)*(e*e);return n*i});let s=5/e,l=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,o,s);if(e=e3(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(l,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:1}).isResolvedFromDuration=!0}return t}({...n,velocity:-e6(n.velocity||0)}),f=h||0,g=u/(2*Math.sqrt(l*c)),m=a-o,b=e6(Math.sqrt(l/c)),y=5>Math.abs(m);if(r||(r=y?.01:2),t||(t=y?.005:.5),g<1){let e=tX(b,g);i=t=>a-Math.exp(-g*b*t)*((f+g*b*m)/e*Math.sin(e*t)+m*Math.cos(e*t))}else if(1===g)i=e=>a-Math.exp(-b*e)*(m+(f+b*m)*e);else{let e=b*Math.sqrt(g*g-1);i=t=>{let r=Math.min(e*t,300);return a-Math.exp(-g*b*t)*((f+g*b*m)*Math.sinh(r)+e*m*Math.cosh(r))/e}}return{calculatedDuration:p&&d||null,next:e=>{let n=i(e);if(p)s.done=e>=d;else{let o=f;0!==e&&(o=g<1?tK(i,e,n):0);let l=Math.abs(o)<=r,u=Math.abs(a-n)<=t;s.done=l&&u}return s.value=s.done?a:n,s}}}function t5({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,h;let p=e[0],f={done:!1,value:p},g=e=>void 0!==s&&e<s||void 0!==l&&e>l,m=e=>void 0===s?l:void 0===l?s:Math.abs(s-e)<Math.abs(l-e)?s:l,b=r*t,y=p+b,v=void 0===a?y:a(y);v!==y&&(b=v-p);let x=e=>-b*Math.exp(-e/n),k=e=>v+x(e),S=e=>{let t=x(e),r=k(e);f.done=Math.abs(t)<=u,f.value=f.done?v:r},w=e=>{g(f.value)&&(d=e,h=t2({keyframes:[f.value,m(f.value)],velocity:tK(k,e,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,S(e),w(e)),void 0!==d&&e>d)?h.next(e-d):(t||S(e),f)}}}let t4=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eT.Wi.update(t,!0),stop:()=>(0,eT.Pn)(t),now:()=>eT.w0.isProcessing?eT.w0.timestamp:performance.now()}};function t3(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}let t6={decay:t5,inertia:t5,tween:tY,keyframes:tY,spring:t2};function t8({autoplay:e=!0,delay:t=0,driver:r=t4,keyframes:n,type:i="keyframes",repeat:o=0,repeatDelay:a=0,repeatType:s="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...h}){let p,f,g,m,b,y=1,v=!1,x=()=>{f=new Promise(e=>{p=e})};x();let k=t6[i]||tY;k!==tY&&"number"!=typeof n[0]&&(m=tG([0,100],n,{clamp:!1}),n=[0,100]);let S=k({...h,keyframes:n});"mirror"===s&&(b=k({...h,keyframes:[...n].reverse(),velocity:-(h.velocity||0)}));let w="idle",C=null,P=null,_=null;null===S.calculatedDuration&&o&&(S.calculatedDuration=t3(S));let{calculatedDuration:A}=S,j=1/0,E=1/0;null!==A&&(E=(j=A+a)*(o+1)-a);let $=0,T=e=>{if(null===P)return;y>0&&(P=Math.min(P,e)),y<0&&(P=Math.min(e-E/y,P)),$=null!==C?C:Math.round(e-P)*y;let r=$-t*(y>=0?1:-1),i=y>=0?r<0:r>E;$=Math.max(r,0),"finished"===w&&null===C&&($=E);let l=$,u=S;if(o){let e=Math.min($,E)/j,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,t=Math.min(t,o+1);let n=Boolean(t%2);n&&("reverse"===s?(r=1-r,a&&(r-=a/j)):"mirror"===s&&(u=b)),l=V(0,1,r)*j}let c=i?{done:!1,value:n[0]}:u.next(l);m&&(c.value=m(c.value));let{done:h}=c;i||null===A||(h=y>=0?$>=E:$<=0);let p=null===C&&("finished"===w||"running"===w&&h);return d&&d(c.value),p&&O(),c},R=()=>{g&&g.stop(),g=void 0},z=()=>{w="idle",R(),p(),x(),P=_=null},O=()=>{w="finished",c&&c(),R(),p()},L=()=>{if(v)return;g||(g=r(T));let e=g.now();l&&l(),null!==C?P=e-C:P&&"finished"!==w||(P=e),"finished"===w&&x(),_=P,C=null,w="running",g.start()};e&&L();let B={then:(e,t)=>f.then(e,t),get time(){return e6($)},set time(newTime){$=newTime=e3(newTime),null===C&&g&&0!==y?P=g.now()-newTime/y:C=newTime},get duration(){let e=null===S.calculatedDuration?t3(S):S.calculatedDuration;return e6(e)},get speed(){return y},set speed(newSpeed){if(newSpeed===y||!g)return;y=newSpeed,B.time=e6($)},get state(){return w},play:L,pause:()=>{w="paused",C=$},stop:()=>{v=!0,"idle"!==w&&(w="idle",u&&u(),z())},cancel:()=>{null!==_&&T(_),z()},complete:()=>{w="finished"},sample:e=>(P=0,T(e))};return B}let t9=(o=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===n&&(n=o()),n)),t7=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),re=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return Boolean(!t||"string"==typeof t&&te[t]||e9(t)||Array.isArray(t)&&t.every(e))}(t.ease),rt={type:"spring",stiffness:500,damping:25,restSpeed:10},rr=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),rn={type:"keyframes",duration:.8},ri={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ro=(e,{keyframes:t})=>t.length>2?rn:R.has(e)?e.startsWith("scale")?rr(t[1]):rt:ri,ra=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tV.test(t)||"0"===t)&&!t.startsWith("url(")),rs=new Set(["brightness","contrast","saturate","opacity"]);function rl(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(J)||[];if(!n)return e;let i=r.replace(n,""),o=rs.has(t)?1:0;return n!==r&&(o*=100),t+"("+o+i+")"}let ru=/([a-z-]*)\(.*?\)/g,rc={...tV,getAnimatableNone:e=>{let t=e.match(ru);return t?t.map(rl).join(" "):e}},rd={...ei,color:tP,backgroundColor:tP,outlineColor:tP,fill:tP,stroke:tP,borderColor:tP,borderTopColor:tP,borderRightColor:tP,borderBottomColor:tP,borderLeftColor:tP,filter:rc,WebkitFilter:rc},rh=e=>rd[e];function rp(e,t){let r=rh(e);return r!==rc&&(r=tV),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let rf=e=>/^0[^.\s]+$/.test(e);function rg(e,t){return e[t]||e.default||e}let rm={skipAnimations:!1},rb=(e,t,r,n={})=>i=>{let o=rg(n,e)||{},a=o.delay||n.delay||0,{elapsed:s=0}=n;s-=e3(a);let l=function(e,t,r,n){let i,o;let a=ra(t,r);i=Array.isArray(r)?[...r]:[null,r];let s=void 0!==n.from?n.from:e.get(),l=[];for(let e=0;e<i.length;e++){var u;null===i[e]&&(i[e]=0===e?s:i[e-1]),("number"==typeof(u=i[e])?0===u:null!==u?"none"===u||"0"===u||rf(u):void 0)&&l.push(e),"string"==typeof i[e]&&"none"!==i[e]&&"0"!==i[e]&&(o=i[e])}if(a&&l.length&&o)for(let e=0;e<l.length;e++){let r=l[e];i[r]=rp(t,o)}return i}(t,e,r,o),u=l[0],c=l[l.length-1],d=ra(e,u),h=ra(e,c);(0,e4.K)(d===h,`You are trying to animate ${e} from "${u}" to "${c}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${c} via the \`style\` property.`);let p={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-s,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&(p={...p,...ro(e,p)}),p.duration&&(p.duration=e3(p.duration)),p.repeatDelay&&(p.repeatDelay=e3(p.repeatDelay)),!d||!h||e8.current||!1===o.type||rm.skipAnimations)return function({keyframes:e,delay:t,onUpdate:r,onComplete:n}){let i=()=>(r&&r(e[e.length-1]),n&&n(),{time:0,speed:1,duration:0,play:eG.Z,pause:eG.Z,stop:eG.Z,then:e=>(e(),Promise.resolve()),cancel:eG.Z,complete:eG.Z});return t?t8({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}(e8.current?{...p,delay:0}:p);if(!n.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let r=function(e,t,{onUpdate:r,onComplete:n,...i}){let o,a;let s=t9()&&t7.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type;if(!s)return!1;let l=!1,u=!1,c=()=>{a=new Promise(e=>{o=e})};c();let{keyframes:d,duration:h=300,ease:p,times:f}=i;if(re(t,i)){let e=t8({...i,repeat:0,delay:0}),t={done:!1,value:d[0]},r=[],n=0;for(;!t.done&&n<2e4;)r.push((t=e.sample(n)).value),n+=10;f=void 0,d=r,h=n-10,p="linear"}let g=function(e,t,r,{delay:n=0,duration:i,repeat:o=0,repeatType:a="loop",ease:s,times:l}={}){let u={[t]:r};l&&(u.offset=l);let c=function e(t){if(t)return e9(t)?e7(t):Array.isArray(t)?t.map(e):te[t]}(s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"})}(e.owner.current,t,d,{...i,duration:h,ease:p,times:f}),m=()=>{u=!1,g.cancel()},b=()=>{u=!0,eT.Wi.update(m),o(),c()};g.onfinish=()=>{u||(e.set(function(e,{repeat:t,repeatType:r="loop"}){let n=t&&"loop"!==r&&t%2==1?0:e.length-1;return e[n]}(d,i)),n&&n(),b())};let y={then:(e,t)=>a.then(e,t),attachTimeline:e=>(g.timeline=e,g.onfinish=null,eG.Z),get time(){return e6(g.currentTime||0)},set time(newTime){g.currentTime=e3(newTime)},get speed(){return g.playbackRate},set speed(newSpeed){g.playbackRate=newSpeed},get duration(){return e6(h)},play:()=>{l||(g.play(),(0,eT.Pn)(m))},pause:()=>g.pause(),stop:()=>{if(l=!0,"idle"===g.playState)return;let{currentTime:t}=g;if(t){let r=t8({...i,autoplay:!1});e.setWithVelocity(r.sample(t-10).value,r.sample(t).value,10)}b()},complete:()=>{u||g.finish()},cancel:b};return y}(t,e,p);if(r)return r}return t8(p)};function ry(e){return Boolean(O(e)&&e.add)}let rv=e=>/^\-?\d*\.?\d+$/.test(e);function rx(e,t){-1===e.indexOf(t)&&e.push(t)}function rk(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class rS{constructor(){this.subscriptions=[]}add(e){return rx(this.subscriptions,e),()=>rk(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rw=e=>!isNaN(parseFloat(e)),rC={current:void 0};class rP{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:r,timestamp:n}=eT.w0;this.lastUpdated!==n&&(this.timeDelta=r,this.lastUpdated=n,eT.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>eT.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=rw(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new rS);let r=this.events[e].add(t);return"change"===e?()=>{r(),eT.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rC.current&&rC.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?e*(1e3/t):0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function r_(e,t){return new rP(e,t)}let rA=e=>t=>t.test(e),rj=[N,Q,X,K,et,ee,{test:e=>"auto"===e,parse:e=>e}],rE=e=>rj.find(rA(e)),r$=[...rj,tP,tV],rT=e=>r$.find(rA(e));function rR(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:a,...s}=e.makeTargetAnimatable(t),l=e.getValue("willChange");n&&(o=n);let u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(let t in s){let n=e.getValue(t),i=s[t];if(!n||void 0===i||c&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(c,t))continue;let a={delay:r,elapsed:0,...rg(o||{},t)};if(window.HandoffAppearAnimations){let r=e.getProps()[p];if(r){let e=window.HandoffAppearAnimations(r,t,n,eT.Wi);null!==e&&(a.elapsed=e,a.isHandoff=!0)}}let d=!a.isHandoff&&!function(e,t){let r=e.get();if(!Array.isArray(t))return r!==t;for(let e=0;e<t.length;e++)if(t[e]!==r)return!0}(n,i);if("spring"===a.type&&(n.getVelocity()||a.velocity)&&(d=!1),n.animation&&(d=!1),d)continue;n.start(rb(t,n,i,e.shouldReduceMotion&&R.has(t)?{type:!1}:a));let h=n.animation;ry(l)&&(l.add(t),h.then(()=>l.remove(t))),u.push(h)}return a&&Promise.all(u).then(()=>{a&&function(e,t){let r=e5(e,t),{transitionEnd:n={},transition:i={},...o}=r?e.makeTargetAnimatable(r,!1):{};for(let t in o={...o,...n}){var a;let r=ej(o[t]);a=t,e.hasValue(a)?e.getValue(a).set(r):e.addValue(a,r_(r))}}(e,a)}),u}function rz(e,t,r={}){let n=e5(e,t,r.custom),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let o=n?()=>Promise.all(rR(e,n,r)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,r=0,n=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>s-e*n;return Array.from(e.variantChildren).sort(rO).forEach((e,n)=>{e.notify("AnimationStart",t),a.push(rz(e,t,{...o,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,o+n,a,s,r)}:()=>Promise.resolve(),{when:s}=i;if(!s)return Promise.all([o(),a(r.delay)]);{let[e,t]="beforeChildren"===s?[o,a]:[a,o];return e().then(()=>t())}}function rO(e,t){return e.sortNodePosition(t)}let rL=[...b].reverse(),rB=b.length;function rM(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let rI=0,rD=(e,t)=>Math.abs(e-t);class rF{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rW(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){let r=rD(e.x,t.x),n=rD(e.y,t.y);return Math.sqrt(r**2+n**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=eT.w0;this.history.push({...n,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rV(t,this.transformPagePoint),eT.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rW("pointercancel"===e.type?this.lastMoveEventInfo:rV(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!eL(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let o=eB(e),a=rV(o,this.transformPagePoint),{point:s}=a,{timestamp:l}=eT.w0;this.history=[{...s,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rW(a,this.history)),this.removeListeners=eF(eI(this.contextWindow,"pointermove",this.handlePointerMove),eI(this.contextWindow,"pointerup",this.handlePointerUp),eI(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,eT.Pn)(this.updatePoint)}}function rV(e,t){return t?{point:t(e.point)}:e}function rN(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rW({point:e},t){return{point:e,delta:rN(e,rU(t)),offset:rN(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rU(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>e3(.1)));)r--;if(!n)return{x:0,y:0};let o=e6(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function rU(e){return e[e.length-1]}function rH(e){return e.max-e.min}function rJ(e,t=0,r=.01){return Math.abs(e-t)<=r}function rq(e,t,r,n=.5){e.origin=n,e.originPoint=t_(t.min,t.max,e.origin),e.scale=rH(r)/rH(t),(rJ(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=t_(r.min,r.max,e.origin)-e.originPoint,(rJ(e.translate)||isNaN(e.translate))&&(e.translate=0)}function rZ(e,t,r,n){rq(e.x,t.x,r.x,n?n.originX:void 0),rq(e.y,t.y,r.y,n?n.originY:void 0)}function rG(e,t,r){e.min=r.min+t.min,e.max=e.min+rH(t)}function rY(e,t,r){e.min=t.min-r.min,e.max=e.min+rH(t)}function rK(e,t,r){rY(e.x,t.x,r.x),rY(e.y,t.y,r.y)}function rX(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rQ(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function r0(e,t,r){return{min:r1(e,t),max:r1(e,r)}}function r1(e,t){return"number"==typeof e?e:e[t]||0}let r2=()=>({translate:0,scale:1,origin:0,originPoint:0}),r5=()=>({x:r2(),y:r2()}),r4=()=>({min:0,max:0}),r3=()=>({x:r4(),y:r4()});function r6(e){return[e("x"),e("y")]}function r8({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function r9(e){return void 0===e||1===e}function r7({scale:e,scaleX:t,scaleY:r}){return!r9(e)||!r9(t)||!r9(r)}function ne(e){return r7(e)||nt(e)||e.z||e.rotate||e.rotateX||e.rotateY}function nt(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function nr(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function nn(e,t=0,r=1,n,i){e.min=nr(e.min,t,r,n,i),e.max=nr(e.max,t,r,n,i)}function ni(e,{x:t,y:r}){nn(e.x,t.translate,t.scale,t.originPoint),nn(e.y,r.translate,r.scale,r.originPoint)}function no(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function na(e,t){e.min=e.min+t,e.max=e.max+t}function ns(e,t,[r,n,i]){let o=void 0!==t[i]?t[i]:.5,a=t_(e.min,e.max,o);nn(e,t[r],t[n],a,t.scale)}let nl=["x","scaleX","originX"],nu=["y","scaleY","originY"];function nc(e,t){ns(e.x,t,nl),ns(e.y,t,nu)}function nd(e,t){return r8(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let nh=({current:e})=>e?e.ownerDocument.defaultView:null,np=new WeakMap;class nf{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=r3(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let n=e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eB(e,"page").point)},i=(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=eU(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),r6(e=>{let t=this.getAxisMotionValue(e).get()||0;if(X.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];if(n){let e=rH(n);t=e*(parseFloat(t)/100)}}}this.originPoint[e]=t}),i&&eT.Wi.update(()=>i(e,t),!1,!0);let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},o=(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},a=(e,t)=>this.stop(e,t),s=()=>r6(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())}),{dragSnapToOrigin:l}=this.getProps();this.panSession=new rF(e,{onSessionStart:n,onStart:i,onMove:o,onSessionEnd:a,resumeAnimation:s},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,contextWindow:nh(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&eT.Wi.update(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!ng(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?t_(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?t_(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&f(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:rX(e.x,r,i),y:rX(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r0(e,"left","right"),y:r0(e,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&r6(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!f(t))return!1;let n=t.current;(0,e4.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=nd(e,r),{scroll:i}=t;return i&&(na(n.x,i.offset.x),na(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),a={x:rQ((e=i.layout.layoutBox).x,o.x),y:rQ(e.y,o.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=r8(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{},l=r6(a=>{if(!ng(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)});return Promise.all(l).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(rb(e,r,0,t))}stopAnimation(){r6(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){r6(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps(),n=r[t];return n||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){r6(t=>{let{drag:r}=this.getProps();if(!ng(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-t_(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!f(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};r6(e=>{let t=this.getAxisMotionValue(e);if(t){let r=t.get();n[e]=function(e,t){let r=.5,n=rH(e),i=rH(t);return i>n?r=tq(t.min,t.max-n,e.min):n>i&&(r=tq(e.min,e.max-i,t.min)),V(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),r6(t=>{if(!ng(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set(t_(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;np.set(this.visualElement,this);let e=this.visualElement.current,t=eI(e,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),r=()=>{let{dragConstraints:e}=this.getProps();f(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",r);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),r();let o=eO(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(r6(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{o(),t(),i(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function ng(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}let nm=e=>(t,r)=>{e&&eT.Wi.update(()=>e(t,r))};var nb=r(15947);let ny={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nv(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nx={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!Q.test(e))return e;e=parseFloat(e)}let r=nv(e,t.target.x),n=nv(e,t.target.y);return`${r}% ${n}%`}};class nk extends a.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;Object.assign($,nw),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),ny.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,n||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||eT.Wi.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nS(e){let[t,r]=(0,nb.oO)(),n=(0,a.useContext)(P.p);return a.createElement(nk,{...e,layoutGroup:n,switchLayoutGroup:(0,a.useContext)(_),isPresent:t,safeToRemove:r})}let nw={borderRadius:{...nx,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nx,borderTopRightRadius:nx,borderBottomLeftRadius:nx,borderBottomRightRadius:nx,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=tV.parse(e);if(n.length>5)return e;let i=tV.createTransformer(e),o="number"!=typeof n[0]?1:0,a=r.x.scale*t.x,s=r.y.scale*t.y;n[0+o]/=a,n[1+o]/=s;let l=t_(a,s,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}},nC=["TopLeft","TopRight","BottomLeft","BottomRight"],nP=nC.length,n_=e=>"string"==typeof e?parseFloat(e):e,nA=e=>"number"==typeof e||Q.test(e);function nj(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nE=nT(0,.5,tc),n$=nT(.5,.95,eG.Z);function nT(e,t,r){return n=>n<e?0:n>t?1:r(tq(e,t,n))}function nR(e,t){e.min=t.min,e.max=t.max}function nz(e,t){nR(e.x,t.x),nR(e.y,t.y)}function nO(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nL(e,t,[r,n,i],o,a){!function(e,t=0,r=1,n=.5,i,o=e,a=e){if(X.test(t)){t=parseFloat(t);let e=t_(a.min,a.max,t/100);t=e-a.min}if("number"!=typeof t)return;let s=t_(o.min,o.max,n);e===o&&(s-=t),e.min=nO(e.min,t,r,s,i),e.max=nO(e.max,t,r,s,i)}(e,t[r],t[n],t[i],t.scale,o,a)}let nB=["x","scaleX","originX"],nM=["y","scaleY","originY"];function nI(e,t,r,n){nL(e.x,t,nB,r?r.x:void 0,n?n.x:void 0),nL(e.y,t,nM,r?r.y:void 0,n?n.y:void 0)}function nD(e){return 0===e.translate&&1===e.scale}function nF(e){return nD(e.x)&&nD(e.y)}function nV(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function nN(e){return rH(e.x)/rH(e.y)}class nW{constructor(){this.members=[]}add(e){rx(this.members,e),e.scheduleRender()}remove(e){if(rk(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function nU(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(n=`translate3d(${i}px, ${o}px, 0) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:e,rotateX:t,rotateY:i}=r;e&&(n+=`rotate(${e}deg) `),t&&(n+=`rotateX(${t}deg) `),i&&(n+=`rotateY(${i}deg) `)}let a=e.x.scale*t.x,s=e.y.scale*t.y;return(1!==a||1!==s)&&(n+=`scale(${a}, ${s})`),n||"none"}let nH=(e,t)=>e.depth-t.depth;class nJ{constructor(){this.children=[],this.isDirty=!1}add(e){rx(this.children,e),this.isDirty=!0}remove(e){rk(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nH),this.isDirty=!1,this.children.forEach(e)}}let nq=["","X","Y","Z"],nZ={visibility:"hidden"},nG=0,nY={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function nK({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=null==t?void 0:t()){this.id=nG++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nY.totalNodes=nY.resolvedTargetDeltas=nY.recalculatedProjection=0,this.nodes.forEach(n0),this.nodes.forEach(n8),this.nodes.forEach(n9),this.nodes.forEach(n1),window.MotionDebug&&window.MotionDebug.record(nY)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nJ)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new rS),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=performance.now(),n=({timestamp:i})=>{let o=i-r;o>=t&&((0,eT.Pn)(n),e(o-t))};return eT.Wi.read(n,!0),()=>(0,eT.Pn)(n)}(n,250),ny.hasAnimatedSinceResize&&(ny.hasAnimatedSinceResize=!1,this.nodes.forEach(n6))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||io,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=o.getProps(),l=!this.targetLayout||!nV(this.targetLayout,n)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...rg(i,"layout"),onPlay:a,onComplete:s};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||n6(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,eT.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n7),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;let e=this.isUpdateBlocked();if(e){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n5);return}this.isUpdating||this.nodes.forEach(n4),this.isUpdating=!1,this.nodes.forEach(n3),this.nodes.forEach(nX),this.nodes.forEach(nQ),this.clearAllSnapshots();let t=performance.now();eT.w0.delta=V(0,1e3/60,t-eT.w0.timestamp),eT.w0.timestamp=t,eT.w0.isProcessing=!0,eT.S6.update.process(eT.w0),eT.S6.preRender.process(eT.w0),eT.S6.render.process(eT.w0),eT.w0.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(n2),this.sharedNodes.forEach(ie)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eT.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eT.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++){let t=this.path[e];t.updateScroll()}let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=r3(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:n(this.instance),offset:r(this.instance)})}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!nF(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&(t||ne(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),il((t=n).x),il(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return r3();let t=e.measureViewportBox(),{scroll:r}=this.root;return r&&(na(t.x,r.offset.x),na(t.y,r.offset.y)),t}removeElementScroll(e){let t=r3();nz(t,e);for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:o}=n;if(n!==this.root&&i&&o.layoutScroll){if(i.isRoot){nz(t,e);let{scroll:r}=this.root;r&&(na(t.x,-r.offset.x),na(t.y,-r.offset.y))}na(t.x,i.offset.x),na(t.y,i.offset.y)}}return t}applyTransform(e,t=!1){let r=r3();nz(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&nc(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),ne(n.latestValues)&&nc(r,n.latestValues)}return ne(this.latestValues)&&nc(r,this.latestValues),r}removeTransform(e){let t=r3();nz(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!ne(r.latestValues))continue;r7(r.latestValues)&&r.updateSnapshot();let n=r3(),i=r.measurePageBox();nz(n,i),nI(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return ne(this.latestValues)&&nI(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eT.w0.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let a=Boolean(this.resumingFrom)||this!==o,s=!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget);if(s)return;let{layout:l,layoutId:u}=this.options;if(this.layout&&(l||u)){if(this.resolvedRelativeTargetAt=eT.w0.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r3(),this.relativeTargetOrigin=r3(),rK(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nz(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=r3(),this.targetWithTransforms=r3()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,rG(r.x,n.x,i.x),rG(r.y,n.y,i.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):nz(this.target,this.layout.layoutBox),ni(this.target,this.targetDelta)):nz(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r3(),this.relativeTargetOrigin=r3(),rK(this.relativeTargetOrigin,this.target,e.target),nz(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nY.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||r7(this.parent.latestValues)||nt(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=Boolean(this.resumingFrom)||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===eT.w0.timestamp&&(n=!1),n)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;nz(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;!function(e,t,r,n=!1){let i,o;let a=r.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=r[s]).projectionDelta;let a=i.instance;(!a||!a.style||"contents"!==a.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nc(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,ni(e,o)),n&&ne(i.latestValues)&&nc(e,i.latestValues))}t.x=no(t.x),t.y=no(t.y)}}(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=r5(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r5(),this.projectionDeltaWithTransform=r5());let u=this.projectionTransform;rZ(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=nU(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==a||this.treeScale.y!==s)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nY.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},a=r5();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=r3(),l=n?n.source:void 0,u=this.layout?this.layout.source:void 0,c=l!==u,d=this.getStack(),h=!d||d.members.length<=1,p=Boolean(c&&!h&&!0===this.options.crossfade&&!this.path.some(ii));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(it(a.x,e.x,n),it(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var l,u,d,f;rK(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,f=this.relativeTargetOrigin,ir(d.x,f.x,s.x,n),ir(d.y,f.y,s.y,n),r&&(l=this.relativeTarget,u=r,l.x.min===u.x.min&&l.x.max===u.x.max&&l.y.min===u.y.min&&l.y.max===u.y.max)&&(this.isProjectionDirty=!1),r||(r=r3()),nz(r,this.relativeTarget)}c&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=t_(0,void 0!==r.opacity?r.opacity:1,nE(n)),e.opacityExit=t_(void 0!==t.opacity?t.opacity:1,0,n$(n))):o&&(e.opacity=t_(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<nP;i++){let o=`border${nC[i]}Radius`,a=nj(t,o),s=nj(r,o);if(void 0===a&&void 0===s)continue;a||(a=0),s||(s=0);let l=0===a||0===s||nA(a)===nA(s);l?(e[o]=Math.max(t_(n_(a),n_(s),n),0),(X.test(s)||X.test(a))&&(e[o]+="%")):e[o]=s}(t.rotate||r.rotate)&&(e.rotate=t_(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,p,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,eT.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eT.Wi.update(()=>{ny.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let n=O(e)?e:r_(e);return n.start(rb("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&iu(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||r3();let t=rH(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rH(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nz(t,r),nc(t,i),rZ(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nW);let r=this.sharedNodes.get(e);r.add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(t=!0),!t)return;let n={};for(let t=0;t<nq.length;t++){let i="rotate"+nq[t];r[i]&&(n[i]=r[i],e.setStaticValue(i,0))}for(let t in e.render(),n)e.setStaticValue(t,n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nZ;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=eE(null==e?void 0:e.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eE(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!ne(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let a=o.animationValues||o.latestValues;this.applyTransformsToTarget(),n.transform=nU(this.projectionDeltaWithTransform,this.treeScale,a),i&&(n.transform=i(a,n.transform));let{x:s,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*s.origin}% ${100*l.origin}% 0`,o.animationValues?n.opacity=o===this?null!==(r=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:n.opacity=o===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,$){if(void 0===a[e])continue;let{correct:t,applyTo:r}=$[e],i="none"===n.transform?a[e]:t(a[e],o);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=i}else n[e]=i}return this.options.layoutId&&(n.pointerEvents=o===this?eE(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(n5),this.root.sharedNodes.clear()}}}function nX(e){e.updateLayout()}function nQ(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:i}=e.options,o=r.source!==e.layout.source;"size"===i?r6(e=>{let n=o?r.measuredBox[e]:r.layoutBox[e],i=rH(n);n.min=t[e].min,n.max=n.min+i}):iu(i,r.layoutBox,t)&&r6(n=>{let i=o?r.measuredBox[n]:r.layoutBox[n],a=rH(t[n]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=r5();rZ(a,t,r.layoutBox);let s=r5();o?rZ(s,e.applyTransform(n,!0),r.measuredBox):rZ(s,t,r.layoutBox);let l=!nF(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let a=r3();rK(a,r.layoutBox,i.layoutBox);let s=r3();rK(s,t,o.layoutBox),nV(a,s)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n0(e){nY.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function n1(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function n2(e){e.clearSnapshot()}function n5(e){e.clearMeasurements()}function n4(e){e.isLayoutDirty=!1}function n3(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function n6(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function n8(e){e.resolveTargetDelta()}function n9(e){e.calcProjection()}function n7(e){e.resetRotation()}function ie(e){e.removeLeadSnapshot()}function it(e,t,r){e.translate=t_(t.translate,0,r),e.scale=t_(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function ir(e,t,r,n){e.min=t_(t.min,r.min,n),e.max=t_(t.max,r.max,n)}function ii(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let io={duration:.45,ease:[.4,0,.1,1]},ia=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),is=ia("applewebkit/")&&!ia("chrome/")?Math.round:eG.Z;function il(e){e.min=is(e.min),e.max=is(e.max)}function iu(e,t,r){return"position"===e||"preserve-aspect"===e&&!rJ(nN(t),nN(r),.2)}let ic=nK({attachResizeListener:(e,t)=>eO(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),id={current:void 0},ih=nK({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!id.current){let e=new ic({});e.mount(window),e.setOptions({layoutScroll:!0}),id.current=e}return id.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),ip=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ig(e,t,r=1){(0,e4.k)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,i]=function(e){let t=ip.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]}(e);if(!n)return;let o=window.getComputedStyle(t).getPropertyValue(n);if(o){let e=o.trim();return rv(e)?parseFloat(e):e}return D(i)?ig(i,t,r+1):i}let im=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),ib=e=>im.has(e),iy=e=>Object.keys(e).some(ib),iv=e=>e===N||e===Q,ix=(e,t)=>parseFloat(e.split(", ")[t]),ik=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/);if(i)return ix(i[1],t);{let t=n.match(/^matrix\((.+)\)$/);return t?ix(t[1],e):0}},iS=new Set(["x","y","z"]),iw=T.filter(e=>!iS.has(e)),iC={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:ik(4,13),y:ik(5,14)};iC.translateX=iC.x,iC.translateY=iC.y;let iP=(e,t,r)=>{let n=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:a}=o,s={};"none"===a&&t.setStaticValue("display",e.display||"block"),r.forEach(e=>{s[e]=iC[e](n,o)}),t.render();let l=t.measureViewportBox();return r.forEach(r=>{let n=t.getValue(r);n&&n.jump(s[r]),e[r]=iC[r](l,o)}),e},i_=(e,t,r={},n={})=>{t={...t},n={...n};let i=Object.keys(t).filter(ib),o=[],a=!1,s=[];if(i.forEach(i=>{let l;let u=e.getValue(i);if(!e.hasValue(i))return;let c=r[i],d=rE(c),h=t[i];if(e_(h)){let e=h.length,t=null===h[0]?1:0;d=rE(c=h[t]);for(let r=t;r<e&&null!==h[r];r++)l?(0,e4.k)(rE(h[r])===l,"All keyframes must be of the same type"):(l=rE(h[r]),(0,e4.k)(l===d||iv(d)&&iv(l),"Keyframes must be of the same dimension as the current value"))}else l=rE(h);if(d!==l){if(iv(d)&&iv(l)){let e=u.get();"string"==typeof e&&u.set(parseFloat(e)),"string"==typeof h?t[i]=parseFloat(h):Array.isArray(h)&&l===Q&&(t[i]=h.map(parseFloat))}else(null==d?void 0:d.transform)&&(null==l?void 0:l.transform)&&(0===c||0===h)?0===c?u.set(l.transform(c)):t[i]=d.transform(h):(a||(o=function(e){let t=[];return iw.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),a=!0),s.push(i),n[i]=void 0!==n[i]?n[i]:t[i],u.jump(h))}}),!s.length)return{target:t,transitionEnd:n};{let r=s.indexOf("height")>=0?window.pageYOffset:null,i=iP(t,e,s);return o.length&&o.forEach(([t,r])=>{e.getValue(t).set(r)}),e.render(),C.j&&null!==r&&window.scrollTo({top:r}),{target:i,transitionEnd:n}}},iA=(e,t,r,n)=>{var i,o;let a=function(e,{...t},r){let n=e.current;if(!(n instanceof Element))return{target:t,transitionEnd:r};for(let i in r&&(r={...r}),e.values.forEach(e=>{let t=e.get();if(!D(t))return;let r=ig(t,n);r&&e.set(r)}),t){let e=t[i];if(!D(e))continue;let o=ig(e,n);o&&(t[i]=o,r||(r={}),void 0===r[i]&&(r[i]=e))}return{target:t,transitionEnd:r}}(e,t,n);return t=a.target,n=a.transitionEnd,i=t,o=n,iy(i)?i_(e,i,r,o):{target:i,transitionEnd:o}},ij={current:null},iE={current:!1},i$=new WeakMap,iT=Object.keys(w),iR=iT.length,iz=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],iO=y.length;class iL{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>eT.Wi.render(this.render,!1,!0);let{latestValues:a,renderState:s}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=s,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.isControllingVariants=v(t),this.isVariantNode=x(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(e&&e.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{});for(let e in u){let t=u[e];void 0!==a[e]&&O(t)&&(t.set(a[e],!1),ry(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,i$.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),iE.current||function(){if(iE.current=!0,C.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ij.current=e.matches;e.addListener(t),t()}else ij.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ij.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in i$.delete(this.current),this.projection&&this.projection.unmount(),(0,eT.Pn)(this.notifyUpdate),(0,eT.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=R.has(e),n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eT.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),i()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},r,n,i){let o,a;for(let e=0;e<iR;e++){let r=iT[e],{isEnabled:n,Feature:i,ProjectionNode:s,MeasureLayout:l}=w[r];s&&(o=s),n(t)&&(!this.features[r]&&i&&(this.features[r]=new i(this)),l&&(a=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:r,drag:n,dragConstraints:a,layoutScroll:s,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:r,alwaysMeasureLayout:Boolean(n)||a&&f(a),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:i,layoutScroll:s,layoutRoot:l})}return a}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):r3()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<iz.length;t++){let r=iz[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){let{willChange:n}=t;for(let i in t){let o=t[i],a=r[i];if(O(o))e.addValue(i,o),ry(n)&&n.add(i);else if(O(a))e.addValue(i,r_(o,{owner:e})),ry(n)&&n.remove(i);else if(a!==o){if(e.hasValue(i)){let t=e.getValue(i);t.hasAnimated||t.set(o)}else{let t=e.getStaticValue(i);e.addValue(i,r_(void 0!==t?t:o,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<iO;e++){let r=y[e],n=this.props[r];(g(n)||!1===n)&&(t[r]=n)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=r_(t,{owner:this}),this.addValue(e,r)),r}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,n="string"==typeof r||"object"==typeof r?null===(t=eC(this.props,r))||void 0===t?void 0:t[e]:void 0;if(r&&void 0!==n)return n;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||O(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new rS),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class iB extends iL{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},{transformValues:n},i){let o=function(e,t,r){let n={};for(let i in e){let e=function(e,t){if(!t)return;let r=t[e]||t.default||t;return r.from}(i,t);if(void 0!==e)n[i]=e;else{let e=r.getValue(i);e&&(n[i]=e.get())}}return n}(r,e||{},this);if(n&&(t&&(t=n(t)),r&&(r=n(r)),o&&(o=n(o))),i){!function(e,t,r){var n,i;let o=Object.keys(t).filter(t=>!e.hasValue(t)),a=o.length;if(a)for(let s=0;s<a;s++){let a=o[s],l=t[a],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(i=null!==(n=r[a])&&void 0!==n?n:e.readValue(a))&&void 0!==i?i:t[a]),null!=u&&("string"==typeof u&&(rv(u)||rf(u))?u=parseFloat(u):!rT(u)&&tV.test(l)&&(u=rp(a,l)),e.addValue(a,r_(u,{owner:e})),void 0===r[a]&&(r[a]=u),null!==u&&e.setBaseTarget(a,u))}}(this,r,o);let e=iA(this,r,o,t);t=e.transitionEnd,r=e.target}return{transition:e,transitionEnd:t,...r}}}class iM extends iB{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(R.has(t)){let e=rh(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=(I(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nd(e,t)}build(e,t,r,n){eo(e,t,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,t){return eS(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;O(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,r,n){ev(e,t,r,n)}}class iI extends iB{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(R.has(t)){let e=rh(t);return e&&e.default||0}return t=ex.has(t)?t:h(t),e.getAttribute(t)}measureInstanceViewportBox(){return r3()}scrapeMotionValuesFromProps(e,t){return ew(e,t)}build(e,t,r,n){eg(e,t,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,r,n){ek(e,t,r,n)}mount(e){this.isSVGTag=eb(e.tagName),super.mount(e)}}let iD=(e,t)=>E(e)?new iI(t,{enableHardwareAcceleration:!1}):new iM(t,{enableHardwareAcceleration:!0}),iF={animation:{Feature:class extends eJ{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t)){let i=t.map(t=>rz(e,t,r));n=Promise.all(i)}else if("string"==typeof t)n=rz(e,t,r);else{let i="function"==typeof t?e5(e,t,r.custom):t;n=Promise.all(rR(e,i,r))}return n.then(()=>e.notify("AnimationComplete",t))})(e,t,r))),r={animate:rM(!0),whileInView:rM(),whileHover:rM(),whileTap:rM(),whileDrag:rM(),whileFocus:rM(),exit:rM()},n=!0,i=(t,r)=>{let n=e5(e,r);if(n){let{transition:e,transitionEnd:r,...i}=n;t={...t,...i,...r}}return t};function o(o,a){let s=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set,d={},h=1/0;for(let t=0;t<rB;t++){var p;let f=rL[t],b=r[f],y=void 0!==s[f]?s[f]:l[f],v=g(y),x=f===a?b.isActive:null;!1===x&&(h=t);let k=y===l[f]&&y!==s[f]&&v;if(k&&n&&e.manuallyAnimateOnMount&&(k=!1),b.protectedKeys={...d},!b.isActive&&null===x||!y&&!b.prevProp||m(y)||"boolean"==typeof y)continue;let S=(p=b.prevProp,"string"==typeof y?y!==p:!!Array.isArray(y)&&!e2(y,p)),w=S||f===a&&b.isActive&&!k&&v||t>h&&v,C=!1,P=Array.isArray(y)?y:[y],_=P.reduce(i,{});!1===x&&(_={});let{prevResolvedValues:A={}}=b,j={...A,..._},E=e=>{w=!0,c.has(e)&&(C=!0,c.delete(e)),b.needsAnimating[e]=!0};for(let e in j){let t=_[e],r=A[e];if(!d.hasOwnProperty(e))(e_(t)&&e_(r)?e2(t,r):t===r)?void 0!==t&&c.has(e)?E(e):b.protectedKeys[e]=!0:void 0!==t?E(e):c.add(e)}b.prevProp=y,b.prevResolvedValues=_,b.isActive&&(d={...d,..._}),n&&e.blockInitialAnimation&&(w=!1),w&&(!k||C)&&u.push(...P.map(e=>({animation:e,options:{type:f,...o}})))}if(c.size){let t={};c.forEach(r=>{let n=e.getBaseTarget(r);void 0!==n&&(t[r]=n)}),u.push({animation:t})}let f=Boolean(u.length);return n&&(!1===s.initial||s.initial===s.animate)&&!e.manuallyAnimateOnMount&&(f=!1),n=!1,f?t(u):Promise.resolve()}return{animateChanges:o,setActive:function(t,n,i){var a;if(r[t].isActive===n)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let s=o(i,t);for(let e in r)r[e].protectedKeys={};return s},setAnimateFunction:function(r){t=r(e)},getState:()=>r}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),m(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends eJ{constructor(){super(...arguments),this.id=rI++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let i=this.node.animationState.setActive("exit",!e,{custom:null!=r?r:this.node.getProps().custom});t&&!e&&i.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}},inView:{Feature:class extends eJ{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:e1[n]},a=e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;eX.has(r)||eX.set(r,{});let n=eX.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(e0,{root:e,...t})),n[i]}(t);return eK.set(e,r),n.observe(e),()=>{eK.delete(e),n.unobserve(e)}}(this.node.current,o,a)}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node,r=["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t));r&&this.startObserver()}unmount(){}}},tap:{Feature:class extends eJ{constructor(){super(...arguments),this.removeStartListeners=eG.Z,this.removeEndListeners=eG.Z,this.removeAccessibleListeners=eG.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),n=(e,t)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:n,globalTapTarget:i}=this.node.getProps();eT.Wi.update(()=>{i||eZ(this.node.current,e.target)?r&&r(e,t):n&&n(e,t)})},i=eI(window,"pointerup",n,{passive:!(r.onTap||r.onPointerUp)}),o=eI(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=eF(i,o),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=e=>{if("Enter"!==e.key||this.isPressing)return;let t=e=>{"Enter"===e.key&&this.checkPressEnd()&&eY("up",(e,t)=>{let{onTap:r}=this.node.getProps();r&&eT.Wi.update(()=>r(e,t))})};this.removeEndListeners(),this.removeEndListeners=eO(this.node.current,"keyup",t),eY("down",(e,t)=>{this.startPress(e,t)})},t=eO(this.node.current,"keydown",e),r=()=>{this.isPressing&&eY("cancel",(e,t)=>this.cancelPress(e,t))},n=eO(this.node.current,"blur",r);this.removeAccessibleListeners=eF(t,n)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&eT.Wi.update(()=>r(e,t))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;let e=this.node.getProps();return e.whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eH()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&eT.Wi.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=eI(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=eO(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=eF(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends eJ{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eF(eO(this.node.current,"focus",()=>this.onFocus()),eO(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends eJ{mount(){this.unmount=eF(eq(this.node,!0),eq(this.node,!1))}unmount(){}}},pan:{Feature:class extends eJ{constructor(){super(...arguments),this.removePointerDownListener=eG.Z}onPointerDown(e){this.session=new rF(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nh(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:nm(e),onStart:nm(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&eT.Wi.update(()=>n(e,t))}}}mount(){this.removePointerDownListener=eI(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends eJ{constructor(e){super(e),this.removeGroupControls=eG.Z,this.removeListeners=eG.Z,this.controls=new nf(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eG.Z}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:ih,MeasureLayout:nS},layout:{ProjectionNode:ih,MeasureLayout:nS}},iV=function(e){function t(t,r={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){e&&function(e){for(let t in e)w[t]={...w[t],...e[t]}}(e);let o=(0,a.forwardRef)(function(o,h){var m,b;let y;let x={...(0,a.useContext)(s),...o,layoutId:function({layoutId:e}){let t=(0,a.useContext)(P.p).id;return t&&void 0!==e?t+"-"+e:e}(o)},{isStatic:S}=x,w=function(e){let{initial:t,animate:r}=function(e,t){if(v(e)){let{initial:t,animate:r}=e;return{initial:!1===t||g(t)?t:void 0,animate:g(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,a.useContext)(l));return(0,a.useMemo)(()=>({initial:t,animate:r}),[k(t),k(r)])}(o),A=n(o,S);if(!S&&C.j){w.visualElement=function(e,t,r,n){let{visualElement:i}=(0,a.useContext)(l),o=(0,a.useContext)(d),h=(0,a.useContext)(u.O),f=(0,a.useContext)(s).reducedMotion,g=(0,a.useRef)();n=n||o.renderer,!g.current&&n&&(g.current=n(e,{visualState:t,parent:i,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:f}));let m=g.current;(0,a.useInsertionEffect)(()=>{m&&m.update(r,h)});let b=(0,a.useRef)(Boolean(r[p]&&!window.HandoffComplete));return(0,c.L)(()=>{m&&(m.render(),b.current&&m.animationState&&m.animationState.animateChanges())}),(0,a.useEffect)(()=>{m&&(m.updateFeatures(),!b.current&&m.animationState&&m.animationState.animateChanges(),b.current&&(b.current=!1,window.HandoffComplete=!0))}),m}(i,A,x,t);let r=(0,a.useContext)(_),n=(0,a.useContext)(d).strict;w.visualElement&&(y=w.visualElement.loadFeatures(x,n,e,r))}return a.createElement(l.Provider,{value:w},y&&w.visualElement?a.createElement(y,{visualElement:w.visualElement,...x}):null,r(i,o,(m=w.visualElement,b=h,(0,a.useCallback)(e=>{e&&A.mount&&A.mount(e),m&&(e?m.mount(e):m.unmount()),b&&("function"==typeof b?b(e):f(b)&&(b.current=e))},[m])),A,S,w.visualElement))});return o[A]=i,o}(e(t,r))}if("undefined"==typeof Proxy)return t;let r=new Map;return new Proxy(t,{get:(e,n)=>(r.has(n)||r.set(n,t(n)),r.get(n))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},r,n){let i=E(e)?eR:ez;return{...i,preloadedFeatures:r,useRender:function(e=!1){let t=(t,r,n,{latestValues:i},o)=>{let s=E(t)?ey:el,l=s(r,i,o,t),u=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(ed(i)||!0===r&&ec(i)||!t&&!ec(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),c={...u,...l,ref:n},{children:d}=r,h=(0,a.useMemo)(()=>O(d)?d.get():d,[d]);return(0,a.createElement)(t,{...c,children:h})};return t}(t),createVisualElement:n,Component:e}})(e,t,iF,iD))},45487:function(e,t,r){"use strict";r.d(t,{K:function(){return i},k:function(){return o}});var n=r(81662);let i=n.Z,o=n.Z},11741:function(e,t,r){"use strict";r.d(t,{j:function(){return n}});let n="undefined"!=typeof document},81662:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=e=>e},96681:function(e,t,r){"use strict";r.d(t,{h:function(){return i}});var n=r(67294);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},58868:function(e,t,r){"use strict";r.d(t,{L:function(){return o}});var n=r(67294),i=r(11741);let o=i.j?n.useLayoutEffect:n.useEffect},67421:function(e,t,r){"use strict";let n;r.d(t,{a3:function(){return E},$G:function(){return j}});var i=r(67294);r(71739),Object.create(null);let o=(e,t,r,n)=>{let i=[r,{code:t,...n||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(i,"warn","react-i18next::",!0);h(i[0])&&(i[0]=`react-i18next:: ${i[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...i):console?.warn&&console.warn(...i)},a={},s=(e,t,r,n)=>{h(r)&&a[r]||(h(r)&&(a[r]=new Date),o(e,t,r,n))},l=(e,t)=>()=>{if(e.isInitialized)t();else{let r=()=>{setTimeout(()=>{e.off("initialized",r)},0),t()};e.on("initialized",r)}},u=(e,t,r)=>{e.loadNamespaces(t,l(e,r))},c=(e,t,r,n)=>{if(h(r)&&(r=[r]),e.options.preload&&e.options.preload.indexOf(t)>-1)return u(e,r,n);r.forEach(t=>{0>e.options.ns.indexOf(t)&&e.options.ns.push(t)}),e.loadLanguages(t,l(e,n))},d=(e,t,r={})=>t.languages&&t.languages.length?t.hasLoadedNamespace(e,{lng:r.lng,precheck:(t,n)=>{if(r.bindI18n?.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!n(t.isLanguageChangingTo,e))return!1}}):(s(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0),h=e=>"string"==typeof e,p=e=>"object"==typeof e&&null!==e,f=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,g={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},m=e=>g[e],b=e=>e.replace(f,m),y={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:b},v=()=>y,x=e=>Array.isArray(e)?e:[e],k=(e,t,r,n)=>{if(!e)return"";let i="",o=x(e),a=t?.transSupportBasicHtmlNodes?t.transKeepBasicHtmlNodesFor??[]:[];return o.forEach((e,o)=>{if(isString(e)){i+=`${e}`;return}if(isValidElement(e)){let{props:s,type:l}=e,u=Object.keys(s).length,c=a.indexOf(l)>-1,d=s.children;if(!d&&c&&!u){i+=`<${l}/>`;return}if(!d&&(!c||u)||s.i18nIsDynamicList){i+=`<${o}></${o}>`;return}if(c&&1===u&&isString(d)){i+=`<${l}>${d}</${l}>`;return}let h=k(d,t,r,n);i+=`<${o}>${h}</${o}>`;return}if(null===e){warn(r,"TRANS_NULL_VALUE","Passed in a null value as child",{i18nKey:n});return}if(isObject(e)){let{format:t,...o}=e,a=Object.keys(o);if(1===a.length){let e=t?`${a[0]}, ${t}`:a[0];i+=`{{${e}}}`;return}warn(r,"TRANS_INVALID_OBJ","Invalid child - Object should only have keys {{ value, format }} (format is optional).",{i18nKey:n,child:e});return}warn(r,"TRANS_INVALID_VAR","Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.",{i18nKey:n,child:e})}),i},S=()=>n,w=(0,i.createContext)();class C{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}let P=(e,t)=>{let r=(0,i.useRef)();return(0,i.useEffect)(()=>{r.current=t?r.current:e},[e,t]),r.current},_=(e,t,r,n)=>e.getFixedT(t,r,n),A=(e,t,r,n)=>(0,i.useCallback)(_(e,t,r,n),[e,t,r,n]),j=(e,t={})=>{let{i18n:r}=t,{i18n:n,defaultNS:o}=(0,i.useContext)(w)||{},a=r||n||S();if(a&&!a.reportNamespaces&&(a.reportNamespaces=new C),!a){s(a,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");let e=(e,t)=>h(t)?t:p(t)&&h(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}a.options.react?.wait&&s(a,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let l={...v(),...a.options.react,...t},{useSuspense:f,keyPrefix:g}=l,m=e||o||a.options?.defaultNS;m=h(m)?[m]:m||["translation"],a.reportNamespaces.addUsedNamespaces?.(m);let b=(a.isInitialized||a.initializedStoreOnce)&&m.every(e=>d(e,a,l)),y=A(a,t.lng||null,"fallback"===l.nsMode?m:m[0],g),x=()=>y,k=()=>_(a,t.lng||null,"fallback"===l.nsMode?m:m[0],g),[j,E]=(0,i.useState)(x),$=m.join();t.lng&&($=`${t.lng}${$}`);let T=P($),R=(0,i.useRef)(!0);(0,i.useEffect)(()=>{let{bindI18n:e,bindI18nStore:r}=l;R.current=!0,b||f||(t.lng?c(a,t.lng,m,()=>{R.current&&E(k)}):u(a,m,()=>{R.current&&E(k)})),b&&T&&T!==$&&R.current&&E(k);let n=()=>{R.current&&E(k)};return e&&a?.on(e,n),r&&a?.store.on(r,n),()=>{R.current=!1,a&&e?.split(" ").forEach(e=>a.off(e,n)),r&&a&&r.split(" ").forEach(e=>a.store.off(e,n))}},[a,$]),(0,i.useEffect)(()=>{R.current&&b&&E(x)},[a,g,b]);let z=[j,a,b];if(z.t=j,z.i18n=a,z.ready=b,b||!b&&!f)return z;throw new Promise(e=>{t.lng?c(a,t.lng,m,()=>e()):u(a,m,()=>e())})};function E({i18n:e,defaultNS:t,children:r}){let n=(0,i.useMemo)(()=>({i18n:e,defaultNS:t}),[e,t]);return(0,i.createElement)(w.Provider,{value:n},r)}}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[9774,179],function(){return t(91118),t(90387)}),_N_E=e.O()}]);