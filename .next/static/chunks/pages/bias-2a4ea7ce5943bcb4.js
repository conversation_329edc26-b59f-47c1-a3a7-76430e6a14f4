(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2767],{26485:function(n,i,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/bias",function(){return s(21334)}])},21334:function(n,i,s){"use strict";s.r(i),s.d(i,{__N_SSG:function(){return r},default:function(){return l}});var e=s(85893),a=s(9008),t=s.n(a),c=s(70425);s(11752);var o=s(47426);s(63214);var r=!0;function l(){return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(t(),{children:[(0,e.jsx)("title",{children:"AI LogicLens: Critical Thinking & Bias Detection | FunBlocks AI Tools"}),(0,e.jsx)("meta",{name:"description",content:"Analyze arguments, detect cognitive biases & logical fallacies with AI LogicLens. Improve critical thinking for academic papers, business reports, news & daily discussions."}),(0,e.jsx)("meta",{name:"keywords",content:"AI LogicLens, critical thinking, logical fallacy detection, cognitive bias detection, argumentation analysis, bias analysis, logic analysis, academic research, business analysis, news analysis, decision making, FunBlocks AI"})]}),(0,e.jsx)(c.Z,{app:o.IF.bias})]})}},63214:function(n,i,s){"use strict";s(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return n(n.s=26485)}),_N_E=n.O()}]);