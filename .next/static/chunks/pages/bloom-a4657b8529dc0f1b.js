(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9978],{4232:function(n,e,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/bloom",function(){return o(45735)}])},45735:function(n,e,o){"use strict";o.r(e),o.d(e,{__N_SSG:function(){return c},default:function(){return u}});var t=o(85893),s=o(9008),r=o.n(s),a=o(70425);o(11752);var i=o(47426);o(63214);var c=!0;function u(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r(),{children:[(0,t.jsx)("title",{children:"AI BloomBrain - Educational Tool Based on <PERSON>'s Taxonomy | FunBlocks AI"}),(0,t.jsx)("meta",{name:"description",content:"AI BloomBrain helps educators and learners create comprehensive learning journeys using <PERSON>'s Taxonomy. Generate structured mind maps, learning objectives, and assessments for any topic."}),(0,t.jsx)("meta",{name:"keywords",content:"Bloom's Taxonomy, educational tool, learning objectives, cognitive levels, AI education, teaching strategies, assessment generator, curriculum planning, AI tools, AI mind map, AI Brainstorming"})]}),(0,t.jsx)(a.Z,{app:i.IF.bloom})]})}},63214:function(n,e,o){"use strict";o(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return n(n.s=4232)}),_N_E=n.O()}]);