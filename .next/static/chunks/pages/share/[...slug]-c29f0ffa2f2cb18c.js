(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5525],{99910:function(e,t,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/share/[...slug]",function(){return i(84699)}])},84699:function(e,t,i){"use strict";i.r(t),i.d(t,{__N_SSP:function(){return H},default:function(){return M}});var n=i(85893),s=i(67294),l=i(9008),r=i.n(l),o=i(11163),a=i(6459),c=i(73636),d=i(38491),p=i(48899),m=i(52867),u=i(83658),h=i(90038),x=i(68029),g=i(96254),f=i(8186),_=i(50232),j=i(90092),w=i(43321),y=i(57879),I=i(5490),F=i(34614),b=i(14437),v=i(70010),k=i(12519),S=i(37945),L=i(24278);let C=(0,L.I)({viewBox:"0 0 14 14",path:(0,n.jsx)("g",{fill:"currentColor",children:(0,n.jsx)("polygon",{points:"5.5 11.9993304 14 3.49933039 12.5 2 5.5 8.99933039 1.5 4.9968652 0 6.49933039"})})});var z=i(86523),A=i(31837),E=i(11752),N=i.n(E),G=i(19399),O=i(92562),P=i(71508),T=i(31082),W=i(71692),R=i(80153),U=i(47426),D=i(45744),$=i(60906);let B=e=>{let{app:t,artifact:i,svgRef:l}=e,{t:r}=(0,A.$G)("common"),o=(0,a.p)(),{openShareModal:m}=(0,T.K)(),[u,h]=(0,s.useState)();return(0,n.jsxs)(c.U,{spacing:4,children:[(0,n.jsx)(d.z,{size:"sm",colorScheme:"whiteAlpha",variant:"link",onClick:async()=>{h("download");try{await (0,D.gp)(l,o)}finally{h(null)}},children:(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",columnGap:6},children:[r("download")," ","download"===u&&(0,n.jsx)(p.$,{size:"sm"})]})}),(0,n.jsx)(d.z,{size:"sm",colorScheme:"blue",onClick:async()=>{h("share");try{await (0,D.Bf)(t,null==i?void 0:i._id,null==i?void 0:i.title,l,m,o)}finally{h(null)}},children:(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",columnGap:6},children:[r("share")," ","share"===u&&(0,n.jsx)(p.$,{size:"sm"})]})})]})},X=e=>{let{app:t,appTranslationFile:i}=e,{t:s}=(0,A.$G)(i);return(0,n.jsx)(m.aV,{spacing:3,textAlign:"left",children:([U.IF.mindmap,U.IF.brainstorming,U.IF.decision,U.IF.graphics,U.IF.infographic,U.IF.dreamlens,U.IF.art,U.IF.photo,U.IF.reading,U.IF.startupmentor,U.IF.businessmodel,U.IF.okr,U.IF.poetic,U.IF.horoscope,U.IF.movie,U.IF.feynman,U.IF.bloom,U.IF.solo,U.IF.dok,U.IF.marzano,U.IF.erase,U.IF.avatar,U.IF.imageEditor,U.IF.layeredExplanation,U.IF.promptOptimizer,U.IF.lessonPlanner,U.IF.dokAssessment].includes(t)?[1,2,3]:[1,2,3,4]).map(e=>(0,n.jsxs)(m.HC,{children:[(0,n.jsx)(m.DE,{as:C,color:"green.300"}),s([U.IF.graphics,U.IF.infographic].includes(t)&&"graphics_feature_"+e+"_text"||t===U.IF.onePageSlides&&"one_slides_pro_benefit_"+e||t===U.IF.promptOptimizer&&"prompt_feature_"+e+"_text"||t===U.IF.lessonPlanner&&"lesson_plans_feature_"+e+"_text"||t===U.IF.teachingSlides&&"teaching_slides_feature_"+e+"_text"||t===U.IF.dokAssessment&&"dok_assessment_feature_"+e+"_text"||t===U.IF.imageEditor&&"editor_feature_"+e+"_text"||i+"_feature_"+e+"_text")]},e+""))})},Z=e=>{let{cardData:t,artifact:i,app:l,appTranslationFile:a}=e;(0,o.useRouter)();let{t:c}=(0,A.$G)("common"),{basePath:p}=N()().publicRuntimeConfig,{isOpen:m,onOpen:L,onClose:C}=(0,u.q)(),[E,D]=(0,s.useState)(""),Z=(0,s.useRef)(),[H]=(0,h.a)("(max-width: 768px)");window.location.href;let M=(0,s.useMemo)(()=>[].concat(...Object.values($.C)),[]);return(0,s.useEffect)(()=>{if((null==i?void 0:i.type)!="svg")return;let e=new Blob([(0,G.w)(i.content)],{type:"image/svg+xml"}),t=URL.createObjectURL(e);D(t)},[i]),console.log("artifact..............",i),(0,n.jsxs)(T.$,{children:[(0,n.jsxs)(r(),{children:[(0,n.jsx)("title",{children:t.title},"title"),(0,n.jsx)("meta",{name:"keywords",content:t.keywords||c("meta.keywords")}),(0,n.jsx)("meta",{name:"description",content:t.description},"description"),(0,n.jsx)("meta",{property:"og:title",content:t.title},"og:title"),(0,n.jsx)("meta",{property:"og:description",content:t.description},"og:description"),(0,n.jsx)("meta",{property:"og:image",content:t.imageUrl},"og:image"),(0,n.jsx)("meta",{property:"og:url",content:t.shareUrl},"og:url"),(0,n.jsx)("meta",{name:"twitter:card",content:"summary_large_image"},"twitter:card"),(0,n.jsx)("meta",{name:"twitter:title",content:t.title},"twitter:title"),(0,n.jsx)("meta",{name:"twitter:description",content:t.description},"twitter:description"),(0,n.jsx)("meta",{name:"twitter:image",content:t.imageUrl},"twitter:image")]}),(0,n.jsxs)(x.x,{minHeight:"100vh",bgGradient:"linear(to-b, purple.500, blue.500)",color:"white",py:6,children:[(0,n.jsx)(g.W,{maxW:"container.xl",pt:{base:4,md:10},children:(0,n.jsxs)(f.g,{spacing:4,align:"center",children:[(0,n.jsx)(_.X,{as:"h1",size:{base:"lg",md:"2xl"},textAlign:"center",mb:2,children:c("app_slogan_"+l.replaceAll("-","").toLowerCase())}),(0,n.jsx)(j.x,{fontSize:{base:"lg",md:"xl"},fontWeight:"bold",textAlign:"center",mb:2,children:c("app_one_sentence_"+l.replaceAll("-","").toLowerCase())}),(null==i?void 0:i.type)==="flow"&&(0,n.jsx)(x.x,{style:{width:"100%",maxWidth:1040,height:H?300:680,color:"black"},ref:Z,align:"left",children:(0,n.jsx)(R.Z,{app:l,mode:i.mode,doc:i})}),(null==i?void 0:i.type)==="slides"&&i.hid&&(0,n.jsx)("div",{ref:Z,style:{width:H?"100%":960,height:H?300:540},children:(0,n.jsx)("iframe",{className:"nodrag",id:"slides-frame",style:{width:"100%",height:"100%"},src:"https://service.funblocks.net/view.html?theme=sky&hid=".concat(i.hid),title:"FunBlocks AI Slides"})}),i&&(["markdown","svg","mermaid"].includes(i.type)||[U.IF.poetic,U.IF.graphics,U.IF.onePageSlides,U.IF.infographic,U.IF.mindsnap,U.IF.insightcards,U.IF.promptOptimizer].includes(l))&&(0,n.jsx)(O.Z,{app:l,artifact:i,svgRef:Z,showDownload:!0,showShare:!0}),i&&(0,n.jsx)(B,{app:l,svgRef:Z,artifact:i}),(0,n.jsx)(f.g,{spacing:2,mt:2,children:(0,n.jsx)(d.z,{borderRadius:"full",colorScheme:"teal",size:"lg",onClick:()=>window.open(p+"/"+((null==l?void 0:l.toLowerCase())||""),"_blank"),px:8,children:c("find_more_cards_or_create_your_own")})}),(0,n.jsxs)(x.x,{maxW:"680px",textAlign:"center",mt:4,mb:8,children:[(0,n.jsx)(_.X,{as:"h2",size:"lg",mb:4,children:c("why_funblocks_ai",{app:(0,U.WD)(l)})}),(0,n.jsx)(X,{app:l,appTranslationFile:a})]}),(0,n.jsxs)(x.x,{maxW:"800px",textAlign:"center",mt:4,mb:8,children:[(0,n.jsx)(_.X,{as:"h2",size:"md",mb:4,children:"FunBlocks AI Tools"}),(0,n.jsx)(w.M,{columns:{base:1,md:2,lg:3},spacing:2,w:"full",children:M.map(e=>(0,n.jsx)(y.r,{href:p+e.link,isExternal:!0,style:{color:"white"},children:e.title}))})]})]})}),(0,n.jsxs)(I.u_,{isOpen:m,onClose:C,size:"full",children:[(0,n.jsx)(F.Z,{}),(0,n.jsx)(b.h,{bg:"rgba(0,0,0,0.8)",children:(0,n.jsxs)(v.f,{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",position:"relative",children:[(0,n.jsx)(k.h,{icon:(0,n.jsx)(z.T,{}),position:"absolute",right:"20px",top:"20px",onClick:C,"aria-label":c("close_modal"),bg:"transparent",color:"white",_hover:{bg:"rgba(255,255,255,0.2)"}}),(0,n.jsx)(S.E,{src:E,alt:"Shared SVG Full Screen",objectFit:"contain",maxHeight:"90vh",maxWidth:"90vw",onClick:e=>e.stopPropagation(),cursor:"default"})]})})]}),(0,n.jsx)(W.Z,{app:l}),(0,n.jsx)("div",{style:{display:"none"},children:(0,n.jsx)(P.Q,{value:"https://www.funblocks.net".concat(p+"/"+((null==l?void 0:l.toLowerCase())||"")),size:100,id:"qrcode-canvas"})})]})]})};var H=!0,M=Z},86523:function(e,t,i){"use strict";i.d(t,{T:function(){return s}});var n=i(24278);let s=(0,n.I)({displayName:"CloseIcon",d:"M.439,21.44a1.5,1.5,0,0,0,2.122,2.121L11.823,14.3a.25.25,0,0,1,.354,0l9.262,9.263a1.5,1.5,0,1,0,2.122-2.121L14.3,12.177a.25.25,0,0,1,0-.354l9.263-9.262A1.5,1.5,0,0,0,21.439.44L12.177,9.7a.25.25,0,0,1-.354,0L2.561.44A1.5,1.5,0,0,0,.439,2.561L9.7,11.823a.25.25,0,0,1,0,.354Z"})},52867:function(e,t,i){"use strict";i.d(t,{DE:function(){return f},HC:function(){return g},aV:function(){return u}});var n=i(85893),s=i(65544),l=i(52110),r=i(90911),o=i(12553),a=i(49381),c=i(73035),d=i(64993);let[p,m]=(0,l.k)({name:"ListStylesContext",errorMessage:"useListStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<List />\" "}),u=(0,a.G)(function(e,t){let i=(0,c.j)("List",e),{children:l,styleType:o="none",stylePosition:a,spacing:m,...u}=(0,s.L)(e),h=(0,r.W)(l);return(0,n.jsx)(p,{value:i,children:(0,n.jsx)(d.m.ul,{ref:t,listStyleType:o,listStylePosition:a,role:"list",__css:{...i.container,...m?{"& > *:not(style) ~ *:not(style)":{mt:m}}:{}},...u,children:h})})});u.displayName="List";let h=(0,a.G)((e,t)=>{let{as:i,...s}=e;return(0,n.jsx)(u,{ref:t,as:"ol",styleType:"decimal",marginStart:"1em",...s})});h.displayName="OrderedList";let x=(0,a.G)(function(e,t){let{as:i,...s}=e;return(0,n.jsx)(u,{ref:t,as:"ul",styleType:"initial",marginStart:"1em",...s})});x.displayName="UnorderedList";let g=(0,a.G)(function(e,t){let i=m();return(0,n.jsx)(d.m.li,{ref:t,...e,__css:i.item})});g.displayName="ListItem";let f=(0,a.G)(function(e,t){let i=m();return(0,n.jsx)(o.J,{ref:t,role:"presentation",...e,__css:i.icon})});f.displayName="ListIcon"}},function(e){e.O(0,[4838,3365,1265,9769,805,8151,8417,5460,1582,2658,2613,5408,906,9774,2888,179],function(){return e(e.s=99910)}),_N_E=e.O()}]);