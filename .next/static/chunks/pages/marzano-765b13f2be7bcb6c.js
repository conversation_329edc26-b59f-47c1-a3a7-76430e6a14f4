(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3696],{86889:function(n,e,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/marzano",function(){return a(44986)}])},44986:function(n,e,a){"use strict";a.r(e),a.d(e,{__N_SSG:function(){return c},default:function(){return u}});var t=a(85893),r=a(9008),o=a.n(r),i=a(70425);a(11752);var s=a(47426);a(63214);var c=!0;function u(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(o(),{children:[(0,t.jsx)("title",{children:"AI MarzanoBrain - Educational Course Designer Based on <PERSON><PERSON>'s Taxonomy | FunBlocks AI"}),(0,t.jsx)("meta",{name:"description",content:"AI <PERSON> helps educators and learners create comprehensive educational plans using <PERSON><PERSON>'s Taxonomy. Generate structured mind maps that transform any topic into a comprehensive learning journey."}),(0,t.jsx)("meta",{name:"keywords",content:"Marzano Taxonomy, educational tool, learning objectives, cognitive levels, AI education, AI tools, AI mind map, AI Brainstorming, teaching strategies, curriculum planning, instructional design, self-system, metacognitive system, cognitive system"})]}),(0,t.jsx)(i.Z,{app:s.IF.marzano})]})}},63214:function(n,e,a){"use strict";a(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return n(n.s=86889)}),_N_E=n.O()}]);