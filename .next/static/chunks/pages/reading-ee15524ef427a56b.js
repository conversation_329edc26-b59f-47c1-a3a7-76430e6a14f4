(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1156],{42759:function(n,e,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/reading",function(){return i(10409)}])},10409:function(n,e,i){"use strict";i.r(e),i.d(e,{__N_SSP:function(){return d},default:function(){return c}});var t=i(85893),a=i(9008),o=i.n(a),r=i(70425);i(11752);var s=i(47426),d=!0;function c(n){let{initial_showcases:e}=n;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(o(),{children:[(0,t.jsx)("title",{children:"AI Reading Map: Mind Map Guided Reading | FunBlocks AI Tools"}),(0,t.jsx)("meta",{name:"description",content:"Unlock deeper insights from your reading with AI Reading Map, a FunBlocks AI tool that creates structured reading guides and mind maps to enhance comprehension and knowledge retention."}),(0,t.jsx)("meta",{name:"keywords",content:"AI reading assistant, reading comprehension, mind mapping, knowledge discovery, book analysis, reading efficiency, intelligent reading, AI learning, educational tools, knowledge management, reading guide, FunBlocks AI"})]}),(0,t.jsx)(r.Z,{app:s.IF.reading,showcases:e})]})}}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return n(n.s=42759)}),_N_E=n.O()}]);