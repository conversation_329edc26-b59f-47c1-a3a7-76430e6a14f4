(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1611],{20656:function(t,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/image-editor",function(){return n(70356)}])},70356:function(t,e,n){"use strict";n.r(e),n.d(e,{__N_SSG:function(){return u},default:function(){return d}});var i=n(85893),o=n(9008),a=n.n(o),r=n(70425);n(11752);var s=n(47426);n(63214);var u=!0;function d(){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(a(),{children:[(0,i.jsx)("title",{children:"AI Image Editor - Transform Images with Natural Language | FunBlocks AI Tools"}),(0,i.jsx)("meta",{name:"description",content:"AI Image Editor lets you edit images using simple text instructions. No Photoshop skills needed - just describe what you want to change and our AI will do the rest. Perfect for photo editing, image enhancement, and creative transformations!"}),(0,i.jsx)("meta",{name:"keywords",content:"AI image editor, photo editing AI, natural language image editing, AI photo enhancement, text-to-edit, intelligent image modification, automated photo editing, AI-powered image transformation, smart photo editor, conversational image editing"})]}),(0,i.jsx)(r.Z,{app:s.IF.imageEditor})]})}},63214:function(t,e,n){"use strict";n(47426)}},function(t){t.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return t(t.s=20656)}),_N_E=t.O()}]);