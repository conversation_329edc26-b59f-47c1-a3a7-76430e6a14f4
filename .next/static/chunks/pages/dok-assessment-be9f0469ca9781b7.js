(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8882],{73751:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/dok-assessment",function(){return t(12661)}])},12661:function(e,s,t){"use strict";t.r(s),t.d(s,{__N_SSG:function(){return d},default:function(){return _}});var n=t(85893),a=t(9008),o=t.n(a),i=t(70425),r=t(47426),c=t(11752),m=t.n(c),p=t(31837),l=t(83454),d=!0;function _(){let{basePath:e}=m()().publicRuntimeConfig,s=l.env.NEXT_PUBLIC_SITE_URL||"https://www.funblocks.net",t="".concat(s).concat(e,"/dok-assessment"),{t:a}=(0,p.$G)("dok-assessment"),c={"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:a("dok_assessment_faq_1_q"),acceptedAnswer:{"@type":"Answer",text:a("dok_assessment_faq_1_a")}},{"@type":"Question",name:a("dok_assessment_faq_2_q"),acceptedAnswer:{"@type":"Answer",text:a("dok_assessment_faq_2_a")}},{"@type":"Question",name:a("dok_assessment_faq_3_q"),acceptedAnswer:{"@type":"Answer",text:a("dok_assessment_faq_3_a")}},{"@type":"Question",name:a("dok_assessment_faq_4_q"),acceptedAnswer:{"@type":"Answer",text:a("dok_assessment_faq_4_a")}},{"@type":"Question",name:a("dok_assessment_faq_5_q"),acceptedAnswer:{"@type":"Answer",text:a("dok_assessment_faq_5_a")}}]};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(o(),{children:[(0,n.jsx)("title",{children:"FunBlocks AI Assessment: DOK-Based Educational Assessment Generator | Create Smart Assessments"}),(0,n.jsx)("meta",{name:"description",content:"FunBlocks AI Assessment generates comprehensive educational assessments based on Webb's Depth of Knowledge (DOK) framework. Transform any teaching topic into professional, multi-level assessments instantly with AI."}),(0,n.jsx)("meta",{name:"keywords",content:"AI assessment generator, DOK assessment, educational assessment, Webb's Depth of Knowledge, AI teaching tools, assessment design, educational evaluation, cognitive assessment, learning assessment, teaching assessment, AI education, assessment creation, DOK framework, educational AI, smart assessment, assessment maker, teaching evaluation, learning evaluation, educational technology, AI assessment tool"}),(0,n.jsx)("link",{rel:"canonical",href:t}),(0,n.jsx)("meta",{property:"og:title",content:"FunBlocks AI Assessment: Create DOK-Based Educational Assessments | AI Assessment Generator"}),(0,n.jsx)("meta",{property:"og:description",content:"Generate comprehensive educational assessments instantly with AI. Based on Webb's DOK framework, create professional multi-level assessments from any teaching topic. Perfect for educators and instructional designers."}),(0,n.jsx)("meta",{property:"og:type",content:"website"}),(0,n.jsx)("meta",{property:"og:url",content:t}),(0,n.jsx)("meta",{property:"og:image",content:"".concat(s,"/img/portfolio/thumbnails/aitools_dok_assessment_generated.png")}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,n.jsx)("meta",{name:"twitter:title",content:"FunBlocks AI Assessment: DOK-Based Educational Assessment Generator"}),(0,n.jsx)("meta",{name:"twitter:description",content:"Create comprehensive educational assessments with AI. Generate DOK-based assessments from any teaching topic instantly. Professional assessment design made simple."}),(0,n.jsx)("meta",{name:"twitter:image",content:"".concat(s,"/img/portfolio/thumbnails/aitools_dok_assessment_generated.png")}),(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Assessment",applicationCategory:"EducationalApplication",operatingSystem:"Web",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},description:"AI-powered educational assessment generator based on Webb's Depth of Knowledge (DOK) framework. Create comprehensive assessments instantly from any teaching topic.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.9",ratingCount:"312"}})}}),(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(c)}})]}),(0,n.jsx)(i.Z,{app:r.IF.dokAssessment})]})}}},function(e){e.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return e(e.s=73751)}),_N_E=e.O()}]);