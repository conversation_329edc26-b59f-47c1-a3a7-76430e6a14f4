(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5588],{50470:function(n,i,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/refine-question",function(){return e(10517)}])},10517:function(n,i,e){"use strict";e.r(i),e.d(i,{__N_SSG:function(){return c},default:function(){return u}});var t=e(85893),s=e(9008),r=e.n(s),o=e(70425);e(11752);var a=e(47426);e(63214);var c=!0;function u(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r(),{children:[(0,t.jsx)("title",{children:"AI-Powered Critical Thinking & Question Optimization | FunBlocks AI Tools"}),(0,t.jsx)("meta",{name:"description",content:"Refine questions, enhance critical thinking, and generate mind maps for research, academic inquiry, and professional problem-solving. Unlock deeper insights with FunBlocks AI."}),(0,t.jsx)("meta",{name:"keywords",content:"AI question optimization, critical thinking, mind mapping, research questions, academic inquiry, problem-solving, strategic planning, FunBlocks AI, question analysis, alternative formulations, inquiry, researchers, students, professionals, educators"})]}),(0,t.jsx)(o.Z,{app:a.IF.refineQuestion})]})}},63214:function(n,i,e){"use strict";e(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,9263,2613,5408,6734,430,9774,2888,179],function(){return n(n.s=50470)}),_N_E=n.O()}]);