(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5405],{75557:function(t,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return n(23486)}])},43598:function(t,e,n){"use strict";n.d(e,{k:function(){return p}});var o=n(85893);n(67294);var a=n(86426),s=n(6459),r=n(38491),i=n(17597),l=n(61951),c=n(52091),u=n(31837);let _=t=>{let{inviteCode:e,showButton:n}=t,{t:_}=(0,u.$G)("common"),{logon:p}=(0,l.a)(),f=(0,s.p)(),m=(0,a.Nq)({onSuccess:async t=>{try{let n=await fetch("".concat(i.J,"/users/oauth-sign-in"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({tokenInfo:t,inviteCode:e}),credentials:"include"});if(n.ok){let t=await n.json();console.log(t,null==t?void 0:t.data),p(null==t?void 0:t.data),f({title:_("login_success"),status:"success",duration:3e3,isClosable:!0})}else console.error("Failed to login"),f({title:_("login_failed"),status:"error",duration:3e3,isClosable:!0})}catch(t){console.error("Error login:",t),f({title:_("login_error"),description:t.message,status:"error",duration:3e3,isClosable:!0})}}});return(0,a._7)({onSuccess:async t=>{try{let e=await fetch("".concat(i.J,"/users/oauth-sign-in-credential"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({credential:t.credential}),credentials:"include"});if(e.ok){let t=await e.json();console.log(t,null==t?void 0:t.data),p(null==t?void 0:t.data),f({title:_("login_success"),status:"success",duration:3e3,isClosable:!0})}else console.error("Failed to login"),f({title:_("login_failed"),status:"error",duration:3e3,isClosable:!0})}catch(t){console.error("Error login:",t),f({title:_("login_error"),description:t.message,status:"error",duration:3e3,isClosable:!0})}},onError:()=>{console.log("Login Failed")}}),n?(0,o.jsxs)(r.z,{width:"100%",onClick:m,justifyContent:"flex-start",spacing:4,children:[(0,o.jsx)(c.ldW,{style:{padding:4,marginRight:6},size:32,color:"dodgerblue"})," ",_("login_with_google")]}):(0,o.jsx)(o.Fragment,{})},p=t=>{let{inviteCode:e,showButton:n}=t;return(0,o.jsx)(a.rg,{clientId:"988058218123-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com",children:(0,o.jsx)(_,{inviteCode:e,showButton:n})})}},49515:function(t,e,n){"use strict";var o=n(85893),a=n(90243),s=n(11163);let r=()=>{let t=(0,s.useRouter)(),e=e=>{let n=e.target.value;t.push(t.pathname,t.asPath,{locale:n})};return(0,o.jsxs)(a.P,{onChange:e,value:t.locale,size:"sm",width:"auto",variant:"filled",borderRadius:"full",children:[(0,o.jsx)("option",{value:"en",children:"English"}),(0,o.jsx)("option",{value:"zh",children:"中文"})]})};e.Z=r},77425:function(t,e,n){"use strict";var o=n(85893),a=n(9008),s=n.n(a);let r=t=>{let{title:e,description:n,image:a,url:r,keywords:i,canonical:l}=t;return(0,o.jsxs)(s(),{children:[(0,o.jsx)("title",{children:e}),n&&(0,o.jsx)("meta",{name:"description",content:n}),i&&(0,o.jsx)("meta",{name:"keywords",content:i}),(0,o.jsx)("meta",{property:"og:type",content:"website"}),(0,o.jsx)("meta",{property:"og:title",content:e}),n&&(0,o.jsx)("meta",{property:"og:description",content:n}),a&&(0,o.jsx)("meta",{property:"og:image",content:a}),r&&(0,o.jsx)("meta",{property:"og:url",content:r}),(0,o.jsx)("meta",{property:"og:site_name",content:"FunBlocks AI"}),(0,o.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,o.jsx)("meta",{name:"twitter:title",content:e}),n&&(0,o.jsx)("meta",{name:"twitter:description",content:n}),a&&(0,o.jsx)("meta",{name:"twitter:image",content:a}),l&&(0,o.jsx)("link",{rel:"canonical",href:l}),(0,o.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),(0,o.jsx)("meta",{httpEquiv:"Content-Type",content:"text/html; charset=utf-8"}),(0,o.jsx)("meta",{name:"robots",content:"index, follow"})]})};e.Z=r},84665:function(t,e,n){"use strict";var o=n(85893);n(67294);var a=n(31837),s=n(11752),r=n.n(s);let i=t=>{let{faqs:e}=t,{t:n}=(0,a.$G)("common"),{basePath:s}=r()().publicRuntimeConfig,i={"@context":"https://schema.org","@type":"Organization",name:"FunBlocks AI",url:"https://www.funblocks.net",logo:"".concat(s,"/icon.png"),description:n("platform_description"),sameAs:["https://twitter.com/funblocks_ai"]},l={"@context":"https://schema.org","@type":"FAQPage",mainEntity:e.map(t=>({"@type":"Question",name:t.question,acceptedAnswer:{"@type":"Answer",text:t.answer}}))},c={"@context":"https://schema.org","@type":"WebSite",url:"https://www.funblocks.net/aitools",name:"FunBlocks AI Tools",description:n("platform_meta_description")};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(i)}}),(0,o.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(c)}}),e&&e.length>0&&(0,o.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(l)}})]})};e.Z=i},23486:function(t,e,n){"use strict";n.r(e),n.d(e,{__N_SSP:function(){return _},default:function(){return p}});var o=n(85893);n(9008);var a=n(68029),s=n(60906),r=n(11752),i=n.n(r),l=n(77425),c=n(84665);n(19030);var u=n(31837),_=!0;function p(){let{basePath:t}=i()().publicRuntimeConfig,{t:e}=(0,u.$G)("common"),n=[{question:e("platform_faq_1_q"),answer:e("platform_faq_1_a")},{question:e("platform_faq_2_q"),answer:e("platform_faq_2_a")},{question:e("platform_faq_3_q"),answer:e("platform_faq_3_a")},{question:e("platform_faq_4_q"),answer:e("platform_faq_4_a")},{question:e("platform_faq_5_q"),answer:e("platform_faq_5_a")},{question:e("platform_faq_6_q"),answer:e("platform_faq_6_a")},{question:e("platform_faq_7_q"),answer:e("platform_faq_7_a")},{question:e("platform_faq_8_q"),answer:e("platform_faq_8_a")},{question:e("platform_faq_9_q"),answer:e("platform_faq_9_a")},{question:e("platform_faq_10_q"),answer:e("platform_faq_10_a")},{question:e("platform_faq_11_q"),answer:e("platform_faq_11_a")},{question:e("platform_faq_12_q"),answer:e("platform_faq_12_a")},{question:e("platform_faq_13_q"),answer:e("platform_faq_13_a")},{question:e("platform_faq_14_q"),answer:e("platform_faq_14_a")},{question:e("platform_faq_15_q"),answer:e("platform_faq_15_a")}],r="https://www.funblocks.net".concat(t);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(l.Z,{title:"FunBlocks AI - Visual AI Tools for Learning, Productivity & Creativity | Mind Maps, Infographics & More",description:"Transform your thinking with FunBlocks AI's visual tools suite: AI Mindmap, MindLadder, Infographic Generator, Slides Creator, Brainstorming Assistant, and more. Our research-backed tools help students, professionals, and creatives visualize complex concepts, enhance learning, and boost productivity through AI-powered visual thinking.",keywords:"AI visual tools, AI mindmap generator, AI infographic maker, AI presentation creator, visual learning tools, educational AI tools, AI brainstorming, mental models, cognitive learning tools, productivity AI, creative thinking tools, AI slides maker, visual thinking, concept visualization, learning frameworks, Bloom's taxonomy, Marzano framework, SOLO taxonomy, AI education tools, professional visualization tools",image:"/og-image.png",url:r,canonical:r}),(0,o.jsx)(c.Z,{faqs:n}),(0,o.jsx)(a.x,{children:(0,o.jsx)(s.Z,{})})]})}},17597:function(t,e,n){"use strict";n.d(e,{J:function(){return o}});let o="https://www.funblocks.net/service"}},function(t){t.O(0,[4838,3365,8151,8417,5460,2658,5408,906,9774,2888,179],function(){return t(t.s=75557)}),_N_E=t.O()}]);