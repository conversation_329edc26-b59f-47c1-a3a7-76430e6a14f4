"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2658],{27091:function(e,t,n){n.d(t,{T:function(){return i}});var a=n(67294),r=n(95372);function i(e){let{value:t,defaultValue:n,onChange:i,shouldUpdate:o=(e,t)=>e!==t}=e,l=(0,r.W)(i),s=(0,r.W)(o),[c,u]=(0,a.useState)(n),d=void 0!==t,f=d?t:c,m=(0,r.W)(e=>{let t="function"==typeof e?e(f):e;s(f,t)&&(d||u(t),l(t))},[d,l,f,s]);return[f,m]}},75133:function(e,t,n){n.d(t,{K:function(){return s}});var a=n(85893),r=n(34926),i=n(64096),o=n(49381),l=n(64993);let s=(0,o.G)(function(e,t){let{getButtonProps:n}=(0,i.bB)(),o=n(e,t),s=(0,i.YO)(),c={display:"flex",alignItems:"center",width:"100%",outline:0,...s.button};return(0,a.jsx)(l.m.button,{...o,className:(0,r.cx)("chakra-accordion__button",e.className),__css:c})});s.displayName="AccordionButton"},64096:function(e,t,n){n.d(t,{YO:function(){return o},_v:function(){return d},bB:function(){return s},di:function(){return c},ec:function(){return l},lh:function(){return i},mc:function(){return f}});var a=n(52110),r=n(28228);let[i,o]=(0,a.k)({name:"AccordionStylesContext",hookName:"useAccordionStyles",providerName:"<Accordion />"}),[l,s]=(0,a.k)({name:"AccordionItemContext",hookName:"useAccordionItemContext",providerName:"<AccordionItem />"}),[c,u,d,f]=(0,r.n)()},19109:function(e,t,n){n.d(t,{X:function(){return s}});var a=n(85893),r=n(34926),i=n(64096),o=n(63985),l=n(12553);function s(e){let{isOpen:t,isDisabled:n}=(0,i.bB)(),{reduceMotion:s}=(0,o.EF)(),c=(0,r.cx)("chakra-accordion__icon",e.className),u=(0,i.YO)(),d={opacity:n?.4:1,transform:t?"rotate(-180deg)":void 0,transition:s?void 0:"transform 0.2s",transformOrigin:"center",...u.icon};return(0,a.jsx)(l.J,{viewBox:"0 0 24 24","aria-hidden":!0,className:c,__css:d,...e,children:(0,a.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})})}s.displayName="AccordionIcon"},21502:function(e,t,n){n.d(t,{Q:function(){return d}});var a=n(85893),r=n(83695),i=n(34926),o=n(67294),l=n(64096),s=n(63985),c=n(49381),u=n(64993);let d=(0,c.G)(function(e,t){let{children:n,className:c}=e,{htmlProps:d,...f}=(0,s.Zl)(e),m=(0,l.YO)(),p=(0,r.k0)({...m.container,overflowAnchor:"none"}),x=(0,o.useMemo)(()=>f,[f]);return(0,a.jsx)(l.ec,{value:x,children:(0,a.jsx)(u.m.div,{ref:t,...d,className:(0,i.cx)("chakra-accordion__item",c),__css:p,children:"function"==typeof n?n({isExpanded:!!f.isOpen,isDisabled:!!f.isDisabled}):n})})});d.displayName="AccordionItem"},449:function(e,t,n){n.d(t,{H:function(){return _}});var a=n(85893),r=n(34926),i=n(64096),o=n(63985),l=n(76894),s=n(51526),c=n(97340),u=n(67294),d=n(82145);let f=e=>null!=e&&parseInt(e.toString(),10)>0,m={exit:{height:{duration:.2,ease:d.Lj.ease},opacity:{duration:.3,ease:d.Lj.ease}},enter:{height:{duration:.3,ease:d.Lj.ease},opacity:{duration:.4,ease:d.Lj.ease}}},p={exit:({animateOpacity:e,startingHeight:t,transition:n,transitionEnd:a,delay:r})=>({...e&&{opacity:f(t)?1:0},height:t,transitionEnd:a?.exit,transition:n?.exit??d.p$.exit(m.exit,r)}),enter:({animateOpacity:e,endingHeight:t,transition:n,transitionEnd:a,delay:r})=>({...e&&{opacity:1},height:t,transitionEnd:a?.enter,transition:n?.enter??d.p$.enter(m.enter,r)})},x=(0,u.forwardRef)((e,t)=>{let{in:n,unmountOnExit:i,animateOpacity:o=!0,startingHeight:d=0,endingHeight:f="auto",style:m,className:x,transition:v,transitionEnd:h,animatePresenceProps:_,...y}=e,[g,b]=(0,u.useState)(!1);(0,u.useEffect)(()=>{let e=setTimeout(()=>{b(!0)});return()=>clearTimeout(e)},[]),(0,l.Z)({condition:Number(d)>0&&!!i,message:"startingHeight and unmountOnExit are mutually exclusive. You can't use them together"});let j=parseFloat(d.toString())>0,N={startingHeight:d,endingHeight:f,animateOpacity:o,transition:g?v:{enter:{duration:0}},transitionEnd:{enter:h?.enter,exit:i?h?.exit:{...h?.exit,display:j?"block":"none"}}};return(0,a.jsx)(s.M,{..._,initial:!1,custom:N,children:(!i||n)&&(0,a.jsx)(c.E.div,{ref:t,...y,className:(0,r.cx)("chakra-collapse",x),style:{overflow:"hidden",display:"block",...m},custom:N,variants:p,initial:!!i&&"exit",animate:n||i?"enter":"exit",exit:"exit"})})});x.displayName="Collapse";var v=n(49381),h=n(64993);let _=(0,v.G)(function(e,t){let{className:n,motionProps:l,...s}=e,{reduceMotion:c}=(0,o.EF)(),{getPanelProps:u,isOpen:d}=(0,i.bB)(),f=u(s,t),m=(0,r.cx)("chakra-accordion__panel",n),p=(0,i.YO)();c||delete f.hidden;let v=(0,a.jsx)(h.m.div,{...f,__css:p.panel,className:m});return c?v:(0,a.jsx)(x,{in:d,...l,children:v})});_.displayName="AccordionPanel"},86561:function(e,t,n){n.d(t,{U:function(){return f}});var a=n(85893),r=n(65544),i=n(34926),o=n(67294),l=n(64096),s=n(63985),c=n(49381),u=n(73035),d=n(64993);let f=(0,c.G)(function({children:e,reduceMotion:t,...n},c){let f=(0,u.j)("Accordion",n),m=(0,r.L)(n),{htmlProps:p,descendants:x,...v}=(0,s.As)(m),h=(0,o.useMemo)(()=>({...v,reduceMotion:!!t}),[v,t]);return(0,a.jsx)(l.di,{value:x,children:(0,a.jsx)(s.a2,{value:h,children:(0,a.jsx)(l.lh,{value:f,children:(0,a.jsx)(d.m.div,{ref:c,...p,className:(0,i.cx)("chakra-accordion",n.className),__css:f.root,children:e})})})})});f.displayName="Accordion"},63985:function(e,t,n){n.d(t,{As:function(){return u},EF:function(){return f},Zl:function(){return m},a2:function(){return d}});var a=n(27091),r=n(29062),i=n(52110),o=n(68928),l=n(76894),s=n(67294),c=n(64096);function u(e){let{onChange:t,defaultIndex:n,index:r,allowMultiple:i,allowToggle:o,...u}=e;(function(e){let t=e.index||e.defaultIndex,n=null!=t&&!Array.isArray(t)&&e.allowMultiple;(0,l.Z)({condition:!!n,message:`If 'allowMultiple' is passed, then 'index' or 'defaultIndex' must be an array. You passed: ${typeof t},`})})(e),(0,l.Z)({condition:!!(e.allowMultiple&&e.allowToggle),message:"If 'allowMultiple' is passed, 'allowToggle' will be ignored. Either remove 'allowToggle' or 'allowMultiple' depending on whether you want multiple accordions visible or not"});let d=(0,c._v)(),[f,m]=(0,s.useState)(-1);(0,s.useEffect)(()=>()=>{m(-1)},[]);let[p,x]=(0,a.T)({value:r,defaultValue:()=>i?n??[]:n??-1,onChange:t}),v=e=>{let t=!1;null!==e&&(t=Array.isArray(p)?p.includes(e):p===e);let n=t=>{if(null!==e){if(i&&Array.isArray(p)){let n=t?p.concat(e):p.filter(t=>t!==e);x(n)}else t?x(e):o&&x(-1)}};return{isOpen:t,onChange:n}};return{index:p,setIndex:x,htmlProps:u,getAccordionItemProps:v,focusedIndex:f,setFocusedIndex:m,descendants:d}}let[d,f]=(0,i.k)({name:"AccordionContext",hookName:"useAccordionContext",providerName:"Accordion"});function m(e){var t;let{isDisabled:n,isFocusable:a,id:i,...u}=e,{getAccordionItemProps:d,setFocusedIndex:m}=f(),p=(0,s.useRef)(null),x=(0,s.useId)(),v=i??x,h=`accordion-button-${v}`,_=`accordion-panel-${v}`;(0,l.Z)({condition:!!(e.isFocusable&&!e.isDisabled),message:`Using only 'isFocusable', this prop is reserved for situations where you pass 'isDisabled' but you still want the element to receive focus (A11y). Either remove it or pass 'isDisabled' as well.
    `});let{register:y,index:g,descendants:b}=(0,c.mc)({disabled:n&&!a}),{isOpen:j,onChange:N}=d(-1===g?null:g);t={isOpen:j,isDisabled:n},(0,l.Z)({condition:t.isOpen&&!!t.isDisabled,message:"Cannot open a disabled accordion item"});let k=()=>{N?.(!0)},C=()=>{N?.(!1)},A=(0,s.useCallback)(()=>{N?.(!j),m(g)},[g,m,j,N]),w=(0,s.useCallback)(e=>{let t={ArrowDown:()=>{let e=b.nextEnabled(g);e?.node.focus()},ArrowUp:()=>{let e=b.prevEnabled(g);e?.node.focus()},Home:()=>{let e=b.firstEnabled();e?.node.focus()},End:()=>{let e=b.lastEnabled();e?.node.focus()}}[e.key];t&&(e.preventDefault(),t(e))},[b,g]),G=(0,s.useCallback)(()=>{m(g)},[m,g]),E=(0,s.useCallback)(function(e={},t=null){return{...e,type:"button",ref:(0,r.lq)(y,p,t),id:h,disabled:!!n,"aria-expanded":!!j,"aria-controls":_,onClick:(0,o.v)(e.onClick,A),onFocus:(0,o.v)(e.onFocus,G),onKeyDown:(0,o.v)(e.onKeyDown,w)}},[h,n,j,A,G,w,_,y]),I=(0,s.useCallback)(function(e={},t=null){return{...e,ref:t,role:"region",id:_,"aria-labelledby":h,hidden:!j}},[h,j,_]);return{isOpen:j,isDisabled:n,isFocusable:a,onOpen:k,onClose:C,getButtonProps:E,getPanelProps:I,htmlProps:u}}},46572:function(e,t,n){n.d(t,{q:function(){return j}});var a=n(85893),r=n(83695),i=n(65544),o=n(34926),l=n(20397),s=n(68928),c=n(67294),u=n(52110);let[d,f]=(0,u.k)({name:"AvatarStylesContext",hookName:"useAvatarStyles",providerName:"<Avatar/>"});var m=n(64993);function p(e){let t=e.trim().split(" "),n=t[0]??"",a=t.length>1?t[t.length-1]:"";return n&&a?`${n.charAt(0)}${a.charAt(0)}`:n.charAt(0)}function x(e){let{name:t,getInitials:n,...r}=e,i=f();return(0,a.jsx)(m.m.div,{role:"img","aria-label":t,...r,__css:i.label,children:t?n?.(t):null})}x.displayName="AvatarName";let v=e=>(0,a.jsxs)(m.m.svg,{viewBox:"0 0 128 128",color:"#fff",width:"100%",height:"100%",className:"chakra-avatar__svg",...e,children:[(0,a.jsx)("path",{fill:"currentColor",d:"M103,102.1388 C93.094,111.92 79.3504,118 64.1638,118 C48.8056,118 34.9294,111.768 25,101.7892 L25,95.2 C25,86.8096 31.981,80 40.6,80 L87.4,80 C96.019,80 103,86.8096 103,95.2 L103,102.1388 Z"}),(0,a.jsx)("path",{fill:"currentColor",d:"M63.9961647,24 C51.2938136,24 41,34.2938136 41,46.9961647 C41,59.7061864 51.2938136,70 63.9961647,70 C76.6985159,70 87,59.7061864 87,46.9961647 C87,34.2938136 76.6985159,24 63.9961647,24"})]});var h=n(65566);function _(e){let{src:t,srcSet:n,onError:r,onLoad:i,getInitials:o,name:l,borderRadius:s,loading:u,iconLabel:d,icon:f=(0,a.jsx)(v,{}),ignoreFallback:p,referrerPolicy:_,crossOrigin:y}=e,g=(0,h.d)({src:t,onError:r,crossOrigin:y,ignoreFallback:p});return t&&"loaded"===g?(0,a.jsx)(m.m.img,{src:t,srcSet:n,alt:l??d,onLoad:i,referrerPolicy:_,crossOrigin:y??void 0,className:"chakra-avatar__img",loading:u,__css:{width:"100%",height:"100%",objectFit:"cover",borderRadius:s}}):l?(0,a.jsx)(x,{className:"chakra-avatar__initials",getInitials:o,name:l}):(0,c.cloneElement)(f,{role:"img","aria-label":d})}_.displayName="AvatarImage";var y=n(49381),g=n(73035);let b=(0,r.k0)({display:"inline-flex",alignItems:"center",justifyContent:"center",textAlign:"center",textTransform:"uppercase",fontWeight:"medium",position:"relative",flexShrink:0}),j=(0,y.G)((e,t)=>{let n=(0,g.j)("Avatar",e),[r,u]=(0,c.useState)(!1),{src:f,srcSet:x,name:h,showBorder:y,borderRadius:j="full",onError:N,onLoad:k,getInitials:C=p,icon:A=(0,a.jsx)(v,{}),iconLabel:w=" avatar",loading:G,children:E,borderColor:I,ignoreFallback:L,crossOrigin:S,referrerPolicy:T,...M}=(0,i.L)(e),D={borderRadius:j,borderWidth:y?"2px":void 0,...b,...n.container};return I&&(D.borderColor=I),(0,a.jsx)(m.m.span,{ref:t,...M,className:(0,o.cx)("chakra-avatar",e.className),"data-loaded":(0,l.P)(r),__css:D,children:(0,a.jsxs)(d,{value:n,children:[(0,a.jsx)(_,{src:f,srcSet:x,loading:G,onLoad:(0,s.v)(k,()=>{u(!0)}),onError:N,getInitials:C,name:h,borderRadius:j,icon:A,iconLabel:w,ignoreFallback:L,crossOrigin:S,referrerPolicy:T}),E]})})});j.displayName="Avatar"},74032:function(e,t,n){n.d(t,{C:function(){return c}});var a=n(85893),r=n(65544),i=n(34926),o=n(49381),l=n(73035),s=n(64993);let c=(0,o.G)(function(e,t){let n=(0,l.m)("Badge",e),{className:o,...c}=(0,r.L)(e);return(0,a.jsx)(s.m.span,{ref:t,className:(0,i.cx)("chakra-badge",e.className),...c,__css:{display:"inline-block",whiteSpace:"nowrap",verticalAlign:"middle",...n}})});c.displayName="Badge"},8455:function(e,t,n){n.d(t,{e:function(){return s}});var a=n(85893),r=n(34926),i=n(9129),o=n(49381),l=n(64993);let s=(0,o.G)(function(e,t){let{className:n,...o}=e,s=(0,i.v)();return(0,a.jsx)(l.m.div,{ref:t,className:(0,r.cx)("chakra-card__body",n),__css:s.body,...o})})},9129:function(e,t,n){n.d(t,{Y:function(){return r},v:function(){return i}});var a=n(66140);let[r,i]=(0,a.eC)("Card")},77475:function(e,t,n){n.d(t,{e:function(){return s}});var a=n(85893),r=n(34926),i=n(9129),o=n(49381),l=n(64993);let s=(0,o.G)(function(e,t){let{className:n,justify:o,...s}=e,c=(0,i.v)();return(0,a.jsx)(l.m.div,{ref:t,className:(0,r.cx)("chakra-card__footer",n),__css:{display:"flex",justifyContent:o,...c.footer},...s})})},58779:function(e,t,n){n.d(t,{O:function(){return s}});var a=n(85893),r=n(34926),i=n(9129),o=n(49381),l=n(64993);let s=(0,o.G)(function(e,t){let{className:n,...o}=e,s=(0,i.v)();return(0,a.jsx)(l.m.div,{ref:t,className:(0,r.cx)("chakra-card__header",n),__css:s.header,...o})})},43695:function(e,t,n){n.d(t,{Z:function(){return u}});var a=n(85893),r=n(65544),i=n(34926),o=n(9129),l=n(49381),s=n(73035),c=n(64993);let u=(0,l.G)(function(e,t){let{className:n,children:l,direction:u="column",justify:d,align:f,...m}=(0,r.L)(e),p=(0,s.j)("Card",e);return(0,a.jsx)(c.m.div,{ref:t,className:(0,i.cx)("chakra-card",n),__css:{display:"flex",flexDirection:u,justifyContent:d,alignItems:f,position:"relative",minWidth:0,wordWrap:"break-word",...p.container},...m,children:(0,a.jsx)(o.Y,{value:p,children:l})})})},15715:function(e,t,n){n.d(t,{M:function(){return o}});var a=n(85893),r=n(64993),i=n(49381);let o=(0,r.m)("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center"}});o.displayName="Center";let l={horizontal:{insetStart:"50%",transform:"translateX(-50%)"},vertical:{top:"50%",transform:"translateY(-50%)"},both:{insetStart:"50%",top:"50%",transform:"translate(-50%, -50%)"}};(0,i.G)(function(e,t){let{axis:n="both",...i}=e;return(0,a.jsx)(r.m.div,{ref:t,__css:l[n],...i,position:"absolute"})})},96254:function(e,t,n){n.d(t,{W:function(){return c}});var a=n(85893),r=n(65544),i=n(34926),o=n(49381),l=n(73035),s=n(64993);let c=(0,o.G)(function(e,t){let{className:n,centerContent:o,...c}=(0,r.L)(e),u=(0,l.m)("Container",e);return(0,a.jsx)(s.m.div,{ref:t,className:(0,i.cx)("chakra-container",n),...c,__css:{...u,...o&&{display:"flex",flexDirection:"column",alignItems:"center"}}})});c.displayName="Container"},16610:function(e,t,n){n.d(t,{r:function(){return o}});var a=n(85893),r=n(49381),i=n(64993);let o=(0,r.G)(function(e,t){let{templateAreas:n,gap:r,rowGap:o,columnGap:l,column:s,row:c,autoFlow:u,autoRows:d,templateRows:f,autoColumns:m,templateColumns:p,...x}=e;return(0,a.jsx)(i.m.div,{ref:t,__css:{display:"grid",gridTemplateAreas:n,gridGap:r,gridRowGap:o,gridColumnGap:l,gridAutoColumns:m,gridColumn:s,gridRow:c,gridAutoFlow:u,gridAutoRows:d,gridTemplateRows:f,gridTemplateColumns:p},...x})});o.displayName="Grid"},43321:function(e,t,n){n.d(t,{M:function(){return c}});var a=n(85893),r=n(1185),i=n(16610),o=n(37984),l=n(70052),s=n(49381);let c=(0,s.G)(function(e,t){let{columns:n,spacingX:s,spacingY:c,spacing:u,minChildWidth:d,...f}=e,m=(0,o.F)(),p=d?(0,r.XQ)(d,e=>{let t=(0,l.LP)("sizes",e,"number"==typeof e?`${e}px`:e)(m);return null===e?null:`repeat(auto-fit, minmax(${t}, 1fr))`}):(0,r.XQ)(n,e=>null===e?null:`repeat(${e}, minmax(0, 1fr))`);return(0,a.jsx)(i.r,{ref:t,gap:u,columnGap:s,rowGap:c,templateColumns:p,...f})});c.displayName="SimpleGrid"},82206:function(e,t,n){n.d(t,{I:function(){return u}});var a=n(85893),r=n(65544),i=n(34926),o=n(16013),l=n(49381),s=n(73035),c=n(64993);let u=(0,l.G)(function(e,t){let{htmlSize:n,...l}=e,u=(0,s.j)("Input",l),d=(0,r.L)(l),f=(0,o.Y)(d),m=(0,i.cx)("chakra-input",e.className);return(0,a.jsx)(c.m.input,{size:n,...f,__css:u.field,ref:t,className:m})});u.displayName="Input",u.id="Input"},74703:function(e,t,n){n.d(t,{i:function(){return f},p:function(){return d}});var a=n(85893),r=n(65544),i=n(52110),o=n(34926),l=n(49381),s=n(73035),c=n(64993);let[u,d]=(0,i.k)({name:"TableStylesContext",errorMessage:"useTableStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Table />\" "}),f=(0,l.G)((e,t)=>{let n=(0,s.j)("Table",e),{className:i,layout:l,...d}=(0,r.L)(e);return(0,a.jsx)(u,{value:n,children:(0,a.jsx)(c.m.table,{ref:t,__css:{tableLayout:l,...n.table},className:(0,o.cx)("chakra-table",i),...d})})});f.displayName="Table"},6007:function(e,t,n){n.d(t,{p:function(){return l}});var a=n(85893),r=n(74703),i=n(49381),o=n(64993);let l=(0,i.G)((e,t)=>{let n=(0,r.p)();return(0,a.jsx)(o.m.tbody,{...e,ref:t,__css:n.tbody})})},43416:function(e,t,n){n.d(t,{Td:function(){return l}});var a=n(85893),r=n(74703),i=n(49381),o=n(64993);let l=(0,i.G)(({isNumeric:e,...t},n)=>{let i=(0,r.p)();return(0,a.jsx)(o.m.td,{...t,ref:n,__css:i.td,"data-is-numeric":e})})},4476:function(e,t,n){n.d(t,{Th:function(){return l}});var a=n(85893),r=n(74703),i=n(49381),o=n(64993);let l=(0,i.G)(({isNumeric:e,...t},n)=>{let i=(0,r.p)();return(0,a.jsx)(o.m.th,{...t,ref:n,__css:i.th,"data-is-numeric":e})})},21901:function(e,t,n){n.d(t,{h:function(){return l}});var a=n(85893),r=n(74703),i=n(49381),o=n(64993);let l=(0,i.G)((e,t)=>{let n=(0,r.p)();return(0,a.jsx)(o.m.thead,{...e,ref:t,__css:n.thead})})},57167:function(e,t,n){n.d(t,{Tr:function(){return l}});var a=n(85893),r=n(74703),i=n(49381),o=n(64993);let l=(0,i.G)((e,t)=>{let n=(0,r.p)();return(0,a.jsx)(o.m.tr,{...e,ref:t,__css:n.tr})})},50232:function(e,t,n){n.d(t,{X:function(){return c}});var a=n(85893),r=n(65544),i=n(34926),o=n(49381),l=n(73035),s=n(64993);let c=(0,o.G)(function(e,t){let n=(0,l.m)("Heading",e),{className:o,...c}=(0,r.L)(e);return(0,a.jsx)(s.m.h2,{ref:t,className:(0,i.cx)("chakra-heading",e.className),...c,__css:n})});c.displayName="Heading"},76894:function(e,t,n){n.d(t,{Z:function(){return a}});let a=e=>{let{condition:t,message:n}=e}}}]);