"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5826],{92285:function(t,e,a){a.d(e,{AD:function(){return h},AE:function(){return c},Mu:function(){return s},O:function(){return n},kc:function(){return d},rB:function(){return l},yU:function(){return o}});var r=a(58222),i=a(17967),s=(0,r.eW)((t,e)=>{let a=t.append("rect");if(a.attr("x",e.x),a.attr("y",e.y),a.attr("fill",e.fill),a.attr("stroke",e.stroke),a.attr("width",e.width),a.attr("height",e.height),e.name&&a.attr("name",e.name),e.rx&&a.attr("rx",e.rx),e.ry&&a.attr("ry",e.ry),void 0!==e.attrs)for(let t in e.attrs)a.attr(t,e.attrs[t]);return e.class&&a.attr("class",e.class),a},"drawRect"),n=(0,r.eW)((t,e)=>{let a={x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"},r=s(t,a);r.lower()},"drawBackgroundRect"),o=(0,r.eW)((t,e)=>{let a=e.text.replace(r.Vw," "),i=t.append("text");i.attr("x",e.x),i.attr("y",e.y),i.attr("class","legend"),i.style("text-anchor",e.anchor),e.class&&i.attr("class",e.class);let s=i.append("tspan");return s.attr("x",e.x+2*e.textMargin),s.text(a),i},"drawText"),c=(0,r.eW)((t,e,a,r)=>{let s=t.append("image");s.attr("x",e),s.attr("y",a);let n=(0,i.N)(r);s.attr("xlink:href",n)},"drawImage"),l=(0,r.eW)((t,e,a,r)=>{let s=t.append("use");s.attr("x",e),s.attr("y",a);let n=(0,i.N)(r);s.attr("xlink:href",`#${n}`)},"drawEmbeddedImage"),d=(0,r.eW)(()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),"getNoteRect"),h=(0,r.eW)(()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0}),"getTextObj")},51541:function(t,e,a){a.d(e,{A:function(){return i}});var r=a(58222),i=class{constructor(t){this.init=t,this.records=this.init()}static{(0,r.eW)(this,"ImperativeState")}reset(){this.records=this.init()}}},15826:function(t,e,a){a.r(e),a.d(e,{diagram:function(){return tU}});var r=a(92285),i=a(51541),s=a(68414),n=a(58222),o=a(13406),c=a(17967),l=function(){var t=(0,n.eW)(function(t,e,a,r){for(a=a||{},r=t.length;r--;a[t[r]]=e);return a},"o"),e=[1,2],a=[1,3],r=[1,4],i=[2,4],s=[1,9],o=[1,11],c=[1,13],l=[1,14],d=[1,16],h=[1,17],p=[1,18],g=[1,24],u=[1,25],x=[1,26],y=[1,27],m=[1,28],b=[1,29],f=[1,30],T=[1,31],E=[1,32],w=[1,33],I=[1,34],L=[1,35],_=[1,36],P=[1,37],k=[1,38],N=[1,39],M=[1,41],A=[1,42],v=[1,43],S=[1,44],O=[1,45],D=[1,46],W=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],R=[4,5,16,50,52,53],Y=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],C=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],B=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],$=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],V=[68,69,70],F=[1,122],q={trace:(0,n.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:(0,n.eW)(function(t,e,a,r,i,s,n){var o=s.length-1;switch(i){case 3:return r.apply(s[o]),s[o];case 4:case 9:case 8:case 13:this.$=[];break;case 5:case 10:s[o-1].push(s[o]),this.$=s[o-1];break;case 6:case 7:case 11:case 12:case 62:this.$=s[o];break;case 15:s[o].type="createParticipant",this.$=s[o];break;case 16:s[o-1].unshift({type:"boxStart",boxData:r.parseBoxData(s[o-2])}),s[o-1].push({type:"boxEnd",boxText:s[o-2]}),this.$=s[o-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(s[o-2]),sequenceIndexStep:Number(s[o-1]),sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(s[o-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:r.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:s[o-1].actor};break;case 23:this.$={type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:s[o-1].actor};break;case 29:r.setDiagramTitle(s[o].substring(6)),this.$=s[o].substring(6);break;case 30:r.setDiagramTitle(s[o].substring(7)),this.$=s[o].substring(7);break;case 31:this.$=s[o].trim(),r.setAccTitle(this.$);break;case 32:case 33:this.$=s[o].trim(),r.setAccDescription(this.$);break;case 34:s[o-1].unshift({type:"loopStart",loopText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.LOOP_START}),s[o-1].push({type:"loopEnd",loopText:s[o-2],signalType:r.LINETYPE.LOOP_END}),this.$=s[o-1];break;case 35:s[o-1].unshift({type:"rectStart",color:r.parseMessage(s[o-2]),signalType:r.LINETYPE.RECT_START}),s[o-1].push({type:"rectEnd",color:r.parseMessage(s[o-2]),signalType:r.LINETYPE.RECT_END}),this.$=s[o-1];break;case 36:s[o-1].unshift({type:"optStart",optText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.OPT_START}),s[o-1].push({type:"optEnd",optText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.OPT_END}),this.$=s[o-1];break;case 37:s[o-1].unshift({type:"altStart",altText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.ALT_START}),s[o-1].push({type:"altEnd",signalType:r.LINETYPE.ALT_END}),this.$=s[o-1];break;case 38:s[o-1].unshift({type:"parStart",parText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.PAR_START}),s[o-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END}),this.$=s[o-1];break;case 39:s[o-1].unshift({type:"parStart",parText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.PAR_OVER_START}),s[o-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END}),this.$=s[o-1];break;case 40:s[o-1].unshift({type:"criticalStart",criticalText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.CRITICAL_START}),s[o-1].push({type:"criticalEnd",signalType:r.LINETYPE.CRITICAL_END}),this.$=s[o-1];break;case 41:s[o-1].unshift({type:"breakStart",breakText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.BREAK_START}),s[o-1].push({type:"breakEnd",optText:r.parseMessage(s[o-2]),signalType:r.LINETYPE.BREAK_END}),this.$=s[o-1];break;case 43:this.$=s[o-3].concat([{type:"option",optionText:r.parseMessage(s[o-1]),signalType:r.LINETYPE.CRITICAL_OPTION},s[o]]);break;case 45:this.$=s[o-3].concat([{type:"and",parText:r.parseMessage(s[o-1]),signalType:r.LINETYPE.PAR_AND},s[o]]);break;case 47:this.$=s[o-3].concat([{type:"else",altText:r.parseMessage(s[o-1]),signalType:r.LINETYPE.ALT_ELSE},s[o]]);break;case 48:s[o-3].draw="participant",s[o-3].type="addParticipant",s[o-3].description=r.parseMessage(s[o-1]),this.$=s[o-3];break;case 49:s[o-1].draw="participant",s[o-1].type="addParticipant",this.$=s[o-1];break;case 50:s[o-3].draw="actor",s[o-3].type="addParticipant",s[o-3].description=r.parseMessage(s[o-1]),this.$=s[o-3];break;case 51:s[o-1].draw="actor",s[o-1].type="addParticipant",this.$=s[o-1];break;case 52:s[o-1].type="destroyParticipant",this.$=s[o-1];break;case 53:this.$=[s[o-1],{type:"addNote",placement:s[o-2],actor:s[o-1].actor,text:s[o]}];break;case 54:s[o-2]=[].concat(s[o-1],s[o-1]).slice(0,2),s[o-2][0]=s[o-2][0].actor,s[o-2][1]=s[o-2][1].actor,this.$=[s[o-1],{type:"addNote",placement:r.PLACEMENT.OVER,actor:s[o-2].slice(0,2),text:s[o]}];break;case 55:this.$=[s[o-1],{type:"addLinks",actor:s[o-1].actor,text:s[o]}];break;case 56:this.$=[s[o-1],{type:"addALink",actor:s[o-1].actor,text:s[o]}];break;case 57:this.$=[s[o-1],{type:"addProperties",actor:s[o-1].actor,text:s[o]}];break;case 58:this.$=[s[o-1],{type:"addDetails",actor:s[o-1].actor,text:s[o]}];break;case 61:this.$=[s[o-2],s[o]];break;case 63:this.$=r.PLACEMENT.LEFTOF;break;case 64:this.$=r.PLACEMENT.RIGHTOF;break;case 65:this.$=[s[o-4],s[o-1],{type:"addMessage",from:s[o-4].actor,to:s[o-1].actor,signalType:s[o-3],msg:s[o],activate:!0},{type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:s[o-1].actor}];break;case 66:this.$=[s[o-4],s[o-1],{type:"addMessage",from:s[o-4].actor,to:s[o-1].actor,signalType:s[o-3],msg:s[o]},{type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:s[o-4].actor}];break;case 67:this.$=[s[o-3],s[o-1],{type:"addMessage",from:s[o-3].actor,to:s[o-1].actor,signalType:s[o-2],msg:s[o]}];break;case 68:this.$={type:"addParticipant",actor:s[o]};break;case 69:this.$=r.LINETYPE.SOLID_OPEN;break;case 70:this.$=r.LINETYPE.DOTTED_OPEN;break;case 71:this.$=r.LINETYPE.SOLID;break;case 72:this.$=r.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=r.LINETYPE.DOTTED;break;case 74:this.$=r.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=r.LINETYPE.SOLID_CROSS;break;case 76:this.$=r.LINETYPE.DOTTED_CROSS;break;case 77:this.$=r.LINETYPE.SOLID_POINT;break;case 78:this.$=r.LINETYPE.DOTTED_POINT;break;case 79:this.$=r.parseMessage(s[o].trim().substring(1))}},"anonymous"),table:[{3:1,4:e,5:a,6:r},{1:[3]},{3:5,4:e,5:a,6:r},{3:6,4:e,5:a,6:r},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],i,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:s,5:o,8:8,9:10,12:12,13:c,14:l,17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},t(W,[2,5]),{9:47,12:12,13:c,14:l,17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},t(W,[2,7]),t(W,[2,8]),t(W,[2,14]),{12:48,50:P,52:k,53:N},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:D},{22:55,70:D},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(W,[2,29]),t(W,[2,30]),{32:[1,61]},{34:[1,62]},t(W,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:D},{22:72,70:D},{22:73,70:D},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:D},{22:90,70:D},{22:91,70:D},{22:92,70:D},t([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),t(W,[2,6]),t(W,[2,15]),t(R,[2,9],{10:93}),t(W,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},t(W,[2,21]),{5:[1,97]},{5:[1,98]},t(W,[2,24]),t(W,[2,25]),t(W,[2,26]),t(W,[2,27]),t(W,[2,28]),t(W,[2,31]),t(W,[2,32]),t(Y,i,{7:99}),t(Y,i,{7:100}),t(Y,i,{7:101}),t(C,i,{40:102,7:103}),t(B,i,{42:104,7:105}),t(B,i,{7:105,42:106}),t($,i,{45:107,7:108}),t(Y,i,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:D},t(V,[2,69]),t(V,[2,70]),t(V,[2,71]),t(V,[2,72]),t(V,[2,73]),t(V,[2,74]),t(V,[2,75]),t(V,[2,76]),t(V,[2,77]),t(V,[2,78]),{22:118,70:D},{22:120,58:119,70:D},{70:[2,63]},{70:[2,64]},{56:121,81:F},{56:123,81:F},{56:124,81:F},{56:125,81:F},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:P,52:k,53:N},{5:[1,131]},t(W,[2,19]),t(W,[2,20]),t(W,[2,22]),t(W,[2,23]),{4:s,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,132],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},{4:s,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,133],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},{4:s,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,134],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},{16:[1,135]},{4:s,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,46],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,49:[1,136],50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},{16:[1,137]},{4:s,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,44],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,48:[1,138],50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},{16:[1,139]},{16:[1,140]},{4:s,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,42],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,47:[1,141],50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},{4:s,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,142],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:f,38:T,39:E,41:w,43:I,44:L,46:_,50:P,52:k,53:N,54:M,59:A,60:v,61:S,62:O,70:D},{15:[1,143]},t(W,[2,49]),{15:[1,144]},t(W,[2,51]),t(W,[2,52]),{22:145,70:D},{22:146,70:D},{56:147,81:F},{56:148,81:F},{56:149,81:F},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(W,[2,16]),t(R,[2,10]),{12:151,50:P,52:k,53:N},t(R,[2,12]),t(R,[2,13]),t(W,[2,18]),t(W,[2,34]),t(W,[2,35]),t(W,[2,36]),t(W,[2,37]),{15:[1,152]},t(W,[2,38]),{15:[1,153]},t(W,[2,39]),t(W,[2,40]),{15:[1,154]},t(W,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:F},{56:158,81:F},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:D},t(R,[2,11]),t(C,i,{7:103,40:160}),t(B,i,{7:105,42:161}),t($,i,{7:108,45:162}),t(W,[2,48]),t(W,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:(0,n.eW)(function(t,e){if(e.recoverable)this.trace(t);else{var a=Error(t);throw a.hash=e,a}},"parseError"),parse:(0,n.eW)(function(t){var e=this,a=[0],r=[],i=[null],s=[],o=this.table,c="",l=0,d=0,h=0,p=s.slice.call(arguments,1),g=Object.create(this.lexer),u={yy:{}};for(var x in this.yy)Object.prototype.hasOwnProperty.call(this.yy,x)&&(u.yy[x]=this.yy[x]);g.setInput(t,u.yy),u.yy.lexer=g,u.yy.parser=this,void 0===g.yylloc&&(g.yylloc={});var y=g.yylloc;s.push(y);var m=g.options&&g.options.ranges;function b(){var t;return"number"!=typeof(t=r.pop()||g.lex()||1)&&(t instanceof Array&&(t=(r=t).pop()),t=e.symbols_[t]||t),t}"function"==typeof u.yy.parseError?this.parseError=u.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,n.eW)(function(t){a.length=a.length-2*t,i.length=i.length-t,s.length=s.length-t},"popStack"),(0,n.eW)(b,"lex");for(var f,T,E,w,I,L,_,P,k,N={};;){if(E=a[a.length-1],this.defaultActions[E]?w=this.defaultActions[E]:(null==f&&(f=b()),w=o[E]&&o[E][f]),void 0===w||!w.length||!w[0]){var M="";for(L in k=[],o[E])this.terminals_[L]&&L>2&&k.push("'"+this.terminals_[L]+"'");M=g.showPosition?"Parse error on line "+(l+1)+":\n"+g.showPosition()+"\nExpecting "+k.join(", ")+", got '"+(this.terminals_[f]||f)+"'":"Parse error on line "+(l+1)+": Unexpected "+(1==f?"end of input":"'"+(this.terminals_[f]||f)+"'"),this.parseError(M,{text:g.match,token:this.terminals_[f]||f,line:g.yylineno,loc:y,expected:k})}if(w[0]instanceof Array&&w.length>1)throw Error("Parse Error: multiple actions possible at state: "+E+", token: "+f);switch(w[0]){case 1:a.push(f),i.push(g.yytext),s.push(g.yylloc),a.push(w[1]),f=null,T?(f=T,T=null):(d=g.yyleng,c=g.yytext,l=g.yylineno,y=g.yylloc,h>0&&h--);break;case 2:if(_=this.productions_[w[1]][1],N.$=i[i.length-_],N._$={first_line:s[s.length-(_||1)].first_line,last_line:s[s.length-1].last_line,first_column:s[s.length-(_||1)].first_column,last_column:s[s.length-1].last_column},m&&(N._$.range=[s[s.length-(_||1)].range[0],s[s.length-1].range[1]]),void 0!==(I=this.performAction.apply(N,[c,d,l,u.yy,w[1],i,s].concat(p))))return I;_&&(a=a.slice(0,-1*_*2),i=i.slice(0,-1*_),s=s.slice(0,-1*_)),a.push(this.productions_[w[1]][0]),i.push(N.$),s.push(N._$),P=o[a[a.length-2]][a[a.length-1]],a.push(P);break;case 3:return!0}}return!0},"parse")},z={EOF:1,parseError:(0,n.eW)(function(t,e){if(this.yy.parser)this.yy.parser.parseError(t,e);else throw Error(t)},"parseError"),setInput:(0,n.eW)(function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,n.eW)(function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},"input"),unput:(0,n.eW)(function(t){var e=t.length,a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var i=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===r.length?this.yylloc.first_column:0)+r[r.length-a.length].length-a[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[i[0],i[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},"unput"),more:(0,n.eW)(function(){return this._more=!0,this},"more"),reject:(0,n.eW)(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:(0,n.eW)(function(t){this.unput(this.match.slice(t))},"less"),pastInput:(0,n.eW)(function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,n.eW)(function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,n.eW)(function(){var t=this.pastInput(),e=Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},"showPosition"),test_match:(0,n.eW)(function(t,e){var a,r,i;if(this.options.backtrack_lexer&&(i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(i.yylloc.range=this.yylloc.range.slice(0))),(r=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack)for(var s in i)this[s]=i[s];return!1},"test_match"),next:(0,n.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var t,e,a,r,i=this._currentRules(),s=0;s<i.length;s++)if((a=this._input.match(this.rules[i[s]]))&&(!e||a[0].length>e[0].length)){if(e=a,r=s,this.options.backtrack_lexer){if(!1!==(t=this.test_match(a,i[s])))return t;if(!this._backtrack)return!1;e=!1;continue}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,i[r]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,n.eW)(function(){return this.next()||this.lex()},"lex"),begin:(0,n.eW)(function(t){this.conditionStack.push(t)},"begin"),popState:(0,n.eW)(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,n.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,n.eW)(function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},"topState"),pushState:(0,n.eW)(function(t){this.begin(t)},"pushState"),stateStackSize:(0,n.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,n.eW)(function(t,e,a,r){switch(a){case 0:case 51:case 66:return 5;case 1:case 2:case 3:case 4:case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;case 8:return this.begin("ID"),50;case 9:return this.begin("ID"),52;case 10:return 13;case 11:return this.begin("ID"),53;case 12:return e.yytext=e.yytext.trim(),this.begin("ALIAS"),70;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;case 14:return this.popState(),this.popState(),5;case 15:return this.begin("LINE"),36;case 16:return this.begin("LINE"),37;case 17:return this.begin("LINE"),38;case 18:return this.begin("LINE"),39;case 19:return this.begin("LINE"),49;case 20:return this.begin("LINE"),41;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),48;case 23:return this.begin("LINE"),44;case 24:return this.begin("LINE"),47;case 25:return this.begin("LINE"),46;case 26:return this.popState(),15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;case 37:return this.begin("ID"),23;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;case 41:return this.popState(),"acc_title_value";case 42:return this.begin("acc_descr"),33;case 43:return this.popState(),"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 52:return e.yytext=e.yytext.trim(),70;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 67:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}};function H(){this.yy={}}return q.lexer=z,(0,n.eW)(H,"Parser"),H.prototype=q,q.Parser=H,new H}();l.parser=l;var d=new i.A(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})),h=(0,n.eW)(function(t){d.records.boxes.push({name:t.text,wrap:t.wrap??A(),fill:t.color,actorKeys:[]}),d.records.currentBox=d.records.boxes.slice(-1)[0]},"addBox"),p=(0,n.eW)(function(t,e,a,r){let i=d.records.currentBox,s=d.records.actors.get(t);if(s){if(d.records.currentBox&&s.box&&d.records.currentBox!==s.box)throw Error(`A same participant should only be defined in one Box: ${s.name} can't be in '${s.box.name}' and in '${d.records.currentBox.name}' at the same time.`);if(i=s.box?s.box:d.records.currentBox,s.box=i,s&&e===s.name&&null==a)return}if(a?.text==null&&(a={text:e,type:r}),(null==r||null==a.text)&&(a={text:e,type:r}),d.records.actors.set(t,{box:i,name:e,description:a.text,wrap:a.wrap??A(),prevActor:d.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:r??"participant"}),d.records.prevActor){let e=d.records.actors.get(d.records.prevActor);e&&(e.nextActor=t)}d.records.currentBox&&d.records.currentBox.actorKeys.push(t),d.records.prevActor=t},"addActor"),g=(0,n.eW)(t=>{let e;let a=0;if(!t)return 0;for(e=0;e<d.records.messages.length;e++)d.records.messages[e].type===D.ACTIVE_START&&d.records.messages[e].from===t&&a++,d.records.messages[e].type===D.ACTIVE_END&&d.records.messages[e].from===t&&a--;return a},"activationCount"),u=(0,n.eW)(function(t,e,a,r){d.records.messages.push({from:t,to:e,message:a.text,wrap:a.wrap??A(),answer:r})},"addMessage"),x=(0,n.eW)(function(t,e,a,r,i=!1){if(r===D.ACTIVE_END){let e=g(t??"");if(e<1){let e=Error("Trying to inactivate an inactive participant ("+t+")");throw e.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},e}}return d.records.messages.push({from:t,to:e,message:a?.text??"",wrap:a?.wrap??A(),type:r,activate:i}),!0},"addSignal"),y=(0,n.eW)(function(){return d.records.boxes.length>0},"hasAtLeastOneBox"),m=(0,n.eW)(function(){return d.records.boxes.some(t=>t.name)},"hasAtLeastOneBoxWithTitle"),b=(0,n.eW)(function(){return d.records.messages},"getMessages"),f=(0,n.eW)(function(){return d.records.boxes},"getBoxes"),T=(0,n.eW)(function(){return d.records.actors},"getActors"),E=(0,n.eW)(function(){return d.records.createdActors},"getCreatedActors"),w=(0,n.eW)(function(){return d.records.destroyedActors},"getDestroyedActors"),I=(0,n.eW)(function(t){return d.records.actors.get(t)},"getActor"),L=(0,n.eW)(function(){return[...d.records.actors.keys()]},"getActorKeys"),_=(0,n.eW)(function(){d.records.sequenceNumbersEnabled=!0},"enableSequenceNumbers"),P=(0,n.eW)(function(){d.records.sequenceNumbersEnabled=!1},"disableSequenceNumbers"),k=(0,n.eW)(()=>d.records.sequenceNumbersEnabled,"showSequenceNumbers"),N=(0,n.eW)(function(t){d.records.wrapEnabled=t},"setWrap"),M=(0,n.eW)(t=>{if(void 0===t)return{};t=t.trim();let e=null!==/^:?wrap:/.exec(t)||null===/^:?nowrap:/.exec(t)&&void 0,a=(void 0===e?t:t.replace(/^:?(?:no)?wrap:/,"")).trim();return{cleanedText:a,wrap:e}},"extractWrap"),A=(0,n.eW)(()=>void 0!==d.records.wrapEnabled?d.records.wrapEnabled:n.nV().sequence?.wrap??!1,"autoWrap"),v=(0,n.eW)(function(){d.reset(),(0,n.ZH)()},"clear"),S=(0,n.eW)(function(t){let e=t.trim(),{wrap:a,cleanedText:r}=M(e),i={text:r,wrap:a};return n.cM.debug(`parseMessage: ${JSON.stringify(i)}`),i},"parseMessage"),O=(0,n.eW)(function(t){let e=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t),a=e?.[1]?e[1].trim():"transparent",r=e?.[2]?e[2].trim():void 0;if(window?.CSS)window.CSS.supports("color",a)||(a="transparent",r=t.trim());else{let e=new Option().style;e.color=a,e.color!==a&&(a="transparent",r=t.trim())}let{wrap:i,cleanedText:s}=M(r);return{text:s?(0,n.oO)(s,(0,n.nV)()):void 0,color:a,wrap:i}},"parseBoxData"),D={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},W=(0,n.eW)(function(t,e,a){let r={actor:t,placement:e,message:a.text,wrap:a.wrap??A()},i=[].concat(t,t);d.records.notes.push(r),d.records.messages.push({from:i[0],to:i[1],message:a.text,wrap:a.wrap??A(),type:D.NOTE,placement:e})},"addNote"),R=(0,n.eW)(function(t,e){let a=I(t);try{let t=(0,n.oO)(e.text,(0,n.nV)());t=(t=t.replace(/&amp;/g,"&")).replace(/&equals;/g,"=");let r=JSON.parse(t);C(a,r)}catch(t){n.cM.error("error while parsing actor link text",t)}},"addLinks"),Y=(0,n.eW)(function(t,e){let a=I(t);try{let t={},r=(0,n.oO)(e.text,(0,n.nV)()),i=r.indexOf("@");r=(r=r.replace(/&amp;/g,"&")).replace(/&equals;/g,"=");let s=r.slice(0,i-1).trim(),o=r.slice(i+1).trim();t[s]=o,C(a,t)}catch(t){n.cM.error("error while parsing actor link text",t)}},"addALink");function C(t,e){if(null==t.links)t.links=e;else for(let a in e)t.links[a]=e[a]}(0,n.eW)(C,"insertLinks");var B=(0,n.eW)(function(t,e){let a=I(t);try{let t=(0,n.oO)(e.text,(0,n.nV)()),r=JSON.parse(t);$(a,r)}catch(t){n.cM.error("error while parsing actor properties text",t)}},"addProperties");function $(t,e){if(null==t.properties)t.properties=e;else for(let a in e)t.properties[a]=e[a]}function V(){d.records.currentBox=void 0}(0,n.eW)($,"insertProperties"),(0,n.eW)(V,"boxEnd");var F=(0,n.eW)(function(t,e){let a=I(t),r=document.getElementById(e.text);try{let t=r.innerHTML,e=JSON.parse(t);e.properties&&$(a,e.properties),e.links&&C(a,e.links)}catch(t){n.cM.error("error while parsing actor details text",t)}},"addDetails"),q=(0,n.eW)(function(t,e){if(t?.properties!==void 0)return t.properties[e]},"getActorProperty"),z=(0,n.eW)(function(t){if(Array.isArray(t))t.forEach(function(t){z(t)});else switch(t.type){case"sequenceIndex":d.records.messages.push({from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":p(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(d.records.actors.has(t.actor))throw Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");d.records.lastCreated=t.actor,p(t.actor,t.actor,t.description,t.draw),d.records.createdActors.set(t.actor,d.records.messages.length);break;case"destroyParticipant":d.records.lastDestroyed=t.actor,d.records.destroyedActors.set(t.actor,d.records.messages.length);break;case"activeStart":case"activeEnd":x(t.actor,void 0,void 0,t.signalType);break;case"addNote":W(t.actor,t.placement,t.text);break;case"addLinks":R(t.actor,t.text);break;case"addALink":Y(t.actor,t.text);break;case"addProperties":B(t.actor,t.text);break;case"addDetails":F(t.actor,t.text);break;case"addMessage":if(d.records.lastCreated){if(t.to!==d.records.lastCreated)throw Error("The created participant "+d.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");d.records.lastCreated=void 0}else if(d.records.lastDestroyed){if(t.to!==d.records.lastDestroyed&&t.from!==d.records.lastDestroyed)throw Error("The destroyed participant "+d.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");d.records.lastDestroyed=void 0}x(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":h(t.boxData);break;case"boxEnd":V();break;case"loopStart":x(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":case"rectEnd":case"optEnd":case"altEnd":case"parEnd":case"criticalEnd":case"breakEnd":x(void 0,void 0,void 0,t.signalType);break;case"rectStart":x(void 0,void 0,t.color,t.signalType);break;case"optStart":x(void 0,void 0,t.optText,t.signalType);break;case"altStart":case"else":x(void 0,void 0,t.altText,t.signalType);break;case"setAccTitle":(0,n.GN)(t.text);break;case"parStart":case"and":x(void 0,void 0,t.parText,t.signalType);break;case"criticalStart":x(void 0,void 0,t.criticalText,t.signalType);break;case"option":x(void 0,void 0,t.optionText,t.signalType);break;case"breakStart":x(void 0,void 0,t.breakText,t.signalType)}},"apply"),H={addActor:p,addMessage:u,addSignal:x,addLinks:R,addDetails:F,addProperties:B,autoWrap:A,setWrap:N,enableSequenceNumbers:_,disableSequenceNumbers:P,showSequenceNumbers:k,getMessages:b,getActors:T,getCreatedActors:E,getDestroyedActors:w,getActor:I,getActorKeys:L,getActorProperty:q,getAccTitle:n.eu,getBoxes:f,getDiagramTitle:n.Kr,setDiagramTitle:n.g2,getConfig:(0,n.eW)(()=>(0,n.nV)().sequence,"getConfig"),clear:v,parseMessage:S,parseBoxData:O,LINETYPE:D,ARROWTYPE:{FILLED:0,OPEN:1},PLACEMENT:{LEFTOF:0,RIGHTOF:1,OVER:2},addNote:W,setAccTitle:n.GN,apply:z,setAccDescription:n.U$,getAccDescription:n.Mx,hasAtLeastOneBox:y,hasAtLeastOneBoxWithTitle:m},j=(0,n.eW)(t=>`.actor {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }

  text.actor > tspan {
    fill: ${t.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${t.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${t.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${t.signalColor};
  }

  #arrowhead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .sequenceNumber {
    fill: ${t.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${t.signalColor};
  }

  #crosshead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .messageText {
    fill: ${t.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${t.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${t.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${t.noteBorderColor};
    fill: ${t.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${t.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation1 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation2 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${t.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),U="actor-top",K="actor-bottom",G="actor-man",X=(0,n.eW)(function(t,e){return(0,r.Mu)(t,e)},"drawRect"),J=(0,n.eW)(function(t,e,a,r,i){if(void 0===e.links||null===e.links||0===Object.keys(e.links).length)return{height:0,width:0};let s=e.links,n=e.actorCnt,o=e.rectData;var l="none";i&&(l="block !important");let d=t.append("g");d.attr("id","actor"+n+"_popup"),d.attr("class","actorPopupMenu"),d.attr("display",l);var h="";void 0!==o.class&&(h=" "+o.class);let p=o.width>a?o.width:a,g=d.append("rect");if(g.attr("class","actorPopupMenuPanel"+h),g.attr("x",o.x),g.attr("y",o.height),g.attr("fill",o.fill),g.attr("stroke",o.stroke),g.attr("width",p),g.attr("height",o.height),g.attr("rx",o.rx),g.attr("ry",o.ry),null!=s){var u=20;for(let t in s){var x=d.append("a"),y=(0,c.N)(s[t]);x.attr("xlink:href",y),x.attr("target","_blank"),tw(r)(t,x,o.x+10,o.height+u,p,20,{class:"actor"},r),u+=30}}return g.attr("height",u),{height:o.height+u,width:p}},"drawPopup"),Z=(0,n.eW)(function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),Q=(0,n.eW)(async function(t,e,a=null){let r=t.append("foreignObject"),i=await (0,n.uT)(e.text,(0,n.iE)()),s=r.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(i),o=s.node().getBoundingClientRect();if(r.attr("height",Math.round(o.height)).attr("width",Math.round(o.width)),"noteText"===e.class){let a=t.node().firstChild;a.setAttribute("height",o.height+2*e.textMargin);let i=a.getBBox();r.attr("x",Math.round(i.x+i.width/2-o.width/2)).attr("y",Math.round(i.y+i.height/2-o.height/2))}else if(a){let{startx:t,stopx:i,starty:s}=a;if(t>i){let e=t;t=i,i=e}r.attr("x",Math.round(t+Math.abs(t-i)/2-o.width/2)),"loopText"===e.class?r.attr("y",Math.round(s)):r.attr("y",Math.round(s-o.height))}return[r]},"drawKatex"),tt=(0,n.eW)(function(t,e){let a=0,r=0,i=e.text.split(n.SY.lineBreakRegex),[o,c]=(0,s.VG)(e.fontSize),l=[],d=0,h=(0,n.eW)(()=>e.y,"yfunc");if(void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0)switch(e.valign){case"top":case"start":h=(0,n.eW)(()=>Math.round(e.y+e.textMargin),"yfunc");break;case"middle":case"center":h=(0,n.eW)(()=>Math.round(e.y+(a+r+e.textMargin)/2),"yfunc");break;case"bottom":case"end":h=(0,n.eW)(()=>Math.round(e.y+(a+r+2*e.textMargin)-e.textMargin),"yfunc")}if(void 0!==e.anchor&&void 0!==e.textMargin&&void 0!==e.width)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle"}for(let[n,p]of i.entries()){void 0!==e.textMargin&&0===e.textMargin&&void 0!==o&&(d=n*o);let i=t.append("text");i.attr("x",e.x),i.attr("y",h()),void 0!==e.anchor&&i.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),void 0!==e.fontFamily&&i.style("font-family",e.fontFamily),void 0!==c&&i.style("font-size",c),void 0!==e.fontWeight&&i.style("font-weight",e.fontWeight),void 0!==e.fill&&i.attr("fill",e.fill),void 0!==e.class&&i.attr("class",e.class),void 0!==e.dy?i.attr("dy",e.dy):0!==d&&i.attr("dy",d);let g=p||s.$m;if(e.tspan){let t=i.append("tspan");t.attr("x",e.x),void 0!==e.fill&&t.attr("fill",e.fill),t.text(g)}else i.text(g);void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0&&(r+=(i._groups||i)[0][0].getBBox().height,a=r),l.push(i)}return l},"drawText"),te=(0,n.eW)(function(t,e){function a(t,e,a,r,i){return t+","+e+" "+(t+a)+","+e+" "+(t+a)+","+(e+r-i)+" "+(t+a-1.2*i)+","+(e+r)+" "+t+","+(e+r)}(0,n.eW)(a,"genPoints");let r=t.append("polygon");return r.attr("points",a(e.x,e.y,e.width,e.height,7)),r.attr("class","labelBox"),e.y=e.y+e.height/2,tt(t,e),r},"drawLabel"),ta=-1,tr=(0,n.eW)((t,e,a,r)=>{t.select&&a.forEach(a=>{let i=e.get(a),s=t.select("#actor"+i.actorCnt);!r.mirrorActors&&i.stopy?s.attr("y2",i.stopy+i.height/2):r.mirrorActors&&s.attr("y2",i.stopy)})},"fixLifeLineHeights"),ti=(0,n.eW)(function(t,e,a,i){let s=i?e.stopy:e.starty,o=e.x+e.width/2,c=s+e.height,l=t.append("g").lower();var d=l;i||(ta++,Object.keys(e.links||{}).length&&!a.forceMenus&&d.attr("onclick",Z(`actor${ta}_popup`)).attr("cursor","pointer"),d.append("line").attr("id","actor"+ta).attr("x1",o).attr("y1",c).attr("x2",o).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),d=l.append("g"),e.actorCnt=ta,null!=e.links&&d.attr("id","root-"+ta));let h=(0,r.kc)();var p="actor";e.properties?.class?p=e.properties.class:h.fill="#eaeaea",i?p+=` ${K}`:p+=` ${U}`,h.x=e.x,h.y=s,h.width=e.width,h.height=e.height,h.class=p,h.rx=3,h.ry=3,h.name=e.name;let g=X(d,h);if(e.rectData=h,e.properties?.icon){let t=e.properties.icon.trim();"@"===t.charAt(0)?(0,r.rB)(d,h.x+h.width-20,h.y+10,t.substr(1)):(0,r.AE)(d,h.x+h.width-20,h.y+10,t)}tE(a,(0,n.l0)(e.description))(e.description,d,h.x,h.y,h.width,h.height,{class:"actor actor-box"},a);let u=e.height;if(g.node){let t=g.node().getBBox();e.height=t.height,u=t.height}return u},"drawActorTypeParticipant"),ts=(0,n.eW)(function(t,e,a,i){let s=i?e.stopy:e.starty,o=e.x+e.width/2,c=t.append("g").lower();i||(ta++,c.append("line").attr("id","actor"+ta).attr("x1",o).attr("y1",s+80).attr("x2",o).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),e.actorCnt=ta);let l=t.append("g"),d=G;i?d+=` ${K}`:d+=` ${U}`,l.attr("class",d),l.attr("name",e.name);let h=(0,r.kc)();h.x=e.x,h.y=s,h.fill="#eaeaea",h.width=e.width,h.height=e.height,h.class="actor",h.rx=3,h.ry=3,l.append("line").attr("id","actor-man-torso"+ta).attr("x1",o).attr("y1",s+25).attr("x2",o).attr("y2",s+45),l.append("line").attr("id","actor-man-arms"+ta).attr("x1",o-18).attr("y1",s+33).attr("x2",o+18).attr("y2",s+33),l.append("line").attr("x1",o-18).attr("y1",s+60).attr("x2",o).attr("y2",s+45),l.append("line").attr("x1",o).attr("y1",s+45).attr("x2",o+18-2).attr("y2",s+60);let p=l.append("circle");p.attr("cx",e.x+e.width/2),p.attr("cy",s+10),p.attr("r",15),p.attr("width",e.width),p.attr("height",e.height);let g=l.node().getBBox();return e.height=g.height,tE(a,(0,n.l0)(e.description))(e.description,l,h.x,h.y+35,h.width,h.height,{class:`actor ${G}`},a),e.height},"drawActorTypeActor"),tn=(0,n.eW)(async function(t,e,a,r){switch(e.type){case"actor":return await ts(t,e,a,r);case"participant":return await ti(t,e,a,r)}},"drawActor"),to=(0,n.eW)(function(t,e,a){let r=t.append("g");th(r,e),e.name&&tE(a)(e.name,r,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},a),r.lower()},"drawBox"),tc=(0,n.eW)(function(t){return t.append("g")},"anchorElement"),tl=(0,n.eW)(function(t,e,a,i,s){let n=(0,r.kc)(),o=e.anchored;n.x=e.startx,n.y=e.starty,n.class="activation"+s%3,n.width=e.stopx-e.startx,n.height=a-e.starty,X(o,n)},"drawActivation"),td=(0,n.eW)(async function(t,e,a,i){let{boxMargin:s,boxTextMargin:o,labelBoxHeight:c,labelBoxWidth:l,messageFontFamily:d,messageFontSize:h,messageFontWeight:p}=i,g=t.append("g"),u=(0,n.eW)(function(t,e,a,r){return g.append("line").attr("x1",t).attr("y1",e).attr("x2",a).attr("y2",r).attr("class","loopLine")},"drawLoopLine");u(e.startx,e.starty,e.stopx,e.starty),u(e.stopx,e.starty,e.stopx,e.stopy),u(e.startx,e.stopy,e.stopx,e.stopy),u(e.startx,e.starty,e.startx,e.stopy),void 0!==e.sections&&e.sections.forEach(function(t){u(e.startx,t.y,e.stopx,t.y).style("stroke-dasharray","3, 3")});let x=(0,r.AD)();x.text=a,x.x=e.startx,x.y=e.starty,x.fontFamily=d,x.fontSize=h,x.fontWeight=p,x.anchor="middle",x.valign="middle",x.tspan=!1,x.width=l||50,x.height=c||20,x.textMargin=o,x.class="labelText",te(g,x),(x=tf()).text=e.title,x.x=e.startx+l/2+(e.stopx-e.startx)/2,x.y=e.starty+s+o,x.anchor="middle",x.valign="middle",x.textMargin=o,x.class="loopText",x.fontFamily=d,x.fontSize=h,x.fontWeight=p,x.wrap=!0;let y=(0,n.l0)(x.text)?await Q(g,x,e):tt(g,x);if(void 0!==e.sectionTitles){for(let[t,a]of Object.entries(e.sectionTitles))if(a.message){x.text=a.message,x.x=e.startx+(e.stopx-e.startx)/2,x.y=e.sections[t].y+s+o,x.class="loopText",x.anchor="middle",x.valign="middle",x.tspan=!1,x.fontFamily=d,x.fontSize=h,x.fontWeight=p,x.wrap=e.wrap,(0,n.l0)(x.text)?(e.starty=e.sections[t].y,await Q(g,x,e)):tt(g,x);let r=Math.round(y.map(t=>(t._groups||t)[0][0].getBBox().height).reduce((t,e)=>t+e));e.sections[t].height+=r-(s+o)}}return e.height=Math.round(e.stopy-e.starty),g},"drawLoop"),th=(0,n.eW)(function(t,e){(0,r.O)(t,e)},"drawBackgroundRect"),tp=(0,n.eW)(function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),tg=(0,n.eW)(function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),tu=(0,n.eW)(function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),tx=(0,n.eW)(function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),ty=(0,n.eW)(function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),tm=(0,n.eW)(function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),tb=(0,n.eW)(function(t){let e=t.append("defs"),a=e.append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5);a.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),tf=(0,n.eW)(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),tT=(0,n.eW)(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),tE=function(){function t(t,e,a,r,s,n,o){let c=e.append("text").attr("x",a+s/2).attr("y",r+n/2+5).style("text-anchor","middle").text(t);i(c,o)}function e(t,e,a,r,o,c,l,d){let{actorFontSize:h,actorFontFamily:p,actorFontWeight:g}=d,[u,x]=(0,s.VG)(h),y=t.split(n.SY.lineBreakRegex);for(let t=0;t<y.length;t++){let s=t*u-u*(y.length-1)/2,n=e.append("text").attr("x",a+o/2).attr("y",r).style("text-anchor","middle").style("font-size",x).style("font-weight",g).style("font-family",p);n.append("tspan").attr("x",a+o/2).attr("dy",s).text(y[t]),n.attr("y",r+c/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),i(n,l)}}function a(t,a,r,s,n,o,c,l){let d=a.append("switch"),h=d.append("foreignObject").attr("x",r).attr("y",s).attr("width",n).attr("height",o),p=h.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");p.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,d,r,s,n,o,c,l),i(p,c)}async function r(t,a,r,s,o,c,l,d){let h=await (0,n.nH)(t,(0,n.iE)()),p=a.append("switch"),g=p.append("foreignObject").attr("x",r+o/2-h.width/2).attr("y",s+c/2-h.height/2).attr("width",h.width).attr("height",h.height),u=g.append("xhtml:div").style("height","100%").style("width","100%");u.append("div").style("text-align","center").style("vertical-align","middle").html(await (0,n.uT)(t,(0,n.iE)())),e(t,p,r,s,o,c,l,d),i(u,l)}function i(t,e){for(let a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return(0,n.eW)(t,"byText"),(0,n.eW)(e,"byTspan"),(0,n.eW)(a,"byFo"),(0,n.eW)(r,"byKatex"),(0,n.eW)(i,"_setTextAttrs"),function(i,s=!1){return s?r:"fo"===i.textPlacement?a:"old"===i.textPlacement?t:e}}(),tw=function(){function t(t,e,a,i,s,n,o){let c=e.append("text").attr("x",a).attr("y",i).style("text-anchor","start").text(t);r(c,o)}function e(t,e,a,i,s,o,c,l){let{actorFontSize:d,actorFontFamily:h,actorFontWeight:p}=l,g=t.split(n.SY.lineBreakRegex);for(let t=0;t<g.length;t++){let s=t*d-d*(g.length-1)/2,n=e.append("text").attr("x",a).attr("y",i).style("text-anchor","start").style("font-size",d).style("font-weight",p).style("font-family",h);n.append("tspan").attr("x",a).attr("dy",s).text(g[t]),n.attr("y",i+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),r(n,c)}}function a(t,a,i,s,n,o,c,l){let d=a.append("switch"),h=d.append("foreignObject").attr("x",i).attr("y",s).attr("width",n).attr("height",o),p=h.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");p.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,d,i,s,n,o,c,l),r(p,c)}function r(t,e){for(let a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return(0,n.eW)(t,"byText"),(0,n.eW)(e,"byTspan"),(0,n.eW)(a,"byFo"),(0,n.eW)(r,"_setTextAttrs"),function(r){return"fo"===r.textPlacement?a:"old"===r.textPlacement?t:e}}(),tI={drawRect:X,drawText:tt,drawLabel:te,drawActor:tn,drawBox:to,drawPopup:J,anchorElement:tc,drawActivation:tl,drawLoop:td,drawBackgroundRect:th,insertArrowHead:tx,insertArrowFilledHead:ty,insertSequenceNumber:tm,insertArrowCrossHead:tb,insertDatabaseIcon:tp,insertComputerIcon:tg,insertClockIcon:tu,getTextObj:tf,getNoteRect:tT,fixLifeLineHeights:tr,sanitizeUrl:c.N},tL={},t_={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:(0,n.eW)(function(){return Math.max.apply(null,0===this.actors.length?[0]:this.actors.map(t=>t.height||0))+(0===this.loops.length?0:this.loops.map(t=>t.height||0).reduce((t,e)=>t+e))+(0===this.messages.length?0:this.messages.map(t=>t.height||0).reduce((t,e)=>t+e))+(0===this.notes.length?0:this.notes.map(t=>t.height||0).reduce((t,e)=>t+e))},"getHeight"),clear:(0,n.eW)(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:(0,n.eW)(function(t){this.boxes.push(t)},"addBox"),addActor:(0,n.eW)(function(t){this.actors.push(t)},"addActor"),addLoop:(0,n.eW)(function(t){this.loops.push(t)},"addLoop"),addMessage:(0,n.eW)(function(t){this.messages.push(t)},"addMessage"),addNote:(0,n.eW)(function(t){this.notes.push(t)},"addNote"),lastActor:(0,n.eW)(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:(0,n.eW)(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:(0,n.eW)(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:(0,n.eW)(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:(0,n.eW)(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,tW((0,n.nV)())},"init"),updateVal:(0,n.eW)(function(t,e,a,r){void 0===t[e]?t[e]=a:t[e]=r(a,t[e])},"updateVal"),updateBounds:(0,n.eW)(function(t,e,a,r){let i=this,s=0;function o(o){return(0,n.eW)(function(n){s++;let c=i.sequenceItems.length-s+1;i.updateVal(n,"starty",e-c*tL.boxMargin,Math.min),i.updateVal(n,"stopy",r+c*tL.boxMargin,Math.max),i.updateVal(t_.data,"startx",t-c*tL.boxMargin,Math.min),i.updateVal(t_.data,"stopx",a+c*tL.boxMargin,Math.max),"activation"!==o&&(i.updateVal(n,"startx",t-c*tL.boxMargin,Math.min),i.updateVal(n,"stopx",a+c*tL.boxMargin,Math.max),i.updateVal(t_.data,"starty",e-c*tL.boxMargin,Math.min),i.updateVal(t_.data,"stopy",r+c*tL.boxMargin,Math.max))},"updateItemBounds")}(0,n.eW)(o,"updateFn"),this.sequenceItems.forEach(o()),this.activations.forEach(o("activation"))},"updateBounds"),insert:(0,n.eW)(function(t,e,a,r){let i=n.SY.getMin(t,a),s=n.SY.getMax(t,a),o=n.SY.getMin(e,r),c=n.SY.getMax(e,r);this.updateVal(t_.data,"startx",i,Math.min),this.updateVal(t_.data,"starty",o,Math.min),this.updateVal(t_.data,"stopx",s,Math.max),this.updateVal(t_.data,"stopy",c,Math.max),this.updateBounds(i,o,s,c)},"insert"),newActivation:(0,n.eW)(function(t,e,a){let r=a.get(t.from),i=tR(t.from).length||0,s=r.x+r.width/2+(i-1)*tL.activationWidth/2;this.activations.push({startx:s,starty:this.verticalPos+2,stopx:s+tL.activationWidth,stopy:void 0,actor:t.from,anchored:tI.anchorElement(e)})},"newActivation"),endActivation:(0,n.eW)(function(t){let e=this.activations.map(function(t){return t.actor}).lastIndexOf(t.from);return this.activations.splice(e,1)[0]},"endActivation"),createLoop:(0,n.eW)(function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},"createLoop"),newLoop:(0,n.eW)(function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},"newLoop"),endLoop:(0,n.eW)(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:(0,n.eW)(function(){return!!this.sequenceItems.length&&this.sequenceItems[this.sequenceItems.length-1].overlap},"isLoopOverlap"),addSectionToLoop:(0,n.eW)(function(t){let e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:t_.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},"addSectionToLoop"),saveVerticalPos:(0,n.eW)(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:(0,n.eW)(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:(0,n.eW)(function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=n.SY.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:(0,n.eW)(function(){return this.verticalPos},"getVerticalPos"),getBounds:(0,n.eW)(function(){return{bounds:this.data,models:this.models}},"getBounds")},tP=(0,n.eW)(async function(t,e){t_.bumpVerticalPos(tL.boxMargin),e.height=tL.boxMargin,e.starty=t_.getVerticalPos();let a=(0,r.kc)();a.x=e.startx,a.y=e.starty,a.width=e.width||tL.width,a.class="note";let i=t.append("g"),s=tI.drawRect(i,a),o=(0,r.AD)();o.x=e.startx,o.y=e.starty,o.width=a.width,o.dy="1em",o.text=e.message,o.class="noteText",o.fontFamily=tL.noteFontFamily,o.fontSize=tL.noteFontSize,o.fontWeight=tL.noteFontWeight,o.anchor=tL.noteAlign,o.textMargin=tL.noteMargin,o.valign="center";let c=(0,n.l0)(o.text)?await Q(i,o):tt(i,o),l=Math.round(c.map(t=>(t._groups||t)[0][0].getBBox().height).reduce((t,e)=>t+e));s.attr("height",l+2*tL.noteMargin),e.height+=l+2*tL.noteMargin,t_.bumpVerticalPos(l+2*tL.noteMargin),e.stopy=e.starty+l+2*tL.noteMargin,e.stopx=e.startx+a.width,t_.insert(e.startx,e.starty,e.stopx,e.stopy),t_.models.addNote(e)},"drawNote"),tk=(0,n.eW)(t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),"messageFont"),tN=(0,n.eW)(t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),"noteFont"),tM=(0,n.eW)(t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight}),"actorFont");async function tA(t,e){let a;t_.bumpVerticalPos(10);let{startx:r,stopx:i,message:o}=e,c=n.SY.splitBreaks(o).length,l=(0,n.l0)(o),d=l?await (0,n.nH)(o,(0,n.nV)()):s.w8.calculateTextDimensions(o,tk(tL));if(!l){let t=d.height/c;e.height+=t,t_.bumpVerticalPos(t)}let h=d.height-10,p=d.width;if(r===i){a=t_.getVerticalPos()+h,tL.rightAngles||(h+=tL.boxMargin,a=t_.getVerticalPos()+h),h+=30;let t=n.SY.getMax(p/2,tL.width/2);t_.insert(r-t,t_.getVerticalPos()-10+h,i+t,t_.getVerticalPos()+30+h)}else h+=tL.boxMargin,a=t_.getVerticalPos()+h,t_.insert(r,a-10,i,a);return t_.bumpVerticalPos(h),e.height+=h,e.stopy=e.starty+e.height,t_.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),a}(0,n.eW)(tA,"boundMessage");var tv=(0,n.eW)(async function(t,e,a,i){let o;let{startx:c,stopx:l,starty:d,message:h,type:p,sequenceIndex:g,sequenceVisible:u}=e,x=s.w8.calculateTextDimensions(h,tk(tL)),y=(0,r.AD)();y.x=c,y.y=d+10,y.width=l-c,y.class="messageText",y.dy="1em",y.text=h,y.fontFamily=tL.messageFontFamily,y.fontSize=tL.messageFontSize,y.fontWeight=tL.messageFontWeight,y.anchor=tL.messageAlign,y.valign="center",y.textMargin=tL.wrapPadding,y.tspan=!1,(0,n.l0)(y.text)?await Q(t,y,{startx:c,stopx:l,starty:a}):tt(t,y);let m=x.width;c===l?o=tL.rightAngles?t.append("path").attr("d",`M  ${c},${a} H ${c+n.SY.getMax(tL.width/2,m/2)} V ${a+25} H ${c}`):t.append("path").attr("d","M "+c+","+a+" C "+(c+60)+","+(a-10)+" "+(c+60)+","+(a+30)+" "+c+","+(a+20)):((o=t.append("line")).attr("x1",c),o.attr("y1",a),o.attr("x2",l),o.attr("y2",a)),p===i.db.LINETYPE.DOTTED||p===i.db.LINETYPE.DOTTED_CROSS||p===i.db.LINETYPE.DOTTED_POINT||p===i.db.LINETYPE.DOTTED_OPEN||p===i.db.LINETYPE.BIDIRECTIONAL_DOTTED?(o.style("stroke-dasharray","3, 3"),o.attr("class","messageLine1")):o.attr("class","messageLine0");let b="";tL.arrowMarkerAbsolute&&(b=(b=(b=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search).replace(/\(/g,"\\(")).replace(/\)/g,"\\)")),o.attr("stroke-width",2),o.attr("stroke","none"),o.style("fill","none"),(p===i.db.LINETYPE.SOLID||p===i.db.LINETYPE.DOTTED)&&o.attr("marker-end","url("+b+"#arrowhead)"),(p===i.db.LINETYPE.BIDIRECTIONAL_SOLID||p===i.db.LINETYPE.BIDIRECTIONAL_DOTTED)&&(o.attr("marker-start","url("+b+"#arrowhead)"),o.attr("marker-end","url("+b+"#arrowhead)")),(p===i.db.LINETYPE.SOLID_POINT||p===i.db.LINETYPE.DOTTED_POINT)&&o.attr("marker-end","url("+b+"#filled-head)"),(p===i.db.LINETYPE.SOLID_CROSS||p===i.db.LINETYPE.DOTTED_CROSS)&&o.attr("marker-end","url("+b+"#crosshead)"),(u||tL.showSequenceNumbers)&&(o.attr("marker-start","url("+b+"#sequencenumber)"),t.append("text").attr("x",c).attr("y",a+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(g))},"drawMessage"),tS=(0,n.eW)(function(t,e,a,r,i,s,o){let c,l=0,d=0,h=0;for(let t of r){let r=e.get(t),s=r.box;c&&c!=s&&(o||t_.models.addBox(c),d+=tL.boxMargin+c.margin),s&&s!=c&&(o||(s.x=l+d,s.y=i),d+=s.margin),r.width=r.width||tL.width,r.height=n.SY.getMax(r.height||tL.height,tL.height),r.margin=r.margin||tL.actorMargin,h=n.SY.getMax(h,r.height),a.get(r.name)&&(d+=r.width/2),r.x=l+d,r.starty=t_.getVerticalPos(),t_.insert(r.x,i,r.x+r.width,r.height),l+=r.width+d,r.box&&(r.box.width=l+s.margin-r.box.x),d=r.margin,c=r.box,t_.models.addActor(r)}c&&!o&&t_.models.addBox(c),t_.bumpVerticalPos(h)},"addActorRenderingData"),tO=(0,n.eW)(async function(t,e,a,r){if(r){let r=0;for(let i of(t_.bumpVerticalPos(2*tL.boxMargin),a)){let a=e.get(i);a.stopy||(a.stopy=t_.getVerticalPos());let s=await tI.drawActor(t,a,tL,!0);r=n.SY.getMax(r,s)}t_.bumpVerticalPos(r+tL.boxMargin)}else for(let r of a){let a=e.get(r);await tI.drawActor(t,a,tL,!1)}},"drawActors"),tD=(0,n.eW)(function(t,e,a,r){let i=0,s=0;for(let n of a){let a=e.get(n),o=tF(a),c=tI.drawPopup(t,a,o,tL,tL.forceMenus,r);c.height>i&&(i=c.height),c.width+a.x>s&&(s=c.width+a.x)}return{maxHeight:i,maxWidth:s}},"drawActorsPopup"),tW=(0,n.eW)(function(t){(0,n.Yc)(tL,t),t.fontFamily&&(tL.actorFontFamily=tL.noteFontFamily=tL.messageFontFamily=t.fontFamily),t.fontSize&&(tL.actorFontSize=tL.noteFontSize=tL.messageFontSize=t.fontSize),t.fontWeight&&(tL.actorFontWeight=tL.noteFontWeight=tL.messageFontWeight=t.fontWeight)},"setConf"),tR=(0,n.eW)(function(t){return t_.activations.filter(function(e){return e.actor===t})},"actorActivations"),tY=(0,n.eW)(function(t,e){let a=e.get(t),r=tR(t),i=r.reduce(function(t,e){return n.SY.getMin(t,e.startx)},a.x+a.width/2-1),s=r.reduce(function(t,e){return n.SY.getMax(t,e.stopx)},a.x+a.width/2+1);return[i,s]},"activationBounds");function tC(t,e,a,r,i){t_.bumpVerticalPos(a);let o=r;if(e.id&&e.message&&t[e.id]){let a=t[e.id].width,i=tk(tL);e.message=s.w8.wrapLabel(`[${e.message}]`,a-2*tL.wrapPadding,i),e.width=a,e.wrap=!0;let c=s.w8.calculateTextDimensions(e.message,i),l=n.SY.getMax(c.height,tL.labelBoxHeight);o=r+l,n.cM.debug(`${l} - ${e.message}`)}i(e),t_.bumpVerticalPos(o)}function tB(t,e,a,r,i,s,o){function c(a,r){a.x<i.get(t.from).x?(t_.insert(e.stopx-r,e.starty,e.startx,e.stopy+a.height/2+tL.noteMargin),e.stopx=e.stopx+r):(t_.insert(e.startx,e.starty,e.stopx+r,e.stopy+a.height/2+tL.noteMargin),e.stopx=e.stopx-r)}function l(a,r){a.x<i.get(t.to).x?(t_.insert(e.startx-r,e.starty,e.stopx,e.stopy+a.height/2+tL.noteMargin),e.startx=e.startx+r):(t_.insert(e.stopx,e.starty,e.startx+r,e.stopy+a.height/2+tL.noteMargin),e.startx=e.startx-r)}if((0,n.eW)(c,"receiverAdjustment"),(0,n.eW)(l,"senderAdjustment"),s.get(t.to)==r){let e=i.get(t.to),r="actor"==e.type?21:e.width/2+3;c(e,r),e.starty=a-e.height/2,t_.bumpVerticalPos(e.height/2)}else if(o.get(t.from)==r){let e=i.get(t.from);if(tL.mirrorActors){let t="actor"==e.type?18:e.width/2;l(e,t)}e.stopy=a-e.height/2,t_.bumpVerticalPos(e.height/2)}else if(o.get(t.to)==r){let e=i.get(t.to);if(tL.mirrorActors){let t="actor"==e.type?21:e.width/2+3;c(e,t)}e.stopy=a-e.height/2,t_.bumpVerticalPos(e.height/2)}}(0,n.eW)(tC,"adjustLoopHeightForWrap"),(0,n.eW)(tB,"adjustCreatedDestroyedData");var t$=(0,n.eW)(async function(t,e,a,r){let i;let{securityLevel:s,sequence:c}=(0,n.nV)();tL=c,"sandbox"===s&&(i=(0,o.Ys)("#i"+e));let l="sandbox"===s?(0,o.Ys)(i.nodes()[0].contentDocument.body):(0,o.Ys)("body"),d="sandbox"===s?i.nodes()[0].contentDocument:document;t_.init(),n.cM.debug(r.db);let h="sandbox"===s?l.select(`[id="${e}"]`):(0,o.Ys)(`[id="${e}"]`),p=r.db.getActors(),g=r.db.getCreatedActors(),u=r.db.getDestroyedActors(),x=r.db.getBoxes(),y=r.db.getActorKeys(),m=r.db.getMessages(),b=r.db.getDiagramTitle(),f=r.db.hasAtLeastOneBox(),T=r.db.hasAtLeastOneBoxWithTitle(),E=await tV(p,m,r);if(tL.height=await tq(p,E,x),tI.insertComputerIcon(h),tI.insertDatabaseIcon(h),tI.insertClockIcon(h),f&&(t_.bumpVerticalPos(tL.boxMargin),T&&t_.bumpVerticalPos(x[0].textMaxHeight)),!0===tL.hideUnusedParticipants){let t=new Set;m.forEach(e=>{t.add(e.from),t.add(e.to)}),y=y.filter(e=>t.has(e))}tS(h,p,g,y,0,m,!1);let w=await tj(m,p,E,r);function I(t,e){let a=t_.endActivation(t);a.starty+18>e&&(a.starty=e-6,e+=12),tI.drawActivation(h,a,e,tL,tR(t.from).length),t_.insert(a.startx,e-10,a.stopx,e)}tI.insertArrowHead(h),tI.insertArrowCrossHead(h),tI.insertArrowFilledHead(h),tI.insertSequenceNumber(h),(0,n.eW)(I,"activeEnd");let L=1,_=1,P=[],k=[],N=0;for(let t of m){let e,a,i;switch(t.type){case r.db.LINETYPE.NOTE:t_.resetVerticalPos(),a=t.noteModel,await tP(h,a);break;case r.db.LINETYPE.ACTIVE_START:t_.newActivation(t,h,p);break;case r.db.LINETYPE.ACTIVE_END:I(t,t_.getVerticalPos());break;case r.db.LINETYPE.LOOP_START:tC(w,t,tL.boxMargin,tL.boxMargin+tL.boxTextMargin,t=>t_.newLoop(t));break;case r.db.LINETYPE.LOOP_END:e=t_.endLoop(),await tI.drawLoop(h,e,"loop",tL),t_.bumpVerticalPos(e.stopy-t_.getVerticalPos()),t_.models.addLoop(e);break;case r.db.LINETYPE.RECT_START:tC(w,t,tL.boxMargin,tL.boxMargin,t=>t_.newLoop(void 0,t.message));break;case r.db.LINETYPE.RECT_END:e=t_.endLoop(),k.push(e),t_.models.addLoop(e),t_.bumpVerticalPos(e.stopy-t_.getVerticalPos());break;case r.db.LINETYPE.OPT_START:tC(w,t,tL.boxMargin,tL.boxMargin+tL.boxTextMargin,t=>t_.newLoop(t));break;case r.db.LINETYPE.OPT_END:e=t_.endLoop(),await tI.drawLoop(h,e,"opt",tL),t_.bumpVerticalPos(e.stopy-t_.getVerticalPos()),t_.models.addLoop(e);break;case r.db.LINETYPE.ALT_START:tC(w,t,tL.boxMargin,tL.boxMargin+tL.boxTextMargin,t=>t_.newLoop(t));break;case r.db.LINETYPE.ALT_ELSE:tC(w,t,tL.boxMargin+tL.boxTextMargin,tL.boxMargin,t=>t_.addSectionToLoop(t));break;case r.db.LINETYPE.ALT_END:e=t_.endLoop(),await tI.drawLoop(h,e,"alt",tL),t_.bumpVerticalPos(e.stopy-t_.getVerticalPos()),t_.models.addLoop(e);break;case r.db.LINETYPE.PAR_START:case r.db.LINETYPE.PAR_OVER_START:tC(w,t,tL.boxMargin,tL.boxMargin+tL.boxTextMargin,t=>t_.newLoop(t)),t_.saveVerticalPos();break;case r.db.LINETYPE.PAR_AND:tC(w,t,tL.boxMargin+tL.boxTextMargin,tL.boxMargin,t=>t_.addSectionToLoop(t));break;case r.db.LINETYPE.PAR_END:e=t_.endLoop(),await tI.drawLoop(h,e,"par",tL),t_.bumpVerticalPos(e.stopy-t_.getVerticalPos()),t_.models.addLoop(e);break;case r.db.LINETYPE.AUTONUMBER:L=t.message.start||L,_=t.message.step||_,t.message.visible?r.db.enableSequenceNumbers():r.db.disableSequenceNumbers();break;case r.db.LINETYPE.CRITICAL_START:tC(w,t,tL.boxMargin,tL.boxMargin+tL.boxTextMargin,t=>t_.newLoop(t));break;case r.db.LINETYPE.CRITICAL_OPTION:tC(w,t,tL.boxMargin+tL.boxTextMargin,tL.boxMargin,t=>t_.addSectionToLoop(t));break;case r.db.LINETYPE.CRITICAL_END:e=t_.endLoop(),await tI.drawLoop(h,e,"critical",tL),t_.bumpVerticalPos(e.stopy-t_.getVerticalPos()),t_.models.addLoop(e);break;case r.db.LINETYPE.BREAK_START:tC(w,t,tL.boxMargin,tL.boxMargin+tL.boxTextMargin,t=>t_.newLoop(t));break;case r.db.LINETYPE.BREAK_END:e=t_.endLoop(),await tI.drawLoop(h,e,"break",tL),t_.bumpVerticalPos(e.stopy-t_.getVerticalPos()),t_.models.addLoop(e);break;default:try{(i=t.msgModel).starty=t_.getVerticalPos(),i.sequenceIndex=L,i.sequenceVisible=r.db.showSequenceNumbers();let e=await tA(h,i);tB(t,i,e,N,p,g,u),P.push({messageModel:i,lineStartY:e}),t_.models.addMessage(i)}catch(t){n.cM.error("error while drawing message",t)}}[r.db.LINETYPE.SOLID_OPEN,r.db.LINETYPE.DOTTED_OPEN,r.db.LINETYPE.SOLID,r.db.LINETYPE.DOTTED,r.db.LINETYPE.SOLID_CROSS,r.db.LINETYPE.DOTTED_CROSS,r.db.LINETYPE.SOLID_POINT,r.db.LINETYPE.DOTTED_POINT,r.db.LINETYPE.BIDIRECTIONAL_SOLID,r.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(L+=_),N++}for(let t of(n.cM.debug("createdActors",g),n.cM.debug("destroyedActors",u),await tO(h,p,y,!1),P))await tv(h,t.messageModel,t.lineStartY,r);for(let t of(tL.mirrorActors&&await tO(h,p,y,!0),k.forEach(t=>tI.drawBackgroundRect(h,t)),tr(h,p,y,tL),t_.models.boxes))t.height=t_.getVerticalPos()-t.y,t_.insert(t.x,t.y,t.x+t.width,t.height),t.startx=t.x,t.starty=t.y,t.stopx=t.startx+t.width,t.stopy=t.starty+t.height,t.stroke="rgb(0,0,0, 0.5)",tI.drawBox(h,t,tL);f&&t_.bumpVerticalPos(tL.boxMargin);let M=tD(h,p,y,d),{bounds:A}=t_.getBounds();void 0===A.startx&&(A.startx=0),void 0===A.starty&&(A.starty=0),void 0===A.stopx&&(A.stopx=0),void 0===A.stopy&&(A.stopy=0);let v=A.stopy-A.starty;v<M.maxHeight&&(v=M.maxHeight);let S=v+2*tL.diagramMarginY;tL.mirrorActors&&(S=S-tL.boxMargin+tL.bottomMarginAdj);let O=A.stopx-A.startx;O<M.maxWidth&&(O=M.maxWidth);let D=O+2*tL.diagramMarginX;b&&h.append("text").text(b).attr("x",(A.stopx-A.startx)/2-2*tL.diagramMarginX).attr("y",-25),(0,n.v2)(h,S,D,tL.useMaxWidth);let W=b?40:0;h.attr("viewBox",A.startx-tL.diagramMarginX+" -"+(tL.diagramMarginY+W)+" "+D+" "+(S+W)),n.cM.debug("models:",t_.models)},"draw");async function tV(t,e,a){let r={};for(let i of e)if(t.get(i.to)&&t.get(i.from)){let e=t.get(i.to);if(i.placement===a.db.PLACEMENT.LEFTOF&&!e.prevActor||i.placement===a.db.PLACEMENT.RIGHTOF&&!e.nextActor)continue;let o=void 0!==i.placement,c=!o,l=o?tN(tL):tk(tL),d=i.wrap?s.w8.wrapLabel(i.message,tL.width-2*tL.wrapPadding,l):i.message,h=(0,n.l0)(d)?await (0,n.nH)(i.message,(0,n.nV)()):s.w8.calculateTextDimensions(d,l),p=h.width+2*tL.wrapPadding;c&&i.from===e.nextActor?r[i.to]=n.SY.getMax(r[i.to]||0,p):c&&i.from===e.prevActor?r[i.from]=n.SY.getMax(r[i.from]||0,p):c&&i.from===i.to?(r[i.from]=n.SY.getMax(r[i.from]||0,p/2),r[i.to]=n.SY.getMax(r[i.to]||0,p/2)):i.placement===a.db.PLACEMENT.RIGHTOF?r[i.from]=n.SY.getMax(r[i.from]||0,p):i.placement===a.db.PLACEMENT.LEFTOF?r[e.prevActor]=n.SY.getMax(r[e.prevActor]||0,p):i.placement===a.db.PLACEMENT.OVER&&(e.prevActor&&(r[e.prevActor]=n.SY.getMax(r[e.prevActor]||0,p/2)),e.nextActor&&(r[i.from]=n.SY.getMax(r[i.from]||0,p/2)))}return n.cM.debug("maxMessageWidthPerActor:",r),r}(0,n.eW)(tV,"getMaxMessageWidthPerActor");var tF=(0,n.eW)(function(t){let e=0,a=tM(tL);for(let r in t.links){let t=s.w8.calculateTextDimensions(r,a),i=t.width+2*tL.wrapPadding+2*tL.boxMargin;e<i&&(e=i)}return e},"getRequiredPopupWidth");async function tq(t,e,a){let r=0;for(let e of t.keys()){let a=t.get(e);a.wrap&&(a.description=s.w8.wrapLabel(a.description,tL.width-2*tL.wrapPadding,tM(tL)));let i=(0,n.l0)(a.description)?await (0,n.nH)(a.description,(0,n.nV)()):s.w8.calculateTextDimensions(a.description,tM(tL));a.width=a.wrap?tL.width:n.SY.getMax(tL.width,i.width+2*tL.wrapPadding),a.height=a.wrap?n.SY.getMax(i.height,tL.height):tL.height,r=n.SY.getMax(r,a.height)}for(let a in e){let r=t.get(a);if(!r)continue;let i=t.get(r.nextActor);if(!i){let t=e[a],i=t+tL.actorMargin-r.width/2;r.margin=n.SY.getMax(i,tL.actorMargin);continue}let s=e[a],o=s+tL.actorMargin-r.width/2-i.width/2;r.margin=n.SY.getMax(o,tL.actorMargin)}let i=0;return a.forEach(e=>{let a=tk(tL),r=e.actorKeys.reduce((e,a)=>e+=t.get(a).width+(t.get(a).margin||0),0);r-=2*tL.boxTextMargin,e.wrap&&(e.name=s.w8.wrapLabel(e.name,r-2*tL.wrapPadding,a));let o=s.w8.calculateTextDimensions(e.name,a);i=n.SY.getMax(o.height,i);let c=n.SY.getMax(r,o.width+2*tL.wrapPadding);if(e.margin=tL.boxTextMargin,r<c){let t=(c-r)/2;e.margin+=t}}),a.forEach(t=>t.textMaxHeight=i),n.SY.getMax(r,tL.height)}(0,n.eW)(tq,"calculateActorMargins");var tz=(0,n.eW)(async function(t,e,a){let r=e.get(t.from),i=e.get(t.to),o=r.x,c=i.x,l=t.wrap&&t.message,d=(0,n.l0)(t.message)?await (0,n.nH)(t.message,(0,n.nV)()):s.w8.calculateTextDimensions(l?s.w8.wrapLabel(t.message,tL.width,tN(tL)):t.message,tN(tL)),h={width:l?tL.width:n.SY.getMax(tL.width,d.width+2*tL.noteMargin),height:0,startx:r.x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===a.db.PLACEMENT.RIGHTOF?(h.width=l?n.SY.getMax(tL.width,d.width):n.SY.getMax(r.width/2+i.width/2,d.width+2*tL.noteMargin),h.startx=o+(r.width+tL.actorMargin)/2):t.placement===a.db.PLACEMENT.LEFTOF?(h.width=l?n.SY.getMax(tL.width,d.width+2*tL.noteMargin):n.SY.getMax(r.width/2+i.width/2,d.width+2*tL.noteMargin),h.startx=o-h.width+(r.width-tL.actorMargin)/2):t.to===t.from?(d=s.w8.calculateTextDimensions(l?s.w8.wrapLabel(t.message,n.SY.getMax(tL.width,r.width),tN(tL)):t.message,tN(tL)),h.width=l?n.SY.getMax(tL.width,r.width):n.SY.getMax(r.width,tL.width,d.width+2*tL.noteMargin),h.startx=o+(r.width-h.width)/2):(h.width=Math.abs(o+r.width/2-(c+i.width/2))+tL.actorMargin,h.startx=o<c?o+r.width/2-tL.actorMargin/2:c+i.width/2-tL.actorMargin/2),l&&(h.message=s.w8.wrapLabel(t.message,h.width-2*tL.wrapPadding,tN(tL))),n.cM.debug(`NM:[${h.startx},${h.stopx},${h.starty},${h.stopy}:${h.width},${h.height}=${t.message}]`),h},"buildNoteModel"),tH=(0,n.eW)(function(t,e,a){if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT,a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type))return{};let[r,i]=tY(t.from,e),[o,c]=tY(t.to,e),l=r<=o,d=l?i:r,h=l?o:c,p=(0,n.eW)(t=>l?-t:t,"adjustValue");t.from===t.to?h=d:(t.activate&&!(Math.abs(o-c)>2)&&(h+=p(tL.activationWidth/2-1)),[a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN].includes(t.type)||(h+=p(3)),[a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(d-=p(3)));let g=[r,i,o,c],u=Math.abs(d-h);t.wrap&&t.message&&(t.message=s.w8.wrapLabel(t.message,n.SY.getMax(u+2*tL.wrapPadding,tL.width),tk(tL)));let x=s.w8.calculateTextDimensions(t.message,tk(tL));return{width:n.SY.getMax(t.wrap?0:x.width+2*tL.wrapPadding,u+2*tL.wrapPadding,tL.width),height:0,startx:d,stopx:h,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,g),toBounds:Math.max.apply(null,g)}},"buildMessageModel"),tj=(0,n.eW)(async function(t,e,a,r){let i,o,c;let l={},d=[];for(let a of t){switch(a.id=s.w8.random({length:10}),a.type){case r.db.LINETYPE.LOOP_START:case r.db.LINETYPE.ALT_START:case r.db.LINETYPE.OPT_START:case r.db.LINETYPE.PAR_START:case r.db.LINETYPE.PAR_OVER_START:case r.db.LINETYPE.CRITICAL_START:case r.db.LINETYPE.BREAK_START:d.push({id:a.id,msg:a.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case r.db.LINETYPE.ALT_ELSE:case r.db.LINETYPE.PAR_AND:case r.db.LINETYPE.CRITICAL_OPTION:a.message&&(l[(i=d.pop()).id]=i,l[a.id]=i,d.push(i));break;case r.db.LINETYPE.LOOP_END:case r.db.LINETYPE.ALT_END:case r.db.LINETYPE.OPT_END:case r.db.LINETYPE.PAR_END:case r.db.LINETYPE.CRITICAL_END:case r.db.LINETYPE.BREAK_END:l[(i=d.pop()).id]=i;break;case r.db.LINETYPE.ACTIVE_START:{let t=e.get(a.from?a.from:a.to.actor),r=tR(a.from?a.from:a.to.actor).length,i=t.x+t.width/2+(r-1)*tL.activationWidth/2,s={startx:i,stopx:i+tL.activationWidth,actor:a.from,enabled:!0};t_.activations.push(s)}break;case r.db.LINETYPE.ACTIVE_END:{let t=t_.activations.map(t=>t.actor).lastIndexOf(a.from);t_.activations.splice(t,1).splice(0,1)}}let t=void 0!==a.placement;t?(o=await tz(a,e,r),a.noteModel=o,d.forEach(t=>{(i=t).from=n.SY.getMin(i.from,o.startx),i.to=n.SY.getMax(i.to,o.startx+o.width),i.width=n.SY.getMax(i.width,Math.abs(i.from-i.to))-tL.labelBoxWidth})):(c=tH(a,e,r),a.msgModel=c,c.startx&&c.stopx&&d.length>0&&d.forEach(t=>{if(i=t,c.startx===c.stopx){let t=e.get(a.from),r=e.get(a.to);i.from=n.SY.getMin(t.x-c.width/2,t.x-t.width/2,i.from),i.to=n.SY.getMax(r.x+c.width/2,r.x+t.width/2,i.to),i.width=n.SY.getMax(i.width,Math.abs(i.to-i.from))-tL.labelBoxWidth}else i.from=n.SY.getMin(c.startx,i.from),i.to=n.SY.getMax(c.stopx,i.to),i.width=n.SY.getMax(i.width,c.width)-tL.labelBoxWidth}))}return t_.activations=[],n.cM.debug("Loop type widths:",l),l},"calculateLoopBounds"),tU={parser:l,db:H,renderer:{bounds:t_,drawActors:tO,drawActorsPopup:tD,setConf:tW,draw:t$},styles:j,init:(0,n.eW)(({wrap:t})=>{H.setWrap(t)},"init")}}}]);